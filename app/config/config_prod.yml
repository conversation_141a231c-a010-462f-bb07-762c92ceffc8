imports:
    - { resource: config.yml }

monolog:
    handlers:
        # The application is scaled in one or more docker containers
        # Therefore file logging do not serve any purpose as the logs are purged each new release
        # Following <PERSON> recommendation, we log errors in stderr only. On his side it will be sent to Kibana
        # We also keep the Sentry handlers which will have the errors below error level (see config comments below)
        # Do no trigger until level is at least error and log only in stderr
        main:
            type:         fingers_crossed
            action_level: error
            handler:      nested

        nested:
            type:  stream
            path:  "php://stderr"
            level: error

        # Buffer all logs until an error has been reached
        buffered:
            type:    buffer
            level:   error
            handler: sentry

        # Message below error levels should be registered as breadcrumb in sentry
        finger_crossed:
            type:    fingers_crossed
            level:   error
            handler: sentry_breadcrumb

        # Sentry logging handler
        sentry:
            type: service
            id: SonVideo\Cms\FrontOffice\AppBundle\Sentry\MonologHandler
            channels: ['!sentry', '!pheanstalk', '!event', '!security', '!request']

        sentry_breadcrumb:
            type: service
            id: SonVideo\Cms\FrontOffice\AppBundle\Sentry\MonologBreadcrumbHandler
            channels: ['!sentry', '!pheanstalk', '!event', '!security', '!request']


        # uncomment to get logging in your browser
        # you may have to allow bigger header sizes in your Web server configuration
        console:
            type: console
            process_psr_3_messages: false
            channels: ["!event", "!doctrine", "!console"]

oneup_flysystem:
    adapters:
        media_adapter:
            awss3v3:
                client: app.s3_client.media_bucket
                bucket: "%aws.media_bucket.name%"
                prefix: ~
                options:
                    ACL: 'public-read'
                    CacheControl: 'max-age=604800'
        legacy_adapter:
            awss3v3:
                client: app.s3_client.erp_backoffice_bucket
                bucket: "%aws.erp_backoffice_bucket.name%"
                prefix: ~
                options:
                    ACL: 'public-read'
        webradio_adapter:
            ftp:
                host: '%webradio_filesystem.host%'
                username: '%webradio_filesystem.login%'
                password: '%webradio_filesystem.password%'
                timeout: 20
        sitemap_adapter:
            awss3v3:
                client: app.s3_client.sitemap_bucket
                bucket: "%aws.sitemap_bucket.name%"
                prefix: ~
                options:
                    ACL: 'public-read'

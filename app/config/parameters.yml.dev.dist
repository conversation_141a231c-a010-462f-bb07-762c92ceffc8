# This file is a "template" of what your parameters.yml file should look like
# Set parameters here that may be different on each deployment target of the app, e.g. development, staging, production.
# http://symfony.com/doc/current/best_practices/configuration.html#infrastructure-related-configuration
parameters:
    database.host:                  cms-postgresql
    database.port:                  5432
    database.name:                  svd_cms
    database.test_port:             5432
    database.test_name:             svd_cms_test
    database.test_name_template:    svd_cms_test_fo_template
    database.user:                  fo_cms_user
    database.password:              azerty
    database.user.cli:              fo_cms_cli
    database.password.cli:          azerty
    database.superuser_password:    devteam
    # You should uncomment this if you want use pdo_sqlite
    # database.path: "%kernel.root_dir%/

    # A secret key that's used to generate certain security-related tokens
    secret:            ThisTokenIsNotSoSecretChangeIt

    # Base url for Behat tests
    behat_url_root: test.fo-cms.lxc

    sentry.dsn: 'https://<EMAIL>/6'
    sentry.app_path: /var/www/cms-front

    beanstalk.host: beanstalkd
    beanstalk.port: 11300
    synapps.server_uri: 'http://synapps.lxc/api'
    synapps.tube: staging
    # Use synapps:register to get a client UUID from synapps server.
    synapps.client_id: 8523988d-6376-4388-8c10-f26b98ceeb0b

    # Country parameters for current instance of website
    country: FR
    currency: EUR
    vat_rate: 0.2
    withdrawal_period_in_days: 15

    # Useful for sitemap url generation
    base_url: 'https://www.son-video.com'

    # In dev, overwrite the assets paths for distribution with Webpack-dev-server
    dev.webpack.url: 'https://localhost:8888/'
    dev.webpack.enable: true

    cdn.article_images:   'https://image-stg.son-video.com'
    cdn.clickbait_images: 'https://image-stg.son-video.com'
    cdn.static_images: 'https://image-stg.son-video.com'
    cdn.assets: 'https://fo-cms.lxc'
    cdn.assets.test: 'http://test.fo-cms.lxc'

    # AWS for cdn. Only for staging and prod.
    aws.media_bucket.version:            '2006-03-01'
    aws.media_bucket.region:             'eu-west-2'
    aws.media_bucket.name:               'svd-statics-stg'
    aws.media_bucket.credentials.key:    's3-key'
    aws.media_bucket.credentials.secret: 's3-secret'

    # AWS for sitemap. Only for staging and prod.
    aws.sitemap_bucket.version:            '2006-03-01'
    aws.sitemap_bucket.region:             'eu-west-2'
    aws.sitemap_bucket.name:               'svd-statics-stg'
    aws.sitemap_bucket.credentials.key:    's3-key'
    aws.sitemap_bucket.credentials.secret: 's3-secret'

    # AWS for erp_backoffice.
    aws.erp_backoffice_bucket.version:            '2006-03-01'
    aws.erp_backoffice_bucket.region:             'eu-west-2'
    aws.erp_backoffice_bucket.name:               'svd-erp-backoffice-stg'
    aws.erp_backoffice_bucket.credentials.key:    's3-key'
    aws.erp_backoffice_bucket.credentials.secret: 's3-secret'

    # Lifetime for a 'silent authentication' session (impersonation for example)
    silent_authentication_session_lifetime: 900

    # Varnish
    varnish_servers:
        - '127.0.0.1:80'
    varnish_base_url: 'fo-cms.lxc'

    # sftp access to legacy filesystem
    legacy_filesystem_host: '************'
    legacy_filesystem_login: support
    legacy_filesystem_password: password

    # SOAP
    soap.so_colissimo.account_number:
    soap.so_colissimo.password:

    soap.mondial_relay.wdsl: https://api.mondialrelay.com/Web_Services.asmx?WSDL
    soap.mondial_relay.shop_identifier: 'F2SVDIST'
    soap.mondial_relay.private_key: password

    soap.chronopost_pickup.wdsl: https://ws.chronopost.fr/recherchebt-ws-cxf/PointRelaisServiceWS?wsdl
    soap.chronopost_pickup.account: '********'
    soap.chronopost_pickup.password: '012542'

    soap.chronopost_precise.wdsl: https://ws.chronopost.fr/rdv-cxf/services/CreneauServiceWS?wsdl
    soap.chronopost_precise.account: '********'
    soap.chronopost_precise.password: '255562'

    # Axialys (immediate recall)
    axialys.username: ''
    axialys.password: ''

    # Sendy
    sendy.url:     'https://sendy.son-video.com'
    sendy.api_key: replace_me
    sendy.list_id: 'XW3h1HExSDlc3t3q17630wQw'

    # Google IDs
    google_analytics_id: 'UA-292967-22'
    google_ga4_id: 'G-Q20M72N24V'
    google_merchant_id: 7197860
    google_tagmanager_id: 'GTM-NMSQ6SJ'
    google_tagmanager_auth: '9GKkq6X3oTMN2RsBYgC2cQ'
    google_tagmanager_preview: 'env-24'
    google_maps_api_key: 'AIzaSyCDaliKxRsGwyzYjD73j4NTJMwhutIvJ8E'

    # reCaptcha
    recaptcha.public_key:  replace_me
    recaptcha.private_key: replace_me
    recaptcha.enabled:     true

    # Bazaarvoice staging & dev
    bazaarvoice_script_url: '//son-video.ugc.bazaarvoice.com/bvstaging/static/7890-fr_fr/bvapi.js'
    bazaarvoice_document_domain: 'son-video.work'

    # WebRadio filesystem conf
    webradio_filesystem.host: 'ftp.son-video.com'
    webradio_filesystem.login:
    webradio_filesystem.password:
    webradio_filesystem.file.playing_now: 'playingnow.xml'
    webradio_filesystem.file.not_found_covers_log: 'images_absentes.txt'

    # Catalog info in order to active period where customer can add it in shopping cart
    catalog_in_basket:
        # accessory_id for the catalog
        catalog_id:   143169
        # Data used to know if catalog is sent or not
        catalog_year: 2021
        # Thumbnail displayed in basket
        catalog_thumbnail: '/images/illustration/pages/catalogue/2021/SVDCATA_202111_Catalogue2021-Article.jpg'
        # Period for display catalog in shopping cart
        begin_date:   '2000-11-27 00:00:00'
        end_date:     '2001-01-31 23:59:59'

    # Time to wait at least on the processing payment page before redirection, in seconds
    processing_page_timer: 2

    # Advice API
    api.advice.url: 'http://advice.lxc'

    # Payment API
    api.payment.url: 'http://payment.lxc'
    api.payment.authentication_key: '904efb77-ea11-40d5-ba69-945a96acc339'

    # Payment API V2
    api.payment_v2.url: 'https://erp-payment.lxc'
    api.payment_v2.internal_api_secret: 'PAYMENT_V2_INTERNAL_API_SECRET_KEY'

    # ElasticSearch
    elastic_search.urls: ['https://cms-elasticsearch:9200']
    elastic_search.id: 'id data of api key'
    elastic_search.api_key: 'api_key data of api key'

    # AppSearch
    elastic.app_search:
        endpoint: 'http://cms-enterprise-search:3002'
        token: 'remplacer par private-key présent dans la page https://cms-kibana.lxc/app/enterprise_search/app_search/credentials'

    # SIPS Paypage (Atos)
    sips_paypage.secret_key: '002001000000003_KEY1'

    # Crypter
    rpc.crypter_passphrase: 'dev_crypter_passphrase'

services:
    app.test_command:
        class: SonVideo\OneRedirection\Command\RewriteGenerateCommand
        calls:
            - ['setPommSession', ['@=service("pomm").getSession("cli")']]
        tags:
            - { name: console.command }

    elastic_search_client_builder:
        class: Elasticsearch\ClientBuilder
        calls:
            - ['setHosts', ['%elastic_search.urls%']]
            - ['setApiKey', ['%elastic_search.id%', '%elastic_search.api_key%']]
            - ['setTracer', ['@logger']]
            - ['setSSLVerification', [false]]

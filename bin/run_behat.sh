#!/bin/bash

# Pagination
# By default, all files are handled.
# If you want to cut your script in n execution, do:
#    ./run_behat.sh 1 n
#    ./run_behat.sh 2 n
#    ...
#    ./run_behat.sh n-1 n
#    ./run_behat.sh n n

function usage {
    echo "run_behat.sh <test_dir> [iterator] [max_iterator]"
    echo
    echo "  ex: ./run_behat.sh tests/Acceptance/features"
    echo "  ex: ./run_behat.sh tests/Acceptance/features 1 3"
    echo
    echo "Using iterator"
    echo "--------------"
    echo " By default, all files are handled."
    echo " If you want to cut your script in n execution, do:"
    echo "    ./run_behat.sh <test_dir> 1 n"
    echo "    ./run_behat.sh <test_dir> 2 n"
    echo "    ..."
    echo "    ./run_behat.sh <test_dir> n-1 n"
    echo "    ./run_behat.sh <test_dir> n n"
}


if [ "$#" -lt 1 ]; then
    usage
    exit 1
fi

TEST_DIR=$1
CURRENT_ITERATOR=${2:-'1'}
MAX_ITERATOR=${3:-'1'}

DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_DIR=$DIR/..

GREEN='\033[0;32m'
RED='\033[0;31m'
NO_COLOR='\033[0m'

cd "$PROJECT_DIR"

FILES_COUNT=`find  "$TEST_DIR" -type f -name "*.feature" | wc -l`
LIMIT=$((($FILES_COUNT + $MAX_ITERATOR - 1) / $MAX_ITERATOR))
TAIL=$(($FILES_COUNT - (($CURRENT_ITERATOR - 1) * $LIMIT)))


FILES_LIST=`find  "$TEST_DIR" -type f -name "*.feature" | sort | tail -n $TAIL | head -n $LIMIT`

tries=0
while : ; do
    tries=$((tries+1))
    ERROR_FILES_LIST=()

    for FEATURE_FILE in $FILES_LIST
    do
        echo -e "${GREEN}==> Start ${FEATURE_FILE}, try number [${tries}] ...${NO_COLOR}"
        php ./vendor/bin/behat "$FEATURE_FILE"
        if [ ! $? -eq 0 ]; then
            ERROR_FILES_LIST+=( "$FEATURE_FILE" )
        fi
    done

    if [ ${#ERROR_FILES_LIST[@]} -eq 0 ]; then
        break;
    fi

    if [ $tries -ge 5 ]; then
        echo
        echo -e "${RED}==> Tests fails 5 times.${NO_COLOR}"
        echo
        echo "Tests in error:"
        for FEATURE_FILE in ${ERROR_FILES_LIST[*]}
        do
            echo $FEATURE_FILE
        done
        exit 1
    fi

    FILES_LIST=${ERROR_FILES_LIST[*]}
    sleep 2
done

echo "done"

cd -

exit 0

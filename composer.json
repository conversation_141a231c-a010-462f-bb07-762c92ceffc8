{"name": "benoit/cms-front", "license": "proprietary", "type": "project", "autoload": {"psr-4": {"SonVideo\\Cms\\FrontOffice\\": "src/"}, "classmap": ["app/AppKernel.php", "app/AppCache.php"]}, "autoload-dev": {"psr-4": {"SonVideo\\Cms\\FrontOffice\\Tests\\": "tests/"}}, "repositories": [{"type": "vcs", "url": "**************:son-video/svd-services.git"}, {"type": "vcs", "url": "**************:son-video/cms-schema.git"}, {"type": "vcs", "url": "**************:son-video/synapps-client.git"}, {"type": "vcs", "url": "**************:son-video/svd-rpc-bundle"}, {"type": "vcs", "url": "**************:son-video/one-redirection.git"}, {"type": "vcs", "url": "**************:son-video/advice-client-bundle.git"}, {"type": "vcs", "url": "**************:son-video/advice-client.git"}], "config": {"github-token": {"github.com": "****************************************"}, "gitlab-token": {"gitlab.com": "**************************"}, "sort-packages": true}, "require": {"php": ">=7.4", "ext-intl": "*", "ext-json": "*", "checkdomain/holiday": "^2.2", "cocur/slugify": "^2.5", "dompdf/dompdf": "^0.8.0", "egulias/email-validator": "^2.1", "elastic/enterprise-search": "8.14.x-dev", "elasticsearch/elasticsearch": "^7.5", "excelwebzone/recaptcha-bundle": "^1.4", "friendsofsymfony/http-cache-bundle": "^2.1", "giggsey/libphonenumber-for-php": "^8.7", "guzzlehttp/guzzle": "^7.0", "guzzlehttp/psr7": "^1.4", "jacobbennett/sendyphp": "^1.3", "league/flysystem-aws-s3-v3": "^1.0", "league/flysystem-sftp": "^1.0", "oneup/flysystem-bundle": "^2.0", "picqer/php-barcode-generator": "^v2.4.0", "pomm-project/pomm-bundle": "2.2.*", "ramsey/uuid": "^3.7", "sensio/distribution-bundle": "^5.0", "sensio/framework-extra-bundle": "^5.0", "sentry/sentry-symfony": "^3.4", "son-video/advice-client": "^1.0", "son-video/advice-client-bundle": "^1.1", "son-video/svd-rpc-bundle": "^v1.2", "son-video/svd-services": "^1.0", "son-video/synapps-client": "^1.1", "symfony/monolog-bundle": "^3.2", "symfony/symfony": "3.4.49", "symfony/webpack-encore-bundle": "^1.8", "twig/extensions": "~1.3", "twig/twig": "^1.35", "willdurand/js-translation-bundle": "^2.6"}, "require-dev": {"atoum/atoum": "^4.1", "behat/behat": "^3.1", "behat/mink": "^1.7", "behat/mink-extension": "^2.2", "behat/mink-selenium2-driver": "^1.3", "behat/symfony2-extension": "^2.1", "behatch/contexts": "*", "phpstan/phpstan": "^1.10", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-symfony": "^1.2", "rector/rector": "^1.2.4", "sensio/generator-bundle": "^3.0", "son-video/cms-schema": "dev-2432_consent_display_order", "son-video/one-redirection": "dev-dev", "symfony/web-server-bundle": "^3.4"}, "scripts": {"post-install-cmd": ["Incenteev\\ParameterHandler\\ScriptHandler::buildParameters", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::buildBootstrap", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::clearCache", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::installAssets", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::installRequirementsFile", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::prepareDeploymentTarget"], "post-update-cmd": ["Incenteev\\ParameterHandler\\ScriptHandler::buildParameters", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::buildBootstrap", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::clearCache", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::installAssets", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::installRequirementsFile", "Sensio\\Bundle\\DistributionBundle\\Composer\\ScriptHandler::prepareDeploymentTarget"]}, "extra": {"symfony-app-dir": "app", "symfony-bin-dir": "bin", "symfony-var-dir": "var", "symfony-web-dir": "web", "symfony-tests-dir": "tests", "symfony-assets-install": "relative", "incenteev-parameters": {"file": "app/config/parameters.yml", "keep-outdated": true}}}
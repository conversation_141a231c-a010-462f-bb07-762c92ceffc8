{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "a5afe1ac7d4b71b366675cb304ff49bb", "packages": [{"name": "aws/aws-crt-php", "version": "v1.2.6", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "a63485b65b6b3367039306496d49737cf1995408"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/a63485b65b6b3367039306496d49737cf1995408", "reference": "a63485b65b6b3367039306496d49737cf1995408", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "https://github.com/awslabs/aws-crt-php", "keywords": ["amazon", "aws", "crt", "sdk"], "support": {"issues": "https://github.com/awslabs/aws-crt-php/issues", "source": "https://github.com/awslabs/aws-crt-php/tree/v1.2.6"}, "time": "2024-06-13T17:21:28+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.319.2", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "99b75502221af095f0a5a72b0df7ec44e865e699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/99b75502221af095f0a5a72b0df7ec44e865e699", "reference": "99b75502221af095f0a5a72b0df7ec44e865e699", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.2.3", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "guzzlehttp/promises": "^1.4.0 || ^2.0", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "mtdowling/jmespath.php": "^2.6", "php": ">=7.2.5", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "composer/composer": "^1.10.22", "dms/phpunit-arraysubset-asserts": "^0.4.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^5.6.3 || ^8.5 || ^9.5", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "sebastian/comparator": "^1.2.3 || ^4.0", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}, "exclude-from-classmap": ["src/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.319.2"}, "time": "2024-08-09T18:32:29+00:00"}, {"name": "checkdomain/holiday", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/checkdomain/Holiday.git", "reference": "a72d6be9fab775dc82a018d17366d44c7057265f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/checkdomain/Holiday/zipball/a72d6be9fab775dc82a018d17366d44c7057265f", "reference": "a72d6be9fab775dc82a018d17366d44c7057265f", "shasum": ""}, "require": {"ext-calendar": "*", "php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-0": {"Checkdomain\\Holiday": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://checkdomain.de"}], "description": "Checkdomain/Holiday provides a simple class to get holidays for a specified country", "keywords": ["holiday"], "support": {"issues": "https://github.com/checkdomain/Holiday/issues", "source": "https://github.com/checkdomain/Holiday/tree/master"}, "time": "2017-09-18T07:19:14+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "cocur/slugify", "version": "v2.5", "source": {"type": "git", "url": "https://github.com/cocur/slugify.git", "reference": "e8167e9a3236044afebd6e8ab13ebeb3ec9ca145"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cocur/slugify/zipball/e8167e9a3236044afebd6e8ab13ebeb3ec9ca145", "reference": "e8167e9a3236044afebd6e8ab13ebeb3ec9ca145", "shasum": ""}, "require": {"php": ">=5.5.9"}, "require-dev": {"laravel/framework": "~5.1", "latte/latte": "~2.2", "league/container": "^2.2.0", "mikey179/vfsstream": "~1.6", "mockery/mockery": "~0.9", "nette/di": "~2.2", "phpunit/phpunit": "~4.8|~5.2", "pimple/pimple": "~1.1", "plumphp/plum": "~0.1", "silex/silex": "~1.3", "symfony/config": "~2.4|~3.0", "symfony/dependency-injection": "~2.4|~3.0", "symfony/http-kernel": "~2.4|~3.0", "twig/twig": "~1.26|~2.0", "zendframework/zend-modulemanager": "~2.2", "zendframework/zend-servicemanager": "~2.2", "zendframework/zend-view": "~2.2"}, "type": "library", "autoload": {"psr-4": {"Cocur\\Slugify\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://florian.ec"}], "description": "Converts a string into a slug.", "keywords": ["slug", "slugify"], "support": {"issues": "https://github.com/cocur/slugify/issues", "source": "https://github.com/cocur/slugify/tree/master"}, "time": "2017-03-23T21:52:55+00:00"}, {"name": "datto/json-rpc", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/datto/php-json-rpc.git", "reference": "0f4b4e574d83ecda2cd0819afddc3003f923f804"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/datto/php-json-rpc/zipball/0f4b4e574d83ecda2cd0819afddc3003f923f804", "reference": "0f4b4e574d83ecda2cd0819afddc3003f923f804", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~3.7"}, "type": "library", "autoload": {"psr-4": {"Datto\\JsonRpc\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://spencermortensen.com", "role": "Developer"}], "description": "Fully unit-tested JSON-RPC 2.0 for PHP", "homepage": "http://datto.com", "keywords": ["json", "json-rpc", "jsonrpc", "php", "php-json-rpc", "rpc"], "support": {"issues": "https://github.com/datto/php-json-rpc/issues", "source": "https://github.com/datto/php-json-rpc/tree/master"}, "time": "2017-09-07T03:13:57+00:00"}, {"name": "datto/json-rpc-http", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/datto/php-json-rpc-http.git", "reference": "e53e3bd597f0bddaa95b0c577385a5f8d6444543"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/datto/php-json-rpc-http/zipball/e53e3bd597f0bddaa95b0c577385a5f8d6444543", "reference": "e53e3bd597f0bddaa95b0c577385a5f8d6444543", "shasum": ""}, "require": {"datto/json-rpc": "~4.0", "php": ">=5.3.0"}, "type": "library", "autoload": {"psr-4": {"Datto\\JsonRpc\\Http\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://spencermortensen.com", "role": "Developer"}], "description": "HTTP client and server for JSON-RPC 2.0", "homepage": "http://datto.com", "keywords": ["client", "http", "json", "json-rpc", "jsonrpc", "php", "php-json-rpc", "rpc", "server"], "support": {"issues": "https://github.com/datto/php-json-rpc-http/issues", "source": "https://github.com/datto/php-json-rpc-http/tree/master"}, "time": "2017-09-12T01:08:04+00:00"}, {"name": "doctrine/annotations", "version": "1.14.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.3"}, "time": "2023-02-01T09:20:38+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/collections", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "phpstan/phpstan": "^1.4.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.8.0"}, "time": "2022-09-01T20:12:10+00:00"}, {"name": "doctrine/common", "version": "2.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/f3812c026e557892c34ef37f6ab808a6b567da7f", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3", "doctrine/reflection": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.13.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2020-06-05T16:46:05+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/event-manager", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:51:15+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "doctrine/persistence", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/7a6eac9fb6f61bba91328f15aa7547f4806ca288", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2020-06-20T12:56:16+00:00"}, {"name": "doctrine/reflection", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7", "reference": "6bcea3e81ab8b3d0abe5fde5300bbc8a968960c7", "shasum": ""}, "require": {"doctrine/annotations": "^1.0 || ^2.0", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9", "doctrine/common": "^3.3", "phpstan/phpstan": "^1.4.10", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.4"}, "abandoned": "roave/better-reflection", "time": "2023-07-27T18:11:59+00:00"}, {"name": "dompdf/dompdf", "version": "v0.8.6", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "db91d81866c69a42dad1d2926f61515a1e3f42c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/db91d81866c69a42dad1d2926f61515a1e3f42c5", "reference": "db91d81866c69a42dad1d2926f61515a1e3f42c5", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "phenx/php-font-lib": "^0.5.2", "phenx/php-svg-lib": "^0.3.3", "php": "^7.1"}, "require-dev": {"mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}, "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "support": {"issues": "https://github.com/dompdf/dompdf/issues", "source": "https://github.com/dompdf/dompdf/tree/master"}, "time": "2020-08-30T22:54:22+00:00"}, {"name": "egulias/email-validator", "version": "2.1.25", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.25"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2020-12-29T14:50:06+00:00"}, {"name": "elastic/enterprise-search", "version": "8.14.x-dev", "source": {"type": "git", "url": "https://github.com/elastic/enterprise-search-php.git", "reference": "77ded171e6e285cc710a19ba0dc9c3ba4736ec03"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/enterprise-search-php/zipball/77ded171e6e285cc710a19ba0dc9c3ba4736ec03", "reference": "77ded171e6e285cc710a19ba0dc9c3ba4736ec03", "shasum": ""}, "require": {"elastic/transport": "^8.0.0", "nyholm/psr7": "^1.3", "php": "^7.4 || ^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.0", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/guzzle": "We suggest to use Guzzle as PSR-18 HTTP library"}, "type": "library", "autoload": {"psr-4": {"Elastic\\EnterpriseSearch\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Enterprise Search official PHP client", "homepage": "https://github.com/elastic/enterprise-search-php", "keywords": ["appsearch", "client", "elastic", "enterprise", "enterprisesearch", "search", "workplace", "workplacesearch"], "support": {"issues": "https://github.com/elastic/enterprise-search-php/issues", "source": "https://github.com/elastic/enterprise-search-php/tree/main"}, "time": "2023-09-28T15:25:36+00:00"}, {"name": "elastic/transport", "version": "v8.8.0", "source": {"type": "git", "url": "https://github.com/elastic/elastic-transport-php.git", "reference": "cdf9f63a16ec6bfb4c881ab89aa0e2a61fb7c20b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elastic-transport-php/zipball/cdf9f63a16ec6bfb4c881ab89aa0e2a61fb7c20b", "reference": "cdf9f63a16ec6bfb4c881ab89aa0e2a61fb7c20b", "shasum": ""}, "require": {"composer-runtime-api": "^2.0", "php": "^7.4 || ^8.0", "php-http/discovery": "^1.14", "php-http/httplug": "^2.3", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"nyholm/psr7": "^1.5", "php-http/mock-client": "^1.5", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Elastic\\Transport\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "HTTP transport PHP library for Elastic products", "keywords": ["PSR_17", "elastic", "http", "psr-18", "psr-7", "transport"], "support": {"issues": "https://github.com/elastic/elastic-transport-php/issues", "source": "https://github.com/elastic/elastic-transport-php/tree/v8.8.0"}, "time": "2023-11-08T10:51:51+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.2", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "2d302233f2bb0926812d82823bb820d405e130fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/2d302233f2bb0926812d82823bb820d405e130fc", "reference": "2d302233f2bb0926812d82823bb820d405e130fc", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v7.17.2"}, "time": "2023-04-21T15:31:12+00:00"}, {"name": "excelwebzone/recaptcha-bundle", "version": "v1.5.40", "source": {"type": "git", "url": "https://github.com/excelwebzone/EWZRecaptchaBundle.git", "reference": "5e6c96f67cc0776b7cd795730321c3f492737399"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/excelwebzone/EWZRecaptchaBundle/zipball/5e6c96f67cc0776b7cd795730321c3f492737399", "reference": "5e6c96f67cc0776b7cd795730321c3f492737399", "shasum": ""}, "require": {"google/recaptcha": "^1.1", "php": "^7.1 || ^8.0", "symfony/form": "^2.8 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0", "symfony/framework-bundle": "^2.8 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0", "symfony/security-bundle": "^2.8 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0", "symfony/validator": "^2.8 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0", "symfony/yaml": "^2.8 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0", "twig/twig": "^1.40 || ^2.9 || ^3.0"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9.5"}, "type": "symfony-bundle", "extra": {"symfony": {"allow-contrib": "true"}}, "autoload": {"psr-4": {"EWZ\\Bundle\\RecaptchaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://excelwebzone.com/"}], "description": "This bundle provides easy reCAPTCHA form field integration", "homepage": "https://github.com/excelwebzone/EWZRecaptchaBundle", "keywords": ["recaptcha"], "support": {"issues": "https://github.com/excelwebzone/EWZRecaptchaBundle/issues", "source": "https://github.com/excelwebzone/EWZRecaptchaBundle/tree/v1.5.40"}, "time": "2024-01-09T14:23:35+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/b4b5a025dfee70d6cd34c780e07330eb93d5b997", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.1.0"}, "time": "2022-10-24T12:58:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/7887fc8488013065f72f977dcb281994f5fde9f4", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.2"}, "time": "2022-12-07T11:28:53+00:00"}, {"name": "fig/link-util", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/link-util.git", "reference": "5d7b8d04ed3393b4b59968ca1e906fb7186d81e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link-util/zipball/5d7b8d04ed3393b4b59968ca1e906fb7186d81e8", "reference": "5d7b8d04ed3393b4b59968ca1e906fb7186d81e8", "shasum": ""}, "require": {"php": ">=5.5.0", "psr/link": "~1.0@dev"}, "provide": {"psr/link-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "^5.1", "squizlabs/php_codesniffer": "^2.3.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Fig\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common utility implementations for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "support": {"issues": "https://github.com/php-fig/link-util/issues", "source": "https://github.com/php-fig/link-util/tree/1.1.2"}, "time": "2021-02-03T23:36:04+00:00"}, {"name": "friendsofsymfony/http-cache", "version": "2.12.1", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSHttpCache.git", "reference": "4d4f3597dd5c59f33e2c87559937e40215811a1a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSHttpCache/zipball/4d4f3597dd5c59f33e2c87559937e40215811a1a", "reference": "4d4f3597dd5c59f33e2c87559937e40215811a1a", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "php-http/client-common": "^1.1.0 || ^2.0", "php-http/client-implementation": "^1.0 || ^2.0", "php-http/discovery": "^1.12", "php-http/message": "^1.0 || ^2.0", "symfony/event-dispatcher": "^3.4 || ^4.3 || ^5.0", "symfony/options-resolver": "^3.4 || ^4.3 || ^5.0"}, "conflict": {"toflar/psr6-symfony-http-cache-store": "<2.2.1"}, "require-dev": {"mockery/mockery": "^1.3.1", "monolog/monolog": "^1.0", "php-http/guzzle7-adapter": "^0.1.1", "php-http/mock-client": "^1.2", "symfony/http-kernel": "^3.4 || ^4.3 || ^5.0", "symfony/phpunit-bridge": "^5.0", "symfony/process": "^3.4 || ^4.3 || ^5.0"}, "suggest": {"friendsofsymfony/http-cache-bundle": "For integration with the Symfony framework", "monolog/monolog": "For logging issues while invalidating"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.10.x-dev"}}, "autoload": {"psr-4": {"FOS\\HttpCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Liip AG", "homepage": "http://www.liip.ch/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.driebit.nl"}, {"name": "Community contributions", "homepage": "https://github.com/friendsofsymfony/FOSHttpCache/contributors"}], "description": "Tools to manage HTTP caching proxies with PHP", "homepage": "https://github.com/friendsofsymfony/FOSHttpCache", "keywords": ["caching", "http", "invalidation", "nginx", "purge", "varnish"], "support": {"issues": "https://github.com/FriendsOfSymfony/FOSHttpCache/issues", "source": "https://github.com/FriendsOfSymfony/FOSHttpCache/tree/2.12.1"}, "time": "2022-03-16T10:56:38+00:00"}, {"name": "friendsofsymfony/http-cache-bundle", "version": "2.11.2", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSHttpCacheBundle.git", "reference": "e299661dd9157033fca3f670a1e248002206ce17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSHttpCacheBundle/zipball/e299661dd9157033fca3f670a1e248002206ce17", "reference": "e299661dd9157033fca3f670a1e248002206ce17", "shasum": ""}, "require": {"friendsofsymfony/http-cache": "^2.6", "php": "^7.3 || ^8.0", "symfony/framework-bundle": "^3.4.26 || ^4.2.7 || ^5.0", "symfony/http-foundation": "^3.4.26 || ^4.2.7 || ^5.0", "symfony/http-kernel": "^3.4.26 || ^4.2.7 || ^5.0"}, "conflict": {"symfony/monolog-bridge": "<3.4.4", "twig/twig": "<1.12.0"}, "require-dev": {"doctrine/annotations": "^1.11", "guzzlehttp/guzzle": "^7.2", "matthiasnoback/symfony-dependency-injection-test": "^4.0", "mockery/mockery": "^1.3.2", "monolog/monolog": "*", "php-http/discovery": "^1.13", "php-http/guzzle7-adapter": "^0.1.1", "php-http/httplug": "^2.2.0", "php-http/message": "^1.0 || ^2.0", "sebastian/exporter": "^2.0", "sensio/framework-extra-bundle": "^3.0 || ^4.0 || ^5.5.1", "symfony/browser-kit": "^3.4.4 || ^4.2.7 || ^5.0", "symfony/console": "^3.4.26 || ^4.2.7 || ^5.0", "symfony/css-selector": "^3.4.26 || ^4.2.7 || ^5.0", "symfony/expression-language": "^3.4.26 || ^4.2.7 || ^5.0", "symfony/finder": "^3.4.26 || ^4.2.7 || ^5.0", "symfony/monolog-bundle": "^3.0 || ^4.2.7 || ^5.0", "symfony/phpunit-bridge": "v5.3.7", "symfony/routing": "^3.4.26 || ^4.2.7 || ^5.0", "symfony/security-bundle": "^3.4.26 || ^4.2.7 || ^5.0", "symfony/twig-bundle": "^3.4.26 || ^4.2.7 || ^5.0", "symfony/yaml": "^3.4.26 || ^4.2.7 || ^5.0", "twig/twig": "^2.13"}, "suggest": {"sensio/framework-extra-bundle": "For Tagged Cache Invalidation", "symfony/console": "To send invalidation requests from the command line", "symfony/expression-language": "For Tagged Cache Invalidation"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"FOS\\HttpCacheBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Liip AG", "homepage": "http://www.liip.ch/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.driebit.nl"}, {"name": "Community contributions", "homepage": "https://github.com/friendsofsymfony/FOSHttpCacheBundle/contributors"}], "description": "Set path based HTTP cache headers and send invalidation requests to your HTTP cache", "homepage": "https://github.com/FriendsOfSymfony/FOSHttpCacheBundle", "keywords": ["caching", "esi", "http", "invalidation", "purge", "varnish"], "support": {"issues": "https://github.com/FriendsOfSymfony/FOSHttpCacheBundle/issues", "source": "https://github.com/FriendsOfSymfony/FOSHttpCacheBundle/tree/2.11.2"}, "time": "2021-11-08T07:27:23+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.13.42", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "b7ee848bbd1958ff7464522d5c6e3688cca2a125"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/b7ee848bbd1958ff7464522d5c6e3688cca2a125", "reference": "b7ee848bbd1958ff7464522d5c6e3688cca2a125", "shasum": ""}, "require": {"giggsey/locale": "^1.7|^2.0", "php": ">=5.3.2", "symfony/polyfill-mbstring": "^1.17"}, "replace": {"giggsey/libphonenumber-for-php-lite": "self.version"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^1.0|^2.0", "symfony/console": "^2.8|^3.0|^v4.4|^v5.2", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2024-07-29T07:19:22+00:00"}, {"name": "giggsey/locale", "version": "2.6", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "37874fa473131247c348059fb7b8985efc18b5ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/37874fa473131247c348059fb7b8985efc18b5ea", "reference": "37874fa473131247c348059fb7b8985efc18b5ea", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"ext-json": "*", "pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^8.5|^9.5", "symfony/console": "^5.0|^6.0", "symfony/filesystem": "^5.0|^6.0", "symfony/finder": "^5.0|^6.0", "symfony/process": "^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/2.6"}, "time": "2024-04-18T19:31:19+00:00"}, {"name": "google/recaptcha", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/google/recaptcha.git", "reference": "614f25a9038be4f3f2da7cbfd778dc5b357d2419"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/google/recaptcha/zipball/614f25a9038be4f3f2da7cbfd778dc5b357d2419", "reference": "614f25a9038be4f3f2da7cbfd778dc5b357d2419", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.2.20|^2.15", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^4.8.36|^5.7.27|^6.59|^7.5.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"ReCaptcha\\": "src/ReCaptcha"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Client library for reCAPTCHA, a free service that protects websites from spam and abuse.", "homepage": "https://www.google.com/recaptcha/", "keywords": ["Abuse", "<PERSON><PERSON>a", "recaptcha", "spam"], "support": {"forum": "https://groups.google.com/forum/#!forum/recaptcha", "issues": "https://github.com/google/recaptcha/issues", "source": "https://github.com/google/recaptcha"}, "time": "2020-03-31T17:50:54+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "f4152d9eb85c445fe1f992001d1748e8bec070d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/f4152d9eb85c445fe1f992001d1748e8bec070d2", "reference": "f4152d9eb85c445fe1f992001d1748e8bec070d2", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^1.9.1 || ^2.6.3", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2024-07-18T11:12:18+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/67ab6e18aaa14d753cc148911d273f6e6cb6721e", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-05-21T12:31:43+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e4490cabc77465aaee90b20cfc9a770f8c04be6b", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.9.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-04-17T16:00:37+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "support": {"issues": "https://github.com/http-interop/http-factory-guzzle/issues", "source": "https://github.com/http-interop/http-factory-guzzle/tree/1.2.0"}, "time": "2021-07-21T13:50:14+00:00"}, {"name": "jacobbe<PERSON><PERSON>/sendyphp", "version": "v1.3", "source": {"type": "git", "url": "https://github.com/JacobBennett/SendyPHP.git", "reference": "cc43cad8c345a013671f44c61db722695877a8f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jacob<PERSON>ennett/SendyPHP/zipball/cc43cad8c345a013671f44c61db722695877a8f2", "reference": "cc43cad8c345a013671f44c61db722695877a8f2", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "3.7.*", "squizlabs/php_codesniffer": "1.*"}, "type": "library", "autoload": {"psr-4": {"SendyPHP\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jakebennett.net"}], "description": "A PHP Library for interfacing with the Sendy newsletter system (http://sendy.co)", "keywords": ["Sendy", "newsletter"], "support": {"issues": "https://github.com/JacobBennett/SendyPHP/issues", "source": "https://github.com/JacobBennett/SendyPHP/tree/master"}, "time": "2016-03-10T16:47:44+00:00"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/jdorn/sql-formatter/issues", "source": "https://github.com/jdorn/sql-formatter/tree/v1.2.17"}, "time": "2014-01-12T16:20:24+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "f9fdd29ad8e6d024f52678b570e5593759b550b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/f9fdd29ad8e6d024f52678b570e5593759b550b4", "reference": "f9fdd29ad8e6d024f52678b570e5593759b550b4", "shasum": ""}, "require": {"composer-runtime-api": "^2.0.0", "php": "^7.1|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^7.5|^8.5|^9.4", "vimeo/psalm": "^4.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.0.6"}, "time": "2024-03-08T09:58:59+00:00"}, {"name": "league/flysystem", "version": "1.1.10", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.10"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2022-10-04T09:16:37+00:00"}, {"name": "league/flysystem-aws-s3-v3", "version": "1.0.30", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-aws-s3-v3.git", "reference": "af286f291ebab6877bac0c359c6c2cb017eb061d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-aws-s3-v3/zipball/af286f291ebab6877bac0c359c6c2cb017eb061d", "reference": "af286f291ebab6877bac0c359c6c2cb017eb061d", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.20.0", "league/flysystem": "^1.0.40", "php": ">=5.5.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "~1.0.1", "phpspec/phpspec": "^2.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\AwsS3v3\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Flysystem adapter for the AWS S3 SDK v3.x", "support": {"issues": "https://github.com/thephpleague/flysystem-aws-s3-v3/issues", "source": "https://github.com/thephpleague/flysystem-aws-s3-v3/tree/1.0.30"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-07-02T13:51:38+00:00"}, {"name": "league/flysystem-sftp", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-sftp.git", "reference": "36fb893d10bb799fa6aa7199e37e84314c9fd97d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-sftp/zipball/36fb893d10bb799fa6aa7199e37e84314c9fd97d", "reference": "36fb893d10bb799fa6aa7199e37e84314c9fd97d", "shasum": ""}, "require": {"league/flysystem": "~1.0", "php": ">=5.6.0", "phpseclib/phpseclib": "~2.0"}, "require-dev": {"mockery/mockery": "0.9.*", "phpunit/phpunit": "^5.7.25"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Sftp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Flysystem adapter for SFTP", "support": {"issues": "https://github.com/thephpleague/flysystem-sftp/issues", "source": "https://github.com/thephpleague/flysystem-sftp/tree/1.1.0"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "abandoned": "league/flysystem-sftp-v3", "time": "2022-01-04T22:02:01+00:00"}, {"name": "league/mime-type-detection", "version": "1.15.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.15.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-01-28T23:22:08+00:00"}, {"name": "leezy/pheanstalk-bundle", "version": "3.4.2", "source": {"type": "git", "url": "https://github.com/armetiz/LeezyPheanstalkBundle.git", "reference": "e27e1507e7224faba0b439a09808839a366a8569"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/armetiz/LeezyPheanstalkBundle/zipball/e27e1507e7224faba0b439a09808839a366a8569", "reference": "e27e1507e7224faba0b439a09808839a366a8569", "shasum": ""}, "require": {"pda/pheanstalk": "~3.0", "php": ">=5.5.9", "psr/log": "~1.0", "symfony/console": "~2.5|~3.0|^4.0", "symfony/framework-bundle": "~2.5|~3.0|^4.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0", "phpunit/phpunit-mock-objects": "2.3.0|~3.4"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Leezy\\PheanstalkBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.armetiz.info"}], "description": "The LeezyPheanstalkBundle is a Symfony2 Bundle that provides a command line interface for manage the Beanstalkd workqueue server & a pheanstalk integration.", "homepage": "https://github.com/armetiz/LeezyPheanstalkBundle", "keywords": ["asynchronous", "beanstalkd", "bundle", "messaging", "<PERSON><PERSON><PERSON><PERSON>", "queueing", "symfony"], "support": {"issues": "https://github.com/armetiz/LeezyPheanstalkBundle/issues", "source": "https://github.com/armetiz/LeezyPheanstalkBundle/tree/3.4.2"}, "time": "2019-03-15T08:14:42+00:00"}, {"name": "monolog/monolog", "version": "1.27.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-06-09T08:53:42+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "bbb69a935c2cbb0c03d7f481a238027430f6440b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/bbb69a935c2cbb0c03d7f481a238027430f6440b", "reference": "bbb69a935c2cbb0c03d7f481a238027430f6440b", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.7.0"}, "time": "2023-08-25T10:54:48+00:00"}, {"name": "nyholm/psr7", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "aa5fc277a4f5508013d571341ade0c3886d4d00e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/aa5fc277a4f5508013d571341ade0c3886d4d00e", "reference": "aa5fc277a4f5508013d571341ade0c3886d4d00e", "shasum": ""}, "require": {"php": ">=7.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0", "psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "php-http/message-factory": "^1.0", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.4", "symfony/error-handler": "^4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.8.1"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2023-11-13T09:31:12+00:00"}, {"name": "oneup/flysystem-bundle", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/1up-lab/OneupFlysystemBundle.git", "reference": "35ef14658f7bb159bab1e907e0f6eeea457be77f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/1up-lab/OneupFlysystemBundle/zipball/35ef14658f7bb159bab1e907e0f6eeea457be77f", "reference": "35ef14658f7bb159bab1e907e0f6eeea457be77f", "shasum": ""}, "require": {"league/flysystem": "^1.0.26", "php": "^7.0", "symfony/framework-bundle": "~2.0|~3.0"}, "require-dev": {"jenko/flysystem-gaufrette": "^1.0", "league/flysystem-aws-s3-v2": "^1.0", "league/flysystem-cached-adapter": "^1.0", "league/flysystem-gridfs": "^1.0", "league/flysystem-memory": "^1.0", "league/flysystem-rackspace": "^1.0", "league/flysystem-replicate-adapter": "^1.0", "league/flysystem-sftp": "^1.0", "league/flysystem-webdav": "^1.0", "league/flysystem-ziparchive": "^1.0", "litipk/flysystem-fallback-adapter": "^0.1", "phpunit/phpunit": "^4.4", "spatie/flysystem-dropbox": "^1.0", "superbalist/flysystem-google-storage": "^4.0", "symfony/asset": "^2.0|^3.0", "symfony/browser-kit": "^2.0|^3.0", "symfony/finder": "^2.0|^3.0", "symfony/templating": "^2.0|^3.0", "symfony/translation": "^2.0|^3.0", "twistor/flysystem-stream-wrapper": "^1.0"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Required for FTP and SFTP", "jenko/flysystem-gaufrette": "Allows you to use gaufrette adapter", "league/flysystem-aws-s3-v2": "Use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Use S3 storage with AWS SDK v3", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-gridfs": "Allows you to use GridFS adapter", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-replicate-adapter": "Allows you to use the Replicate adapter from Flysystem", "league/flysystem-sftp": "Allows SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "litipk/flysystem-fallback-adapter": "Allows you to use a fallback filesystem", "spatie/flysystem-dropbox": "Use Dropbox storage", "superbalist/flysystem-google-storage": "Allows you to use Google Cloud Storage buckets", "twistor/flysystem-stream-wrapper": "Allows you to use stream wrapper"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Oneup\\FlysystemBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://1up.io", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://1up.io", "role": "Developer"}], "description": "Integrates Flysystem filesystem abstraction library to your Symfony2 project.", "homepage": "http://1up.io", "keywords": ["Flysystem", "Symfony2", "abstraction", "filesystem"], "support": {"issues": "https://github.com/1up-lab/OneupFlysystemBundle/issues", "source": "https://github.com/1up-lab/OneupFlysystemBundle/tree/master"}, "time": "2017-10-04T15:46:18+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "pda/pheanstalk", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/pda/pheanstalk.git", "reference": "57b6e76f1b06ca798e739a8dee92c2dac04fd170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pda/pheanstalk/zipball/57b6e76f1b06ca798e739a8dee92c2dac04fd170", "reference": "57b6e76f1b06ca798e739a8dee92c2dac04fd170", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Pheanstalk\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://paul.annesley.cc/", "role": "Developer"}], "description": "PHP client for beanstalkd queue", "homepage": "https://github.com/pda/pheanstalk", "keywords": ["beanstalkd"], "support": {"issues": "https://github.com/pda/pheanstalk/issues", "source": "https://github.com/pda/pheanstalk/tree/master"}, "time": "2018-09-19T12:16:28+00:00"}, {"name": "phenx/php-font-lib", "version": "0.5.6", "source": {"type": "git", "url": "https://github.com/dompdf/php-font-lib.git", "reference": "a1681e9793040740a405ac5b189275059e2a9863"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-font-lib/zipball/a1681e9793040740a405ac5b189275059e2a9863", "reference": "a1681e9793040740a405ac5b189275059e2a9863", "shasum": ""}, "require": {"ext-mbstring": "*"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^4 || ^5 || ^6"}, "type": "library", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "support": {"issues": "https://github.com/dompdf/php-font-lib/issues", "source": "https://github.com/dompdf/php-font-lib/tree/0.5.6"}, "time": "2024-01-29T14:45:26+00:00"}, {"name": "phenx/php-svg-lib", "version": "0.3.4", "source": {"type": "git", "url": "https://github.com/PhenX/php-svg-lib.git", "reference": "f627771eb854aa7f45f80add0f23c6c4d67ea0f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PhenX/php-svg-lib/zipball/f627771eb854aa7f45f80add0f23c6c4d67ea0f2", "reference": "f627771eb854aa7f45f80add0f23c6c4d67ea0f2", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "sabberworm/php-css-parser": "^8.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib", "support": {"issues": "https://github.com/PhenX/php-svg-lib/issues", "source": "https://github.com/PhenX/php-svg-lib/tree/0.3.4"}, "time": "2021-10-18T02:13:32+00:00"}, {"name": "php-http/client-common", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "29e0c60d982f04017069483e832b92074d0a90b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/29e0c60d982f04017069483e832b92074d0a90b2", "reference": "29e0c60d982f04017069483e832b92074d0a90b2", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/options-resolver": "^2.6 || ^3.4.20 || ~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.0", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.4.0"}, "time": "2021-07-05T08:19:25+00:00"}, {"name": "php-http/discovery", "version": "1.19.4", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "0700efda8d7526335132360167315fdab3aeb599"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/0700efda8d7526335132360167315fdab3aeb599", "reference": "0700efda8d7526335132360167315fdab3aeb599", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.19.4"}, "time": "2024-03-29T13:00:05+00:00"}, {"name": "php-http/httplug", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/625ad742c360c8ac580fcc647a1541d29e257f67", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.0"}, "time": "2023-04-14T15:10:03+00:00"}, {"name": "php-http/message", "version": "1.16.1", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "5997f3289332c699fa2545c427826272498a2088"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/5997f3289332c699fa2545c427826272498a2088", "reference": "5997f3289332c699fa2545c427826272498a2088", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.1"}, "time": "2024-03-07T13:22:09+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/1.1.0"}, "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "time": "2024-03-15T13:55:21+00:00"}, {"name": "phpseclib/phpseclib", "version": "2.0.47", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "b7d7d90ee7df7f33a664b4aea32d50a305d35adb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/b7d7d90ee7df7f33a664b4aea32d50a305d35adb", "reference": "b7d7d90ee7df7f33a664b4aea32d50a305d35adb", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-xml": "Install the XML extension to load XML formatted public keys."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.47"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-02-26T04:55:38+00:00"}, {"name": "picqer/php-barcode-generator", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/picqer/php-barcode-generator.git", "reference": "4cec18909dffd86e14beb69b1040f2520c2e1bb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/picqer/php-barcode-generator/zipball/4cec18909dffd86e14beb69b1040f2520c2e1bb1", "reference": "4cec18909dffd86e14beb69b1040f2520c2e1bb1", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.3|^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5"}, "suggest": {"ext-bcmath": "Barcode IMB (Intelligent Mail Barcode) needs bcmath extension", "ext-gd": "For JPG and PNG generators, GD or Imagick is required", "ext-imagick": "For JPG and PNG generators, GD or Imagick is required"}, "type": "library", "autoload": {"psr-4": {"Picqer\\Barcode\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nicolaasuni.tecnick.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://picqer.com"}], "description": "An easy to use, non-bloated, barcode generator in PHP. Creates SVG, PNG, JPG and HTML images from the most used 1D barcode standards.", "homepage": "https://github.com/picqer/php-barcode-generator", "keywords": ["CODABAR", "Code11", "Code93", "EAN13", "KIX", "KIXCODE", "MSI", "POSTNET", "<PERSON><PERSON>", "Standard 2 of 5", "barcode", "barcode generator", "code128", "code39", "ean", "html", "jpeg", "jpg", "php", "png", "svg", "upc"], "support": {"issues": "https://github.com/picqer/php-barcode-generator/issues", "source": "https://github.com/picqer/php-barcode-generator/tree/v2.4.0"}, "funding": [{"url": "https://github.com/casperbakker", "type": "github"}], "time": "2023-09-16T08:58:52+00:00"}, {"name": "pomm-project/cli", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/pomm-project/Cli.git", "reference": "8e9839b9028c83f5d1cf49cf4ff72be574ce584e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pomm-project/Cli/zipball/8e9839b9028c83f5d1cf49cf4ff72be574ce584e", "reference": "8e9839b9028c83f5d1cf49cf4ff72be574ce584e", "shasum": ""}, "require": {"ext-pgsql": "*", "php": ">=5.4.4", "pomm-project/foundation": "~2.0", "pomm-project/model-manager": "~2.0", "psr/log": "~1.0", "symfony/console": "~2.5|~3.0|~4.0|~5.0"}, "require-dev": {"atoum/atoum": "^2.8 || ^3.0", "symfony/filesystem": "~2.5|~3.0|~4.0"}, "bin": ["bin/pomm.php"], "type": "library", "autoload": {"psr-4": {"PommProject\\Cli\\": "sources/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Grégoire HUBERT", "email": "<EMAIL>", "homepage": "http://www.pomm-project.org"}], "description": "Command line for Pomm.", "homepage": "http://www.pomm-project.org", "keywords": ["cli", "database", "pomm", "postgresql"], "support": {"issues": "https://github.com/pomm-project/Cli/issues", "source": "https://github.com/pomm-project/Cli/tree/2.0.3"}, "time": "2020-05-06T23:02:09+00:00"}, {"name": "pomm-project/foundation", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/pomm-project/Foundation.git", "reference": "1300fde443e7cb29e498ba31486b04c356cfc2a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pomm-project/Foundation/zipball/1300fde443e7cb29e498ba31486b04c356cfc2a4", "reference": "1300fde443e7cb29e498ba31486b04c356cfc2a4", "shasum": ""}, "require": {"ext-pgsql": "*", "php": ">=5.4.4", "psr/log": "~1.0"}, "require-dev": {"atoum/atoum": "^2.8 || ^4.0"}, "type": "library", "autoload": {"psr-4": {"PommProject\\Foundation\\": "sources/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Grégoire HUBERT", "email": "<EMAIL>", "homepage": "http://www.coolkeums.org"}], "description": "Pomm connection manager for Postgresql", "homepage": "http://www.pomm-project.org", "keywords": ["database", "framework", "pomm", "postgresql"], "support": {"issues": "https://github.com/pomm-project/Foundation/issues", "source": "https://github.com/pomm-project/Foundation/tree/2.0.5"}, "time": "2020-12-01T13:19:38+00:00"}, {"name": "pomm-project/model-manager", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/pomm-project/ModelManager.git", "reference": "628e54ede7e5cafd3274bb413a91fb56d1a6b414"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pomm-project/ModelManager/zipball/628e54ede7e5cafd3274bb413a91fb56d1a6b414", "reference": "628e54ede7e5cafd3274bb413a91fb56d1a6b414", "shasum": ""}, "require": {"ext-pgsql": "*", "php": ">=5.4.4", "pomm-project/foundation": "~2.0", "psr/log": "~1.0"}, "require-dev": {"atoum/atoum": "dev-master"}, "suggest": {"pomm-project/cli": "dev-master"}, "type": "library", "autoload": {"psr-4": {"PommProject\\ModelManager\\": "sources/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Grégoire HUBERT", "email": "<EMAIL>", "homepage": "http://www.pomm-project.org"}], "description": "PHP Object Model Manager for Postgresql", "homepage": "http://www.pomm-project.org", "keywords": ["database", "orm", "pomm", "postgresql"], "support": {"issues": "https://github.com/pomm-project/ModelManager/issues", "source": "https://github.com/pomm-project/ModelManager/tree/2.0.2"}, "time": "2017-05-15T19:42:26+00:00"}, {"name": "pomm-project/pomm-bundle", "version": "2.2.2", "source": {"type": "git", "url": "https://github.com/pomm-project/pomm-bundle.git", "reference": "de9ffa2d93d86e53f012765cd805dc7c4994b345"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pomm-project/pomm-bundle/zipball/de9ffa2d93d86e53f012765cd805dc7c4994b345", "reference": "de9ffa2d93d86e53f012765cd805dc7c4994b345", "shasum": ""}, "require": {"jdorn/sql-formatter": "~1.2", "php": ">=5.4.4", "pomm-project/cli": "~2.0", "pomm-project/model-manager": "~2.0", "pomm-project/pomm-symfony-bridge": "~2.2", "symfony/framework-bundle": "~2.8|~3.0"}, "require-dev": {"symfony/console": "~2.6|~3.0"}, "suggest": {"sensio/distribution-bundle": "To use the entity param converter", "symfony/web-profiler-bundle": "Display queries log"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"PommProject\\PommBundle\\": "sources/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Grégoire HUBERT", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Symfony2 bundle for Pomm2", "homepage": "http://www.pomm-project.org", "keywords": ["database", "orm", "pomm", "postgresql", "symfony"], "support": {"issues": "https://github.com/pomm-project/pomm-bundle/issues", "source": "https://github.com/pomm-project/pomm-bundle/tree/master"}, "time": "2016-12-22T21:04:09+00:00"}, {"name": "pomm-project/pomm-symfony-bridge", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/pomm-project/pomm-symfony-bridge.git", "reference": "bc036f67b9b30fde09e91fa44882e6d87d13d3ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pomm-project/pomm-symfony-bridge/zipball/bc036f67b9b30fde09e91fa44882e6d87d13d3ab", "reference": "bc036f67b9b30fde09e91fa44882e6d87d13d3ab", "shasum": ""}, "require": {"pomm-project/foundation": "~2.0", "symfony/http-foundation": "~2.5|~3.0|~4.0", "symfony/http-kernel": "~2.5|~3.0|~4.0", "symfony/property-info": "~2.8|~3.0|~4.0", "symfony/serializer": "~2.5|~3.0|~4.0"}, "require-dev": {"squizlabs/php_codesniffer": "~2.7"}, "suggest": {"pomm-project/cli": "~2.0", "pomm-project/model-manager": "~2.0", "silex/silex": "~1.0", "symfony/symfony": "~2.5|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"PommProject\\SymfonyBridge\\": "sources/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Grégoire HUBERT", "email": "<EMAIL>"}], "description": "Pomm2 shared elements between Silex and Symfony.", "homepage": "http://www.pomm-project.org", "keywords": ["debug", "pomm", "profiler", "silex", "symfony"], "support": {"issues": "https://github.com/pomm-project/pomm-symfony-bridge/issues", "source": "https://github.com/pomm-project/pomm-symfony-bridge/tree/master"}, "time": "2018-06-06T20:46:24+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/link", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/link.git", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link/zipball/eea8e8662d5cd3ae4517c9b864493f59fca95562", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "support": {"source": "https://github.com/php-fig/link/tree/master"}, "time": "2016-10-28T16:06:13+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/uuid", "version": "3.9.7", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "dc75aa439eb4c1b77f5379fd958b3dc0e6014178"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/dc75aa439eb4c1b77f5379fd958b3dc0e6014178", "reference": "dc75aa439eb4c1b77f5379fd958b3dc0e6014178", "shasum": ""}, "require": {"ext-json": "*", "paragonie/random_compat": "^1 | ^2 | ^9.99.99", "php": "^5.4 | ^7.0 | ^8.0", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | >=2.1.0 <=2.3.2", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "nikic/php-parser": "<=4.5.0", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1 | ^2.6", "php-parallel-lint/php-parallel-lint": "^1.3", "phpunit/phpunit": ">=4.8.36 <9.0.0 | >=9.3.0", "squizlabs/php_codesniffer": "^3.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-openssl": "Provides the OpenSSL extension for use with the OpenSslGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Formerly rhumsaa/uuid. A PHP 5.4+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "rss": "https://github.com/ramsey/uuid/releases.atom", "source": "https://github.com/ramsey/uuid", "wiki": "https://github.com/ramsey/uuid/wiki"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2022-12-19T21:55:10+00:00"}, {"name": "react/promise", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-16T16:16:50+00:00"}, {"name": "sabberworm/php-css-parser", "version": "v8.6.0", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "d2fb94a9641be84d79c7548c6d39bbebba6e9a70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/d2fb94a9641be84d79c7548c6d39bbebba6e9a70", "reference": "d2fb94a9641be84d79c7548c6d39bbebba6e9a70", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.6.20"}, "require-dev": {"phpunit/phpunit": "^5.7.27"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.6.0"}, "time": "2024-07-01T07:33:21+00:00"}, {"name": "sensio/distribution-bundle", "version": "v5.0.25", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioDistributionBundle.git", "reference": "80a38234bde8321fb92aa0b8c27978a272bb4baf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioDistributionBundle/zipball/80a38234bde8321fb92aa0b8c27978a272bb4baf", "reference": "80a38234bde8321fb92aa0b8c27978a272bb4baf", "shasum": ""}, "require": {"php": ">=5.3.9", "sensiolabs/security-checker": "~5.0|~6.0", "symfony/class-loader": "~2.3|~3.0", "symfony/config": "~2.3|~3.0", "symfony/dependency-injection": "~2.3|~3.0", "symfony/filesystem": "~2.3|~3.0", "symfony/http-kernel": "~2.3|~3.0", "symfony/process": "~2.3|~3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\DistributionBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Base bundle for Symfony Distributions", "keywords": ["configuration", "distribution"], "support": {"issues": "https://github.com/sensiolabs/SensioDistributionBundle/issues", "source": "https://github.com/sensiolabs/SensioDistributionBundle/tree/master"}, "abandoned": true, "time": "2019-06-18T15:43:58+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.4.1", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "585f4b3a1c54f24d1a8431c729fc8f5acca20c8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/585f4b3a1c54f24d1a8431c729fc8f5acca20c8a", "reference": "585f4b3a1c54f24d1a8431c729fc8f5acca20c8a", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/persistence": "^1.0", "php": ">=7.1.3", "symfony/config": "^3.4|^4.3", "symfony/dependency-injection": "^3.4|^4.3", "symfony/framework-bundle": "^3.4|^4.3", "symfony/http-kernel": "^3.4|^4.3"}, "require-dev": {"doctrine/doctrine-bundle": "^1.6", "doctrine/orm": "^2.5", "nyholm/psr7": "^1.1", "symfony/browser-kit": "^3.4|^4.3", "symfony/dom-crawler": "^3.4|^4.3", "symfony/expression-language": "^3.4|^4.3", "symfony/finder": "^3.4|^4.3", "symfony/monolog-bridge": "^3.0|^4.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^3.4.19|^4.1.8", "symfony/psr-http-message-bridge": "^1.1", "symfony/security-bundle": "^3.4|^4.3", "symfony/twig-bundle": "^3.4|^4.3", "symfony/yaml": "^3.4|^4.3", "twig/twig": "~1.12|~2.0"}, "suggest": {"symfony/expression-language": "", "symfony/psr-http-message-bridge": "To use the PSR-7 converters", "symfony/security-bundle": ""}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.4.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "support": {"issues": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/issues", "source": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/tree/v5.4.1"}, "abandoned": "Symfony", "time": "2019-07-08T08:31:25+00:00"}, {"name": "sensiolabs/security-checker", "version": "v6.0.3", "source": {"type": "git", "url": "https://github.com/sensiolabs/security-checker.git", "reference": "a576c01520d9761901f269c4934ba55448be4a54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/security-checker/zipball/a576c01520d9761901f269c4934ba55448be4a54", "reference": "a576c01520d9761901f269c4934ba55448be4a54", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/console": "^2.8|^3.4|^4.2|^5.0", "symfony/http-client": "^4.3|^5.0", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-ctype": "^1.11"}, "bin": ["security-checker"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.0-dev"}}, "autoload": {"psr-4": {"SensioLabs\\Security\\": "SensioLabs/Security"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A security checker for your composer.lock", "support": {"issues": "https://github.com/sensiolabs/security-checker/issues", "source": "https://github.com/sensiolabs/security-checker/tree/master"}, "abandoned": "https://github.com/fabpot/local-php-security-checker", "time": "2019-11-01T13:20:14+00:00"}, {"name": "sentry/sdk", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php-sdk.git", "reference": "089858b1b27d3705a5fd1c32d8d10beb55980190"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php-sdk/zipball/089858b1b27d3705a5fd1c32d8d10beb55980190", "reference": "089858b1b27d3705a5fd1c32d8d10beb55980190", "shasum": ""}, "require": {"http-interop/http-factory-guzzle": "^1.0", "sentry/sentry": "^2.5", "symfony/http-client": "^4.3|^5.0"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "This is a metapackage shipping sentry/sentry with a recommended HTTP client.", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"source": "https://github.com/getsentry/sentry-php-sdk/tree/2.2.0"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2020-09-14T09:30:55+00:00"}, {"name": "sentry/sentry", "version": "2.5.2", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "ce63f13e2cf9f72ec169413545a3f7312b2e45e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/ce63f13e2cf9f72ec169413545a3f7312b2e45e3", "reference": "ce63f13e2cf9f72ec169413545a3f7312b2e45e3", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "guzzlehttp/promises": "^1.3", "guzzlehttp/psr7": "^1.7", "jean85/pretty-package-versions": "^1.5|^2.0.1", "php": "^7.1", "php-http/async-client-implementation": "^1.0", "php-http/client-common": "^1.5|^2.0", "php-http/discovery": "^1.6.1", "php-http/httplug": "^1.1|^2.0", "php-http/message": "^1.5", "psr/http-factory": "^1.0", "psr/http-message-implementation": "^1.0", "psr/log": "^1.0", "symfony/options-resolver": "^2.7|^3.0|^4.0|^5.0", "symfony/polyfill-uuid": "^1.13.1"}, "conflict": {"php-http/client-common": "1.8.0", "raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "monolog/monolog": "^1.3|^2.0", "php-http/mock-client": "^1.4", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.5.20", "symfony/phpunit-bridge": "^5.2", "vimeo/psalm": "^4.2"}, "suggest": {"monolog/monolog": "Allow sending log messages to Sentry by using the included Monolog handler."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Sentry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "A PHP SDK for Sentry (http://sentry.io)", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/2.5.2"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2021-02-02T08:58:09+00:00"}, {"name": "sentry/sentry-symfony", "version": "3.5.4", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-symfony.git", "reference": "b3f9b7f0fff3725f0b7811aa4e070e4658b8722b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-symfony/zipball/b3f9b7f0fff3725f0b7811aa4e070e4658b8722b", "reference": "b3f9b7f0fff3725f0b7811aa4e070e4658b8722b", "shasum": ""}, "require": {"jean85/pretty-package-versions": "^1.5 || ^2.0", "php": "^7.1", "sentry/sdk": "^2.1", "symfony/config": "^3.4||^4.0||^5.0", "symfony/console": "^3.4||^4.0||^5.0", "symfony/dependency-injection": "^3.4||^4.0||^5.0", "symfony/event-dispatcher": "^3.4||^4.0||^5.0", "symfony/http-kernel": "^3.4||^4.0||^5.0", "symfony/security-core": "^3.4||^4.0||^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.8", "jangregor/phpstan-prophecy": "^0.6.2", "monolog/monolog": "^1.11||^2.0", "php-http/mock-client": "^1.0", "phpspec/prophecy": "!=1.11.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.19", "phpstan/phpstan-phpunit": "^0.12.8", "phpunit/phpunit": "^7.5||^8.5", "symfony/browser-kit": "^3.4||^4.0||^5.0", "symfony/expression-language": "^3.4||^4.0||^5.0", "symfony/framework-bundle": "^3.4||^4.0||^5.0", "symfony/messenger": "^4.3||^5.0", "symfony/monolog-bundle": "^3.4", "symfony/phpunit-bridge": "^5.2.6", "symfony/yaml": "^3.4||^4.0||^5.0"}, "suggest": {"monolog/monolog": "Required to use the Monolog handler"}, "type": "symfony-bundle", "extra": {"branch-alias": {"master": "3.x-dev", "releases/3.2.x": "3.2.x-dev", "releases/2.x": "2.x-dev", "releases/1.x": "1.x-dev"}}, "autoload": {"psr-4": {"Sentry\\SentryBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony integration for Sentry (http://getsentry.com)", "homepage": "http://getsentry.com", "keywords": ["errors", "logging", "sentry", "symfony"], "support": {"issues": "https://github.com/getsentry/sentry-symfony/issues", "source": "https://github.com/getsentry/sentry-symfony/tree/3.5.4"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2021-05-13T08:38:28+00:00"}, {"name": "son-video/advice-client", "version": "1.0.1", "source": {"type": "git", "url": "**************:son-video/advice-client.git", "reference": "83198e053ecb9bf2ad218541ffaadc0f572b32e3"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/son-video%2Fadvice-client/repository/archive.zip?sha=83198e053ecb9bf2ad218541ffaadc0f572b32e3", "reference": "83198e053ecb9bf2ad218541ffaadc0f572b32e3", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=7.0", "psr/log": "^1.0"}, "require-dev": {"atoum/atoum": "^3.2", "symfony/yaml": "^3.0"}, "type": "library", "autoload": {"psr-4": {"SonVideo\\AdviceClient\\": "src/"}}, "autoload-dev": {"psr-4": {"SonVideo\\AdviceClient\\Tests\\": "tests/"}}, "license": ["proprietary"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP client for the Advice API", "support": {"issues": "https://gitlab.com/api/v4/projects/26446100/issues"}, "time": "2024-04-30T10:57:56+02:00"}, {"name": "son-video/advice-client-bundle", "version": "1.1.1", "source": {"type": "git", "url": "**************:son-video/advice-client-bundle.git", "reference": "9ff11bf31ef5cd53b4103ec4168749127dc8bcea"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/son-video%2Fadvice-client-bundle/repository/archive.zip?sha=9ff11bf31ef5cd53b4103ec4168749127dc8bcea", "reference": "9ff11bf31ef5cd53b4103ec4168749127dc8bcea", "shasum": ""}, "require": {"php": ">=7.0", "son-video/advice-client": "^1.0", "symfony/framework-bundle": "~2.8|~3.0|~4.0"}, "type": "library", "autoload": {"psr-4": {"SonVideo\\AdviceClientBundle\\": "src/"}}, "license": ["proprietary"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony bundle for advice-client library", "support": {"issues": "https://gitlab.com/api/v4/projects/26446087/issues"}, "time": "2023-06-26T16:34:11+02:00"}, {"name": "son-video/svd-rpc-bundle", "version": "v1.2.0", "source": {"type": "git", "url": "**************:son-video/svd-rpc-bundle.git", "reference": "dc93445e7149f051301841ff8066fb02cf285eca"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/son-video%2Fsvd-rpc-bundle/repository/archive.zip?sha=dc93445e7149f051301841ff8066fb02cf285eca", "reference": "dc93445e7149f051301841ff8066fb02cf285eca", "shasum": ""}, "require": {"pomm-project/foundation": "^2.0"}, "require-dev": {"atoum/atoum": "^2.5", "datto/json-rpc-http": "^3.1"}, "type": "library", "autoload": {"psr-4": {"SonVideo\\RpcBundle\\": "sources/lib"}}, "autoload-dev": {"psr-4": {"SonVideo\\RpcBundle\\Test\\": "sources/tests"}}, "authors": [{"name": "Grégoire HUBERT", "email": "<EMAIL>"}], "description": "RPC dispatcher and response manager", "keywords": ["rpc", "web service"], "support": {"issues": "https://gitlab.com/api/v4/projects/26445953/issues"}, "time": "2024-08-12T10:54:44+02:00"}, {"name": "son-video/svd-services", "version": "1.0.0", "source": {"type": "git", "url": "**************:son-video/svd-services.git", "reference": "d989be2f68e3f351a716087bafcad38c688c7c6b"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/son-video%2Fsvd-services/repository/archive.zip?sha=d989be2f68e3f351a716087bafcad38c688c7c6b", "reference": "d989be2f68e3f351a716087bafcad38c688c7c6b", "shasum": ""}, "type": "library", "description": "Services shell scripts.", "support": {"issues": "https://gitlab.com/api/v4/projects/26426971/issues"}, "abandoned": true, "time": "2017-06-29T16:41:51+02:00"}, {"name": "son-video/synapps-client", "version": "v1.1.1", "source": {"type": "git", "url": "**************:son-video/synapps-client.git", "reference": "a9d07517c36dd41de2b185f68d67752fbf7d6c45"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/son-video%2Fsynapps-client/repository/archive.zip?sha=a9d07517c36dd41de2b185f68d67752fbf7d6c45", "reference": "a9d07517c36dd41de2b185f68d67752fbf7d6c45", "shasum": ""}, "require": {"datto/json-rpc": "@stable", "datto/json-rpc-http": "^3.2", "leezy/pheanstalk-bundle": "~3.3", "php": ">=7.0", "pomm-project/pomm-bundle": "~2.1", "psr/log": "~1.0", "symfony/console": "~3.0|~4.0", "symfony/http-kernel": "~3.0|~4.0", "symfony/yaml": "~3.0|~4.0"}, "require-dev": {"atoum/atoum": "^3.2"}, "type": "library", "autoload": {"psr-4": {"SonVideo\\Synapps\\Client\\": "sources/lib"}}, "autoload-dev": {"psr-4": {"SonVideo\\Synapps\\Client\\Test\\": "sources/tests"}}, "description": "Client lib for the synapps application", "support": {"issues": "https://gitlab.com/api/v4/projects/26445941/issues"}, "time": "2024-03-15T11:18:49+01:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "80d075412b557d41002320b96a096ca65aa2c98d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/80d075412b557d41002320b96a096ca65aa2c98d", "reference": "80d075412b557d41002320b96a096ca65aa2c98d", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-24T14:02:46+00:00"}, {"name": "symfony/http-client", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "ed17728617f53903ac684aa4f7a1739f121798d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/ed17728617f53903ac684aa4f7a1739f121798d3", "reference": "ed17728617f53903ac684aa4f7a1739f121798d3", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-client-contracts": "^2.5.3", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2|^3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "2.4"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4.13|^5.1.5|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-10T08:10:21+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1", "reference": "e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-26T19:42:53+00:00"}, {"name": "symfony/mime", "version": "v5.4.13", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "bb2ccf759e2b967dcd11bdee5bdf30dddd2290bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/bb2ccf759e2b967dcd11bdee5bdf30dddd2290bd", "reference": "bb2ccf759e2b967dcd11bdee5bdf30dddd2290bd", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.2|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-09-01T18:18:29+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "e495f5c7e4e672ffef4357d4a4d85f010802f940"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/e495f5c7e4e672ffef4357d4a4d85f010802f940", "reference": "e495f5c7e4e672ffef4357d4a4d85f010802f940", "shasum": ""}, "require": {"monolog/monolog": "~1.22 || ~2.0", "php": ">=5.6", "symfony/config": "~3.4 || ~4.0 || ^5.0", "symfony/dependency-injection": "~3.4.10 || ^4.0.10 || ^5.0", "symfony/http-kernel": "~3.4 || ~4.0 || ^5.0", "symfony/monolog-bridge": "~3.4 || ~4.0 || ^5.0"}, "require-dev": {"symfony/console": "~3.4 || ~4.0 || ^5.0", "symfony/phpunit-bridge": "^4.4 || ^5.0", "symfony/yaml": "~3.4 || ~4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-06T15:12:11+00:00"}, {"name": "symfony/polyfill-apcu", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-apcu.git", "reference": "586c59feeb05c55575080b543b2966591d097aa5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-apcu/zipball/586c59feeb05c55575080b543b2966591d097aa5", "reference": "586c59feeb05c55575080b543b2966591d097aa5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Apcu\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting apcu_* functions to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["apcu", "compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-apcu/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "0424dff1c58f028c451efff2045f5d92410bd540"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/0424dff1c58f028c451efff2045f5d92410bd540", "reference": "0424dff1c58f028c451efff2045f5d92410bd540", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "e76343c631b453088e2260ac41dfebe21954de81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/e76343c631b453088e2260ac41dfebe21954de81", "reference": "e76343c631b453088e2260ac41dfebe21954de81", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/a95281b0be0d9ab48050ebd988b967875cdb9fdb", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fd22ab50000ef01661e2a31d850ebaa297f8e03c", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php56/tree/v1.20.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "5f03a781d984aae42cebd18e7912fa80f02ee644"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/5f03a781d984aae42cebd18e7912fa80f02ee644", "reference": "5f03a781d984aae42cebd18e7912fa80f02ee644", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php70/tree/v1.20.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "10112722600777e02d2745716b70c5db4ca70442"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/10112722600777e02d2745716b70c5db4ca70442", "reference": "10112722600777e02d2745716b70c5db4ca70442", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "ec444d3f3f6505bb28d11afa41e75faadebc10a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/ec444d3f3f6505bb28d11afa41e75faadebc10a1", "reference": "ec444d3f3f6505bb28d11afa41e75faadebc10a1", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/77fa7995ac1b21ab60769b7323d600a991a90433", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "2ba1f33797470debcda07fe9dce20a0003df18e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/2ba1f33797470debcda07fe9dce20a0003df18e9", "reference": "2ba1f33797470debcda07fe9dce20a0003df18e9", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/a2329596ddc8fd568900e3fc76cba42489ecc7f3", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-04-21T15:04:16+00:00"}, {"name": "symfony/symfony", "version": "v3.4.49", "source": {"type": "git", "url": "https://github.com/symfony/symfony.git", "reference": "ba0e346e3ad11de4a307fe4fa2452a3656dcc17b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/symfony/zipball/ba0e346e3ad11de4a307fe4fa2452a3656dcc17b", "reference": "ba0e346e3ad11de4a307fe4fa2452a3656dcc17b", "shasum": ""}, "require": {"doctrine/common": "~2.4", "ext-xml": "*", "fig/link-util": "^1.0", "php": "^5.5.9|>=7.0.8", "psr/cache": "~1.0", "psr/container": "^1.0", "psr/link": "^1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "symfony/polyfill-apcu": "~1.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-icu": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php56": "~1.0", "symfony/polyfill-php70": "~1.6", "twig/twig": "^1.41|^2.10"}, "conflict": {"monolog/monolog": ">=2", "phpdocumentor/reflection-docblock": "<3.0||>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "provide": {"psr/cache-implementation": "1.0", "psr/container-implementation": "1.0", "psr/log-implementation": "1.0", "psr/simple-cache-implementation": "1.0"}, "replace": {"symfony/asset": "self.version", "symfony/browser-kit": "self.version", "symfony/cache": "self.version", "symfony/class-loader": "self.version", "symfony/config": "self.version", "symfony/console": "self.version", "symfony/css-selector": "self.version", "symfony/debug": "self.version", "symfony/debug-bundle": "self.version", "symfony/dependency-injection": "self.version", "symfony/doctrine-bridge": "self.version", "symfony/dom-crawler": "self.version", "symfony/dotenv": "self.version", "symfony/event-dispatcher": "self.version", "symfony/expression-language": "self.version", "symfony/filesystem": "self.version", "symfony/finder": "self.version", "symfony/form": "self.version", "symfony/framework-bundle": "self.version", "symfony/http-foundation": "self.version", "symfony/http-kernel": "self.version", "symfony/inflector": "self.version", "symfony/intl": "self.version", "symfony/ldap": "self.version", "symfony/lock": "self.version", "symfony/monolog-bridge": "self.version", "symfony/options-resolver": "self.version", "symfony/process": "self.version", "symfony/property-access": "self.version", "symfony/property-info": "self.version", "symfony/proxy-manager-bridge": "self.version", "symfony/routing": "self.version", "symfony/security": "self.version", "symfony/security-bundle": "self.version", "symfony/security-core": "self.version", "symfony/security-csrf": "self.version", "symfony/security-guard": "self.version", "symfony/security-http": "self.version", "symfony/serializer": "self.version", "symfony/stopwatch": "self.version", "symfony/templating": "self.version", "symfony/translation": "self.version", "symfony/twig-bridge": "self.version", "symfony/twig-bundle": "self.version", "symfony/validator": "self.version", "symfony/var-dumper": "self.version", "symfony/web-link": "self.version", "symfony/web-profiler-bundle": "self.version", "symfony/web-server-bundle": "self.version", "symfony/workflow": "self.version", "symfony/yaml": "self.version"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/annotations": "~1.0", "doctrine/cache": "~1.6", "doctrine/data-fixtures": "^1.1", "doctrine/dbal": "~2.4", "doctrine/doctrine-bundle": "~1.4", "doctrine/orm": "~2.4,>=2.4.5", "egulias/email-validator": "~1.2,>=1.2.8|~2.0", "monolog/monolog": "~1.11", "ocramius/proxy-manager": "~0.4|~1.0|~2.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "predis/predis": "~1.0", "symfony/phpunit-bridge": "^5.2", "symfony/security-acl": "~2.8|~3.0"}, "type": "library", "extra": {"branch-version": "3.4"}, "autoload": {"psr-4": {"Symfony\\Bundle\\": "src/Symfony/Bundle/", "Symfony\\Component\\": "src/Symfony/Component/", "Symfony\\Bridge\\Twig\\": "src/Symfony/Bridge/Twig/", "Symfony\\Bridge\\Monolog\\": "src/Symfony/Bridge/Monolog/", "Symfony\\Bridge\\Doctrine\\": "src/Symfony/Bridge/Doctrine/", "Symfony\\Bridge\\ProxyManager\\": "src/Symfony/Bridge/ProxyManager/"}, "classmap": ["src/Symfony/Component/Intl/Resources/stubs"], "exclude-from-classmap": ["**/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "The Symfony PHP framework", "homepage": "https://symfony.com", "keywords": ["framework"], "support": {"issues": "https://github.com/symfony/symfony/issues", "source": "https://github.com/symfony/symfony/tree/v3.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-19T12:07:19+00:00"}, {"name": "symfony/webpack-encore-bundle", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/symfony/webpack-encore-bundle.git", "reference": "c879bc50c69f6b4f2984b2bb5fe8190bbc5befdd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/webpack-encore-bundle/zipball/c879bc50c69f6b4f2984b2bb5fe8190bbc5befdd", "reference": "c879bc50c69f6b4f2984b2bb5fe8190bbc5befdd", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/asset": "^3.4 || ^4.0 || ^5.0", "symfony/config": "^3.4 || ^4.0 || ^5.0", "symfony/dependency-injection": "^3.4 || ^4.0 || ^5.0", "symfony/http-kernel": "^3.4 || ^4.0 || ^5.0", "symfony/service-contracts": "^1.0 || ^2.0"}, "require-dev": {"symfony/framework-bundle": "^3.4 || ^4.0 || ^5.0", "symfony/phpunit-bridge": "^4.3.5 || ^5.0", "symfony/twig-bundle": "^3.4 || ^4.0 || ^5.0", "symfony/web-link": "^3.4 || ^4.0 || ^5.0"}, "type": "symfony-bundle", "extra": {"thanks": {"name": "symfony/webpack-encore", "url": "https://github.com/symfony/webpack-encore"}}, "autoload": {"psr-4": {"Symfony\\WebpackEncoreBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Integration with your Symfony app & Webpack Encore!", "support": {"issues": "https://github.com/symfony/webpack-encore-bundle/issues", "source": "https://github.com/symfony/webpack-encore-bundle/tree/v1.8.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-28T17:07:25+00:00"}, {"name": "twig/extensions", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig-extensions.git", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig-extensions/zipball/57873c8b0c1be51caa47df2cdb824490beb16202", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202", "shasum": ""}, "require": {"twig/twig": "^1.27|^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4", "symfony/translation": "^2.7|^3.4"}, "suggest": {"symfony/translation": "Allow the time_diff output to be translated"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Twig_Extensions_": "lib/"}, "psr-4": {"Twig\\Extensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common additional features for Twig that do not directly belong in core", "keywords": ["i18n", "text"], "support": {"issues": "https://github.com/twigphp/Twig-extensions/issues", "source": "https://github.com/twigphp/Twig-extensions/tree/master"}, "abandoned": true, "time": "2018-12-05T18:34:18+00:00"}, {"name": "twig/twig", "version": "v1.44.7", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "0887422319889e442458e48e2f3d9add1a172ad5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/0887422319889e442458e48e2f3d9add1a172ad5", "reference": "0887422319889e442458e48e2f3d9add1a172ad5", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4.9|^5.0.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.44-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v1.44.7"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2022-09-28T08:38:36+00:00"}, {"name": "will<PERSON><PERSON>/js-translation-bundle", "version": "2.6.6", "source": {"type": "git", "url": "https://github.com/willdurand/BazingaJsTranslationBundle.git", "reference": "9c80406dd4cc195f1f835a52e038fb80a96563b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/BazingaJsTranslationBundle/zipball/9c80406dd4cc195f1f835a52e038fb80a96563b2", "reference": "9c80406dd4cc195f1f835a52e038fb80a96563b2", "shasum": ""}, "require": {"symfony/console": "~2.7|~3.1|~4.0", "symfony/finder": "~2.7|~3.1|~4.0", "symfony/framework-bundle": "~2.7|~3.1|~4.0", "symfony/intl": "~2.7|~3.1|~4.0", "symfony/translation": "~2.7|~3.1|~4.0", "symfony/twig-bundle": "~2.7|~3.1|~4.0"}, "replace": {"willdurand/expose-translation-bundle": "2.5.*"}, "require-dev": {"phpunit/phpunit": "^4.8|~5.7", "symfony/asset": "~2.7|~3.1|~4.0", "symfony/browser-kit": "~2.7|~3.1|~4.0", "symfony/phpunit-bridge": "~2.7|~3.1|~4.0", "symfony/twig-bundle": "~2.7|~3.1|~4.0", "symfony/yaml": "~2.7|~3.1|~4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Bazinga\\Bundle\\JsTranslationBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A pretty nice way to expose your translation messages to your JavaScript.", "keywords": ["javascript", "symfony", "translation"], "support": {"issues": "https://github.com/willdurand/BazingaJsTranslationBundle/issues", "source": "https://github.com/willdurand/BazingaJsTranslationBundle/tree/master"}, "time": "2018-02-11T14:19:14+00:00"}], "packages-dev": [{"name": "atoum/atoum", "version": "4.1", "source": {"type": "git", "url": "https://github.com/atoum/atoum.git", "reference": "e866f3d4ad683c35757cd73fc6da3e3d5e563667"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/atoum/atoum/zipball/e866f3d4ad683c35757cd73fc6da3e3d5e563667", "reference": "e866f3d4ad683c35757cd73fc6da3e3d5e563667", "shasum": ""}, "require": {"ext-hash": "*", "ext-json": "*", "ext-tokenizer": "*", "ext-xml": "*", "php": "^7.4 || ^8.0"}, "replace": {"mageekguy/atoum": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2"}, "suggest": {"atoum/stubs": "Provides IDE support (like autocompletion) for atoum", "ext-mbstring": "Provides support for UTF-8 strings", "ext-xdebug": "Provides code coverage report (>= 2.3)"}, "bin": ["bin/atoum"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"classmap": ["classes/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.mageekbox.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Simple modern and intuitive unit testing framework for PHP 5.3+", "homepage": "http://www.atoum.org", "keywords": ["TDD", "atoum", "test", "unit testing"], "support": {"issues": "https://github.com/atoum/atoum/issues", "source": "https://github.com/atoum/atoum/tree/4.1"}, "time": "2022-11-20T20:18:31+00:00"}, {"name": "behat/behat", "version": "v3.7.0", "source": {"type": "git", "url": "https://github.com/Behat/Behat.git", "reference": "08052f739619a9e9f62f457a67302f0715e6dd13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Behat/zipball/08052f739619a9e9f62f457a67302f0715e6dd13", "reference": "08052f739619a9e9f62f457a67302f0715e6dd13", "shasum": ""}, "require": {"behat/gherkin": "^4.6.0", "behat/transliterator": "^1.2", "ext-mbstring": "*", "php": ">=5.3.3", "psr/container": "^1.0", "symfony/config": "^2.7.51 || ^3.0 || ^4.0 || ^5.0", "symfony/console": "^2.7.51 || ^2.8.33 || ^3.3.15 || ^3.4.3 || ^4.0.3 || ^5.0", "symfony/dependency-injection": "^2.7.51 || ^3.0 || ^4.0 || ^5.0", "symfony/event-dispatcher": "^2.7.51 || ^3.0 || ^4.0 || ^5.0", "symfony/translation": "^2.7.51 || ^3.0 || ^4.0 || ^5.0", "symfony/yaml": "^2.7.51 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"container-interop/container-interop": "^1.2", "herrera-io/box": "~1.6.1", "phpunit/phpunit": "^4.8.36 || ^6.5.14 || ^7.5.20", "symfony/process": "~2.5 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"ext-dom": "Needed to output test results in JUnit format."}, "bin": ["bin/behat"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "autoload": {"psr-4": {"Behat\\Behat\\": "src/Behat/Behat/", "Behat\\Testwork\\": "src/Behat/Testwork/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Scenario-oriented BDD framework for PHP 5.3", "homepage": "http://behat.org/", "keywords": ["Agile", "BDD", "ScenarioBDD", "Scrum", "StoryBDD", "User story", "business", "development", "documentation", "examples", "symfony", "testing"], "support": {"issues": "https://github.com/Behat/Behat/issues", "source": "https://github.com/Behat/Behat/tree/v3.7.0"}, "time": "2020-06-03T13:08:44+00:00"}, {"name": "behat/gherkin", "version": "v4.9.0", "source": {"type": "git", "url": "https://github.com/Behat/Gherkin.git", "reference": "0bc8d1e30e96183e4f36db9dc79caead300beff4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Gherkin/zipball/0bc8d1e30e96183e4f36db9dc79caead300beff4", "reference": "0bc8d1e30e96183e4f36db9dc79caead300beff4", "shasum": ""}, "require": {"php": "~7.2|~8.0"}, "require-dev": {"cucumber/cucumber": "dev-gherkin-22.0.0", "phpunit/phpunit": "~8|~9", "symfony/yaml": "~3|~4|~5"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-0": {"Behat\\Gherkin": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Gherkin DSL parser for PHP", "homepage": "http://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "support": {"issues": "https://github.com/Behat/Gher<PERSON>/issues", "source": "https://github.com/Behat/Gherkin/tree/v4.9.0"}, "time": "2021-10-12T13:05:09+00:00"}, {"name": "behat/mink", "version": "v1.9.0", "source": {"type": "git", "url": "https://github.com/minkphp/Mink.git", "reference": "e35f4695de8800fc776af34ebf665ad58ebdd996"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/minkphp/Mink/zipball/e35f4695de8800fc776af34ebf665ad58ebdd996", "reference": "e35f4695de8800fc776af34ebf665ad58ebdd996", "shasum": ""}, "require": {"php": ">=5.4", "symfony/css-selector": "^2.7|^3.0|^4.0|^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5 || ^9.5", "symfony/debug": "^2.7|^3.0|^4.0|^5.0", "symfony/phpunit-bridge": "^3.4.38 || ^4.4 || ^5.0.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"behat/mink-browserkit-driver": "extremely fast headless driver for Symfony\\Kernel-based apps (Sf2, Silex)", "behat/mink-goutte-driver": "fast headless driver for any app without JS emulation", "behat/mink-selenium2-driver": "slow, but JS-enabled driver for any app (requires Selenium2)", "behat/mink-zombie-driver": "fast and JS-enabled headless driver for any app (requires node.js)", "dmore/chrome-mink-driver": "fast and JS-enabled driver for any app (requires chromium or google chrome)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Behat\\Mink\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Browser controller/emulator abstraction for PHP", "homepage": "https://mink.behat.org/", "keywords": ["browser", "testing", "web"], "support": {"issues": "https://github.com/minkphp/Mink/issues", "source": "https://github.com/minkphp/Mink/tree/v1.9.0"}, "time": "2021-10-11T11:58:47+00:00"}, {"name": "behat/mink-extension", "version": "2.3.1", "source": {"type": "git", "url": "https://github.com/Behat/MinkExtension.git", "reference": "80f7849ba53867181b7e412df9210e12fba50177"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/MinkExtension/zipball/80f7849ba53867181b7e412df9210e12fba50177", "reference": "80f7849ba53867181b7e412df9210e12fba50177", "shasum": ""}, "require": {"behat/behat": "^3.0.5", "behat/mink": "^1.5", "php": ">=5.3.2", "symfony/config": "^2.7|^3.0|^4.0"}, "require-dev": {"behat/mink-goutte-driver": "^1.1", "phpspec/phpspec": "^2.0"}, "type": "behat-extension", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-0": {"Behat\\MinkExtension": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mink extension for Behat", "homepage": "http://extensions.behat.org/mink", "keywords": ["browser", "gui", "test", "web"], "support": {"issues": "https://github.com/Behat/MinkExtension/issues", "source": "https://github.com/Behat/MinkExtension/tree/master"}, "abandoned": "friends-of-behat/mink-extension", "time": "2018-02-06T15:36:30+00:00"}, {"name": "behat/mink-selenium2-driver", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/minkphp/MinkSelenium2Driver.git", "reference": "e5f8421654930da725499fb92983e6948c6f973e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/minkphp/MinkSelenium2Driver/zipball/e5f8421654930da725499fb92983e6948c6f973e", "reference": "e5f8421654930da725499fb92983e6948c6f973e", "shasum": ""}, "require": {"behat/mink": "^1.9@dev", "ext-json": "*", "instaclick/php-webdriver": "^1.4", "php": ">=7.2"}, "require-dev": {"mink/driver-testsuite": "dev-master", "phpunit/phpunit": "^8.5.22 || ^9.5.11", "symfony/error-handler": "^4.4 || ^5.0"}, "type": "mink-driver", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Behat\\Mink\\Driver\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/pete-otaqui"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Selenium2 (WebDriver) driver for Mink framework", "homepage": "https://mink.behat.org/", "keywords": ["ajax", "browser", "javascript", "selenium", "testing", "webdriver"], "support": {"issues": "https://github.com/minkphp/MinkSelenium2Driver/issues", "source": "https://github.com/minkphp/MinkSelenium2Driver/tree/v1.6.0"}, "time": "2022-03-28T14:55:17+00:00"}, {"name": "behat/symfony2-extension", "version": "2.1.5", "source": {"type": "git", "url": "https://github.com/Behat/Symfony2Extension.git", "reference": "d7c834487426a784665f9c1e61132274dbf2ea26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Symfony2Extension/zipball/d7c834487426a784665f9c1e61132274dbf2ea26", "reference": "d7c834487426a784665f9c1e61132274dbf2ea26", "shasum": ""}, "require": {"behat/behat": "^3.4.3", "php": ">=5.3.3", "symfony/framework-bundle": "~2.0|~3.0|~4.0"}, "require-dev": {"behat/mink": "~1.7@dev", "behat/mink-browserkit-driver": "~1.3@dev", "behat/mink-extension": "~2.0", "phpspec/phpspec": "~2.0|~3.0|~4.0", "phpunit/phpunit": "~4.0|~5.0", "symfony/symfony": "~2.1|~3.0|~4.0"}, "type": "behat-extension", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-0": {"Behat\\Symfony2Extension": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony2 framework extension for Behat", "homepage": "http://behat.org", "keywords": ["BDD", "framework", "symfony"], "support": {"issues": "https://github.com/Behat/Symfony2Extension/issues", "source": "https://github.com/Behat/Symfony2Extension/tree/master"}, "abandoned": "friends-of-behat/symfony-extension", "time": "2018-04-20T15:48:23+00:00"}, {"name": "behat/transliterator", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/Behat/Transliterator.git", "reference": "baac5873bac3749887d28ab68e2f74db3a4408af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Transliterator/zipball/baac5873bac3749887d28ab68e2f74db3a4408af", "reference": "baac5873bac3749887d28ab68e2f74db3a4408af", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"chuyskywalker/rolling-curl": "^3.1", "php-yaoi/php-yaoi": "^1.0", "phpunit/phpunit": "^8.5.25 || ^9.5.19"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Behat\\Transliterator\\": "src/Behat/Transliterator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Artistic-1.0"], "description": "String transliterator", "keywords": ["i18n", "slug", "transliterator"], "support": {"issues": "https://github.com/Behat/Transliterator/issues", "source": "https://github.com/Behat/Transliterator/tree/v1.5.0"}, "time": "2022-03-30T09:27:43+00:00"}, {"name": "behatch/contexts", "version": "3.3.0", "source": {"type": "git", "url": "https://github.com/Behatch/contexts.git", "reference": "e45284b1dddb9dce78e186a56683527802bccd47"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behatch/contexts/zipball/e45284b1dddb9dce78e186a56683527802bccd47", "reference": "e45284b1dddb9dce78e186a56683527802bccd47", "shasum": ""}, "require": {"behat/behat": "^3.0.13", "friends-of-behat/mink-extension": "^2.3.1", "justinrainbow/json-schema": "^5.0", "php": ">=5.5", "symfony/dom-crawler": "^2.4|^3.0|^4.0|^5.0", "symfony/http-foundation": "^2.3|^3.0|^4.0|^5.0", "symfony/property-access": "^2.3|^3.0|^4.0|^5.0"}, "replace": {"sanpi/behatch-contexts": "self.version"}, "require-dev": {"atoum/atoum": "^2.8|^3.0", "behat/mink-goutte-driver": "^1.1", "behat/mink-selenium2-driver": "^1.4@dev", "fabpot/goutte": "^3.2", "guzzlehttp/guzzle": "^6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Behatch\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["beerware"], "description": "Behatch contexts", "keywords": ["BDD", "<PERSON><PERSON>", "Context", "Symfony2"], "support": {"issues": "https://github.com/Behatch/contexts/issues", "source": "https://github.com/Behatch/contexts/tree/3.3.0"}, "abandoned": true, "time": "2020-02-27T08:40:50+00:00"}, {"name": "friends-of-behat/mink-extension", "version": "2.3.1", "source": {"type": "git", "url": "https://github.com/FriendsOfBehat/MinkExtension.git", "reference": "80f7849ba53867181b7e412df9210e12fba50177"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfBehat/MinkExtension/zipball/80f7849ba53867181b7e412df9210e12fba50177", "reference": "80f7849ba53867181b7e412df9210e12fba50177", "shasum": ""}, "require": {"behat/behat": "^3.0.5", "behat/mink": "^1.5", "php": ">=5.3.2", "symfony/config": "^2.7|^3.0|^4.0"}, "require-dev": {"behat/mink-goutte-driver": "^1.1", "phpspec/phpspec": "^2.0"}, "type": "behat-extension", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-0": {"Behat\\MinkExtension": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mink extension for Behat", "homepage": "http://extensions.behat.org/mink", "keywords": ["browser", "gui", "test", "web"], "support": {"source": "https://github.com/FriendsOfBehat/MinkExtension/tree/2.3.1"}, "time": "2018-02-06T15:36:30+00:00"}, {"name": "instaclick/php-webdriver", "version": "1.4.19", "source": {"type": "git", "url": "https://github.com/instaclick/php-webdriver.git", "reference": "3b2a2ddc4e0a690cc691d7e5952964cc4b9538b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/instaclick/php-webdriver/zipball/3b2a2ddc4e0a690cc691d7e5952964cc4b9538b1", "reference": "3b2a2ddc4e0a690cc691d7e5952964cc4b9538b1", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.5", "satooshi/php-coveralls": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"WebDriver": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Fork Maintainer"}], "description": "PHP WebDriver for Selenium 2", "homepage": "http://instaclick.com/", "keywords": ["browser", "selenium", "webdriver", "webtest"], "support": {"issues": "https://github.com/instaclick/php-webdriver/issues", "source": "https://github.com/instaclick/php-webdriver/tree/1.4.19"}, "time": "2024-03-19T01:58:53+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/jsonrainbow/json-schema.git", "reference": "feb2ca6dd1cebdaf1ed60a4c8de2e53ce11c4fd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jsonrainbow/json-schema/zipball/feb2ca6dd1cebdaf1ed60a4c8de2e53ce11c4fd8", "reference": "feb2ca6dd1cebdaf1ed60a4c8de2e53ce11c4fd8", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/jsonrainbow/json-schema/issues", "source": "https://github.com/jsonrainbow/json-schema/tree/5.3.0"}, "time": "2024-07-06T21:00:26+00:00"}, {"name": "phpstan/phpstan", "version": "1.11.11", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "707c2aed5d8d0075666e673a5e71440c1d01a5a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/707c2aed5d8d0075666e673a5e71440c1d01a5a3", "reference": "707c2aed5d8d0075666e673a5e71440c1d01a5a3", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2024-08-19T14:37:29+00:00"}, {"name": "phpstan/phpstan-deprecation-rules", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-deprecation-rules.git", "reference": "fa8cce7720fa782899a0aa97b6a41225d1bb7b26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-deprecation-rules/zipball/fa8cce7720fa782899a0aa97b6a41225d1bb7b26", "reference": "fa8cce7720fa782899a0aa97b6a41225d1bb7b26", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.11"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan rules for detecting usage of deprecated classes, methods, properties, constants and traits.", "support": {"issues": "https://github.com/phpstan/phpstan-deprecation-rules/issues", "source": "https://github.com/phpstan/phpstan-deprecation-rules/tree/1.2.0"}, "time": "2024-04-20T06:39:48+00:00"}, {"name": "phpstan/phpstan-symfony", "version": "1.4.7", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-symfony.git", "reference": "ee88a01bc48f608143d3376802ec952270737cb8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-symfony/zipball/ee88a01bc48f608143d3376802ec952270737cb8", "reference": "ee88a01bc48f608143d3376802ec952270737cb8", "shasum": ""}, "require": {"ext-simplexml": "*", "php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.11.7"}, "conflict": {"symfony/framework-bundle": "<3.0"}, "require-dev": {"nikic/php-parser": "^4.13.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-phpunit": "^1.3.11", "phpstan/phpstan-strict-rules": "^1.5.1", "phpunit/phpunit": "^8.5.29 || ^9.5", "psr/container": "1.0 || 1.1.1", "symfony/config": "^5.4 || ^6.1", "symfony/console": "^5.4 || ^6.1", "symfony/dependency-injection": "^5.4 || ^6.1", "symfony/form": "^5.4 || ^6.1", "symfony/framework-bundle": "^5.4 || ^6.1", "symfony/http-foundation": "^5.4 || ^6.1", "symfony/messenger": "^5.4", "symfony/polyfill-php80": "^1.24", "symfony/serializer": "^5.4", "symfony/service-contracts": "^2.2.0"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon", "rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://lookyman.net"}], "description": "Symfony Framework extensions and rules for PHPStan", "support": {"issues": "https://github.com/phpstan/phpstan-symfony/issues", "source": "https://github.com/phpstan/phpstan-symfony/tree/1.4.7"}, "time": "2024-08-09T12:37:24+00:00"}, {"name": "rector/rector", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/rectorphp/rector.git", "reference": "42a4aa23b48b4cfc8ebfeac2b570364e27744381"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rectorphp/rector/zipball/42a4aa23b48b4cfc8ebfeac2b570364e27744381", "reference": "42a4aa23b48b4cfc8ebfeac2b570364e27744381", "shasum": ""}, "require": {"php": "^7.2|^8.0", "phpstan/phpstan": "^1.11.11"}, "conflict": {"rector/rector-doctrine": "*", "rector/rector-downgrade-php": "*", "rector/rector-phpunit": "*", "rector/rector-symfony": "*"}, "suggest": {"ext-dom": "To manipulate phpunit.xml via the custom-rule command"}, "bin": ["bin/rector"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Instant Upgrade and Automated Refactoring of any PHP code", "keywords": ["automation", "dev", "migration", "refactoring"], "support": {"issues": "https://github.com/rectorphp/rector/issues", "source": "https://github.com/rectorphp/rector/tree/1.2.4"}, "funding": [{"url": "https://github.com/tomasvotruba", "type": "github"}], "time": "2024-08-23T09:03:01+00:00"}, {"name": "sensio/generator-bundle", "version": "v3.1.7", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioGeneratorBundle.git", "reference": "28cbaa244bd0816fd8908b93f90380bcd7b67a65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioGeneratorBundle/zipball/28cbaa244bd0816fd8908b93f90380bcd7b67a65", "reference": "28cbaa244bd0816fd8908b93f90380bcd7b67a65", "shasum": ""}, "require": {"symfony/console": "~2.7|~3.0", "symfony/framework-bundle": "~2.7|~3.0", "symfony/process": "~2.7|~3.0", "symfony/yaml": "~2.7|~3.0", "twig/twig": "^1.28.2|^2.0"}, "require-dev": {"doctrine/orm": "~2.4", "symfony/doctrine-bridge": "~2.7|~3.0", "symfony/filesystem": "~2.7|~3.0", "symfony/phpunit-bridge": "^3.3"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\GeneratorBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle generates code for you", "support": {"issues": "https://github.com/sensiolabs/SensioGeneratorBundle/issues", "source": "https://github.com/sensiolabs/SensioGeneratorBundle/tree/master"}, "abandoned": "symfony/maker-bundle", "time": "2017-12-07T15:36:41+00:00"}, {"name": "son-video/cms-schema", "version": "dev-2432_consent_display_order", "source": {"type": "git", "url": "**************:son-video/cms-schema.git", "reference": "49836333584ceb3e12cd179c56ddbe320ed0af76"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/son-video%2Fcms-schema/repository/archive.zip?sha=49836333584ceb3e12cd179c56ddbe320ed0af76", "reference": "49836333584ceb3e12cd179c56ddbe320ed0af76", "shasum": ""}, "require": {"symfony/console": "^3.1 || ^4.1", "symfony/yaml": "^3.1 || ^4.1"}, "type": "library", "autoload": {"psr-4": {"SonVideo\\Cms\\": "sources/lib/"}}, "license": ["proprietary"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "CMS Postgresql database schema", "support": {"issues": "https://gitlab.com/api/v4/projects/26484755/issues"}, "time": "2025-05-30T15:23:01+02:00"}, {"name": "son-video/one-redirection", "version": "dev-dev", "source": {"type": "git", "url": "**************:son-video/one-redirection.git", "reference": "a632e1ea43bfccaa16a66ad4cf1b69ee9d3c123f"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/son-video%2Fone-redirection/repository/archive.zip?sha=a632e1ea43bfccaa16a66ad4cf1b69ee9d3c123f", "reference": "a632e1ea43bfccaa16a66ad4cf1b69ee9d3c123f", "shasum": ""}, "require": {"pomm-project/foundation": "^2.0", "symfony/console": "^3.2"}, "require-dev": {"atoum/atoum": "^3.0"}, "default-branch": true, "type": "project", "autoload": {"psr-4": {"SonVideo\\OneRedirection\\": "sources/lib/", "SonVideo\\OneRedirection\\Test\\": "sources/tests/"}}, "license": ["proprietary"], "description": "Url redirection application for cms front", "support": {"issues": "https://gitlab.com/api/v4/projects/26501276/issues"}, "time": "2018-03-05T11:20:46+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"elastic/enterprise-search": 20, "son-video/cms-schema": 20, "son-video/one-redirection": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.4", "ext-intl": "*", "ext-json": "*"}, "platform-dev": [], "plugin-api-version": "2.0.0"}
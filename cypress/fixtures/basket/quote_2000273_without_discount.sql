UPDATE customer.quote
SET prices = '{"total_vat": 150, "computed_vat_rate": 1.2, "total_price_tax_excluded": 750.0000000000001, "total_price_tax_included": 900, "total_discount_tax_excluded": 0, "total_discount_tax_included": 0}',
    lines = '[{"data": {"product": {"sku": "YAMHTR2071NR", "vat": 0.2, "type": "article", "image": "/images/dynamic/Enceintes/articles/KEF/KEFQ150BC/KEF-Q150-Blanc_P_300_square.jpg", "product_id": 149617, "description": "Paire denceintes KEF Q150 Blanc", "ecotax_price": 1, "sorecop_price": 0, "short_description": "KEF Q150 Blanc (la paire)", "selling_price_tax_included": 499}, "quantity": 3, "total_price": 900, "selected_warranties": [{"type": "extension", "label": "Extension de garantie 5 ans", "duration": 5, "quantity": 1, "unit_selling_price_tax_included": 229}], "total_discount_amount": 0, "selling_price_tax_excluded": 415.83333333333337, "selling_price_tax_included": 499, "unit_discount_amount_abs_tax_included": 0}, "type": "product", "display_order": 1}, {"data": {"type": "extension", "label": "Extension de garantie 5 ans", "duration": 5, "quantity": 1, "price_tax_excluded": 190.83333333333334, "total_price_tax_excluded": 190.83333333333334, "total_price_tax_included": 229, "unit_selling_price_tax_included": 229}, "type": "product_warranty", "display_order": 1}]'
WHERE quote_id = 2000273;

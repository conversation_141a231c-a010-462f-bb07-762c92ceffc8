const DESTOCK_URL = '/article/super-enceinte-destock'
const PARENT_DESTOCK_URL = '/article/elipson-prestige-4i-calvados-fr'
describe('Article - Destock (Second Life)', function () {
    before(function () {
        cy.exec(Cypress.env().SETUP_DATABASE).its('stderr').should('eq', '')
    })

    beforeEach(function () {
        cy.exec(Cypress.env().RELOAD_FIXTURES).its('stderr').should('eq', '')
        cy.applySqlFixture('article/destock/add_destocks.sql')
        cy.preAcceptSiteCookies()
    })

    it('shows second life data on destock product', function () {
        cy.visit(DESTOCK_URL)
        cy.get('[data-context=second-life]')
            .should('be.visible')
            .within((root) => {
                cy.get(root)
                    .should('contain', 'Offre de seconde vie')
                    .should('contain', 'État acceptable')
                    .should('contain', 'Modèle d’expo')
                    .should('contain', 'L’emballage d’origine est manquant')
                    .should('contain', 'Le produit présente de légères traces')
                    .should('contain', 'Description publique.')

                cy.get('img')
                    .should('have.length', 1)
                    .eq(0)
                    .should('have.attr', 'src')
                    .should(
                        'contain',
                        '/images/article/cambridge/CAMBMINXMIN12NR/minx-min-12-noir_6214c63b7ee3f_1200.jpg?p=150_square'
                    )

                cy.get('[data-context=other-offers]')
                    .should('be.visible')
                    .should('contain', 'Toutes les offres pour ce produit')
                    .htmlContains('3 offres de seconde vie dès 10 €')
                    .htmlContains('Également vendu neuf au prix de 599 €')

                cy.log('=== contains a link to the parent product ===')
                cy.get('a:contains(vendu neuf)').should('have.attr', 'href', PARENT_DESTOCK_URL)
            })

        cy.log('=== Second life data should not be present on non destock product ===')
        cy.visit(PARENT_DESTOCK_URL)
        cy.get('[data-context=second-life]').should('not.exist')

        cy.log('=== Parent offer should not be displayed if parent is unbasketable ===')
        cy.applySqlFixture('article/destock/set_parent_unbasketable.sql')
        cy.visit(DESTOCK_URL)
        cy.get('[data-context=second-life]').should('be.visible').find('a:contains(vendu neuf)').should('not.exist')
    })

    it('displays images from parent product and its own', () => {
        cy.visit(DESTOCK_URL)

        cy.get('#slick-slider-nav .slick-slide:not(.slick-cloned)').should('have.length', 9).as('thumbnails')

        cy.log(`=== last thumbnail should be the destock's image ===`)
        cy.get('@thumbnails')
            .eq(8)
            .find('img')
            .should('have.attr', 'data-src')
            .should(
                'contain',
                '/images/article/cambridge/CAMBMINXMIN12NR/minx-min-12-noir_6214c63b7ee3f_1200.jpg?p=100'
            )

        cy.log(`=== should display the right tab and picture when clicking from carrousel ===`)
        function checkTheRightTabAndPictureIsDisplayed() {
            cy.get('#article-picture').should('be.visible')
            cy.get('.choice.selected').should('contain', 'Seconde vie')
            cy.get('.main-pic img[src*="minx-min-12-noir_6214c63b7ee3f_1200.jpg"]').should('be.visible')
            cy.get('[data-context=close-modal]').click()
            cy.get('#article-picture').should('not.exist')
        }
        cy.get('@thumbnails').eq(8).click()
        cy.wait(500) // wait for animation to complete
        cy.get('#slick-slider .slick-active img[data-src*="minx-min-12-noir_6214c63b7ee3f_1200.jpg"]')
            .should('be.visible')
            .click()
        cy.wait(100) // wait for animation to complete
        checkTheRightTabAndPictureIsDisplayed()

        cy.log(`=== should display the carrousel right tab and picture when clicking from second life's media list ===`)
        cy.get('[data-context=second-life] img').click()
        cy.wait(100) // wait for animation to complete
        checkTheRightTabAndPictureIsDisplayed()
    })

    it('should display offers summary in island', () => {
        const checkIslandOffers = (quantity, price) => {
            switch (quantity) {
                case 0:
                    cy.get('[data-context=second-life-offers-summary]').should('not.exist')
                    break
                case 1:
                    cy.get('[data-context=second-life-offers-summary]')
                        .should('be.visible')
                        .htmlContains(`${quantity} offre seconde vie à ${price}`)
                    break
                default:
                    cy.get('[data-context=second-life-offers-summary]')
                        .should('be.visible')
                        .htmlContains(`${quantity} offres seconde vie dès ${price}`)
                    break
            }
        }
        cy.log('=== multi offer on parent ===')
        cy.visit(PARENT_DESTOCK_URL)
        checkIslandOffers(3, '10 €')

        cy.log('=== multi offer on the destock ===')
        cy.visit(DESTOCK_URL)
        checkIslandOffers(3, '10 €')

        cy.applySqlFixture('article/destock/set_unbasketable_destocks.sql')

        cy.log('=== one offer on the destock, itself, should not be visible ===')
        cy.visit(DESTOCK_URL)
        checkIslandOffers(0)

        cy.log('=== one offer on the destock, another one, should be visible ===')
        cy.visit('/sku/DESTOCK-COPY1')
        checkIslandOffers(1, '10 €')

        cy.log('=== no offer at all, should not be visible ===')
        cy.visit('/article/packs-d-enceintes-grand-spectacle/triangle/elara-ln07-cinema-blanc-laque')
        checkIslandOffers(0)
    })

    it('can display destock offers in the slide panel', () => {
        cy.visit(DESTOCK_URL)

        cy.log('=== From second life content ===')
        cy.get('[data-context=second-life] [data-context=other-offers] li:contains(3 offres de seconde vie)').click()

        cy.get('[data-context="side-panel"]')
            .should('be.visible')
            .within((root) => {
                cy.get(root).should('contain', '3 offres de seconde vie')

                cy.get('[data-context=second-life-article]').should('have.length', 3).as('offers')

                cy.log('=== check ordered by price ===')
                cy.get('@offers').eq(0).htmlContains('10 €')
                cy.get('@offers').eq(1).htmlContains('253,45 €')
                cy.get('@offers').eq(2).htmlContains('300 €')

                cy.log('=== check an offer content ===')
                cy.get('@offers')
                    .eq(0)
                    .should('contain', 'État acceptable')
                    .should('contain', 'Modèle d’expo')
                    .should('be.visible')
                    .should('contain', 'Voir le détail')

                cy.log('=== check add to basket button ===')
                cy.get('@offers').eq(0).find('button:contains(Ajouter au panier)').click()
            })
        cy.log('=== check add to basket confirmation. Complementary articles should be there. ===')
        cy.get('[data-context="side-panel"]').should('not.exist')
        cy.get('.swal2-modal')
            .should('be.visible')
            .should('contain', 'Cet article a été ajouté à votre panier')
            .should('contain', 'Ceci pourrait également vous intéresser')
            .find('button:contains(Poursuivre les achats)')
            .click()

        cy.log('=== From island content ===')
        cy.get('[data-context=second-life-offers-summary]').click()
        cy.get('[data-context="side-panel"]').should('be.visible').should('contain', '3 offres de seconde vie')
    })

    it('should display additional data for color declination', () => {
        cy.applySqlFixture('article/destock/set_color_declination.sql')
        cy.visit(PARENT_DESTOCK_URL)
        cy.get('[data-context=second-life-offers-summary]').click()
        cy.get('[data-context="side-panel"]').should('be.visible')

        cy.get('[data-context=second-life-article]').as('offers')

        cy.log(
            `- should display the product's name,
            - a warning if different name than current product,
            - and list must be reordered to have other color in last position (for same price)`
        )
        cy.get('@offers')
            .eq(0)
            .should('contain', 'Prestige 4i Calvados')
            .find('[data-context=warning-color]')
            .should('not.exist')
        cy.get('@offers')
            .eq(2)
            .should('contain', 'Prestige 4i Autre couleur')
            .find('[data-context=warning-color]')
            .should('be.visible')
            .should('contain', 'Coloris différent')
    })
})

describe('Sustainability label', function () {
    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        cy.reloadFixtures()
    })

    it('should display the sustainability label without document', function () {
        cy.applySqlFixture('article/regulated_attributes/repairability_index.sql')
        cy.applySqlFixture('article/regulated_attributes/sustainability_attribute.sql')
        cy.preAcceptSiteCookies().visit('/article/elipson-prestige-4i-calvados-fr')

        cy.get('[data-context=product-header-optional-section]').should('be.visible')
        cy.get('[data-context=sustainability]').should('be.visible').should('not.have.attr', 'href')
        cy.get('[data-context=sustainability] img')
            .should('have.attr', 'src')
            .should('contain', '/images/ui/gouvernement/durabilite/IDD_73.svg')

        cy.log('=== repairability must not be displayed ===')
        cy.get('[data-context=repairability-index]').should('not.exist')
    })

    it('should display the sustainability label with document link', function () {
        cy.applySqlFixture('article/regulated_attributes/sustainability_attribute.sql')
        cy.applySqlFixture('article/regulated_attributes/sustainability_document.sql')
        cy.preAcceptSiteCookies().visit('/article/elipson-prestige-4i-calvados-fr')

        cy.get('[data-context=product-header-optional-section]').should('be.visible')
        cy.get('[data-context=sustainability]')
            .should('be.visible')
            .should('have.attr', 'href')
            .should('contain', '/image/pdf/guides/GuidePlacement.pdf')
        cy.get('[data-context=sustainability] img')
            .should('have.attr', 'src')
            .should('contain', '/images/ui/gouvernement/durabilite/IDD_73.svg')
    })
})

describe('Basket - basket to basket order', function () {
    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        // reset and seed the database prior to every test
        cy.reloadFixtures()
    })

    it('should redirect to funnel v2', function () {
        cy.login('geo', '/mon-panier')
        cy.get('[data-context=quote-line]').should('not.exist')
        cy.get('[data-context=go-to-tunnel]').click()

        cy.url().should('eq', Cypress.config().baseUrl + '/commander')
    })
})

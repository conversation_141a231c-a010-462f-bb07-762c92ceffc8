describe('Basket - Customer logged in', function () {
    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        // reset and seed the database prior to every test
        cy.reloadFixtures()

        cy.applySqlFixture('basket/cart_with_quote_2000273.sql')

        cy.intercept('POST', '/**/mon-panier/ajouter-promo').as('ajouter-promo')
        cy.login('jack', '/mon-panier')
    })

    it('should display a quote offer with a discount successfully in the basket on desktop', function () {
        cy.get('[data-context=quote-line]').should('be.visible').as('quote')

        cy.get('@quote').find('[data-context=quotation-title]').htmlContains(`Offre n°2000273`)
        cy.get('@quote').find('[data-context=quotation-delete]').should('exist')

        // products
        cy.get('@quote')
            .find('[data-context=quotation-products]')
            .within(() => {
                cy.get('[data-context=item-title]').should('contain', 'KEF Q150 Blanc (la paire)')
                cy.get('[data-context=article-availability]').should('contain', 'En stock')
                cy.get('[data-context=item-warranty]').should('contain', 'Garantie initiale : 2 ans')
                cy.get('[data-context=item-percent]').htmlContains('- 40 %')
                cy.get('[data-context=item-old]').htmlContains('499,00 €')
                cy.get('[data-context=item-total]').htmlContains('300,00 €')
                cy.get('[data-context=quantity-select]').find('select').should('have.value', 3).should('be.disabled')
                cy.get('[data-context=quotation-extension]').should('exist')
            })

        // promo code
        cy.get('[data-context=promo]').click()
        cy.get(`input[name=promo_code]`).should('be.visible').should('have.value', '')

        // summary
        cy.get('[data-context=basket] [data-context=badge]:visible').should('contain.text', '9')

        cy.get('.basket-item:contains(super ampli) [data-context=quantity-select] select').should('have.value', 2)
        cy.get('.basket-item:contains(Super enceinte) [data-context=quantity-select] select').should('have.value', 4)
        cy.get('.basket-item:contains(KEF Q150 Blanc) [data-context=quantity-select] select').should('have.value', 3)
    })

    it('should display a quote offer with a discount successfully in the basket on mobile', function () {
        cy.viewport('samsung-s10')

        cy.get('[data-context=quote-line]').should('be.visible').as('quote')

        // header
        cy.get('@quote').find('[data-context=quotation-title]').htmlContains(`Offre n°2000273`)
        cy.get('@quote').find('[data-context=quotation-delete]').should('exist')

        // products
        cy.get('[data-context=promo]').click()
        cy.get(`input[name=promo_code]`).should('be.visible').should('have.value', '')

        // summary
        cy.get('[data-context=basket] [data-context=badge]:visible').should('contain.text', '9')

        cy.get('.basket-item:contains(super ampli) [data-context=quantity-select] select').should('have.value', 2)
        cy.get('.basket-item:contains(Super enceinte) [data-context=quantity-select] select').should('have.value', 4)
        cy.get('.basket-item:contains(KEF Q150 Blanc) [data-context=quantity-select] select').should('have.value', 3)
    })

    it('should display a quote offer without a discount successfully in the basket', function () {
        cy.applySqlFixture('basket/quote_2000273_without_discount.sql')
        cy.visit('/mon-panier')
        cy.get('[data-context=quote-line]').should('be.visible').as('quote')

        // header
        cy.get('[data-context=quotation-title]').htmlContains(`Offre n°2000273`)
        cy.get('[data-context=quotation-delete]').should('exist')

        // products
        cy.get('.basket-item:contains(super ampli) [data-context=quantity-select] select').should('have.value', 2)
        cy.get('.basket-item:contains(Super enceinte) [data-context=quantity-select] select').should('have.value', 4)
        cy.get('.basket-item:contains(KEF Q150 Blanc) [data-context=quantity-select] select').should('have.value', 3)

        // summary
        cy.get('[data-context=price-sub-total]').should('not.exist')
        cy.get('[data-context=price-discount]').should('not.exist')
    })

    it('should display an error when a promo code is submitted', function () {
        cy.get('[data-context=quote-line]').should('be.visible')

        cy.get('[data-context=promo]').click()
        cy.get(`input[name=promo_code]`).clear().type(`KITSECU{enter}`)

        cy.wait('@ajouter-promo').then((xhr) => {
            expect(xhr.response.statusCode).equals(400)
        })
        cy.get(`[data-context=promo-error]`).should(
            'contain',
            'La promotion ne peut être appliquée à un panier contenant une offre.'
        )
    })

    it('should be able to remove a quote offer', function () {
        cy.get('[data-context=quotation-title]').htmlContains(`Offre n°2000273`)
        cy.get(`[data-context=price-total`).htmlContains('1 986,80 €')
        cy.get('[data-context=quotation-delete]').should('exist').click()
        cy.get('[data-context=quotation-title]').should('not.exist')
        cy.get(`[data-context=price-total`).htmlContains('879,80 €')
    })

    it('should show a message when a promotional code is automatically removed', function () {
        cy.get('[data-context=quotation-delete]').click()
        cy.get('[data-context=promo]').click()
        cy.get(`input[name=promo_code]`).clear().type(`KITSECU{enter}`)

        cy.wait('@ajouter-promo').then((xhr) => {
            expect(xhr.response.statusCode).equals(200)
        })
        cy.visit('/mon-compte/mes-devis-et-offres')

        cy.get('a[href="/mon-panier/ajouter-quote/2000273"]').click()
        cy.get('[data-context=flash-message-info]').should(
            'contain',
            "L'offre ne peut pas être cumulée avec un code promotionnel. Celui-ci a été supprimé de votre panier."
        )
    })

    it('display discount in funnel', function () {
        cy.intercept('GET', '**/ma-commande/v2').as('api-order')
        cy.mockRPCCall('bridge', 'carrier:get_carriers_for_order', '[]')
        cy.intercept('**/api-adresse.data.gouv.fr/search/?q=*', {
            fixture: 'api/external/api-adresse.data.gouv.fr/response.json',
            statusCode: 200,
        })
        cy.get('[data-context=go-to-tunnel]').click()

        cy.wait(['@api-order'])

        cy.get('[data-context=summary] [data-context=articles-price]').should('contain.text', 'Sous-total 2 583,80 €')
        cy.get('[data-context=summary] [data-context=articles-discount]').should('contain.text', 'Remise -597,00 €')
    })
})

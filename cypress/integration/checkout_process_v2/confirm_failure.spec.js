import { createCustomerOrderInErp, cancelCustomerOrderPayments } from './helpers'
import { IDS } from '../../support/helpers'

describe('Confirmation page at the end of payment process', () => {
    const CUSTOMER_ORDER_ID = 9999999
    const visitPage = (options = {}) => {
        options = Object.assign(
            {
                no_awaiting_order: false,
            },
            options
        )

        if (!options.no_awaiting_order) {
            cy.applySqlFixture('checkout_process_v2/confirmation/geo_order_awaiting_payment.sql')
            createCustomerOrderInErp(IDS.geo.customer_id, CUSTOMER_ORDER_ID)
        }

        cy.login('geo', `/ma-commande/confirmation/echec`)
    }

    before(() => {
        cy.setupDatabase()
        cy.viewport('macbook-15')
    })

    beforeEach(() => {
        cy.reloadFixtures()
    })

    it('should redirect to orders list if user has no order awaiting payment', () => {
        visitPage({ no_awaiting_order: true })

        cy.url().should('eq', Cypress.config().baseUrl + '/mon-compte/mes-commandes')
        cy.get('[data-context=message-error]').should(
            'contain.text',
            `Vous n'avez actuellement aucune commande en attente de paiement.`
        )
    })

    it('should redirect to the additional payment process, with an error message', () => {
        visitPage()

        cy.mockPaymentV2Call('/internal-api/v1/tunnel/aborted', '[]')

        cy.url().should('include', `/mon-compte/ma-commande/${CUSTOMER_ORDER_ID}/ajouter-paiement`)
        cy.get('[data-context=failure-message]').should('be.visible')
        cy.get('[data-context=contact-us-error]').click()
        cy.get('[data-context=side-panel]').should('be.visible')

        cy.log('back navigator works has expected')
        cy.go('back')
        cy.get('[data-context=side-panel]').should('not.exist')
        cy.get('[data-context=failure-message]').should('be.visible')

        cy.log('direct closure of popin trigger an handled back')
        cy.get('[data-context=contact-us-error]').click()
        cy.get('[data-context=side-panel]').should('be.visible')
        cy.get('[data-context=side-panel] [data-context=close-btn]').click()
        cy.get('[data-context=side-panel]').should('not.exist')
        cy.get('[data-context=failure-message]').should('be.visible')
    })
})

import { createCustomerOrderInErp } from './helpers'
import { IDS } from '../../support/helpers'

describe('Confirmation page at the end of payment process', () => {
    const CUSTOMER_ORDER_ID = 9999999
    const visitPage = (options = {}) => {
        options = Object.assign(
            {
                no_awaiting_order: false,
            },
            options
        )
        cy.applySqlFixture('home/default.sql')

        if (!options.no_awaiting_order) {
            cy.applySqlFixture('checkout_process_v2/confirmation/geo_order_awaiting_payment.sql')
            createCustomerOrderInErp(IDS.geo.customer_id, CUSTOMER_ORDER_ID)
        }
        if (options.payment) {
            cy.applySqlFixture(`checkout_process_v2/confirmation/update_order_9999999_payment_${options.payment}.sql`)
        }
        if (options.has_newsletter) {
            cy.applySqlFixture(`checkout_process_v2/confirmation/geo_has_newsletter.sql`)
        }
        if (options.has_no_delay) {
            cy.applySqlFixture(`checkout_process_v2/confirmation/update_order_9999999_with_no_delay.sql`)
        }

        cy.login('geo', `/ma-commande/confirmation/succes`)
    }

    before(() => {
        cy.setupDatabase()
        cy.viewport('macbook-15')
    })

    beforeEach(() => {
        cy.reloadFixtures()
    })

    it('should redirect to orders list if user has no order awaiting payment', () => {
        visitPage({ no_awaiting_order: true })

        cy.url().should('eq', Cypress.config().baseUrl + '/mon-compte/mes-commandes')
        cy.get('[data-context=message-error]').should(
            'contain.text',
            `Vous n'avez actuellement aucune commande en attente de paiement.`
        )
    })

    it('should display the confirmation page', () => {
        visitPage()

        cy.log('=== check title and main message existence ===')
        cy.get('[data-context=title]')
            .should('contain.text', `Votre commande a bien été enregistrée.`)
            .should('be.visible')
        cy.get('[data-context=confirm-message-success]').should('be.visible')

        cy.log('=== check some data ===')
        cy.get('[data-context=data]')
            .should('be.visible')
            .as('data')
            .should('contain.text', `Commande #${CUSTOMER_ORDER_ID}`)
            .should('contain.text', `Payée par Carte bancaire`)
            .should('contain.text', `Livraison à domicile`)
            .should('contain.text', `Dès jeudi 22 février`)

        cy.log('=== check shipping address ===')
        cy.get('@data')
            .find('[data-context=address]')
            .should('contain.text', `Adresse de livraison`)
            .should('contain.text', `My Company`)
            .should('contain.text', `chrystel hannequin`)
            .should('contain.text', `32 rue de la tache`)
            .should('contain.text', `10210 chaource`)
            .should('contain.text', `FRANCE (MÉTROPOLITAINE)`)

        cy.log('=== check link to customer order ===')
        cy.get('@data')
            .find('[data-context=link-to-order]')
            .should('be.visible')
            .should('contain.text', `Suivre ma commande`)
            .should('have.attr', 'href', `/mon-compte/ma-commande/${CUSTOMER_ORDER_ID}`)

        cy.log('=== check order content ===')
        cy.get('[data-context=order-content]')
            .should('be.visible')
            .should('contain.text', `Votre commande`)
            .find('[data-context=product]')
            .should('have.length', 2)
            .as('products')
        cy.get('@products')
            .eq(0)
            .should('contain.text', '2x')
            .should('contain.text', 'Prestige 4i Calvados')
            .should('not.contain.text', 'Garantie PREMIUM 5 ans')
            .find('img')
            .attribute('src')
            .should('contain', '/images/dynamic/Baladeurs/articles/Fiio/FIIOX5IIGD/Fiio-X5-II-Gold_P_600.jpg?p=55')
        cy.get('@products')
            .eq(1)
            .should('contain.text', '1x')
            .should('contain.text', 'Gaïa EZ Noyer')
            .should('contain.text', 'Garantie PREMIUM 5 ans')
            .find('img')
            .attribute('src')
            .should(
                'contain',
                '/images/dynamic/Enceintes/articles/Triangle/TRIGAIAEZNY/Triangle-Gaia-EZ-Noyer_Vd3_600.jpg?p=55'
            )

        cy.log('=== check newsletter form ===')
        cy.log(`See desc. "Newsletter handling"`)

        cy.log('=== check guides ===')
        cy.get('[data-context=home-guides]')
            .should('be.visible')
            .should('contain.text', `Avec vous pour que la passion continue`)
            .as('guides-section')
        cy.get('@guides-section').find('[data-context=guide-highlights] > a').should('have.length', 3)
        cy.get('@guides-section').find('[data-context=guide-links] > a').should('have.length', 4)
    })

    it('should display the shipment method fallback message when having no delay', () => {
        visitPage({ has_no_delay: true })

        cy.get('[data-context=data]').should('contain.text', `Livraison sous 48/72h`)
    })

    describe('Newsletter handling', () => {
        it('should be possible to subscribe to newsletter', () => {
            visitPage()

            cy.log('=== check newsletter form ===')
            cy.get('[data-context=newsletter-form]')
                .should('be.visible')
                .should('contain.text', `S'abonner à la newsletter Son-vidéo.com`)
                .find('[data-context=subscribe-btn]')
                .should('be.visible')
                .as('subscribe-btn')

            cy.log('=== check newsletter subscription success ===')

            cy.mockRPCCall('bo-cms', 'customer:get_status_newsletter', '{"status": "inactive" }')
            cy.mockRPCCall('bo-cms', 'customer:subscribe_newsletter', '{"is_changed": true}')

            cy.get('@subscribe-btn').click()
            cy.get('[data-context=newsletter-form]').should('not.be.visible')
            cy.get('[data-context=newsletter-confirm]').should('be.visible')
        })

        // For some reason, this test fails on CI. Should be investigated.
        it.skip('should handle subscription error', () => {
            visitPage()

            cy.mockRPCCall('bo-cms', 'customer:get_status_newsletter', '{"status": "subscribed" }')
            cy.mockRPCCall('bo-cms', 'customer:subscribe_newsletter', '{"status": false, "message": "Unexpected error" }')
           
            cy.get('[data-context=newsletter-form] [data-context=subscribe-btn]').click()
            cy.get('[data-context=newsletter-form]').should('not.be.visible')
            cy.get('[data-context=newsletter-confirm]').should('not.be.visible')
            cy.get('[data-context=newsletter-error]').should('be.visible')
        })
        it('should hide the newsletter form if customer has already subscribed', () => {
            visitPage({ has_newsletter: true })

            cy.get('[data-context=newsletter-form]').should('not.exist')
        })
    })

    describe('Other kinds of payment', () => {
        const should_have_delayed_payment_message = () => {
            cy.log('=== check payment and shipping messages for delayed payment ===')
            cy.get('[data-context=data]')
                .should('be.visible')
                .as('data')
                .should('contain.text', `En attente de paiement`)
                .should('contain.text', `Expédition à réception du paiement`)
        }

        it('should handle VIR', () => {
            visitPage({ payment: 'VIR' })

            cy.log('=== check title ===')
            cy.get('[data-context=title]').should(
                'contain.text',
                `Votre commande est enregistrée et en attente de votre virement bancaire`
            )

            cy.log('=== check dedicated message ===')
            cy.get('[data-context=confirm-message-vir]')
                .should('be.visible')
                .as('vir')
                .htmlContains(`Nous vous remercions d’effectuer un virement de 1 203,90 €`)
                .htmlContains(`Montant : 1 203,90 €`)
            cy.get('@vir')
                .find('a[data-context=link-to-pdf]')
                .should('contain.text', `Télécharger le RIB en PDF`)
                .attribute('href')
                .should('contain', '/images/pdf/faq/rib-son-video-com.pdf')
            cy.get('@vir').find('[data-context=copy-btn]').should('be.visible')

            should_have_delayed_payment_message()
        })

        it('should handle COT', () => {
            visitPage({ payment: 'COT' })

            cy.log('=== check title ===')
            cy.get('[data-context=title]').should(
                'contain.text',
                `Votre demande de cotation transport a été enregistrée par nos services`
            )

            cy.log('=== check dedicated message ===')
            cy.get('[data-context=confirm-message-cot]')
                .should('be.visible')
                .htmlContains(`nous vous enverrons un devis personnalisé à l’adresse <EMAIL>`)

            should_have_delayed_payment_message()
        })

        it('should handle Gift Cards', () => {
            visitPage({ payment: 'BKDO' })

            cy.log('=== check title ===')
            cy.get('[data-context=title]').should(
                'contain.text',
                `Votre commande est enregistrée et en attente de réception de vos chèques cadeaux`
            )

            cy.log('=== check dedicated message ===')
            cy.get('[data-context=confirm-message-gift-card]')
                .should('be.visible')
                .htmlContains(`Merci de nous envoyer votre ou vos chèques cadeaux pour un montant de 1 203,90 €`)

            should_have_delayed_payment_message()
        })

        it('should handle TEL', () => {
            visitPage({ payment: 'TEL' })

            cy.log('=== check title ===')
            cy.get('[data-context=title]').should(
                'contain.text',
                `Votre commande est enregistrée et en attente de paiement`
            )

            cy.log('=== check dedicated message ===')
            cy.get('[data-context=confirm-message-tel]')
                .should('be.visible')
                .htmlContains(
                    `Un conseiller va vous appeler au plus vite afin de valider votre paiement par téléphone.`
                )
                .htmlContains(`Notre service client est disponible du lundi au samedi`)
                .htmlContains(`L’un de nos conseillers va vous contacter au 0626663927`)

            should_have_delayed_payment_message()
        })

        it('should handle SVD Gift Cards fully paid', () => {
            visitPage({ payment: 'SVDCC' })

            cy.log('=== check title ===')
            cy.get('[data-context=title]').should('contain.text', `Votre commande a bien été enregistrée.`)

            cy.log('=== check some data ===')
            cy.get('[data-context=data]')
                .should('be.visible')
                .should('contain.text', `Payée par Bons d'achats et cartes cadeaux`)
                .should('contain.text', `Dès jeudi 22 février`)
        })
    })
})

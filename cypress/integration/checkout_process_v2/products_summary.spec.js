describe(`Checkout Process - products summary`, () => {
    before(() => {
        cy.setupDatabase()
    })

    beforeEach(() => {
        cy.reloadFixtures()
        cy.applySqlFixture('checkout_process_v2/products_summary.sql')

        cy.intercept('GET', '/ma-commande/liste-paiements', {
            fixture: 'api/ma-commande/liste-paiements/all_type_of_payments.json',
        }).as('api-liste-paiements')

        cy.login('geo', '/commander')
    })

    it(`should display the destock's parent image if none is provided`, () => {
        cy.get(`[data-context=article-item]:contains(Cambridge La super enceinte destockée)`).within(() => {
            cy.get('img')
                .should('have.attr', 'src')
                .should('contain', '/images/dynamic/Baladeurs/articles/Fiio/FIIOX5IIGD/Fiio-X5-II-Gold_P_600.jpg')
        })
    })
})

import { checkNoTimeline, checkTimeline, TIMELINE_STEPS } from '../helpers'

describe('Sales funnel for quote of type offer — addresses handling', function () {
    before(function () {
        cy.setupDatabase()
        cy.viewport('macbook-15')
    })

    beforeEach(() => {
        // reset and seed the database prior to every test
        cy.reloadFixtures()
        cy.server()
        cy.intercept('**/api-adresse.data.gouv.fr/search/?q=*', {
            fixture: 'api/external/api-adresse.data.gouv.fr/response.json',
            statusCode: 200,
        }).as('government_address')
        cy.intercept('GET', '/ma-commande/liste-paiements', {
            fixture: 'api/ma-commande/liste-paiements/all_type_of_payments.json',
        }).as('api-liste-paiements')

        cy.intercept('PUT', '/**/ma-commande/mise-a-jour-panier').as('basket-order-update')
        cy.intercept('GET', '/**/ma-commande/liste-moyens-transport').as('api-liste-transport')

        cy.mockRPCCall(
            'bridge',
            'carrier:get_carriers_for_order',
            '[{"shipment_method_id": 1, "cost": 9.9},{"shipment_method_id": 31, "cost": 0}]'
        )
        cy.intercept('GET', '**/ma-commande/v2').as('api-order')
    })

    function type_address_name() {
        cy.get('input[name=address]:visible').clear().type('Rue Marechal Joffre 59153')
        cy.wait('@government_address')
        cy.get('[data-context=address-suggestion]').contains('Rue Marechal Joffre 59153 Grand-Fort-Philippe').click()
    }

    const CHECKOUT_PAGE = '/commander'

    const BILLING_ADDRESS_BEFORE = '38 rue de la ville en bois'
    const SHIPPING_ADDRESS_BEFORE = '12 place du cirque'

    /**
     * Test a TwInput present in the @book and return a chainable on its input field
     * @param name
     * @param label
     * @param default_value
     * @returns {Cypress.Chainable<Subject>}
     */
    const testInputInBook = (name, label, default_value) => {
        cy.get('@book').find(`[name=${name}]`).as('input')
        cy.get('@book').find('.input-label').should('contain', label)
        return cy.get('@input').should('have.value', default_value)
    }

    /**
     * Test a TwSelect present in the @book and return a chainable on its select field
     * @param name
     * @param label
     * @param selected_value
     * @returns {Cypress.Chainable<Subject>}
     */
    const testSelectInBook = (name, label, selected_value) => {
        cy.get('@book').find(`[name=${name}]`).as('select')
        cy.get('@book').find('.input-label').should('contain', label)

        return cy.get('@select').should('have.value', selected_value)
    }

    const testPhoneInBook = (name, label, default_value) => {
        cy.get('@book').find(`.vue-tel-input [name=${name}]`).as('phone')
        cy.get('@book').find('.input-label').should('contain', label)

        return cy.get('@phone').should('have.value', default_value)
    }

    const goToPaymentStep = () => {
        cy.get('[data-context=shipment-methods] [data-context=confirm-btn]').click()
        cy.wait('@api-liste-paiements')
        cy.wait('@basket-order-update')
    }

    /**
     * Tests on the creation workflow, done multiple times in different circumstances
     * @param has_another_address Indicates that the customer has another address in its book
     * @param replaced_addresses Array of addresses that must be replaced after save. Accepted values are "billing" or "shipping"
     * @param go_to_payment_step
     * @param is_same
     * @pamam go_to_payment_step Indicates that we want to go to payment step before checking billing
     */
    const testCreationWorkflow = ({ has_another_address, replaced_addresses, go_to_payment_step }, is_same = true) => {
        cy.get('[data-context=address-book]').should('be.visible').should('have.attr', 'data-mode', 'create').as('book')
        const new_address = {
            name: 'Ma base secrète',
            country_code: 'FR',
            title: 'Mr',
            firstname: 'Coco',
            lastname: 'L’asticot',
            company_name: 'Ma petite entreprise',
            address: 'Rue Maréchal Joffre',
            additional_address: 'Mon complément',
            cellphone: '0612345678',
            phone: '9876543210',
        }

        testInputInBook('name', `Nom de l’adresse`, has_another_address ? '' : 'Adresse 1')
            .clear()
            .type(new_address.name)
        testSelectInBook('country_code', `Pays`, 'FR').select(new_address.country_code)
        testSelectInBook('title', `Civilité`, 'Ms').select(new_address.title)
        testInputInBook('firstname', `Prénom`, 'Chuck').clear().type(new_address.firstname)
        testInputInBook('lastname', `Nom`, 'Norris').clear().type(new_address.lastname)
        cy.get('a').contains("Ajouter un nom d'entreprise").click()
        testInputInBook('company_name', `Entreprise`, '').type(new_address.company_name)
        type_address_name()
        cy.get('a').contains("Ajouter un complément d'adresse").click()
        testInputInBook('additional_address', `Complément d'adresse`, '', 'additional-address').type(
            new_address.additional_address
        )

        testPhoneInBook('cellphone', `N° mobile`, '').type(new_address.cellphone)
        cy.get('a').contains('Ajouter un numéro de téléphone').click()
        testPhoneInBook('phone', `N° téléphone`, '').type(new_address.phone)

        cy.intercept('POST', '**/mon-compte/mes-adresses/creer').as('create-address')
        // save new address
        cy.get('button:contains(Sauvegarder):visible').click()
        cy.wait('@create-address')

        // should be on summary with shipping and billing addresses selected
        cy.get('[data-context=address-book]').should('not.exist')
        if (replaced_addresses.includes('shipping')) {
            cy.get('[data-context=shipping-address]')
                .should('be.visible')
                .should('contain', 'Ma petite entreprise')
                .should('contain', 'M.')
                .should('contain', new_address.firstname)
                .should('contain', `L'asticot`) // instead of L’asticot, automatically converted
                .should('contain', 'Rue Marechal Joffre') // instead of Rue Maréchal Joffre
                .should('contain', `Mon complement`) // instead of "Mon complément"
                .should('contain', '59153')
                .should('contain', 'Grand-fort-philippe')
                .should('contain', 'France (métropolitaine)') // instead of Réunion!
            cy.get('[data-context=other-address]').should('exist')
        } else {
            cy.get('[data-context=shipping-address]').should('contain', SHIPPING_ADDRESS_BEFORE)
        }

        if (go_to_payment_step) {
            checkTimeline(TIMELINE_STEPS.delivery)
            goToPaymentStep()
        }

        if (replaced_addresses.includes('billing')) {
            if (is_same) {
                cy.get('[data-context=billing-address]')
                    .should('be.visible')
                    .should('contain', 'Adresse de facturation')
                    .should('contain', "Identique à l'adresse de livraison")
                    .find('[data-context=modify-btn]')
                    .should('be.visible')
            } else {
                cy.get('[data-context=billing-address]')
                    .should('be.visible')
                    .should('contain', 'Adresse de facturation')
                    .should('contain', 'M.')
                    .should('contain', new_address.firstname)
                    .should('contain', `L'asticot`)
                    .should('contain', 'Rue Marechal Joffre')
                    .should('contain', `Mon complement`)
                    .should('contain', '59153 Grand-fort-philippe')
                    .should('contain', 'France (métropolitaine)')
                    .find('[data-context=modify-btn]')
                    .should('be.visible')
            }
        } else {
            cy.get('[data-context=billing-address]').should('contain', BILLING_ADDRESS_BEFORE)
        }
    }

    describe('Header works', () => {
        beforeEach(function () {
            // Custom fixture only for current page
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-basket-order-without-addresses.sql')
        })

        it('Contact us in funnel', () => {
            cy.login('chuck', CHECKOUT_PAGE)
            cy.get('[data-context=contact-us]').should('contain', 'Contactez-nous').should('be.visible').click()
            cy.get('[data-context=side-panel] [data-context=side-panel-title]').should('contain', 'Contactez-nous')
            cy.get('[data-context=side-panel] button[data-context=action]').should('not.exist')
        })
        it('You should not leaved', () => {
            cy.login('chuck', CHECKOUT_PAGE)
            cy.get('img.icon').should('be.visible').click()
            cy.get('div.tooltip')
                .should('contain', 'Êtes-vous sûr(e) de vouloir retourner à votre panier ?')
                .should('be.visible')
            cy.get('[data-context=stay-in-funnel]').click()
            cy.get('div.tooltip').should('not.exist')
            cy.get('img.icon').should('be.visible').click()
            cy.get('[data-context=confirm]').should('contain.text', 'Modifier mon panier').click()
            cy.url().should('eq', Cypress.config().baseUrl + '/mon-panier')
        })
    })

    describe('No address in basket order', () => {
        beforeEach(function () {
            // Custom fixture only for current page
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-basket-order-without-addresses.sql')
        })

        describe('No address in customer addresses book', () => {
            beforeEach(function () {
                // Custom fixture only for current page
                cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-customer-without-addresses.sql')
            })

            it('should display the address creation form', () => {
                cy.login('chuck', CHECKOUT_PAGE)

                checkNoTimeline()

                testCreationWorkflow({
                    has_another_address: false,
                    go_to_payment_step: true,
                    replaced_addresses: ['shipping', 'billing'],
                })
            })
        })

        describe('There are adresses in customer book', () => {
            beforeEach(function () {
                // Custom fixture only for current page
                cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-customer-with-addresses.sql')
            })

            it('should automatically select the customer main address', () => {
                cy.login('chuck', CHECKOUT_PAGE)
                cy.wait('@api-order')

                checkTimeline(TIMELINE_STEPS.delivery)

                cy.get('[data-context=shipping-address]')
                    .should('be.visible')
                    .should('contain', 'Mme')
                    .should('contain', 'Stephanie')
                    .should('contain', `tratatat`)
                    .should('contain', '2 rue de la bastide, appartement 15')
                    .should('contain', 'Toulouse')
                    .should('contain', '31000')
                    .should('contain', 'France (métropolitaine)')

                // go check billing on payment step
                cy.get('[data-context=shipment-methods] [data-context=confirm-btn]').click()
                cy.wait('@api-liste-paiements')

                // billing address
                cy.get('[data-context=billing-address]')
                    .should('be.visible')
                    .should('contain', "Identique à l'adresse de livraison")
                    .find('[data-context=modify-btn]')
                    .should('be.visible')
            })

            it('should force to modify invalid address', () => {
                cy.login('geo', '/ma-commande/creation')
                cy.wait('@api-order')

                cy.get('[data-context=address-book]').should('be.visible').should('have.attr', 'data-mode', 'list')

                cy.log('=== Check invalid address content ===')
                cy.get('[data-context=customer-address]').eq(0).as('invalid-address')
                cy.get('@invalid-address')
                    .find('[data-context=svd-message-error]')
                    .should('be.visible')
                    .should('contain', 'Cette adresse est invalide')
                cy.get('@invalid-address')
                    .find('[data-context=select-btn]')
                    .find('[data-context=svd-button]')
                    .should('be.disabled')

                cy.log('=== Modify invalid address ===')
                const address = `5 bis rue du Puits`
                cy.get('@invalid-address').find('[data-context=modify-btn]').click()
                cy.get('[data-context=address-book]').should('have.attr', 'data-mode', 'update').as('book')
                cy.get('@book').find('input[name=address]').should('have.class', 'error').type(address)
                cy.get('@book').find('form').submit()

                cy.log('=== Addresses should be automatically set on customer order ===')
                cy.get('[data-context=shipping-address]').should('contain', address)
                cy.get('[data-context=confirm-btn]').click()
                cy.get('[data-context=billing-address]').should('contain', `Identique à l'adresse de livraison`)
            })
        })
    })

    describe('The offer has shipping and billing addresses set', () => {
        beforeEach(function () {
            // Custom fixture only for current page
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-basket-order-with-addresses.sql')
        })

        describe('No address in customer addresses book', () => {
            beforeEach(function () {
                // Custom fixture only for current page
                cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-customer-without-addresses.sql')
            })

            it('can create another address for shipping', () => {
                cy.intercept('GET', '**/mon-compte/mes-adresses').as('mes-adresses')
                cy.login('chuck', CHECKOUT_PAGE)

                checkTimeline(TIMELINE_STEPS.delivery)

                // modify button should redirect to the creation workflow
                cy.get('[data-context=shipping-address]').should('be.visible')
                cy.get('[data-context=other-address]').should('exist').click()
                cy.wait('@mes-adresses')
                testCreationWorkflow({
                    has_another_address: false,
                    go_to_payment_step: true,
                    replaced_addresses: ['shipping'],
                })
            })
        })

        describe('Customer has addresses and can manage them', () => {
            beforeEach(function () {
                // Custom fixture only for current page
                cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-customer-with-addresses.sql')

                cy.intercept('GET', '**/mon-compte/mes-adresses').as('mes-adresses')
                cy.login('chuck', CHECKOUT_PAGE)
                cy.wait('@api-order')
                cy.wait('@api-liste-transport')
            })

            it('can select another billing address', () => {
                goToPaymentStep()

                cy.log('=== go to customer address book on modify')
                cy.get('[data-context=billing-address]').should('be.visible').find('[data-context=modify-btn]').click()
                cy.wait('@mes-adresses')
                cy.get('[data-context=address-book]')
                    .should('be.visible')
                    .should('have.attr', 'data-mode', 'list')
                    .as('book')

                cy.log('=== cancellation should not modify addresses')
                cy.get('@book').find('button:contains(Annuler):visible').click()
                cy.get('[data-context=billing-address]').should('contain', BILLING_ADDRESS_BEFORE)
                cy.get('[data-context=shipping-address]').should('contain', SHIPPING_ADDRESS_BEFORE)

                cy.log('=== go to customer address book on modify')
                cy.get('[data-context=billing-address]').should('be.visible').find('[data-context=modify-btn]').click()
                cy.wait('@mes-adresses')
                cy.get('[data-context=address-book]')
                    .should('be.visible')
                    .should('have.attr', 'data-mode', 'list')
                    .as('book')

                cy.log('=== do some check on address list element')
                cy.get('@book')
                    .find('[data-context=customer-address]')
                    .should('have.length', 2)
                    .eq(0)
                    .as('address')
                    .should('contain', 'Domicile')
                    .should('contain', 'Mme.')
                    .should('contain', 'Stephanie')
                    .should('contain', 'tratatat')
                    .should('contain', '2 rue de la bastide, appartement 15')
                    .should('contain', '31000 Toulouse')
                    .should('contain', 'France (métropolitaine)')

                cy.log('=== select the first address in the book')
                cy.get('@address').find('[data-context=select-btn]').click()
                cy.wait('@mes-adresses')

                cy.log('=== billing has been updated, shipping is unchanged')
                cy.wait('@api-order')
                cy.get('[data-context=billing-address]').should('contain', '2 rue de la bastide, appartement 15')
                cy.get('[data-context=shipping-address]').should('contain', SHIPPING_ADDRESS_BEFORE)

                cy.log('=== the payment list is reloaded')
                cy.wait('@api-liste-paiements')
            })

            it('can select another shipping address', () => {
                cy.get('[data-context=shipping-address]').should('be.visible')
                cy.get('[data-context=other-address]').should('exist').click()
                cy.wait('@mes-adresses')
                cy.get('[data-context=address-book]')
                    .should('be.visible')
                    .should('have.attr', 'data-mode', 'list')
                    .as('book')

                cy.get('@book')
                    .find('[data-context=customer-address]')
                    .should('have.length', 2)
                    .eq(1)
                    .should('contain', 'avenue prince charles 17d')
                    .find('[data-context=select-btn]')
                    .click()
                cy.wait('@mes-adresses')
                cy.wait('@basket-order-update')
                cy.wait('@api-order')

                //Shipping has been updated, billing is unchanged
                cy.get('[data-context=shipping-address]').should('contain', 'avenue prince charles 17d')
                goToPaymentStep()
                cy.get('[data-context=billing-address]').should('contain', BILLING_ADDRESS_BEFORE)
            })

            it('can create another address and instantly use it', () => {
                goToPaymentStep()

                // go to customer address book on modify
                cy.get('[data-context=billing-address]').should('be.visible').find('[data-context=modify-btn]').click()
                cy.wait('@mes-adresses')
                cy.get('[data-context=address-book]')
                    .should('be.visible')
                    .should('have.attr', 'data-mode', 'list')
                    .as('book')

                // click on new address creation should display the creation form
                cy.get('@book').find('[data-context=new-address-btn]').click()
                cy.get('[data-context=address-book][data-mode=create]').should('be.visible')

                // cancellation should be possible (has one or more other address)
                cy.get('button:contains(Annuler):visible').click()
                cy.get('[data-context=address-book][data-mode=list]').should('be.visible')

                // click on new address creation should display the creation form
                cy.get('@book').find('[data-context=new-address-btn]').click()
                cy.get('[data-context=address-book][data-mode=create]').should('be.visible')

                testCreationWorkflow(
                    {
                        has_another_address: true,
                        replaced_addresses: ['billing'],
                    },
                    false
                )
            })

            it('can modify an address and instantly use it', () => {
                goToPaymentStep()

                // go to customer address book on modify
                cy.get('[data-context=billing-address]').should('be.visible').find('[data-context=modify-btn]').click()
                cy.wait('@mes-adresses')
                cy.get('[data-context=address-book]')
                    .should('be.visible')
                    .should('have.attr', 'data-mode', 'list')
                    .as('book')

                // click on the first address modification button should display the modification form
                cy.get('@book').find('[data-context=customer-address] [data-context=modify-btn]').eq(0).click()
                cy.get('[data-context=address-book][data-mode=update]').should('be.visible')

                // cancellation should be possible
                cy.get('button:contains(Annuler):visible').click()
                cy.get('[data-context=address-book][data-mode=list]').should('be.visible')

                // click on the first address modification button should display the modification form
                cy.get('@book').find('[data-context=customer-address] [data-context=modify-btn]').eq(0).click()
                cy.get('[data-context=address-book][data-mode=update]').should('be.visible')

                // check modification form
                testSelectInBook('country_code', `Pays`, 'FR').select('BE')
                testSelectInBook('title', `Civilité`, 'Ms').select('Mr')
                testInputInBook('firstname', `Prénom`, 'Stephanie').clear().type('Georges')
                testInputInBook('lastname', `Nom`, 'tratatat').clear().type('Abitbol')
                cy.get('a').contains("Ajouter un nom d'entreprise").click()
                testInputInBook('company_name', `Entreprise`, '').type('Evil Corp')
                // note: trimming is a bit strong, the first space is not inputted, no problem after that
                testInputInBook('address', `Adresse`, '2 rue de la bastide').type('  folle')
                cy.get('a').contains("Ajouter un complément d'adresse").should('not.exist')
                testInputInBook('additional_address', `Complément d'adresse`, 'appartement 15', 'additional-address')
                    .clear()
                    .type('appt 666')

                testInputInBook('postal_code', `Code postal`, '31000')
                testInputInBook('city', `Ville`, 'TOULOUSE')
                testPhoneInBook('cellphone', `N° mobile`, '+33 6 07 85 05 23')

                // save updated address
                cy.get('button:contains(Sauvegarder):visible').click()
                cy.wait('@mes-adresses')

                // should be on summary with billing address updated and shipping unchanged
                cy.get('[data-context=address-book]').should('not.exist')
                cy.get('[data-context=billing-address]')
                    .should('be.visible')
                    .should('contain', 'M.')
                    .should('contain', 'Georges')
                    .should('contain', `Abitbol`)
                    .should('contain', 'Evil Corp')
                    .should('contain', `2 rue de la bastide folle, appt 666`)
                    .should('contain', '31000 Toulouse')
                    .should('contain', 'Belgique')
                cy.get('[data-context=shipping-address]').should('contain', SHIPPING_ADDRESS_BEFORE)
            })

            it('can set an address as default and instantly use it', () => {
                goToPaymentStep()

                // go to customer address book on modify
                cy.get('[data-context=billing-address]').should('be.visible').find('[data-context=modify-btn]').click()
                cy.wait('@mes-adresses')
                cy.get('[data-context=address-book]').as('book')
                cy.get('@book').should('be.visible').should('have.attr', 'data-mode', 'list')

                // set the second address as default (= first "set as default" button in the list),
                // should set the billing address and leave shipping unchanged
                cy.get('@book').find('[data-context=customer-address] [data-context=set-default-btn]').eq(0).click()
                cy.wait('@mes-adresses')
                cy.get('[data-context=address-book]').should('not.exist')
                cy.get('[data-context=billing-address]')
                    .should('not.contain', BILLING_ADDRESS_BEFORE)
                    .should('contain', 'avenue prince charles 17d')
                cy.get('[data-context=shipping-address]').should('contain', SHIPPING_ADDRESS_BEFORE)

                // verify that the address in now first in list
                cy.get('[data-context=billing-address] [data-context=modify-btn]').click()
                cy.wait('@mes-adresses')
                cy.get('[data-context=address-book][data-mode=list]').should('be.visible')
                cy.get('[data-context=customer-address]')
                    .should('have.length', 2)
                    .eq(0)
                    .should('contain', 'avenue prince charles 17d')
            })
            before(() => {
                cy.viewport(390, 760)
            })
            it('Can reduce header and summary on mobile', () => {
                cy.then(() => {
                    cy.wrap(Cypress.config('viewportWidth')).should('eq', 390) // passes
                    cy.wrap(Cypress.config('viewportHeight')).should('eq', 760) // passes
                })
                goToPaymentStep()

                cy.get('[data-context=sales-funnel]').should('be.visible')

                // article details should be hidden by default
                cy.get('[data-context=summary-details]').should('have.css', 'opacity', '0')

                // can display the summary details
                cy.get('[data-context=summary-header]:contains(Panier)').click({ force: true })
                cy.get('[data-context=summary-details]').should('have.css', 'opacity', '1')

                // summary delivery should be hidden by default
                cy.get('[data-context=delivery-summary]').should('not.be.visible')

                // can display the summary details
                cy.get('[data-context=summary-header]:contains(Livraison)').click({ force: true })
                cy.get('[data-context=delivery-summary]').should('be.visible')

                // can collapse the article details
                cy.get('[data-context=summary-header]:contains(Panier)').click({ force: true })
                cy.get('[data-context=article-details]').should('not.be.visible')

                // can collapse the summary delivery
                cy.get('[data-context=summary-header]:contains(Livraison)').click({ force: true })
                cy.get('[data-context=delivery-summary').should('not.be.visible')
            })
        })
    })

    describe('Handle api errors', () => {
        it('handle failure when retrieving the basket order', () => {
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-basket-order-without-addresses.sql')
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-customer-with-addresses.sql')
            cy.intercept('GET', '**/ma-commande/v2', { statusCode: 500, body: {} }).as('api_basket_order_ko')
            cy.login('chuck', CHECKOUT_PAGE)
            cy.wait('@api_basket_order_ko')
            cy.get('[data-context=data-failure-error-message]').should('be.visible')
        })

        it('handle failure when loading customer address book while no shipping and billing address', () => {
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-basket-order-without-addresses.sql')
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-customer-with-addresses.sql')
            cy.intercept('GET', '**/mon-compte/mes-adresses', { statusCode: 500, body: {} }).as(
                'api_customer_addresses_ko'
            )
            cy.login('chuck', CHECKOUT_PAGE)
            cy.wait('@api_customer_addresses_ko')
            cy.get('[data-context=data-failure-error-message]').should('be.visible')
        })

        it('handle failure when loading customer address book for modification', () => {
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-basket-order-with-addresses.sql')
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-customer-with-addresses.sql')
            cy.login('chuck', CHECKOUT_PAGE)

            cy.route({ method: 'GET', url: '**/mon-compte/mes-adresses', status: 500, response: {} }).as(
                'api_customer_addresses_ko'
            )
            cy.get('[data-context=other-address]').should('exist').click()

            cy.wait('@api_customer_addresses_ko')
            cy.get('[data-context=address-book] [data-context=init-error]').should('be.visible').as('error')

            cy.get('@error')
                .find('[data-context=svd-message-error]')
                .should('be.visible')
                .should('contain', `Une erreur s’est produite lors du chargement des données. Veuillez réessayer.`)

            cy.log('=== can return to the checkout process ===')
            cy.get('@error').find('[data-context=cancel-error-btn]').click()
            cy.get('[data-context=address-book]').should('not.exist')
            cy.get('[data-context=shipping-address]').should('be.visible')

            // restore the mocked route
            // and mock the customer info
            cy.route({ method: 'GET', url: '**/mon-compte/mes-adresses' }).as('mes-adresses')
            cy.route({ method: 'GET', url: '**/api/customer-info', status: 500, response: {} }).as(
                'api_customer_info_ko'
            )

            cy.log('=== this error should also be handled ===')
            cy.get('[data-context=other-address]').should('exist').click()
            cy.wait('@mes-adresses')
            cy.wait('@api_customer_info_ko')
            cy.get('[data-context=address-book] [data-context=init-error]').should('be.visible')
        })

        it('handle failure when address is saved (creation or modification)', () => {
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-basket-order-with-addresses.sql')
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-customer-with-addresses.sql')
            cy.login('chuck', CHECKOUT_PAGE)
            cy.intercept('GET', '**/mon-compte/mes-adresses').as('mes-adresses')

            cy.get('[data-context=other-address]').should('exist').click()
            cy.wait('@mes-adresses')

            // creation failure
            cy.get('[data-context=address-book] [data-context=new-address-btn]').click()
            cy.intercept('POST', '**/mon-compte/mes-adresses/creer', { statusCode: 500, body: {} }).as(
                'api_create_address_ko'
            )
            cy.get('button:contains(Sauvegarder):visible').click()
            cy.wait('@api_create_address_ko')
            cy.get('[data-context=address-book][data-mode=create] [data-context=svd-message-error]')
                .should('be.visible')
                .should('have.length', 1)
                .should('contain', `Une erreur s’est produite lors de la sauvegarde. Veuillez réessayer.`)

            cy.get('button:contains(Annuler):visible').click()

            // modification failure
            cy.get('[data-context=address-book] [data-context=customer-address]')
                .eq(0)
                .find('[data-context=modify-btn]')
                .click()
            cy.intercept('POST', '**/mon-compte/mes-adresses/mettre-a-jour/0', { statusCode: 500, body: {} }).as(
                'api_modify_address_ko'
            )
            cy.get('button:contains(Sauvegarder):visible').click()
            cy.wait('@api_modify_address_ko')
            cy.get('[data-context=address-book][data-mode=update] [data-context=svd-message-error]')
                .should('be.visible')
                .should('have.length', 1)
                .should('contain', `Une erreur s’est produite lors de la sauvegarde. Veuillez réessayer.`)
        })

        it('handle failure when setting an address as default', () => {
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-basket-order-with-addresses.sql')
            cy.applySqlFixture('checkout_process_v2/quote_type_offer/1-customer-with-addresses.sql')
            cy.login('chuck', CHECKOUT_PAGE)

            cy.intercept('GET', '**/mon-compte/mes-adresses').as('mes-adresses')
            cy.get('[data-context=other-address]').should('exist').click()
            cy.wait('@mes-adresses')

            cy.intercept('PUT', '**/mon-compte/mes-adresses/principale/1', { statusCode: 500, body: {} }).as(
                'api_set_default_ko'
            )

            cy.get('[data-context=address-book][data-mode=list] [data-context=customer-address]')
                .eq(1)
                .as('address')
                .find('[data-context=set-default-btn]')
                .click()
            cy.wait('@api_set_default_ko')
            cy.get('@address')
                .find('[data-context=svd-message-error]')
                .should('be.visible')
                .should('contain', `L’utilisation de l’adresse en tant que défaut a échoué.`)
        })
    })
})

Cypress.on('viewport:changed', (newValue) => {
    Cypress.config('viewportWidth', newValue.viewportWidth) // this works
    Cypress.config('viewportHeight', newValue.viewportHeight)
})

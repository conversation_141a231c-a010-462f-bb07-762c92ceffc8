describe('Sales funnel for quote of type offer — convert and display cart content', () => {
    before(() => {
        cy.setupDatabase()
        cy.viewport('macbook-15')
    })

    beforeEach(() => {
        // reset and seed the database prior to every test
        cy.reloadFixtures()

        cy.applySqlFixture('checkout_process_v2/cart_with_many_things.sql')
    })

    const CART_PAGE = '/mon-panier'

    it('can display all kind of mixed content (articles, accessories, offer) with accessories at the end', () => {
        cy.login('chuck', CART_PAGE)

        // select the magazine, then go to the checkout
        cy.get('[data-context=basket-magazine] [data-context=toggle]').click()
        cy.get('[data-context=go-to-tunnel]').click()

        // summary checks
        cy.get('[data-context=summary]')
            .should('be.visible')
            .find('[data-context=article-item]')
            .should('have.length', 5)
            .as('lines')

        cy.get('@lines')
            .eq(0)
            .as('line')
            .should('contain', 'Cambridge super enceinte')
            .find('[data-context=quantity]')
            .should('contain', 5)
        cy.get('@line')
            .find('img')
            .should('have.attr', 'src')
            .and('contain', '/images/article/studio-lab/STUDLABSLB102ANY/slb-102a-noyer_61fbfccc6a8b2_600.jpg?p=55')

        cy.get('@lines')
            .eq(1)
            .as('line')
            .should('contain', 'Elipson super ampli')
            .find('[data-context=quantity]')
            .should('contain', 3)
        cy.get('@line').find('[data-context=warranties]').should('contain', 'Garantie vol & casse 1 an')
        cy.get('@line')
            .find('img')
            .should('have.attr', 'src')
            .and(
                'match',
                new RegExp(
                    '/images/dynamic/Accessoires/articles/Stabren/STABRIND352535/Stabren-Industrie-35-25-35-la-piece-_P_140.jpg'
                )
            )

        cy.get('@lines')
            .eq(2)
            .as('line')
            .should('contain', 'KEF Q150 Blanc (la paire)')
            .find('[data-context=quantity]')
            .should('contain', 6)
        cy.get('@line')
            .find('img')
            .should('have.attr', 'src')
            .and(
                'match',
                new RegExp('/images/dynamic/Enceintes/articles/KEF/KEFQ150BC/KEF-Q150-Blanc_P_300_square.jpg')
            )

        cy.get('@lines')
            .eq(3)
            .as('line')
            .should('contain', 'Sennheiser Embouts noir, 4x s / 4x m / 4xl')
            .find('[data-context=quantity]')
            .should('contain', 4)
        cy.get('@line')
            .find('img')
            .should('have.attr', 'src')
            .and('match', new RegExp('/images/ui/uiV3/graphics/no-img-300.png'))

        cy.get('@lines')
            .eq(4)
            .as('line')
            .should('contain', 'Description du magazine SV #1')
            .find('[data-context=quantity]')
            .should('contain', 1)
        cy.get('@line')
            .find('img')
            .should('have.attr', 'src')
            .and(
                'match',
                new RegExp('/images/illustration/pages/magazine/SVDZINE_202103_MagazinePrintemps_Article.jpg')
            )

        // price check
        cy.get('[data-context=summary] [data-context=total-price]').should('contain.text', 'TOTAL 3 483,62 €')
    })
})

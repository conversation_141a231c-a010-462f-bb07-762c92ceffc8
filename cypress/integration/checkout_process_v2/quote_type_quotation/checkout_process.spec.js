import { checkNoTimeline } from '../helpers'
import { format } from 'date-fns'

describe('Sales funnel - Recap for quote of type quotation', function () {
    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        // reset and seed the database prior to every test
        cy.reloadFixtures()

        // Custom fixture only for current page
        cy.applySqlFixture('checkout_process_v2/quote_type_quotation/1-recap.sql')

        cy.intercept('GET', '/ma-commande/liste-paiements', {
            fixture: 'api/ma-commande/liste-paiements/all_type_of_payments.json',
        }).as('api-liste-paiements')
    })

    describe('Behaviours on page access', () => {
        const FUNNEL_PAGE = '/commander'
        const CUSTOMER_ORDER_PAGE = '/mon-compte/mes-commandes'

        it('should redirect to login page if not already connected', function () {
            cy.visit(FUNNEL_PAGE)
            cy.url().should('eq', Cypress.config().baseUrl + '/mon-compte/connexion')
        })

        it('should redirect to customer order page if trying to access the funnel directly', function () {
            cy.login('geo', FUNNEL_PAGE)

            cy.get('[data-context=sales-funnel-skeleton]').should('not.exist')
            cy.url().should('eq', Cypress.config().baseUrl + CUSTOMER_ORDER_PAGE)
            cy.get('[data-context=message-error]').should('contain', 'Une erreur est survenue, veuillez réessayer.')
        })
    })

    describe('Behaviours with different screen sizes', function () {
        const visitPage = (quote_id = 1000272) => {
            cy.login('geo', `/mon-compte/payer-mon-devis/${quote_id}`)
        }

        const testContentOnAllScreenSizes = () => {
            cy.log('=== Test for all screen')

            // the dynamic date is set in sql fixtures hence why we can't use cy.clock to freeze the date
            // today + 15 days
            const expiration_date = new Date(new Date().getTime() + 15 * 24 * 60 * 60 * 1000)

            cy.log('=== Check quote information')
            cy.get('[data-context=checkout-title')
                .should('be.visible')
                .should('contain', 'Devis n°1000272')
                .should('contain', `Valable jusqu'au ${format(expiration_date, 'DD/MM/YYYY')}`)

            cy.log('=== check shipping address ===')
            cy.get('[data-context=delivery-summary]')
                .should('be.visible')
                .should('contain', 'Mme.')
                .should('contain', 'Cléa')
                .should('contain', 'Molette')
                .should('contain', 'Ma petite compagnie')
                .should('contain', '3 bis de la rue perdue')
                .should('contain', '27600')
                .should('contain', 'Saint Pierre la Garenne')
                .should('contain', 'France (métropolitaine)')

            cy.log('=== check billing address ===')
            cy.get('[data-context=billing-summary]')
                .should('be.visible')
                .should('contain', 'M.')
                .should('contain', 'Alex')
                .should('contain', 'Terrieur')
                .should('contain', 'My Big company')
                .should('contain', 'je ne sais pas trop où')
                .should('contain', '1234')
                .should('contain', 'Skopje')
                .should('contain', 'Macédoine (ancienne république yougoslave de)')
                .find('[data-context=modify-btn]')
                .should('not.exist')

            cy.log('=== check shipment method ===')
            cy.get('[data-context=shipment-method]')
                .as('shipment-method')
                .should('be.visible')
                .should('contain', 'Livraison standard')
                .should('contain', 'Livraison à domicile sous 2 à 3 jours ouvrés, pour les produits en stock.')
            cy.get('@shipment-method').find('[data-context=change-shipment]').should('not.exist')

            cy.log('=== check summary header')
            cy.get('[data-context=summary-header]:contains(Panier)').should('exist')

            cy.log('=== check summary content')
            cy.get('[data-context=summary]').should('be.visible').as('summary')
            cy.get('@summary').find('[data-context=article-item]').should('have.length', 2).as('articles')
            cy.get('@summary').find('[data-context=articles-price]').should('contain.text', 'Sous-total 12 955,17 €')
            cy.get('@summary').find('[data-context=articles-discount]').should('contain.text', 'Remise -128,07 €')
            cy.get('@summary').find('[data-context=shipment-cost]').should('contain.text', 'Livraison 5,90 €')
            cy.get('@summary').find('[data-context=total-price]').should('contain.text', 'TOTAL 12 833,00 €')
            cy.get('@summary').find('[data-context=modify-basket-btn]').should('not.exist')

            cy.log('=== first article')
            cy.get('@articles').eq(0).find('[data-context=quantity]').should('contain.text', '8×')
            cy.get('@articles')
                .eq(0)
                .find('[data-context=description]')
                .should('contain.text', 'Focal IFP 207 (la paire)')
            cy.get('@articles')
                .eq(0)
                .find('[data-context=stock]')
                .should('not.have.class', 'available')
                .should('contain.text', 'Sur commande')
            cy.get('@articles')
                .eq(0)
                .find('img')
                .attribute('src')
                .should(
                    'contain',
                    '/images/dynamic/Hi_fi_embarquee/articles/Focal/FOCAIFP207/Focal-Kit-specifique-Peugeot-207-et-307_P_300_square.jpg'
                )

            cy.log('=== second article')
            cy.get('@articles')
                .eq(1)
                .find('[data-context=stock]')
                .should('have.class', 'available')
                .should('contain.text', 'En stock')
        }

        describe('On wide screen', function () {
            beforeEach(function () {
                cy.viewport('macbook-15')
            })

            it('should display basket order content (mac15)', function () {
                visitPage()

                cy.wait('@api-liste-paiements')
                checkNoTimeline()

                cy.log('=== article details should be always displayed')
                cy.get('[data-context=article-details]').should('be.visible')

                cy.log('=== shipment details should be always displayed')
                cy.get('[data-context=summary-header]:contains(Livraison)')
                    .should('be.visible')
                    .find('[data-context=toggle-icon]')
                    .should('not.exist')
                cy.get('[data-context=summary-header]:contains(Adresse de facturation)')
                    .should('be.visible')
                    .find('[data-context=toggle-icon]')
                    .should('not.exist')

                testContentOnAllScreenSizes()
            })

            it('should have adapted labels when delivery fee is free and delivered in a store', function () {
                visitPage(1000273)

                cy.get('[data-context=sales-funnel]').should('be.visible')

                // shipping address to a store
                cy.get('[data-context=shipment-method]')
                    .should('be.visible')
                    .should('not.contain', 'Livraison')
                    .should('contain', 'Retrait en magasin')

                // shipment price = free
                // no-discount
                // summary content
                cy.get('[data-context=summary]').should('be.visible').as('summary')
                cy.get('@summary').find('[data-context=article-item]').should('have.length', 2).as('articles')
                cy.get('@summary')
                    .find('[data-context=articles-price]')
                    .should('contain.text', 'Sous-total 12 833,00 €')
                cy.get('@summary').find('[data-context=articles-discount]').should('not.exist')
                cy.get('@summary').find('[data-context=shipment-cost]').should('contain.text', 'Livraison Offerte')
                cy.get('@summary').find('[data-context=total-price]').should('contain.text', 'TOTAL 12 833,00 €')
            })

            it('should display quote with warranties', function () {
                visitPage(1000274)

                cy.get('[data-context=sales-funnel]').should('be.visible')

                // articles
                cy.get('[data-context=summary]')
                    .find('[data-context=article-item]')
                    .should('have.length', 2)
                    .as('articles')

                // first article with warranties
                cy.get('@articles').eq(0).as('article')
                cy.get('@article').find('[data-context=warranties] div').should('have.length', 2).as('warranties')
                cy.get('@warranties').eq(0).should('contain.text', 'Extension de garantie 5 ans')
                cy.get('@warranties').eq(1).should('contain.text', 'Garantie vol-casse 2 ans')

                // second article without warranties
                cy.get('@articles').eq(1).as('second_article')
                cy.get('@second_article').find('[data-context=warranties]').should('not.exist')
            })
        })

        describe('On Samsung S10', function () {
            beforeEach(function () {
                cy.viewport('samsung-s10')
            })

            it('should display basket order content (S10)', function () {
                visitPage()

                cy.get('[data-context=sales-funnel]').should('be.visible')

                // article details should be displayed by default
                cy.get('[data-context=article-details]').should('be.visible')

                // can hide the summary details
                cy.get('[data-context=summary-header]:contains(Panier)').click()
                cy.get('[data-context=article-details]').should('not.be.visible')

                // delivery summary should always be visible
                cy.get('[data-context=delivery-summary]').should('be.visible')
                cy.get('[data-context=summary-header]:contains(Livraison):visible').click()
                cy.wait(200)
                cy.get('[data-context=delivery-summary]').should('be.visible')

                // billing summary should always be visible
                cy.get('[data-context=billing-summary]').should('be.visible')
                cy.get('[data-context=summary-header]:contains(Adresse de facturation):visible').click()
                cy.wait(200)
                cy.get('[data-context=billing-summary]').should('be.visible')

                testContentOnAllScreenSizes()
            })
        })
    })
})

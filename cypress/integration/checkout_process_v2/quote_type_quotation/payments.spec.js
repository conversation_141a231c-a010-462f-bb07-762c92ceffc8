import { createCustomerOrderInErp } from '../helpers'

describe('Sales funnel - Payment for quote of type quotation', function () {
    const visitPage = (quote_id = 1000272) => {
        cy.login('geo', `/mon-compte/payer-mon-devis/${quote_id}`)
    }

    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        // reset and seed the database prior to every test
        cy.reloadFixtures()

        // Custom fixture only for current page
        cy.applySqlFixture('checkout_process_v2/quote_type_quotation/2-payments.sql')

        cy.clearCookies()
        cy.server()
    })

    describe('Behaviour when the payment list load successfully', () => {
        it('should show all payment methods', function () {
            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    {
                        payment_method_id: 22,
                        name: 'Bons d&apos;achats et cartes cadeaux',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 32,
                        name: 'CB VISA / MASTERCARD / E-CARTE BLEUE',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 33,
                        name: 'American Express',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 37,
                        name: 'Paiement Paypal',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 50,
                        name: 'Paiement en 3 ou 4 fois sans frais',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 51,
                        name: 'Paiement en 3 ou 4 fois sans frais',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 52,
                        name: 'Chèques cadeaux',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 10,
                        name: 'Virement et autres moyens de paiement',
                        total_cost: 0,
                    },
                ])
            )

            visitPage()

            cy.get('[data-context=sales-funnel]')
                .should('be.visible')
                .find('[data-context=payment-methods]')
                .should('be.visible')
                .as('payment_methods')

            cy.get('@payment_methods').find('[data-context=payment-mean]').should('have.length', 2).as('payment_means')

            cy.log('--- first payment is selected by default ---')
            cy.get('@payment_means').eq(0).as('first_payment')
            cy.get('@first_payment').find('[data-context=label]').should('contain', 'Carte bancaire')
            cy.get('@first_payment').find('[data-context=payment-method-radio-32]').find('input').should('be.checked')
            cy.get('[data-context=payment-method-content]').should('be.visible').as('info')
            cy.get('@info')
                .find('[data-context=amount]')
                .should('contain.text', 'Montant')
                .find('span')
                .should('contain.text', '12 833,00 €')
            cy.get('@info')
                .find('[data-context=cgv]')
                .htmlContains(
                    'En cliquant sur « confirmer et payer » vous acceptez sans réserve les conditions générales de vente . Une commande sera créée avec obligation de paiement.'
                )
            cy.get('@info').find('[data-context=pay-btn]').should('exist')

            cy.log('--- other two payment groups are collapsed by default ---')
            cy.get('@payment_methods').find('[data-context=method-group-title]').should('have.length', 4)
            cy.get('@payment_methods').find('[data-context=cb-payment-expanded]').should('have.length', 2)
            cy.get('@payment_methods').find('[data-context=multiple-payment-expanded]').should('have.length', 0)
            cy.get('@payment_methods').find('[data-context=other-payment-expanded]').should('have.length', 0)

            cy.log('--- we can now see multiple payment ---')
            cy.get('@payment_methods')
                .find('[data-context=method-group-title]')
                .contains('Paiement en plusieurs fois')
                .click()
            cy.get('@payment_methods').find('[data-context=cb-payment-expanded]').should('have.length', 0)
            cy.get('@payment_methods').find('[data-context=multiple-payment-expanded]').should('have.length', 2)
            cy.get('@payment_methods').find('[data-context=other-payment-expanded]').should('have.length', 0)
            cy.get('@payment_methods').find('[data-context=schedule-calendar-0]').should('exist')
            cy.get('@payment_methods').find('[data-context=schedule-calendar-1]').should('exist')
            cy.get('@payment_methods').find('[data-context=schedule-calendar-2]').should('exist')

            cy.log('--- we can now see other payment ---')
            cy.get('@payment_methods')
                .find('[data-context=method-group-title]')
                .contains('Autres modes de paiement')
                .click()
            cy.get('@payment_methods').find('[data-context=cb-payment-expanded]').should('have.length', 0)
            cy.get('@payment_methods').find('[data-context=multiple-payment-expanded]').should('have.length', 0)
            cy.get('@payment_methods').find('[data-context=other-payment-expanded]').should('have.length', 3)
        })

        it('should load the funnel without multi payment', function () {
            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    {
                        payment_method_id: 10,
                        name: 'Virement et autres moyens de paiement',
                        total_cost: 0,
                    },
                ])
            )

            visitPage()

            cy.get('[data-context=sales-funnel]')
                .should('be.visible')
                .find('[data-context=payment-methods]')
                .should('be.visible')
                .as('payment_methods')

            cy.get('@payment_methods').find('[data-context=payment-mean]').should('have.length', 1).as('payment_means')
            cy.get('@payment_means').find('[data-context=label]').should('not.contain', 'Paiements en plusieurs fois')
            cy.get('@payment_means').find('[data-context=label]').should('not.contain', 'Autres moyens de paiement')
        })

        it('should allow selection of a payment method which is not selected by default', function () {
            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    // Should be ignored
                    {
                        payment_method_id: 22,
                        name: 'Bons d&apos;achats et cartes cadeaux',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 32,
                        name: 'CB VISA / MASTERCARD / E-CARTE BLEUE',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 33,
                        name: 'American Express',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 37,
                        name: 'Paiement Paypal',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 50,
                        name: 'Paiement en 3 ou 4 fois sans frais',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 51,
                        name: 'Paiement en 3 ou 4 fois sans frais',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 10,
                        name: 'Virement et autres moyens de paiement',
                        total_cost: 0,
                    },
                ])
            )

            visitPage()

            cy.get('[data-context=sales-funnel]')
                .should('be.visible')
                .find('[data-context=payment-methods]')
                .should('be.visible')
                .as('payment_methods')

            cy.get('@payment_methods')
                .find('[data-context=method-group-title]')
                .should('have.length', 4)
                .as('payment_means')

            cy.get('@payment_methods')
                .find('[data-context=method-group-title]')
                .contains('Autres modes de paiement')
                .click()
            cy.get('@payment_methods').find('[data-context=payment-mean]').should('have.length', 2).as('payment_means')

            createCustomerOrderInErp(970482)

            cy.get('@payment_means').find('[data-context=payment-method-radio-10]').click()
            cy.get('[data-context=pay-btn]').click()

            cy.catchInBetweenScreensError()

            cy.url({ timeout: 10000 }).should('eq', Cypress.config().baseUrl + '/ma-commande/confirmation/succes')
        })
    })

    describe('Paypal description', () => {
        it('Basket > 2000', function () {
            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    {
                        payment_method_id: 37,
                        name: 'Paiement Paypal',
                        total_cost: 0,
                    },
                ])
            )

            visitPage()

            cy.get('[data-context=sales-funnel]')
                .should('be.visible')
                .find('[data-context=payment-methods]')
                .should('be.visible')
                .as('payment_methods')
            cy.get('[data-context=payment-mean').should('contain.text', 'Paypal')
            cy.get('[data-context=payment-mean').should(
                'not.contain.text',
                'PayPal au comptant ou en 4 fois sans frais'
            )
        })
        it('Basket < 30', function () {
            cy.reloadFixtures()
            cy.applySqlFixture('checkout_process_v2/quote_type_quotation/3-payment-under-30.sql')

            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    {
                        payment_method_id: 37,
                        name: 'Paiement Paypal',
                        total_cost: 0,
                    },
                ])
            )

            visitPage()

            cy.get('[data-context=sales-funnel]')
                .should('be.visible')
                .find('[data-context=payment-methods]')
                .should('be.visible')
                .as('payment_methods')
            cy.get('[data-context=payment-mean').should('contain.text', 'Paypal')
            cy.get('[data-context=payment-mean').should(
                'not.contain.text',
                'PayPal au comptant ou en 4 fois sans frais'
            )
        })

        it('Basket > 30 and < 2000', function () {
            cy.reloadFixtures()
            cy.applySqlFixture('checkout_process_v2/quote_type_quotation/4-payment-under-2000.sql')

            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    {
                        payment_method_id: 37,
                        name: 'Paiement Paypal',
                        total_cost: 0,
                    },
                ])
            )

            visitPage()

            cy.get('[data-context=sales-funnel]')
                .should('be.visible')
                .find('[data-context=payment-methods]')
                .should('be.visible')
                .as('payment_methods')
            cy.get('[data-context=payment-mean').should('contain.text', 'PayPal au comptant ou en 4 fois sans frais')
        })
    })

    describe('Behaviour when payment fails', () => {
        it('should show an error message if no payment method is returned', function () {
            cy.mockRPCCall('bridge', 'payment:get_payments_for_order', JSON.stringify([]))
            cy.mockRPCCall('payment_client', 'election', JSON.stringify([]))
            cy.mockPaymentV2Call('/api/payment-methods/election/1305005', JSON.stringify([]))

            visitPage()

            cy.get('[data-context=sales-funnel]')
                .should('be.visible')
                .find('[data-context=payment-methods]')
                .should('be.visible')
                .as('payment_methods')

            cy.get('@payment_methods')
                .find('[data-context=error-message]')
                .should('contain', 'Aucun moyen de paiement éligible n’a été retourné par le système.')
        })

        it('should show an error when the payment fails', function () {
            cy.mockRPCCall(
                'bridge',
                'payment:get_payments_for_order',
                JSON.stringify([
                    // Should be ignored
                    {
                        payment_method_id: 22,
                        name: 'Bons d&apos;achats et cartes cadeaux',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 32,
                        name: 'CB VISA / MASTERCARD / E-CARTE BLEUE',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 33,
                        name: 'American Express',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 37,
                        name: 'Paiement Paypal',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 50,
                        name: 'Paiement en 3 ou 4 fois sans frais',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 51,
                        name: 'Paiement en 3 ou 4 fois sans frais',
                        total_cost: 0,
                    },
                    {
                        payment_method_id: 10,
                        name: 'Virement et autres moyens de paiement',
                        total_cost: 0,
                    },
                ])
            )

            visitPage()

            cy.route('PUT', '/ma-commande/ajouter-paiement', { status: 'success', needs_additional_payment: false }).as(
                'add_payment_method'
            )
            cy.route('POST', '/ma-commande/v2/confirmer', { status: 'error' }).as('confirm')

            cy.get('[data-context=sales-funnel]')
                .should('be.visible')
                .find('[data-context=payment-methods]')
                .should('be.visible')
                .as('payment_methods')

            cy.get('@payment_methods').find('[data-context=payment-mean]').should('have.length', 2).as('payment_means')

            cy.get('[data-context=sales-funnel]').find('[data-context=modal]').should('not.exist')
            cy.get('@payment_means').contains('American Express').click()
            cy.get('[data-context=pay-btn]').click()

            cy.wait('@add_payment_method').then((xhr) => {
                expect(xhr.request.body.payment_method_id).to.eq(33)
            })
            cy.wait('@confirm')

            cy.get('[data-context=sales-funnel]').find('[data-context=modal]').should('exist').as('modal')
        })
    })
})

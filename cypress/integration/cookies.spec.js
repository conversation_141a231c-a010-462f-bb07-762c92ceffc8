describe('Cookies acceptance', function () {
    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        cy.clearCookies()
        cy.reloadFixtures()
    })

    it('Check cookies banner and cookies refused', function () {
        cy.visit('/')

        cy.get('[data-context=cookies-banner]').as('cookies-banner').should('be.visible')

        cy.get('@cookies-banner')
            .find('p')
            .should('contain', 'Son-Vidéo.com utilise des cookies')
            .should('have.class', 'text-lg')

        cy.get('@cookies-banner').find('button').should('have.length', 3)

        cy.get('@cookies-banner').find('button').eq(0).should('contain', 'Tout refuser').click()
        cy.get('@cookies-banner').should('not.exist')
    })

    it('Check cookies banner and cookies accepted', function () {
        cy.visit('/')

        cy.get('[data-context=cookies-banner]').as('cookies-banner').should('be.visible')

        cy.get('@cookies-banner').find('button').eq(1).should('contain', 'Paramétrer les cookies').click()
        cy.get('[data-context=consent-group]').as('consent-group').should('have.length', 4)
        cy.get('@consent-group').eq(1).find('h2').should('contain', `Mesures d’audience`)

        cy.get('[data-context=cookie-consent]').eq(1).find('span').eq(0).click()

        cy.get('[data-context=button-cookie-params]').should('contain', 'Sauvegarder mes paramètres').click()
        cy.get('[data-context=button-cookie-params]').should('not.exist')
        cy.getCookies().then((cookies) => {
            expect(cookies.map((c) => c.name)).to.include('euconsent')
        })
    })
})

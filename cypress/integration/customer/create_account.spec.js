describe('Account creation page', function () {
    const PAGE = '/mon-compte/creation'

    before(() => {
        cy.logout()
        cy.setupDatabase()
        cy.mockRPCCall('bo-cms', 'customer:get_status_newsletter', '{"status": "inactive" }')
    })

    beforeEach(() => {
        // reset and seed the database prior to every test
        cy.reloadFixtures()
    })

    it('Can create a new account', function () {
        cy.visit(PAGE)

        // set all the fields
        cy.get('[data-context=email]').type('<EMAIL>')
        cy.get('[data-context=password-1]').type('Azerty123!')

        // submit form (with success creation mocked from BO)
        cy.mockRPCCall('bo-cms', 'customer:create_active_account', '{"status": true, "account": {"customer_id": 26}}')
        cy.mockRPCCall(
            'bridge',
            'customer:update_account_info',
            JSON.stringify({
                success: true,
            })
        )

        cy.get('#btnInscription').click()

        // should be authenticated and redirected to customer homepage, with customer's information properly set
        cy.url().should('eq', Cypress.config().baseUrl + '/mon-compte')
        cy.get('[data-context=message-success]').should('be.visible').htmlContains('Merci votre compte est validé.')
        cy.get('#account').htmlContains('Bonjour').htmlContains('N° DE CLIENT : 26')
        cy.visit('/mon-compte/mes-informations')
        cy.get('[data-context=account_informations]')
        cy.get('input[name="account_information_form[email]').should('have.value', '<EMAIL>')
    })

    it('Do some validations on the fields', function () {
        cy.visit(PAGE)

        const getFormErrorItem = (label) => {
            return cy.get(`.error:contains(${label})`)
        }
        const getFormValidItem = (label) => {
            return cy.get(`.valid:contains(${label})`)
        }
        const getFormInvalidItem = (label) => {
            return cy.get(`.invalid:contains(${label})`)
        }

        // email
        cy.get('[data-context=email]').type('123456')
        getFormErrorItem('Veuillez saisir une adresse email valide')
        cy.get('[data-context=email]').clear().type('@test.com')
        getFormErrorItem('Veuillez saisir une adresse email valide')
        cy.get('[data-context=email]').clear().type('123456@test')
        getFormErrorItem('Veuillez saisir une adresse email valide')

        // password with force meter
        cy.get('[data-context=password-1]').type('a')
        getFormInvalidItem('Minimum de 8 caractères')
        getFormInvalidItem('Une minuscule et une majuscule')
        getFormInvalidItem('Un chiffre')
        getFormInvalidItem('Un caractère spécial (!?&#$*)')
        cy.get('[data-context=password-1]').type('A')
        getFormInvalidItem('Minimum de 8 caractères')
        getFormValidItem('Une minuscule et une majuscule')
        getFormInvalidItem('Un chiffre')
        getFormInvalidItem('Un caractère spécial (!?&#$*)')
        cy.get('[data-context=password-1]').type('3')
        getFormInvalidItem('Minimum de 8 caractères')
        getFormValidItem('Une minuscule et une majuscule')
        getFormValidItem('Un chiffre')
        getFormInvalidItem('Un caractère spécial (!?&#$*)')
        cy.get('[data-context=password-1]').type('!')
        getFormInvalidItem('Minimum de 8 caractères')
        getFormValidItem('Une minuscule et une majuscule')
        getFormValidItem('Un chiffre')
        getFormValidItem('Un caractère spécial (!?&#$*)')
        cy.get('[data-context=password-1]').type('aaaa')
        getFormValidItem('Minimum de 8 caractères')
        getFormValidItem('Une minuscule et une majuscule')
        getFormValidItem('Un chiffre')
        getFormValidItem('Un caractère spécial (!?&#$*)')
    })

    it('Notify when account already exists', function () {
        cy.visit(PAGE)

        cy.get("[name='account_creation_form[email]']").should('be.empty').type('<EMAIL>')
        cy.get('[data-context=password-1]').type('Azerty123!')

        cy.get('#btnInscription').click()

        cy.get("[name='account_creation_form[email]']").should('have.value', '<EMAIL>')
        cy.get('div.helper span.error').as('error-helper')
        cy.get('@error-helper').should(
            'contain',
            'Vous avez déjà un compte enregistré à cette adresse email. Connectez-vous pour y accéder'
        )
        cy.get('@error-helper')
            .find('a')
            .attribute('href')
            .should('eq', '/mon-compte/connexion?email=batman%40wayneenterprise.com')

        cy.get('div.subtext:contains(Vous avez déjà un compte ?)')
            .find('a:contains(Se connecter)')
            .attribute('href')
            .should('eq', '/mon-compte/connexion?email=batman%40wayneenterprise.com')
    })
})

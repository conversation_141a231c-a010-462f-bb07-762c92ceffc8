import { beforeAll, getViewportConfig, SITE_VERSIONS, visitDesktop, visitMobile } from './helpers'

describe('Header - Mega Menu', function () {
    before(beforeAll)
    SITE_VERSIONS.forEach((version) => {
        describe(version.name, function () {
            const PAGE = version.page

            before(function () {
                if ('v5' === version.name) {
                    cy.applySqlFixture('home/default.sql')
                }
            })

            context('Mobile', () => {
                before(function () {
                    visitMobile(PAGE, version.name)
                })
                context('Mobile (between 320px and 380px)', getViewportConfig([321, 736]), function () {
                    it('should not show the horizontal menu bar', function () {
                        cy.get('header [data-context=bottom] [data-context=horizontal-menu]')
                            .should('exist')
                            .should('not.be.visible')
                    })
                })

                context('Mobile (between 380px and 640px', getViewportConfig([386, 736]), function () {
                    it('should not show the horizontal menu bar', function () {
                        cy.get('header [data-context=bottom] [data-context=horizontal-menu]')
                            .should('exist')
                            .should('not.be.visible')
                    })
                })

                context('Tablet (between 640px and 768px)', getViewportConfig([641, 736]), function () {
                    it('should not show the horizontal menu bar', function () {
                        cy.get('header [data-context=bottom] [data-context=horizontal-menu]')
                            .should('exist')
                            .should('not.be.visible')
                    })
                })
            })

            context('Desktop', () => {
                before(function () {
                    visitDesktop(PAGE, version.name)
                })
                context('Tablet (between 768px and 1024px)', getViewportConfig([769, 768]), function () {
                    describe('On the bottom section', function () {
                        it('should allow to scroll horizontally the menu if some items are not visible', function () {
                            cy.get('header [data-context=bottom] [data-context=horizontal-menu] li:contains(Soldes)')
                                .should('exist')
                                .then(($formActions) => {
                                    $formActions.css({ position: 'relative' })
                                })
                                .should('not.be.visible')
                                .scrollIntoView()
                                .should('be.visible')
                        })
                    })
                })

                context('Large screen (more than 1920px)', getViewportConfig([1920, 1080]), function () {
                    describe('On the bottom section', function () {
                        it('should show the horizontal menu entirely', function () {
                            cy.get(
                                'header [data-context=bottom] [data-context=horizontal-menu] li:contains(Soldes)'
                            ).should('be.visible')

                            cy.get(
                                'header [data-context=bottom] [data-context=horizontal-menu] li:contains(Aide)'
                            ).should('be.visible')
                        })
                    })
                })

                context('Behaviour when the mega menu is open', getViewportConfig([1920, 1080]), () => {
                    const openMegaMenu = (name) => {
                        cy.get(`[data-context=bottom] [data-context=horizontal-menu] li:contains(${name})`).trigger(
                            'mouseover'
                        )
                        cy.wait(500) // wait for animation to finish
                        cy.get('[data-context=mega-menu] [data-context=dropdown]').as('dropdown').should('be.visible')
                    }

                    it('checks the dropdown content displayed in mega menu', function () {
                        openMegaMenu('Enceintes')
                        cy.get('@dropdown').find('[data-context=column]').as('column').should('have.length', 5)
                        cy.get('@column').eq(0).find('[data-context=mega-menu-section]').should('exist')
                        cy.get('@column').eq(0).find('[data-context=mega-menu-item]').should('exist')
                        cy.get('@column').eq(4).find('[data-context=mega-menu-cb]').should('exist')

                        // Menu without content should not show the dropdown
                        cy.get('[data-context=bottom] [data-context=horizontal-menu] li:contains(Soldes)').trigger(
                            'mouseover'
                        )
                        cy.wait(1000)
                        cy.get('[data-context=mega-menu] [data-context=dropdown]').should('not.exist')
                    })
                })
            })
        })
    })
})

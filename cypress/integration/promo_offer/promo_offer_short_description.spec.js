describe('Check promo offer short description display', function () {
    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        cy.server().route('GET', '/**').as('articles')

        // reset and seed the database prior to every test
        cy.reloadFixtures()
    })

    describe('Tests without EAV facets', function () {
        it('Check article promo offer display on stand search', function () {
            cy.preAcceptSiteCookies().visit('/rayon/haute-fidelite/enceintes/enceintes-colonne')

            cy.get('[data-context=search-article]').should('have.length', 3)
            cy.get('[data-context=search-article]:contains(Elipson Prestige)')
                .find('[data-context=promo-code]')
                .should('be.visible')
                .should('contain', 'Méga offre exceptionnelle')
            cy.get('[data-context=search-article]:contains(Super enceinte)')
                .find('[data-context=promo-code]')
                .should('be.visible')
                .should('contain', 'Méga offre exceptionnelle')
            cy.get('[data-context=search-article]:contains(Gaïa EZ)')
                .find('[data-context=promo-code]')
                .should('not.exist')
        })

        it('Check article promo offer display on brand search', function () {
            cy.preAcceptSiteCookies().visit('/marque/cambridge')

            // 7 - 2 = Articles with no selling price are not displayed
            cy.get('[data-context=article-line]').should('have.length', 5)
            cy.get('[data-context=article-line]').eq(0).find('[data-context=promo-offer]').should('not.exist')
            cy.get('[data-context=article-line]')
                .eq(1)
                .find('[data-context=promo-offer]')
                .should('be.visible')
                .should('contain', 'Méga offre exceptionnelle')
            cy.get('[data-context=article-line]').eq(2).find('[data-context=promo-offer]').should('not.exist')
        })

        it('Check article promo offer display on selection search', function () {
            cy.preAcceptSiteCookies().visit('/selection/enceintes-elipson-lanet')

            cy.get('[data-context=search-article]').should('have.length', 4)
            cy.get('[data-context=search-article]')
                .eq(0)
                .find('[data-context=promo-code]')
                .should('be.visible')
                .should('contain', 'Méga offre exceptionnelle')
            cy.get('[data-context=search-article]')
                .eq(3)
                .find('[data-context=promo-code]')
                .should('be.visible')
                .should('contain', 'Méga offre exceptionnelle')
            cy.get('[data-context=search-article]').eq(1).find('[data-context=promo-code]').should('not.exist')
            cy.get('[data-context=search-article]').eq(2).find('[data-context=promo-code]').should('not.exist')
        })
    })

    describe('Tests when EAV facets', function () {
        beforeEach(function () {
            // reset and seed the database prior to every test
            cy.reloadFixtures()
            // necessary only because we need a stand using the new display
            cy.applySqlFixture('article_lister/eav.sql')

            cy.server()
            cy.route('GET', '/**/_filters**', 'fixture:api/rayon/{slug}/_filters/article_filters.json').as(
                'api_filters'
            )
            cy.route('POST', '/**/_search**', 'fixture:api/articles/_search/article_lister.json').as('api_search')
        })

        it('display promo offers short description on article on line', function () {
            cy.preAcceptSiteCookies().visit('/rayon/television/televiseurs/tv-uhd-8k')
            cy.wait('@api_filters')
            cy.wait('@api_search')

            cy.get('[data-context=line-item]').eq(0).as('first_row')
            cy.get('@first_row').find('[data-context=article-name]').htmlContains('Philips 43PUS7505')
            cy.get('@first_row')
                .find('[data-context=promo-code]')
                .should('be.visible')
                .should('contain', '-10% avec le code SVD2020')
            cy.get('[data-context=line-item]').eq(1).as('second_row')
            cy.get('@second_row').find('[data-context=article-name]').htmlContains('TCL 43P718')
            cy.get('@second_row')
                .find('[data-context=promo-code]')
                .should('be.visible')
                .should('contain', 'Un DVD offert')
            cy.get('[data-context=line-item]').eq(2).as('third_row')
            cy.get('@third_row').find('[data-context=article-name]').htmlContains('Philips 50PUS7505')
            cy.get('@third_row').find('[data-context=promo-code]').should('not.exist')
        })

        it('display promo offers short description on article on grid', function () {
            cy.preAcceptSiteCookies().visit('/rayon/television/televiseurs/tv-uhd-8k')
            cy.wait('@api_filters')
            cy.wait('@api_search')

            cy.get('[data-context=toggle-grid]').should('have.class', 'btn-default').click()

            cy.get('[data-context=grid-item]').eq(0).as('first_row')
            cy.get('@first_row').find('[data-context=article-name]').should('contain', 'Philips 43PUS7505')
            cy.get('@first_row')
                .find('[data-context=promo-code]')
                .should('be.visible')
                .should('contain', '-10% avec le code SVD2020')
            cy.get('[data-context=grid-item]').eq(1).as('second_row')
            cy.get('@second_row').find('[data-context=article-name]').should('contain', 'TCL 43P718')
            cy.get('@second_row')
                .find('[data-context=promo-code]')
                .should('be.visible')
                .should('contain', 'Un DVD offert')
            cy.get('[data-context=grid-item]').eq(2).as('third_row')
            cy.get('@third_row').find('[data-context=article-name]').should('contain', 'Philips 50PUS7505')
            cy.get('@third_row').find('[data-context=promo-code]').should('not.exist')
        })
    })
})

describe('Brand article list', function () {
    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        cy.reloadFixtures()
    })

    it('Display reference price', function () {
        cy.visit('marque/cambridge')

        cy.get('[data-context=article-line]')
            .should('contain.text', 'Super enceinte')
            .within(() => {
                cy.get('[data-context=reference-price]').within(() => {
                    cy.get('[data-context=rounded-percentage]').should('contain.text', '- 33 %')
                    cy.get('[data-context=reference-price-cross]').should('contain.text', '150 €')
                })
            })
    })
})

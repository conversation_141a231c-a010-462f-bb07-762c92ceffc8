/* global cy, Cypress */
describe('Stand title format', function () {
    before(function () {
        cy.setupDatabase()
        cy.reloadFixtures()
        cy.wait(500)
    })

    it('Check level 1 stand with title defined', function () {
        cy.visit('/rayon/home-cinema')
        cy.title().should('eq', 'Tout pour le home cinema : ampli, enceintes, caisson, bluray')
    })

    it('Check level 1 stand with title empty', function () {
        cy.visit('/rayon/haute-fidelite')
        cy.title().should('eq', 'Le rayon Haute fidélité')
    })

    it('Check level 2 stand with title defined', function () {
        cy.visit('/rayon/home-cinema/chaines-home-cinema')
        cy.title().should('eq', 'Systèmes home-cinéma compacts, éléments séparés, barre de son')
    })

    it('Check level 2 stand with title empty', function () {
        cy.visit('/rayon/television/videoprojecteurs')
        cy.title().should('eq', 'Le rayon Vidéoprojecteurs')
    })

    it('Check level 3 stand with title defined', function () {
        cy.visit('/rayon/haute-fidelite/enceintes/enceintes-colonne')
        cy.title().should('eq', 'Enceintes Colonne - Retrait Gratuit Magasin - Son-Vidéo.com')
    })

    it('Check level 3 stand with title empty', function () {
        cy.visit('/rayon/home-cinema/chaines-home-cinema/chaines-home-cinema-compactes')
        cy.title().should('eq', 'Chaînes Home-Cinéma Compactes - Son-Vidéo.com')
    })

    it('Check level 3 stand with preposition', function () {
        cy.visit('/rayon/home-cinema/chaines-home-cinema/chaines-home-cinema-grand-spectacle')
        cy.title().should('eq', 'Chaînes Home-Cinéma pour Grand Spectacle - Son-Vidéo.com')
    })
})

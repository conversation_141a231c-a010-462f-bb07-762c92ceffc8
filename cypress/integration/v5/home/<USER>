describe('Check the homepage', function () {
    before(function () {
        cy.setupDatabase()
    })

    beforeEach(function () {
        // reset and seed the database prior to every test
        cy.reloadFixtures()
        cy.clearCookies()

        cy.server()
    })

    it('should show a main slider', function () {
        cy.applySqlFixture('home/default.sql')
        cy.applySqlFixture('home/delete_images.sql')

        cy.preAcceptSiteCookies().visit('/')

        cy.get('[data-context=main-slider]').as('main-slider').find('.swiper-slide:visible').should('have.length', 1)
        cy.get('@main-slider').find('img').eq(0).should('be.exist')

        cy.get('[data-context=main-slider-thumbnails]').find('.swiper-slide:visible').should('have.length', 6)
    })

    it('should show a popular categories section', function () {
        cy.applySqlFixture('home/default.sql')
        cy.applySqlFixture('home/delete_images.sql')

        cy.preAcceptSiteCookies().visit('/')

        cy.get('[data-context=popular-categories]').as('popular_categories')
        cy.get('@popular_categories').find('.swiper-slide').should('have.length', 9)
    })

    describe('Check "our unmissable" articles section', function () {
        it('should perform some basic checks', function () {
            cy.applySqlFixture('home/default.sql')
            cy.applySqlFixture('home/delete_images.sql')

            cy.preAcceptSiteCookies().visit('/')

            cy.get('[data-context=our-unmissable]')
                .as('our-unmissable')
                .find('h2')
                .scrollIntoView()
                .should('be.visible')
                .should('contain', 'Les immanquables')

            cy.get('@our-unmissable').find('img').should('be.visible').should('have.class', 'swiper-lazy-loaded')
            cy.get('@our-unmissable').find('p').should('contain', '€')
            // a packaged product should indicate its price without discount
            cy.get('@our-unmissable').find('[data-context=unique-unmissable]').should('have.length', 4)
            cy.get('@our-unmissable').find('[data-context=unique-unmissable]').eq(0).find('p').should('have.length', 3)

            // check reference price
            cy.get('@our-unmissable')
                .find('[data-context=article]:contains(Cambridge Super enceinte)')
                .within(() => {
                    cy.get('[data-context=reference-price]').should('be.visible').should('contain.text', '150 €')

                    cy.get('[data-context=highlight]').should('contain.text', '- 33 %')

                    cy.get('[data-context=reference-price]').click()
                })
            cy.get('[data-context=side-panel]')
                .should('be.visible')
                .should('contain.text', 'Prix de référence')
                .should('contain.text', `L’indication prix de référence`)
        })
    })

    describe('Check the "best selling" articles section', function () {
        it('should perform some basic checks', function () {
            cy.applySqlFixture('home/default.sql')
            cy.applySqlFixture('home/delete_images.sql')

            cy.preAcceptSiteCookies().visit('/')

            cy.get('[data-context=our-best-sellers]')
                .as('our-best-sellers')
                .find('h2')
                .scrollIntoView()
                .should('be.visible')
                .should('contain', 'Les meilleures ventes')

            cy.get('@our-best-sellers').find('img').should('be.visible').should('have.class', 'swiper-lazy-loaded')
            cy.get('@our-best-sellers').find('p').should('contain', '€')
            cy.get('@our-best-sellers').find('[data-context=unique-unmissable]').should('have.length', 1)
            cy.get('@our-best-sellers')
                .find('[data-context=unique-unmissable]')
                .eq(0)
                .find('p')
                .should('have.length', 3)
        })
    })

    describe('Check the "new articles" section', function () {
        it('should perform some basic checks', function () {
            cy.applySqlFixture('home/default.sql')
            cy.applySqlFixture('home/delete_images.sql')

            cy.preAcceptSiteCookies().visit('/')

            cy.get('[data-context=our-new-articles]')
                .as('our-new-articles')
                .find('h2')
                .scrollIntoView()
                .should('be.visible')
                .should('contain', 'Les nouveautés')

            cy.get('@our-new-articles').find('img').should('be.visible').should('have.class', 'swiper-lazy-loaded')
            cy.get('@our-new-articles').find('p').should('contain', '€')
            // a new articles should not display its "instead of" configured price (instead_of_price)
            cy.get('@our-new-articles')
                .contains('div', 'Cabasse MC170 Socoa')
                .find('p')
                .should('not.contain', 'au lieu de')
            cy.get('@our-new-articles').find('[data-context=unique-unmissable]').should('have.length', 1)
            cy.get('@our-new-articles')
                .find('[data-context=unique-unmissable]')
                .eq(0)
                .find('p')
                .should('have.length', 3)
        })
    })

    it('should show a store map section', function () {
        cy.applySqlFixture('home/default.sql')
        cy.applySqlFixture('home/delete_images.sql')

        cy.preAcceptSiteCookies().visit('/')

        cy.get('[data-context=stores-near-you]')
            .scrollIntoView()
            .find('h2')
            .should('contain', '16 magasins Son-Vidéo.com')
    })

    it('should show a guide related section', function () {
        cy.applySqlFixture('home/default.sql')
        cy.applySqlFixture('home/delete_images.sql')

        cy.preAcceptSiteCookies().visit('/')

        cy.get('[data-context=home-guides]')
            .find('h2')
            .scrollIntoView()
            .should('be.visible')
            .should('contain', "Regarder, écouter, s'informer")

        // Bigger highlighted guides

        cy.get('[data-context=guide-highlights] a:visible').as('guide-highlights').should('have.length', 3)
        cy.get('@guide-highlights').find('[data-context=guide-title]').eq(0).should('contain', 'Décoration')

        // Smaller guide links

        cy.get('[data-context=guide-links] a:visible').as('guide-links').should('have.length', 4)
        cy.get('@guide-links').find('img').should('be.visible')
        cy.get('@guide-links').find('[data-context=guide-link-name]').should('be.visible')
        cy.get('@guide-links').find('[data-context=guide-link-name]').eq(1).should('contain', "Guide d'achats")
    })

    it('should show an highlighted brands section', function () {
        cy.applySqlFixture('home/default.sql')
        cy.applySqlFixture('home/delete_images.sql')

        cy.preAcceptSiteCookies().visit('/')

        cy.get('[data-context=brands]').as('brands').should('contain', 'Plus de 300 grandes marques')
        cy.get('@brands')
            .find('a')
            .eq(0)
            .scrollIntoView()
            .should('be.visible')
            .should('have.attr', 'href', '/marque/denon')
        cy.get('@brands').find('img').should('be.visible')
    })

    it('should show a customers reviews section', function () {
        cy.applySqlFixture('home/default.sql')
        cy.applySqlFixture('home/delete_images.sql')

        cy.preAcceptSiteCookies().visit('/')

        cy.get('[data-context=site-reviews]')
            .as('site-reviews')
            .find('h2')
            .scrollIntoView()
            .should('be.visible')
            .should('contain', 'Merci pour votre (haute) fidélité !')

        cy.get('[data-context=site-reviews] .swiper-slide').eq(0).find('p').should('have.length', 2)
        cy.get('[data-context=site-reviews] .swiper-slide')
            .eq(0)
            .find('p')
            .eq(0)
            .should('contain', 'Trop de la balle ce site !!!')
        cy.get('[data-context=site-reviews] .swiper-slide')
            .eq(0)
            .find('p')
            .eq(1)
            .should('contain', 'J. B. le jeudi 3 août 2017')
    })

    describe('should record GA events', function () {
        it('should record GA view events', function () {
            cy.applySqlFixture('home/default.sql')
            cy.applySqlFixture('home/delete_images.sql')

            cy.visit('/')

            cy.window().then((win) => {
                expect(
                    win.dataLayer.filter(
                        (d) => d.hasOwnProperty('google_analytics_id') && d.hasOwnProperty('google_analytics_v4_id')
                    )
                ).to.have.length(1)

                expect(
                    win.dataLayer.filter((d) => d.hasOwnProperty('event') && d.event === 'eventTracking')[0]?.category
                ).to.eq('Consent')

                const ecommerce_view = win.dataLayer.find(
                    (d) => d.hasOwnProperty('event') && d.event === 'ecommerceView'
                )
                expect(ecommerce_view.ecommerce).to.be.not.undefined
                expect(ecommerce_view.ecommerce.currencyCode).to.eq('EUR')
                expect(ecommerce_view.ecommerce.promoView.promotions).to.have.length(6)
                expect(ecommerce_view.ecommerce.impressions).to.have.length(6)

                const view_promotion = win.dataLayer.find(
                    (d) => d.hasOwnProperty('event') && d.event === 'view_promotion'
                )
                expect(view_promotion.ecommerce).to.be.not.undefined
                expect(view_promotion.ecommerce.items).to.have.length(6)

                const view_item_list = win.dataLayer.find(
                    (d) => d.hasOwnProperty('event') && d.event === 'view_item_list'
                )
                expect(view_item_list.ecommerce).to.be.not.undefined
                expect(view_item_list.ecommerce.items).to.have.length(6)
                const item = view_item_list.ecommerce.items.find((item) => item.item_id === '999999')
                expect(item).to.be.not.undefined
                expect(item.item_name).to.eq('Elipson Prestige 4i Calvados + Tulip noir')
                expect(item.price).to.eq(999.99)
            })
        })

        it('should record GA banner click events', function () {
            cy.applySqlFixture('home/default.sql')
            cy.applySqlFixture('home/delete_images.sql')

            cy.preAcceptSiteCookies().visit('/')

            cy.get('[data-context=main-slider-thumbnails] [data-thumb-index=2]').should('be.visible').click()
            cy.get('[data-context=main-slider] [data-index=2]').should('be.visible').trigger('click')

            cy.window().then((win) => {
                expect(
                    win.dataLayer.filter(
                        (d) => d.hasOwnProperty('google_analytics_id') && d.hasOwnProperty('google_analytics_v4_id')
                    )
                ).to.have.length(1)

                const promotion_click = win.dataLayer.find(
                    (d) => d.hasOwnProperty('event') && d.event === 'promotionClick'
                )
                expect(promotion_click.ecommerce).to.be.not.undefined
                expect(promotion_click.ecommerce.promoClick.promotions).to.have.length(1)
                expect(promotion_click.ecommerce.promoClick.promotions[0].name).to.eq('MonitoV')
                expect(promotion_click.ecommerce.promoClick.promotions[0].creative).to.eq('home')

                const select_promotion = win.dataLayer.find(
                    (d) => d.hasOwnProperty('event') && d.event === 'select_promotion'
                )
                expect(select_promotion.ecommerce).to.be.not.undefined
                expect(select_promotion.ecommerce.items).to.have.length(1)
                expect(select_promotion.ecommerce.items[0].promotion_name).to.eq('MonitoV')
                expect(select_promotion.ecommerce.items[0].creative_name).to.eq('home')
            })
        })

        it('should record GA products sliders click events', function () {
            cy.applySqlFixture('home/default.sql')
            cy.applySqlFixture('home/delete_images.sql')

            cy.preAcceptSiteCookies().visit('/')

            cy.get('[data-context=our-unmissable]').contains('div', 'Name for the pack').trigger('click')

            cy.window().then((win) => {
                expect(
                    win.dataLayer.filter(
                        (d) => d.hasOwnProperty('google_analytics_id') && d.hasOwnProperty('google_analytics_v4_id')
                    )
                ).to.have.length(1)

                const product_click = win.dataLayer.find((d) => d.hasOwnProperty('event') && d.event === 'productClick')
                expect(product_click.ecommerce).to.be.not.undefined
                expect(product_click.ecommerce.click.actionField.list).to.eq('Homepage : sélection')
                expect(product_click.ecommerce.click.products).to.have.length(1)
                expect(product_click.ecommerce.click.products[0]).to.have.property(
                    'name',
                    'Elipson Prestige 4i Calvados + Tulip noir'
                )
                expect(product_click.ecommerce.click.products[0]).to.have.property('id', '999999')
                expect(product_click.ecommerce.click.products[0]).to.have.property('price', '999.99')

                const select_item = win.dataLayer.find((d) => d.hasOwnProperty('event') && d.event === 'select_item')
                expect(select_item.ecommerce).to.be.not.undefined
                expect(select_item.ecommerce.currencyCode).to.eq('EUR')
                expect(select_item.ecommerce.items).to.have.length(1)
                expect(select_item.ecommerce.items[0].item_list_name).to.eq('Homepage : sélection')
                expect(select_item.ecommerce.items[0]).to.have.property(
                    'item_name',
                    'Elipson Prestige 4i Calvados + Tulip noir'
                )
                expect(select_item.ecommerce.items[0]).to.have.property('item_id', '999999')
                expect(select_item.ecommerce.items[0]).to.have.property('price', 999.99)
            })
        })
    })
})

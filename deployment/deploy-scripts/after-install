#!/bin/bash
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
source ${DIR}/environment
SCRIPT="deployer:after-install"

# Get environment
INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
REGION=$(curl -s http://***************/latest/dynamic/instance-identity/document | jq -r ".region")
ENVIRONMENT=$(aws ec2 describe-tags --region ${REGION} --filters "Name=resource-type,Values=instance" "Name=resource-id,Values=${INSTANCE_ID}" "Name=key,Values=env" | jq -r '.Tags[].Value')

cd ${DEPLOY_DIR}

if [ ! -e app/config/parameters.yml ];
then
    ln -s ${TARGET_DIR}/shared/config/parameters.yml app/config/parameters.yml;
    echo "Created symbolic link to the parameters.yml." | logger --tag="${SCRIPT}"
fi

mkdir ${DEPLOY_DIR}/var
chown svd_admin:nginx ${DEPLOY_DIR}/var
setfacl -R -m u:nginx:rwX -m u:`whoami`:rwX ${DEPLOY_DIR}/var
setfacl -dR -m u:nginx:rwX -m u:`whoami`:rwX ${DEPLOY_DIR}/var
setfacl -R -m u:svd_admin:rwX -m u:`whoami`:rwX ${DEPLOY_DIR}/var
setfacl -dR -m u:svd_admin:rwX -m u:`whoami`:rwX ${DEPLOY_DIR}/var

cp app/bootstrap.php.cache var/

for sf_command in "cache:clear" "assets:install web";
do
    php bin/console $sf_command;
done;

cd ${DEPLOY_DIR}/web/dist/css
ln -s globals.*.css globals.css

if [ "${ENVIRONMENT}" == "prd" ];
then
    rm ${DEPLOY_DIR}/web/dist/entrypoints.json
    rm ${DEPLOY_DIR}/web/dist/manifest.json
    mv ${DEPLOY_DIR}/web/dist/entrypoints.json.prod ${DEPLOY_DIR}/web/dist/entrypoints.json
    mv ${DEPLOY_DIR}/web/dist/manifest.json.prod ${DEPLOY_DIR}/web/dist/manifest.json
fi

[ -d "${ROLLBACK_DIR}" ] && rm -rf ${ROLLBACK_DIR};
mv ${PROJECT_DIR} ${ROLLBACK_DIR};
mv ${DEPLOY_DIR} ${PROJECT_DIR};

# Modify robots.txt for all environments except production
if [ "${ENVIRONMENT}" != "prd" ];
then
echo "User-agent: *
Noindex: /
Disallow: /" > ${TARGET_DIR}/release/web/robots.txt
fi

# vim: set ft=sh

{"name": "fo-cms", "version": "2.0.0", "private": true, "scripts": {"serve": "encore dev-server --https --port 8888 --host 0.0.0.0 --public https://fo-cms.lxc:8888 --allowed-hosts all", "serve-lionel": "encore dev-server --https --port 8888 --host 0.0.0.0 --public https://fo-cms.lionel:8888 --allowed-hosts all", "build": "encore production --progress", "build-report": "encore production --progress --env REPORT", "build-deploy": "encore production --env BUILD_MODE=deploy --progress", "build-test": "encore production --env BUILD_MODE=test --progress", "build-ci": "encore production --env BUILD_MODE=test", "before-deploy": "node before.deploy.js", "optimize-svg-icons": "svgo -r ./src/AppBundle/Resources/icons/svd", "e2e": "npm run build-test && npx cypress run", "e2e:open": "npm run build-test && npx cypress open", "e2e:no-build": "npx cypress run", "e2e:no-build:open": "npx cypress open", "delete:e2e-reports": "rm -rf cypress/results/* || true", "pree2e:ci": "npm run delete:e2e-reports", "e2e:ci": "npx cypress run --env split=true --browser chrome --config-file cypress-ci.json", "poste2e:ci": "npm run combine:e2e-reports", "combine:e2e-reports": "jrm cypress/results/combined-report.xml \"cypress/results/*.xml\" && npx mochawesome-merge \"cypress/results/*.json\" > mochawesome.json && npx marge mochawesome.json -o cypress/results/html", "commit": "commit-wizard", "stop-only": "stop-only --folder cypress/integration", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@babel/core": "^7.21.4", "@fancyapps/fancybox": "^3.3.5", "@floating-ui/dom": "^1.4.2", "@floating-ui/vue": "^1.0.1", "@storybook/theming": "^7.0.18", "@symfony/webpack-encore": "^2.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.9", "@vueuse/core": "^9.12.0", "autoprefixer": "^10.4.14", "axios": "^0.27.2", "babel-preset-latest-node": "^5.5.1", "bazinga-translator": "^2.6.6", "bootstrap": "^4.3.1", "css-rewrite-webpack-plugin": "^0.1.1", "date-fns": "^1.29.0", "filesize": "^3.6.0", "intersection-observer": "^0.7.0", "javascript-autocomplete": "^1.0.2", "jquery": "^3.4.1", "leaflet": "^1.9.4", "libphonenumber-js": "^1.10.15", "lodash.capitalize": "^4.2.1", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "lodash.intersection": "^4.4.0", "lodash.isequal": "^4.5.0", "lodash.union": "^4.6.0", "lodash.upperfirst": "^4.3.1", "lozad": "^1.9.0", "mochawesome-merge": "^4.3.0", "node-sass": "^8.0.0", "onecolor": "^4.0.0", "pinia": "^2.0.28", "postcss-loader": "^6.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "replace": "^1.2.2", "sass-loader": "^13.2.2", "svg-to-vue-component": "^0.3.8", "sweetalert2": "^6.11.5", "swiper": "^8.3.2", "tabs": "^0.2.0", "tailwindcss": "^3.3.1", "tailwindcss-touch": "^1.0.1", "url-search-params-polyfill": "^2.0.3", "v-click-outside": "^3.0.1", "v-tooltip": "^2.0.3", "vue": "^2.7.0", "vue-country-flag": "^2.0.1", "vue-loader": "^15.9.8", "vue-multiselect": "^2.1.0", "vue-round-filter": "^1.1.2", "vue-slick": "^1.1.15", "vue-tel-input": "^4.4.2", "vue-template-compiler": "^2.7.14", "vue-touch": "^2.0.0-beta.4", "vue2-google-maps": "^0.10.2", "vue2-leaflet": "^2.7.1", "vue2-touch-events": "^2.3.1", "vuex": "^2.5.0", "webpack-bundle-analyzer": "^4.8.0", "yaml": "^1.10.0", "zxcvbn": "^4.4.2"}, "devDependencies": {"@prettier/plugin-php": "^0.18.9", "@storybook/addon-essentials": "^7.0.18", "@storybook/addon-interactions": "^7.0.18", "@storybook/addon-links": "^7.0.18", "@storybook/addon-mdx-gfm": "^7.0.18", "@storybook/addon-styling": "^1.0.5", "@storybook/blocks": "^7.0.18", "@storybook/testing-library": "^0.0.14-next.2", "@storybook/vue": "^7.0.18", "@storybook/vue-webpack5": "^7.0.18", "cypress": "^7.4.0", "cypress-commands": "^1.1.0", "cypress-multi-reporters": "^1.5.0", "cypress-on-fix": "^1.0.2", "cypress-split": "^1.3.7", "junit-report-merger": "^3.0.2", "mocha": "^9.1.3", "mocha-junit-reporter": "^2.0.2", "mochawesome": "^7.0.1", "mochawesome-report-generator": "^6.0.1", "postcss": "^8.4.13", "pre-git": "^3.17.1", "prettier": "^2.8.7", "react": "^18.2.0", "react-dom": "^18.2.0", "sass": "^1.62.1", "stop-only": "^3.1.2", "storybook": "^7.0.18", "web-vitals": "^3.3.1-types-2"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "iOS >= 9", "Firefox ESR", "Edge >= 13", "not ie <= 10"], "prettier": {"printWidth": 120, "trailingComma": "es5", "tabWidth": 4, "semi": false, "singleQuote": true, "braceStyle": "psr-2", "phpVersion": "7.3", "trailingCommaPHP": true}, "release": {"analyzeCommits": "simple-commit-message"}, "config": {"pre-git": {"enabled": true, "pre-commit": [". ~/.nvm/nvm.sh && nvm use && npm run stop-only"], "allow-untracked-files": true}}}
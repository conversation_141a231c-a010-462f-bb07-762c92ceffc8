includes:
    - phpstan.common.neon

parameters:
    symfony:
        # you need to warm up the cache with `bin/console cache:warm`
        containerXmlPath: var/cache/dev/appDevDebugProjectContainer.xml
    paths:
        - src
        - web
        - app
    tmpDir: var/cache/phpstan/dev
    excludePaths:
        - src/*/Tests/*
    ignoreErrors:
        - message: '#^Constant SESSION_KEY_IMPERSONATION_(LOGIN|USERNAME|STAFF_ACCOUNT) not found.$#'
        - message: '#^Instantiated class SymfonyRequirements not found.$#'

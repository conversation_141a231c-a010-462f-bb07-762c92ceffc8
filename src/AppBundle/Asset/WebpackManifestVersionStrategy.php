<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Asset;

use Symfony\Component\Asset\VersionStrategy\VersionStrategyInterface;

/**
 * Class WebpackManifestVersionStrategy
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Asset
 * <AUTHOR> <<EMAIL>>
 */
class WebpackManifestVersionStrategy implements VersionStrategyInterface
{
    private string $manifest_path;

    /**
     * @var mixed
     */
    private $hashes;

    /**
     * WebpackManifestVersionStrategy constructor.
     */
    public function __construct(string $manifest_path)
    {
        $this->manifest_path = $manifest_path;
    }

    /**
     * loadManifest
     *
     * @return mixed
     */
    private function loadManifest()
    {
        if (!file_exists($this->manifest_path)) {
            return [];
        }

        return json_decode(file_get_contents($this->manifest_path), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * getVersion
     *
     * @param string $path
     */
    public function getVersion($path): string
    {
        if (!is_array($this->hashes)) {
            $this->hashes = $this->loadManifest();
        }

        return $this->hashes[$path] ?? '';
    }

    /**
     * applyVersion
     *
     * @param string $path
     */
    public function applyVersion($path): string
    {
        $version = $this->getVersion($path);

        return $version !== '' ? $version : $path;
    }
}

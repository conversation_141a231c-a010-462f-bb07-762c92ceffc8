<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Client;

/**
 * Class AxialysApiClient
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Client
 */
class AxialysApiClient implements AxialysApiClientInterface
{
    public const URL = 'https://webservices.axialys.net/get/webappel/api.php';

    /**
     * @var string
     */
    protected $method;

    /**
     * @var array
     */
    protected $params = [];

    /**
     * @var mixed
     */
    protected $response;

    private string $user;

    private string $password;

    /**
     * AxialysApiClient constructor.
     */
    public function __construct(string $user, string $password)
    {
        $this->user = $user;
        $this->password = $password;
    }

    /**
     * setMethod
     *
     *
     */
    public function setMethod(string $method): AxialysApiClientInterface
    {
        $this->method = $method;

        return $this;
    }

    /**
     * addParameter
     *
     *
     */
    public function addParameter(string $key, string $value): AxialysApiClientInterface
    {
        $this->params[$key] = $value;

        return $this;
    }

    /**
     * addParameters
     *
     *
     */
    public function addParameters(array $values): AxialysApiClientInterface
    {
        $this->params = array_merge($this->params, $values);

        return $this;
    }

    /**
     * getParameters
     *
     * Prepare parameters before hitting the supplier API webservice
     * Which of course gives us a lot more code to build a simple query string otherwise it would not be fun
     */
    public function getParameters(): string
    {
        return '?' .
            http_build_query(
                array_merge(
                    [
                        'methode' => $this->method,
                        'email' => $this->user,
                        'pass' => $this->password,
                    ],
                    $this->params,
                ),
            );
    }

    /**
     * Hit the API and return a formatted/raw response from the call
     */
    public function call(): AxialysApiClientInterface
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, self::URL . $this->getParameters());
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);

        $this->response = curl_exec($ch);
        curl_close($ch);

        return $this;
    }

    /**
     * getResult
     */
    public function getResult(): array
    {
        return $this->formatOutput($this->response);
    }

    /**
     * getRawResult
     *
     * @return mixed
     */
    public function getRawResult()
    {
        return $this->response;
    }

    /**
     * Return the url to be used to hit the API
     */
    public function debug(): string
    {
        return self::URL . $this->getParameters();
    }

    /**
     * Format output if necessary, produce a php readable array
     *
     * @param $raw
     */
    protected function formatOutput($raw): array
    {
        $response = $keys = [];
        $result = explode("\n", trim($raw));
        if (count($result) > 1) {
            foreach ($result as $key => $values) {
                if ($key == 0) {
                    $keys = explode(';', $values);
                    continue;
                }

                $lines = explode(';', $values);
                $current = [];

                foreach ($lines as $i => $line) {
                    if (array_key_exists($i, $keys)) {
                        $current[$keys[$i]] = $line;
                    }
                }

                $response[] = $current;
            }
        }

        return $response;
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Api;

use Symfony\Component\Routing\Annotation\Route;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\RequestApiException;
use SonVideo\Cms\FrontOffice\AppBundle\Formatter\Http\JSendResponse;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class CategoryController
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Api
 * <AUTHOR> <<EMAIL>>
 */
class CategoryController extends BaseController
{
    /**
     * getCategoriesForBrandAction
     *
     * @throws RequestApiException
     *
     * @Route("/api/categories", requirements={"brand_id" = "\d+"}, name="get_categories_api", methods={"GET"})
     */
    public function getCategoriesForBrandAction(Request $request): JsonResponse
    {
        $this->mustBeXmlHttpRequest($request);

        $brand_id = $request->get('brand_id', null);
        if (!is_numeric($brand_id)) {
            throw new RequestApiException('brand_id is mandatory and must be an integer.');
        }

        return JSendResponse::success([
            'categories' => $this->getPommDefaultSession()
                ->getModel(CategoryModel::class)
                ->findSimpleByBrandIdAndCulture($brand_id, $request->getLocale())
                ->extract(),
        ]);
    }
}

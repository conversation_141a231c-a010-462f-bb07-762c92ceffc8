<?php
/*
 * This file is part of FO CMS package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Api;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\NavigationSchema\MegaMenuModel;
use SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema\Clickbait;
use SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema\ClickbaitContent;
use Symfony\Component\Routing\Annotation\Route;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\RequestApiException;
use SonVideo\Cms\FrontOffice\AppBundle\Formatter\Http\JSendResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class MegaMenuController extends BaseController
{
    /**
     * @throws RequestApiException
     *
     * @Route(
     *     "/mega-menu",
     *     name="api_get_mega_menu",
     *     methods={"GET"}
     * )
     */
    public function getAction(Request $request, string $_locale = APP_DEFAULT_LOCALE): JsonResponse
    {
        $this->mustBeXmlHttpRequest($request);

        /** @var MegaMenuModel $model */

        $model = $this->getPommDefaultSession()->getModel(MegaMenuModel::class);
        $mega_menu = $model->findWhere('is_active', [], 'order by display_order');
        $mega_menu = $this->addClickbaitsToMegaMenu($mega_menu->extract(), $_locale);
        $mega_menu = $model->cleanEntries($mega_menu);

        $this->addTags(['menu']);

        return JSendResponse::success(['mega_menu' => $mega_menu]);
    }

    private function addClickbaitsToMegaMenu(array $mega_menu, string $locale): array
    {
        $clickbait_manager = $this->get('cms.manager.widget.clickbait');

        return array_map(static function (array $menu) use ($locale, $clickbait_manager): array {
            $clickbaits = $clickbait_manager->resolve(
                Clickbait::CONTENT,
                $locale,
                sprintf('mega-menu-%s', $menu['mega_menu_id']),
            );

            if (count($clickbaits) > 0) {
                $menu['content'][] = array_map(static function (ClickbaitContent $clickbait) use ($locale): array {
                    $clickbait = $clickbait->toArray($locale);
                    return [
                        'type' => 'clickbait',
                        'is_active' => true,
                        'name' => $clickbait['name'],
                        'url' => $clickbait['url'],
                        'media' => $clickbait['media'],
                        'description' => $clickbait['description'],
                    ];
                }, iterator_to_array($clickbaits));
            }

            return $menu;
        }, $mega_menu);
    }
}

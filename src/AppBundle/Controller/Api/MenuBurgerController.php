<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Api;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\NavigationSchema\BurgerMenuSectionModel;
use Symfony\Component\Routing\Annotation\Route;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\RequestApiException;
use SonVideo\Cms\FrontOffice\AppBundle\Formatter\Http\JSendResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class MenuBurgerController extends BaseController
{
    /**
     * @throws RequestApiException
     *
     * @Route(
     *     "/menu-burger",
     *     name="api_get_menu_burger",
     *     methods={"GET"}
     * )
     */
    public function getAction(Request $request): JsonResponse
    {
        $this->mustBeXmlHttpRequest($request);

        /** @var BurgerMenuSectionModel $model */
        $model = $this->getPommDefaultSession()->getModel(BurgerMenuSectionModel::class);
        $sections = $model->findAll('order by display_order');

        $sections = $model->removeEmptySections($sections->extract());
        $shrinked_sections = $this->shrinkSections($sections);

        $this->addTags(['menu']);

        return JSendResponse::success(['menu_sections' => $shrinked_sections]);
    }

    /**
     * "Compress" data structure of sections by keeping only used keys and using smaller names
     */
    private function shrinkSections(array $sections): array
    {
        return array_map(
            fn($section): array => [
                'id' => $section['burger_menu_section_id'],
                'd' => $section['display_order'],
                'e' => $this->shrinkEntries($section['entries']),
                'g' => $this->shrinkGroups($section['groups']),
            ],
            $sections,
        );
    }

    private function shrinkEntries(array $entries): array
    {
        return array_map(
            fn($entry): array => [
                'id' => $entry['entry_id'],
                'd' => $entry['display_order'],
                'u' => (is_countable($entry['sections']) ? count($entry['sections']) : 0) > 0 ? null : $entry['url'],
                'i' => $entry['icon'],
                'c' => $entry['color'],
                'l' => $entry['label'],
                's' => $this->shrinkSections($entry['sections']),
            ],
            $entries,
        );
    }

    private function shrinkGroups(array $groups): array
    {
        return array_map(
            fn($group): array => [
                'id' => $group['group_id'],
                'd' => $group['display_order'],
                't' => $group['title'],
                'e' => $this->shrinkEntries($group['entries']),
            ],
            $groups,
        );
    }
}

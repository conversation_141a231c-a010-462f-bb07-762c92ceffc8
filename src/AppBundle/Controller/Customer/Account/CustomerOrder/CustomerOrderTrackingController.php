<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\Account\CustomerOrder;

use SonVideo\Cms\FrontOffice\AppBundle\Exception\RequestApiException;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\CustomerOrderTrackingManager;
use Symfony\Component\HttpFoundation\Request;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Formatter\Http\JSendResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class CustomerOrderTrackingController
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer
 * <AUTHOR> <<EMAIL>>
 */
class CustomerOrderTrackingController extends BaseController
{
    /**
     * @Route(
     *     "/api/customer-order/tracking/{customer_order_id}",
     *     requirements={"customer_order_id" = "^\d+"},
     *     name="api_customer_order_tracking_get",
     *     methods={"GET"}
     * )
     *
     * @throws RequestApiException
     *
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getAction(
        Request $request,
        int $customer_order_id,
        string $supported_culture_id = APP_DEFAULT_LOCALE
    ): JsonResponse {
        $this->mustBeXmlHttpRequest($request);

        try {
            /** @var CustomerOrderTrackingManager $manager */
            $manager = $this->get(CustomerOrderTrackingManager::class);
            $customer_order = $manager->fetchOneForCustomerId(
                $customer_order_id,
                $this->getCustomerId(),
                $supported_culture_id,
            );

            return JSendResponse::success(['customer_order' => $customer_order]);
        } catch (\InvalidArgumentException|\RuntimeException $exception) {
            $this->get('logger')->warning('customer_order_tracking', ['exception' => $exception]);

            return JSendResponse::fail(['error' => $exception->getMessage()], Response::HTTP_OK);
        } catch (\Exception $exception) {
            $this->get('logger')->error('customer_order_tracking', ['exception' => $exception]);

            return new JsonResponse(['error'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}

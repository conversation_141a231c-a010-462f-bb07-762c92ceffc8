<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\Account\CustomerOrder;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Factory\CustomerOrder\CustomerOrderContextFactory;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Account\CustomerOrder\CustomerOrderForCheckoutRetriever;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\CustomerOrderManager;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrder;
use Symfony\Component\Routing\Annotation\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\BaseAccountController;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentMethod;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class PaymentController
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\Account\CustomerOrder
 * <AUTHOR> Marniesse <<EMAIL>>
 *
 * @Route("/mon-compte/ma-commande")
 * @Security("has_role('SVD_CUSTOMER')")
 */
class PaymentController extends BaseAccountController
{
    use CustomerOrderTrait;

    public const AVAILABLE_PAYMENTS_SESSION_KEY = 'available_payment_methods_for_additional_payment';
    public const SHOW_CREATED_ORDER_MESSAGE_SESSION_KEY = 'show_created_order_message';
    public const CHECKOUT_V2_FAILURE_FLASH_BAG = 'checkout_v2_failure';

    /**
     * addPaymentAction
     *
     *
     * @Route("/{customer_order_id}/ajouter-paiement", name="customer_account_order_add_payment", methods={"GET"})
     */
    public function addPaymentAction(int $customer_order_id): Response
    {
        $customer_order = $this->getCustomerOrderForLoggedUser($customer_order_id);

        /** @var CustomerOrderManager $customer_order_manager */
        $customer_order_manager = $this->get('app.manager.customer_order_manager');

        // Cancel any payment not completed yet and reload customer order
        if (
            CustomerOrder::STATUS_AWAITING_PAYMENT === $customer_order->get('status') &&
            $customer_order_manager->getAwaitingPaymentsFrom($customer_order) !== []
        ) {
            $customer_order = $customer_order_manager->cancelAwaitingPaymentsFor($customer_order);
        }

        if (!$customer_order->needsAdditionalPayment()) {
            $this->addFlash(
                'error',
                $this->get('translator')->trans('your_order_does_not_need_additional_payment', [], 'order'),
            );

            return $this->redirectToRoute('customer_account_order_detail_get', [
                'customer_order_id' => $customer_order_id,
            ]);
        }

        // Clear additional payments
        $this->get('app.manager.customer_order.additional_payment_handler')->clearPayments();

        $show_order_created_message = $this->get('session')->get(self::SHOW_CREATED_ORDER_MESSAGE_SESSION_KEY);
        $this->get('session')->remove(self::SHOW_CREATED_ORDER_MESSAGE_SESSION_KEY);

        $checkout_failures = $this->get('session')
            ->getFlashBag()
            ->get(self::CHECKOUT_V2_FAILURE_FLASH_BAG);

        return $this->render('AppBundle:Order:add_payment_v2.html.twig', [
            'customer_order_id' => $customer_order->get('customer_order_id'),
            'show_order_created_message' => $show_order_created_message ?? false,
            'has_failed' => $checkout_failures !== [],
        ]);
    }

    /**
     * getAvailablePaymentsAction
     *
     *
     * @Route("/{customer_order_id}/liste-paiements", name="customer_account_order_list_payments", methods={"GET"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getAvailablePaymentsAction(int $customer_order_id, Request $request): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        $customer_order = $this->getCustomerOrderForLoggedUser($customer_order_id);

        $payments = $this->get('app.manager.basket_order.payment_fetcher')->getPaymentsForCustomerOrder(
            $customer_order,
            $this->getCustomerId(),
        );
        if (!$payments instanceof CollectionIterator) {
            return new JsonResponse([], Response::HTTP_NOT_FOUND);
        }

        $output = $payments->extract();

        // Put the results in session
        $this->get('session')->set(static::AVAILABLE_PAYMENTS_SESSION_KEY, $output);

        return new JsonResponse($output);
    }

    /**
     * putSvdGiftCardToBasketOrderAction
     *
     *
     * @Route("/{customer_order_id}/ajouter-carte-cadeau", name="customer_account_order_put_svd_gift_card", methods={"PUT"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function putSvdGiftCardToBasketOrderAction(int $customer_order_id, Request $request): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        $customer_order = $this->getCustomerOrderForLoggedUser($customer_order_id);

        $payload = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
        if (!isset($payload['gift_card_number'])) {
            return new JsonResponse(['status' => 'error']);
        }

        try {
            // Check if payment method is available for current order
            if (!$this->paymentMethodIsAvailableForOrder(PaymentMethod::SVD_GIFT_CARD_PAYMENT_METHOD)) {
                throw new \Exception('SVD Gift card not eligible for this basket order');
            }

            // Add gift card payment method to current order
            $result = $this->get('app.manager.customer_order.svd_gift_card_payment_handler')->addGiftCard(
                $payload['gift_card_number'],
                $customer_order,
            );

            return new JsonResponse([
                'status' => 'success',
                'needs_additional_payment' => $result,
            ]);
        } catch (\Exception $exception) {
            $this->get('logger')->error($exception->getMessage(), ['exception' => $exception, 'payload' => $payload]);

            return new JsonResponse(['status' => 'error']);
        }
    }

    /**
     * getPaymentsAddedToBasketOrder
     *
     * Must be an ajax request.
     *
     *
     * @Route("/{customer_order_id}/paiements", name="customer_account_order_get_payments", methods={"GET"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getAdditionalPayments(int $customer_order_id, Request $request): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        $this->getCustomerOrderForLoggedUser($customer_order_id);

        return new JsonResponse(
            $this->get('app.manager.customer_order.additional_payment_handler')->fetchPaymentMethods(
                $request->getLocale(),
            ),
        );
    }

    /**
     * getSummaryAction
     * Must be an ajax request
     *
     * @Route("/{customer_order_id}/resume", name="customer_account_order_get_summary", methods={"GET"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getSummaryAction(int $customer_order_id, Request $request): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        $customer_order = $this->getCustomerOrderForLoggedUser($customer_order_id);
        $balance = $customer_order->getBalance();

        $summary = [
            'nb_articles' => is_countable($customer_order->get('products'))
                ? count($customer_order->get('products'))
                : 0,
            'article_ids' => array_column($customer_order->get('products'), 'article_id'),
            'accessory_ids' => [],
            'amount_selling_price' => $customer_order->get('total_price'),
            'amount_ecotaxe' => $customer_order->get('ecotax_price'),
            'amount_paid' => $customer_order->get('total_price') - $balance,
            'balance' => $balance,
            'shipping_address' => $customer_order->get('shipping_address'),
            'billing_address' => $customer_order->get('billing_address'),
            'customer_id' => $this->getCustomerId(),
        ];

        return new JsonResponse($summary);
    }

    /**
     * @Route("/{customer_order_id}/resume-v2", name="customer_account_order_get_summary_v2", methods={"GET"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getSummaryV2Action(int $customer_order_id, Request $request): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        $customer_order = $this->get(CustomerOrderForCheckoutRetriever::class)->retrieve(
            $customer_order_id,
            $this->getCustomerId(),
        );

        return new JsonResponse($customer_order);
    }

    /**
     * putPaymentToBasketOrderAction
     *
     *
     * @Route("/{customer_order_id}/ajouter-paiement", name="customer_account_order_put_payment", methods={"PUT"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function putPaymentToBasketOrderAction(int $customer_order_id, Request $request): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        $customer_order = $this->getCustomerOrderForLoggedUser($customer_order_id);
        $payload = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);

        try {
            if (!isset($payload['payment_method_id'])) {
                throw new \InvalidArgumentException('No payment method was supplied with the request');
            }

            // Check if payment method is available for current order
            if (!$this->paymentMethodIsAvailableForOrder($payload['payment_method_id'])) {
                throw new \Exception(
                    sprintf('Payment method "%d" is not eligible for this basket order', $payload['payment_method_id']),
                );
            }

            // Add payment method to current order
            $result = $this->get('app.manager.customer_order.additional_payment_handler')->addPayment(
                $customer_order,
                $payload['payment_method_id'],
                null,
                CustomerOrderContextFactory::decorateExtraData(
                    $payload['extra_data'] ?? [],
                    $payload['payment_method_id'],
                    $request,
                ),
            );

            return new JsonResponse([
                'status' => 'success',
                'needs_additional_payment' => $result,
            ]);
        } catch (\Exception $exception) {
            $this->get('logger')->error($exception->getMessage(), ['exception' => $exception, 'payload' => $payload]);

            return new JsonResponse(['status' => 'error']);
        }
    }

    /**
     * clearPayments
     *
     *
     * @Route("/{customer_order_id}/retirer-paiements", name="customer_account_order_clear_payments", methods={"DELETE"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function clearPayments(int $customer_order_id, Request $request): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        // Check customer order belongs to logged user
        $customer_order = $this->getCustomerOrderForLoggedUser($customer_order_id);

        // Clear payments
        $this->get('app.manager.customer_order.additional_payment_handler')->clearPayments();

        return new JsonResponse([
            'status' => 'success',
            'needs_additional_payment' => $customer_order->needsAdditionalPayment(),
        ]);
    }

    /**
     * postConfirmPaymentAction
     *
     * This action sends a request to bridge to create the new payments in Legacy
     * If one of the chosen payment method is a gift card
     * it is validated and burned in this request
     *
     * @Route("/{customer_order_id}/confirmer", name="customer_account_order_confirm", methods={"POST"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function postConfirmPaymentAction(int $customer_order_id, Request $request): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        $customer_order = $this->getCustomerOrderForLoggedUser($customer_order_id);

        try {
            $this->get('app.manager.customer_order.additional_payment_handler')->submit($customer_order);

            if ($customer_order->needsAdditionalPayment() && $customer_order->containsGiftCard()) {
                $this->get('session')->set(PaymentController::SHOW_CREATED_ORDER_MESSAGE_SESSION_KEY, true);
            }

            return new JsonResponse([
                'status' => 'success',
                'url' => $this->generateUrl('order_get_payment_in_progress'),
            ]);
        } catch (\Exception $exception) {
            $this->get('logger')->error($exception->getMessage(), ['exception' => $exception]);

            return new JsonResponse(['status' => 'error'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * paymentMethodIsAvailableForOrder
     */
    private function paymentMethodIsAvailableForOrder(int $payment_method_id): bool
    {
        $available_payment_methods = $this->get('session')->get(static::AVAILABLE_PAYMENTS_SESSION_KEY) ?? [];
        $check = array_column($available_payment_methods, 'payment_method_id');

        return in_array($payment_method_id, $check, true);
    }
}

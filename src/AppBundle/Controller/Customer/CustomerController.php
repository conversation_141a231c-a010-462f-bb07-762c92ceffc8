<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer;

use PommProject\ModelManager\Exception\ModelException;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\DataFormatter\Account\InformationDataFormatter;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\Account\PasswordException;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\CustomerCreationException;
use SonVideo\Cms\FrontOffice\AppBundle\Form\Customer\AccountCreationFormType;
use SonVideo\Cms\FrontOffice\AppBundle\Form\Customer\ChangePasswordFormType;
use SonVideo\Cms\FrontOffice\AppBundle\Form\Customer\ResetPasswordFormType;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Account\EmailManager;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Account\PasswordManager;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\AccountCreator;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\CustomerManager;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\NewsletterManager;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Account;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountInformationModelLayer;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\QuoteModel;
use SonVideo\Cms\FrontOffice\Application\Manager\Feature\FeatureStatus;
use Symfony\Component\Form\FormError;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;

/**
 * Class CustomerController
 *
 * Manage interactions with customers.
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer
 * <AUTHOR> Audic <<EMAIL>>
 * @see     BaseController
 */
class CustomerController extends BaseController
{
    public const AUTH_PROVIDER_KEY = 'main';

    /**
     * loginAction
     *
     * Login form and authentication.
     *
     * @Route("/mon-compte/connexion", name="login", methods={"GET", "POST"})
     */
    public function loginAction(Request $request): Response
    {
        // Redirect to account page if already fully authenticated
        if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        $this->get('logger')->info('Authentication Attempt');

        $authentication_utils = $this->get('security.authentication_utils');

        // get the login error if there is one
        $error = $authentication_utils->getLastAuthenticationError();

        // last username entered by the user
        $last_username = $authentication_utils->getLastUsername() ?? $request->get('email');

        if (!is_null($error)) {
            $this->get('logger')->error('Authentification error', [
                'exception' => $error,
            ]);
        }

        return $this->render('AppBundle:V5/Page/Customer/login:login.html.twig', [
            'last_username' => $last_username,
            'error' => $error,
        ]);
    }

    /**
     * logoutAction
     *
     * Disconnect authenticated customer.
     *
     * @Route("/mon-compte/deconnexion", name="logout", methods={"GET"})
     */
    public function logoutAction(): void
    {
    }

    /**
     * createAction
     *
     *
     *
     * @throws  \Exception
     *
     * @Route("/mon-compte/creation", name="customer_create", methods={"GET", "POST"})
     */
    public function createAction(Request $request): Response
    {
        $_locale = $request->getLocale();
        $form = $this->createForm(AccountCreationFormType::class, null, ['locale' => $request->getLocale()]);
        $form->handleRequest($request);

        $form_data = $form->getData();
        $login_path = $this->generateUrl('login', ['email' => $form_data['email'] ?? null]);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Create new customer account
                $account = $this->get('app.manager.account_creator')->createActiveAccount(
                    $form_data['email'],
                    $form_data['password'] ?? '',
                    $_locale,
                );
                // register to newsletter if wanted (will silently fail if needed)
                if ($form_data['want_newsletter']) {
                    $result = $this->get(NewsletterManager::class)->subscribe($account->get('email'), 'create-account');
                    $form_data['preferences'] = ['newsletter' => $result];
                }

                // set account information

                /** @var AccountInformationModelLayer $customer_model_layer */
                $customer_model_layer = $this->getPommDefaultSession()->getModelLayer(
                    AccountInformationModelLayer::class,
                );

                $customer_model_layer
                    ->setRpcClient($this->get('sonvideo.synapps.rpc_client'))
                    ->createOrUpdate(
                        $account->get('customer_id'),
                        (new InformationDataFormatter())->format($form_data),
                    );

                $this->get(CustomerManager::class)->autologUser($account);

                $this->addFlash('success', $this->trans('customer_confirm_success', [], 'account'));

                if ($request->cookies->has('euconsent')) {
                    $consents = json_decode($request->cookies->get('euconsent'), true, 512, JSON_THROW_ON_ERROR);
                    if (isset($consents['bloomreach']) && $consents['bloomreach']) {
                        $request
                            ->getSession()
                            ->getFlashBag()
                            ->add('identify', [
                                'customer_id' => $account->get('customer_id'),
                                'email' => $account->get('email'),
                            ]);
                    }
                }

                if ($request->request->has('_target_path')) {
                    return new RedirectResponse($request->get('_target_path'));
                }

                if ($this->get('session')->has('_security.main.target_path')) {
                    return new RedirectResponse($this->get('session')->get('_security.main.target_path'));
                }

                return $this->redirectToRoute('customer_account_customer_area');
            } catch (CustomerCreationException $e) {
                $message = null;
                switch ($e->getCode()) {
                    case AccountCreator::CONFIRM_ACCOUNT_PASSWORD_ERROR_CODE:
                        $form
                            ->get('password')
                            ->addError(
                                new FormError(
                                    'Le mot de passe saisi n\'est pas suffisamment sécurisé. Nous vous recommandons le choix d\'un mot de passe plus complexe.',
                                ),
                            );
                        break;
                    case AccountCreator::CREATION_ACCOUNT_EMAIL_EXIST:
                        $message = $this->trans(
                            'customer.subscribe.email_exists',
                            ['%url%' => $login_path],
                            'validator',
                        );
                        break;
                    default:
                        $message = $this->trans('customer_confirm_unknown_error', [], 'account');
                }

                if ($message) {
                    $this->addFlash('error', $message);
                }
            } catch (\Exception $e) {
                $this->get('logger')->error('Account creation error', ['exception' => $e]);
                $this->addFlash('error', $this->trans('customer_creation_error', [], 'account'));

                return $this->redirectToRoute('customer_create');
            }
        }

        return $this->render('AppBundle:V5/Page/Customer/register:register.html.twig', [
            'form' => $form->createView(),
            'login_path' => $login_path,
        ]);
    }

    /**
     * confirmAccountAction
     *
     * If token is valid, displays a form to set the password.
     * Then a rpc call is called in order to set the password and set the new account role.
     * Finally we authenticate the user is all is correct.
     *
     *
     * @return  RedirectResponse|Response
     * @Route(
     *     "/mon-compte/confirmation/{token}",
     *     requirements={"token" = "[a-zA-Z0-9-]+"},
     *     name="customer_creation_confirm",
     *     methods={"GET", "POST"}
     * )
     */
    public function confirmAccountAction(string $token): RedirectResponse
    {
        // Check if token exists and if it is still valid
        if (!$this->get('app.manager.token_manager')->isValid($token)) {
            return $this->confirmAccountExpiredAndRedirect();
        }

        try {
            $account = $this->getUser();
            if (!$account instanceof Account) {
                $account = $this->get(CustomerManager::class)->autologUserWithToken($token);
            }

            if (!$account instanceof Account) {
                throw new CustomUserMessageAuthenticationException(
                    sprintf('Customer is not logged and can not be found with token "%s"', $token),
                );
            }

            // set account information
            $this->getAccountInformation()->setConfirmEmail();

            $this->inform(EmailManager::CONFIRM_EMAIL_SYNAPPS_SUBJECT, [
                'email' => $account->get('email'),
                'customer' => $account->get('customer_id'),
            ]);

            $this->addFlash('success', $this->trans('email_confirm_success', [], 'account'));

            return $this->redirectToRoute('customer_account_customer_area');
        } catch (\Exception $e) {
            $this->addFlash('error', $this->trans('customer_confirm_unknown_error', [], 'account'));
            $this->get('logger')->error('Account confirmation error', ['exception' => $e]);
        }

        return $this->redirectToRoute('login');
    }

    /**
     * confirmAccountExpiredAndRedirect
     */
    protected function confirmAccountExpiredAndRedirect(): RedirectResponse
    {
        $this->addFlash('warning', $this->trans('customer_confirm_token_error', [], 'account'));

        return $this->redirectToRoute('customer_account_customer_area');
    }

    /**
     * resetPasswordAction
     *
     *
     *
     * @Route("/mon-compte/reinitialisation-mot-de-passe", name="customer_reset_password", methods={"GET", "POST"})
     */
    public function resetPasswordAction(Request $request): Response
    {
        $form = $this->createForm(ResetPasswordFormType::class, null, ['locale' => $request->getLocale()]);
        $form->handleRequest($request);

        $response = null;

        if ($form->isSubmitted() && $form->isValid()) {
            $email = $form->getData()['email'];

            // Call RPC to ask to renew password
            try {
                $rpc = $this->get('sonvideo.synapps.rpc_client')->call('bo-cms', 'customer:ask_renew_password', [
                    $email,
                    $this->container
                        ->get('app.router_manager')
                        ->getRenewPasswordTemplateUrl($this->get('translator')->getLocale()),
                    $this->get('translator')->getLocale(),
                ]);

                // If RPC result status is false it means that the token has not been created
                // (account should be deactivated)
                if ($rpc['result']['status'] == false) {
                    $this->addFlash(
                        'error',
                        $this->trans(
                            'error_renew_password_account_deactivated',
                            [
                                'hotline_phone' => $this->getParameter('telephone')['hotline_numero'],
                                'hotline_price' => $this->getParameter('telephone')['hotline_mention'],
                            ],
                            'account',
                        ),
                    );
                } else {
                    // Token generate and email sent, redirect to a confirmation page
                    $response = $this->render('AppBundle:V5/Page/Customer/login:confirm_reset_password.html.twig', [
                        'email' => $email,
                    ]);
                }
            } catch (\Exception $e) {
                $this->addFlash('error', $this->trans('error_renew_password_request', [], 'account'));
            }
        } elseif (!$form->isSubmitted()) {
            $form->setData(['email' => $request->get('email')]);
        }

        return $response ??
            $this->render('AppBundle:V5/Page/Customer/login:reset_password.html.twig', [
                'reset_password_form' => $form->createView(),
            ]);
    }

    /**
     * changePasswordAction
     *
     * Change password form. Called from a reinitialisation link.
     *
     *
     *
     * @Route(
     *     "/mon-compte/changer-mot-de-passe/{token}",
     *     requirements={"token" = "[a-zA-Z0-9-]+"},
     *     name="customer_change_password",
     *     methods={"GET", "POST"}
     * )
     */
    public function changePasswordAction(string $token, Request $request): Response
    {
        if (!$this->get('app.manager.token_manager')->isValid($token)) {
            $this->addFlash('error', $this->trans('reset_token_invalid', [], 'account'));

            return $this->redirectToRoute('customer_account_customer_area');
        }

        $form = $this->createForm(ChangePasswordFormType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $password = $form->getData()['password'];
                $email = $this->get('app.manager.account.password_manager')->setPassword($token, $password);
                $this->logUser($request, $email, $password)->addFlash(
                    'success',
                    $this->trans('password_changed', [], 'account'),
                );

                return $this->redirectToRoute('customer_account_customer_area');
            } catch (PasswordException $e) {
                $this->addFlash('error', $this->trans($e->getMessage(), [], 'account'));

                if ($e->getCode() !== PasswordManager::SET_PASSWORD_INSECURE_RPC_RETURN) {
                    return $this->redirectToRoute('customer_reset_password');
                }
            }
        }

        return $this->render('AppBundle:V5/Page/Customer/login:change_password.html.twig', [
            'change_password_form' => $form->createView(),
        ]);
    }

    /**
     * apiAction
     *
     * @Route("/api/customer-info", name="api_customer_info", methods={"GET"})
     *
     *
     * @throws ModelException
     */
    public function apiAction(Request $request): JsonResponse
    {
        $user = $this->getUser();

        $features = $this->get(FeatureStatus::class)->getActiveFeaturesForJs();
        if (!$user instanceof Account) {
            return new JsonResponse(['user' => null, 'features' => $features]);
        }

        $customer_information = $this->getAccountInformation();

        $impersonation_username = $request->getSession()->get(SESSION_KEY_IMPERSONATION_LOGIN, false)
            ? $request->getSession()->get(SESSION_KEY_IMPERSONATION_USERNAME)
            : null;

        $has_newsletter =
            isset($customer_information['preferences']['newsletter']) &&
            $customer_information['preferences']['newsletter'] === true;
        $show_cancelled_orders =
            isset($customer_information['preferences']['show_cancelled_orders']) &&
            $customer_information['preferences']['show_cancelled_orders'] === true;

        return new JsonResponse([
            'user' => [
                'fullname' => $user->getFullName(),
                'firstname' => $user->getFirstName(),
                'lastname' => $user->getLastName(),
                'username' => $user->getUsername(),
                'civility' => $customer_information->get('title'),
                'phone' => $customer_information->get('cellphone') ?? $customer_information->get('phone'),
                'is_authenticated_fully' => $this->isGranted('IS_AUTHENTICATED_FULLY'),
                'impersonation_username' => $impersonation_username,
                'has_newsletter' => $has_newsletter,
                'show_cancelled_orders' => $show_cancelled_orders,
            ],
            'quotes' => [
                'count' => $this->getPommDefaultSession()
                    ->getModel(QuoteModel::class)
                    ->getCustomerActiveUnorderedQuotesCount($this->getCustomerId()),
            ],
            'features' => $features,
        ]);
    }

    /**
     * apiOptionsAction
     *
     * OPTIONS method used by google translate (for example) to determine the available methods of the route.
     *
     * @Route("/api/customer-info", name="options_api_customer_info", methods={"OPTIONS"})
     */
    public function apiOptionsAction(): JsonResponse
    {
        $response = new JsonResponse('ok');
        $response->headers->add([
            'Allow' => 'OPTIONS, GET, HEAD',
            'Access-Control-Allow-Method' => 'OPTIONS, GET',
        ]);

        return $response;
    }

    /**
     * sendValidationMailAction
     *
     * @Route("/api/send-validation-mail", name="api_customer_validation_mail", methods={"POST"})
     *
     *
     */
    public function sendValidationMailAction(Request $request): JsonResponse
    {
        $this->mustBeXmlHttpRequest($request);

        $user = $this->getCustomer();
        $email = $user->get('email');
        $result = false;

        // account must be in confirmation waiting status
        if ($user instanceof Account && $email !== '' && $user->getRoles() === []) {
            try {
                $this->get('app.manager.account_creator')->sendNewValidationMail($email, $request->getLocale());
                $result = true;
            } catch (CustomerCreationException $e) {
                $this->get('logger')->error('customer.send_new_validation_mail', [
                    'account' => $user,
                    'exception' => $e->getMessage(),
                ]);

                return new JsonResponse([], Response::HTTP_INTERNAL_SERVER_ERROR);
            }
        }

        return new JsonResponse(['status' => $result]);
    }

    /**
     * getCustomer
     *
     * @return Account
     */
    protected function getCustomer(): ?Account
    {
        $user = $this->getUser();

        if (!$user instanceof Account) {
            return null;
        }

        return $user;
    }
}

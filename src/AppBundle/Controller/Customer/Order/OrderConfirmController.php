<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\Order;

use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\Account\CustomerOrder\PaymentController;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder\PaymentHandler;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Paypal\ExpressCheckoutException;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Paypal\ExpressCheckOutManager;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMedia;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMediaModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\ShipmentMethodModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrder;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrderModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentMethod;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentMethodModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\SystemSchema\ParameterModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;

/**
 * Class OrderConfirmController
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\Order
 * <AUTHOR> Marniesse <<EMAIL>>
 */
class OrderConfirmController extends BaseController
{
    /**
     * This is an in between page on which we show waiting block
     * While we interrogate bridge to know what to do with the payments
     *
     * @Route("/ma-commande/paiement-en-cours", name="order_get_payment_in_progress", methods={"GET"})
     *
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getPaymentInProgressAction(): Response
    {
        // Get customer order
        $customer_order = $this->getLastCustomerOrderAwaitingPayment();

        // No order awaiting payment for current customer
        if ($customer_order === null) {
            $this->addFlash('warning', $this->get('translator')->trans('no_awaiting_payment_order', [], 'order'));

            return $this->redirectToRoute('customer_account_order_list');
        }

        $canonical_url = $this->generateUrl('order_get_payment_in_progress');

        return $this->render('AppBundle:Order:processing.html.twig', [
            'customer_order' => $customer_order,
            'canonical_url' => $canonical_url,
        ]);
    }

    /**
     * This method interrogate bridge about an order payments
     * Must be an ajax request from a logged-in user
     *
     * @Route("/ma-commande/traitement-paiement", name="order_get_handle_payment", methods={"GET"})
     *
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getHandlePaymentAction(Request $request): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        try {
            $customer_order = $this->getLastCustomerOrderAwaitingPayment();

            if ($customer_order === null) {
                throw new \Exception(
                    sprintf('There are no order awaiting payment for customer \'%d\'', $this->getCustomerId()),
                );
            }

            $customer_order_id = $customer_order->get('customer_order_id');

            $return_url = sprintf(
                '%s%s',
                $request->getSchemeAndHttpHost(),
                str_replace(
                    '000',
                    '{payment_method_id}',
                    $this->get('router')->generate('return_payment_result', ['payment_method_id' => '000']),
                ),
            );
            /** @var PaymentHandler $handler */
            $handler = $this->get('app.manager.basket_order.payment_handler');
            $result = $handler->handlePaymentForCustomerOrder($customer_order_id, $this->getCustomerId(), $return_url);

            $action = 'confirm';
            $payload = null;
            if (isset($result['auto_form'])) {
                $action = 'auto_form';
                $payload = $result['auto_form'];
            }

            if (isset($result['redirect_to'])) {
                $action = 'redirect_to';
                $payload = $result['redirect_to'];
            }

            return new JsonResponse([
                'status' => 'success',
                'action' => $action,
                'payload' => $payload,
                'customer_order_id' => $customer_order_id,
            ]);
        } catch (\Exception $exception) {
            $this->get('logger')->error($exception->getMessage(), ['exception' => $exception]);

            return new JsonResponse(['status' => 'error'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * This page is used for auto_form bank redirection
     *
     * @Route("/ma-commande/redirection", name="order_get_redirect_page", methods={"GET"})
     *
     * @Security("has_role('SVD_CUSTOMER')")
     * @deprecated remove after deployment
     */
    public function getOrderRedirectionPageAction(): Response
    {
        $customer_order = $this->getLastCustomerOrderAwaitingPayment();

        // The customer order is neither awaiting payment or exists, redirect to customer area page
        if ($customer_order === null) {
            $this->addFlash('warning', $this->get('translator')->trans('no_awaiting_payment_order', [], 'order'));

            return $this->redirectToRoute('customer_account_order_list');
        }

        $key = sprintf('auto_form_%d', $customer_order->get('customer_order_id'));

        if (
            !$this->get('session')
                ->getFlashBag()
                ->has($key)
        ) {
            return $this->redirectToRoute('customer_account_customer_area');
        }

        return $this->render('AppBundle:Order:redirect.html.twig', ['key' => $key]);
    }

    /**
     * getOrderConfirmationAfterPayment
     *
     * This action is called once payment have been processed whether we're waiting for the payment (bank transfer)
     * of as landing page returning from third party payment handler (Ogone, Presto, etc...)
     *
     * We check request parameters to notify bridge if necessary so that it can update the order status
     *
     * @Route(
     *     "/ma-commande/confirmation/{result}",
     *     name="get_order_confirmation",
     *     requirements={"result" = "succes|echec"},
     *     methods={"GET", "POST"}
     * )
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getOrderConfirmationAfterPayment(string $result): Response
    {
        $customer_order = $this->getLastCustomerOrderAwaitingPayment();
        // The customer order is neither awaiting payment or exists, redirect to customer area page
        if ($customer_order === null) {
            $this->addFlash('warning', $this->get('translator')->trans('no_awaiting_payment_order', [], 'order'));

            return $this->redirectToRoute('customer_account_order_list');
        }

        try {
            // Request an order status update from bridge
            $customer_order = $this->get('app.manager.customer_order_manager')->updateStatusAfterPayment(
                $customer_order->get('customer_order_id'),
                $result === 'succes',
            );

            $payment_method_ids = array_column($customer_order->get('payments') ?? [], 'payment_method_id');
            $payment_methods = [];
            if ($payment_method_ids !== []) {
                $payment_methods =
                    $this->getPommDefaultSession()
                        ->getModel(PaymentMethodModel::class)
                        ->findWhere(Where::createWhereIn('payment_method_id', $payment_method_ids))
                        ->extract() ?? [];
            }

            $customer_order->set('payment_methods', $payment_methods);
        } catch (\Exception $exception) {
            $this->get('logger')->error($exception->getMessage(), ['exception' => $exception]);

            // Redirect to customer order detail page
            return $this->redirectToRoute('customer_account_order_detail_get', [
                'customer_order_id' => $customer_order->get('customer_order_id'),
            ]);
        }

        try {
            if ($result === 'succes') {
                $this->get('app.manager.article_unpublished_article_manager')->checkDestockToUnpublished(
                    $customer_order,
                );
            }
        } catch (\Exception $e) {
            $this->get('logger')->error($e->getMessage(), ['e' => $e]);
        }

        try {
            if ($result === 'succes' && $customer_order->needsAdditionalPayment()) {
                if ($customer_order->get('payments') === []) {
                    $this->handleConfirmErrorV2($customer_order);
                }

                return $this->redirectToRoute('customer_account_order_add_payment', [
                    'customer_order_id' => $customer_order->get('customer_order_id'),
                ]);
            }
        } catch (\Exception $e) {
            $this->get('logger')->error($e->getMessage(), ['e' => $e]);
            return new JsonResponse(['status' => 'error'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        $paid_orders = $this->getPaidOrdersForCustomer();
        $customer_order->set('new_customer', (is_countable($paid_orders) ? count($paid_orders) : 0) <= 0);

        return $result === 'succes'
            ? $this->handleConfirmSuccessV2($customer_order, $payment_methods)
            : $this->handleConfirmErrorV2($customer_order);
    }

    private function handleConfirmSuccessV2(CustomerOrder $customer_order, array $payment_methods): ?Response
    {
        $shipment_methods = $this->getPommDefaultSession()
            ->getModel(ShipmentMethodModel::class)
            ->fetchWithList([
                [
                    'shipment_method_id' => $customer_order->get('shipment_method_id'),
                    'cost' => $customer_order->get('shipping_price'),
                ],
            ]);

        $this->decorateConfirmOrder($customer_order);

        $payment_method = end($payment_methods);

        $customer_service = $this->getPommDefaultSession()
            ->getModel(ParameterModel::class)
            ->extractLastAncestorsFromPath('customer_service');

        $customer_phone =
            $customer_order->get('billing_address')['cellphone'] ??
            ($customer_order->get('shipping_address')['cellphone'] ??
                ($customer_order->get('billing_address')['phone'] ??
                    $customer_order->get('shipping_address')['phone']));

        return $this->render('AppBundle:Order:order_confirmation/confirmation-v5.html.twig', [
            'customer_order' => $customer_order,
            'payment_method' => $payment_method,
            'countries' => $this->getCountryCodes(),
            'has_newsletter' => $this->hasNewsletter(),
            'shipment_method' => $shipment_methods->current()->extract(),
            'is_delayed_payment_method' =>
                in_array(trim($payment_method['code']), ['VIR', 'COT', 'TEL']) ||
                $payment_method['payment_group_id'] === 'gift-card',
            'customer_email' => $this->getUser()->get('email'),
            'customer_service' => $customer_service,
            'customer_phone' => $customer_phone,
        ]);
    }

    private function handleConfirmErrorV2(CustomerOrder $customer_order): ?Response
    {
        $this->addFlash(PaymentController::CHECKOUT_V2_FAILURE_FLASH_BAG, 'handleConfirmErrorV2');

        return $this->redirectToRoute('customer_account_order_add_payment', [
            'customer_order_id' => $customer_order->get('customer_order_id'),
        ]);
    }

    private function decorateConfirmOrder(CustomerOrder $customer_order): void
    {
        $customer_order_products = $customer_order->get('products');
        $article_ids = array_column($customer_order_products, 'article_id');

        $article_medias =
            $article_ids === []
                ? []
                : $this->getPommDefaultSession()
                    ->getModel(ArticleMediaModel::class)
                    ->findMainImageMediaForArticles($article_ids)
                    ->extract();

        foreach ($customer_order_products as $key => $customer_order_product) {
            $found_medias = array_filter(
                $article_medias,
                fn($media): bool => $customer_order_product['article_id'] === $media['article_id'],
            );
            $found_media = array_values($found_medias)[0] ?? ['url' => ArticleMedia::ARTICLE_NO_IMAGE];

            $customer_order_products[$key]['media_url'] = $found_media['url'];
        }

        $customer_order->set('products', $customer_order_products);
    }

    /**
     * getOrderConfirmationAfterPaypalPayment
     *
     * This action is called once payment have been processed whether we're waiting for the payment (bank transfer)
     * of as landing page returning from third party payment handler (Paypal)
     *
     * We check request parameters to notify bridge if necessary so that it can update the order status
     *
     * @Route("/ma-commande/confirmation/paypal/succes", name="get_paypal_order_confirmation", methods={"GET", "POST"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function getOrderConfirmationAfterPaypalPayment(Request $request): Response
    {
        $customer_order = $this->getLastCustomerOrderAwaitingPayment();

        // The customer order is neither awaiting payment or exists, redirect to customer area page
        if ($customer_order === null) {
            $this->addFlash('warning', $this->get('translator')->trans('no_awaiting_payment_order', [], 'order'));

            return $this->redirectToRoute('customer_account_order_list');
        }

        $token = $request->query->get('token', null);

        try {
            // Check if order has Paypal Payment
            $payments = array_filter(
                $customer_order->get('payments'),
                fn($payment): bool => $payment['code'] == PaymentMethod::PAYPAL_PAYMENT_METHOD,
            );

            // If no or multiple payments throw an exception
            if (count((array) $payments) !== 1) {
                throw new \UnexpectedValueException(
                    sprintf(
                        'Payment for customer order n° %d can not be validated. Found %d payment(s), only one must be provided.',
                        $customer_order->get('customer_order_id'),
                        count((array) $payments),
                    ),
                );
            }

            $this->get(ExpressCheckOutManager::class)->doPaypalPaymentExpressCheckout($token);
        } catch (ExpressCheckoutException $exception) {
            $this->get('logger')->info('Paypal payment error', [
                'token' => $token,
                'customer_order' => $customer_order,
            ]);

            return $this->getOrderConfirmationAfterPayment('echec');
        } catch (\Exception $exception) {
            $this->get('logger')->error($exception->getMessage(), ['exception' => $exception]);

            // Redirect to customer order detail page
            return $this->redirectToRoute('customer_account_order_detail_get', [
                'customer_order_id' => $customer_order->get('customer_order_id'),
            ]);
        }

        return $this->getOrderConfirmationAfterPayment('succes');
    }

    /**
     * This action is called once payment has been processed through the third party Sips Paypage (= Presto).
     * We check that the payload has not been altered and the responseCode before processing as usual.
     *
     * @Route("/ma-commande/confirmation/sips", name="get_order_confirmation_after_sips", methods={"POST"})
     * @Security("has_role('SVD_CUSTOMER')")
     * @deprecated
     */
    public function getOrderConfirmationAfterSipsPayment(Request $request): Response
    {
        $data = $request->request->get('Data');
        $seal = $request->request->get('Seal');
        // default algorithm is the one we use when sending the request to Sips, cf. Pmt_atos class in backoffice
        $seal_algorithm = strtoupper($request->request->get('SealAlgorithm', 'HMAC-SHA-256'));
        $encode = strtolower($request->request->get('Encode', ''));

        // check mandatory fields
        if ($data === null || $seal === null) {
            $this->get('logger')->error('Miss mandatory field from SIPS', ['data' => $data, 'seal' => $seal]);

            return $this->getOrderConfirmationAfterPayment('echec');
        }

        // check the seal
        $hash = '';
        $secret_key = $this->getParameter('sips_paypage.secret_key');
        switch ($seal_algorithm) {
            case 'SHA-256':
                $hash = hash('sha256', $data . $secret_key);
                break;
            case 'HMAC-SHA-256':
                $hash = hash_hmac('sha256', $data, $secret_key);
                break;
            case 'HMAC-SHA-512':
                $hash = hash_hmac('sha512', $data, $secret_key);
                break;
        }
        if ($seal !== $hash) {
            $this->get('logger')->error('Seal does not match hashed data', [
                'data' => $data,
                'seal_algorithm' => $seal_algorithm,
                'seal' => $seal,
            ]);

            return $this->getOrderConfirmationAfterPayment('echec');
        }

        // decode and extract usable data
        if ($encode === 'base64') {
            $data = base64_decode($data);
        }
        $payload = array_reduce(
            explode('|', $data),
            function (array $payload, $key_value) {
                $elements = explode('=', $key_value);
                $payload[trim($elements[0])] = trim($elements[1]);

                return $payload;
            },
            [],
        );

        // check response code
        // https://documentation.sips.worldline.com/en/WLSIPS.001-GD-Data-dictionary.html#Sips.001_DD_en-Value-responseCode_
        $response_code = $payload['responseCode'] ?? 'undefined';
        if ($response_code !== '00') {
            switch ($response_code) {
                case '17':
                    $this->get('logger')->info('Customer cancellation', ['payload' => $payload]);
                    break;
                default:
                    $this->get('logger')->error(sprintf('SIPS sent a response code "%s"', $response_code), [
                        'payload' => $payload,
                    ]);
                    break;
            }

            return $this->getOrderConfirmationAfterPayment('echec');
        }

        return $this->getOrderConfirmationAfterPayment('succes');
    }

    /**
     * getLastCustomerOrderAwaitingPayment
     *
     * @return array|mixed|null
     */
    private function getLastCustomerOrderAwaitingPayment()
    {
        return $this->getPommDefaultSession()
            ->getModel(CustomerOrderModel::class)
            ->findLastAwaitingPayment($this->getCustomerId());
    }

    /**
     * getPaidOrdersForCustomer
     *
     * Find all the orders with a validated payment for the current customer
     *
     * @return array|mixed|null
     */
    private function getPaidOrdersForCustomer()
    {
        return $this->getPommDefaultSession()
            ->getModel(CustomerOrderModel::class)
            ->findPaidOrdersForCustomer($this->getCustomerId());
    }
}

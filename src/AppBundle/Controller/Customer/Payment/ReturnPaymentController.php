<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\Payment;

use Symfony\Component\Routing\Annotation\Route;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\ThirdPartyResult\PaymentResultHandler;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class ReturnPaymentController
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\Payment
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 * @deprecated use RedirectBackAfterPaymentController instead. Should be removed after FullCB/Payment API v1 is decommissioned
 */
class ReturnPaymentController extends Controller
{
    /**
     * payment_method_id is in string in order to construct a pattern
     *
     * @Route(
     *     "/ma-commande/retour-paiement/{payment_method_id}",
     *     name="return_payment_result",
     *     requirements={"payment_method_id" = "^\d+$"},
     *     methods={"GET", "POST"}
     * )
     */
    public function handleResultAction(int $payment_method_id, Request $request): RedirectResponse
    {
        try {
            if ($this->get(PaymentResultHandler::class)->isInSuccess($payment_method_id, $request)) {
                return $this->redirectToRoute('get_order_confirmation', ['result' => 'succes']);
            }

            // At this step, the customer cancel its payment or third party decides to decline its request.
            // So the payment is in error, but, it's not an error from our side.
            $this->get('logger')->error(
                sprintf('[PAYMENT v1] Unsuccessful payment return for payment_method_id "%d".', $payment_method_id),
            );
        } catch (\Exception $e) {
            $wrapped_exception = new \UnexpectedValueException(
                sprintf('[PAYMENT v1] Payment return error for payment_method_id "%d".', $payment_method_id),
                $e->getCode(),
                $e,
            );

            $this->get('logger')->error($e->getMessage(), [
                'exception' => $wrapped_exception,
            ]);
        }

        return $this->redirectToRoute('get_order_confirmation', ['result' => 'echec']);
    }
}

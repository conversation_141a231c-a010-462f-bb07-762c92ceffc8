<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Event;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class LandingController
 *
 * @package     SonVideo\Cms\FrontOffice\AppBundle\Controller\Event
 * @copyright   2017 Son-Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 * @see         BaseController
 */
class LandingController extends BaseController
{
    /**
     * showEventsAction
     *
     *
     * @Route("/evenements", name="show_events", methods={"GET"})
     */
    public function showEventsAction(): Response
    {
        $this->addTags(['events']);

        return $this->render('AppBundle:Event:show_events.html.twig', [
            'canonical_url' => $this->generateUrl('show_events'),
        ]);
    }
}

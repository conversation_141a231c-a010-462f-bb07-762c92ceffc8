<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Event\PavInscription;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class LandingController
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Event\PavInscription
 * <AUTHOR> <<EMAIL>>
 */
class LandingController extends BaseController
{
    /**
     * pavInscriptionAction
     *
     * Display landing page for PAV Inscription
     *
     *
     * @Route("/invitation-offerte-paris-audio-video-show", name="pav_inscription", methods={"GET"})
     */
    public function pavInscriptionAction(): Response
    {
        return $this->render('AppBundle:Event/PavInscription:show_pav_inscription.html.twig');
    }
}

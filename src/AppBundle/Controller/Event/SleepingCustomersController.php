<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Event;

use Symfony\Component\Routing\Annotation\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class SleepingCustomersController
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Event
 */
class SleepingCustomersController extends BaseController
{
    /**
     * showSleepingCustomersAction
     *
     * @Route("/offre-clients", name="show_sleeping_customers_page", methods={"GET"})
     * @Security("has_role('SVD_CUSTOMER')")
     */
    public function showSleepingCustomersAction(): Response
    {
        $this->addTags(['events']);

        // Retrieve account information
        $account = $this->getUser();
        $account_information = $this->getAccountInformation();
        $has_newsletter =
            isset($account_information['preferences']['newsletter']) &&
            $account_information['preferences']['newsletter'] === true;

        $response = $this->render(
            'AppBundle:Event:sleeping_customers.html.twig',
            array_merge($account->extract(), [
                'has_newsletter' => $has_newsletter,
                'promo_code' => 'YRJ56PM23XLZ',
            ]),
        );
        $response->headers->addCacheControlDirective('no-cache', true);

        return $response;
    }
}

<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller;

use PommProject\Foundation\Inflector;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class MarketingRedirectionController
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\Controller
 * @copyright 2017 Son-Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 * @see       BaseController
 */
class MarketingRedirectionController extends BaseController
{
    /**
     * redirectionAction
     *
     *
     *
     * @Route("/marketing-redirection", name="marketing_redirection", methods={"GET"}))
     * @throws \InvalidArgumentException
     */
    public function redirectionAction(Request $request): RedirectResponse
    {
        $params = $request->query->all();

        // extract redirect url - default to homepage
        $parsed_url = parse_url($params['url'] ?? '/');
        $redirect_url = $parsed_url['path'] ?? '/';

        // Extract first parsed query string if the url contains a second question mark (path?url=toto?myparam=value)
        $internal_query_string = [];
        [$key, $value] = explode('=', $parsed_url['query'] ?? '=');
        if (strlen($value) > 0) {
            $internal_query_string[$key] = $value;
        }

        // Keep the query string if any, merge with extracted internal query string
        $query_string = http_build_query(array_merge(array_diff_key($params, ['url' => true]), $internal_query_string));
        // prefix with the question mark separator
        $query_string = strlen($query_string) > 0 ? sprintf('?%s', $query_string) : '';

        // Use it to create redirect response
        $response = new RedirectResponse($redirect_url . $query_string);
        $response->headers->addCacheControlDirective('no-cache', true);

        // Extract others GET parameters and set corresponding cookie if method exists
        // Does not reuse the $redirect_url parameter cause the array_diff needs the original value
        $cookies_params = array_diff($params, [$params['url'] ?? '/']);
        foreach ($cookies_params as $cookie_id => $cookie_value) {
            $cookie_method = sprintf('setCookieFor%s', Inflector::studlyCaps($cookie_id));
            if (method_exists($this, $cookie_method)) {
                $this->{$cookie_method}($response, $cookie_value);
            } else {
                $this->get('logger')->error(
                    sprintf('No method exists for set cookie "%s', $cookie_id),
                    ['params' => $params],
                );
            }
        }

        return $response;
    }

    /**
     * setCookieForTduid
     *
     * Set cookies for Tradedoubler.
     *
     *
     * @throws \InvalidArgumentException
     */
    private function setCookieForTduid(Response &$response, string $value): self
    {
        $expiration_time = time() + 60 * 60 * 24 * 15; // Expires 15 days

        $response->headers->setCookie(new Cookie('TRADEDOUBLER', 'TD', $expiration_time));
        $response->headers->setCookie(new Cookie('tradedoubler_id', $value, $expiration_time));

        return $this;
    }

    /**
     * setCookieForAffId
     *
     * Do nothing on affId, as tduuid reacts instead.
     */
    private function setCookieForAffId(): self
    {
        return $this;
    }
}

<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Promotion\Cetelem;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class LandingController
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Controller\Promotion\Cetelem
 * <AUTHOR> <<EMAIL>>
 */
class LandingController extends BaseController
{
    /**
     * cetelemAction
     *
     * Display landing page for Cetelem
     *
     *
     * @Route("/faq/cetelem", name="cetelem", methods={"GET"})
     */
    public function cetelemAction(): Response
    {
        return $this->render('AppBundle:Promotion:cetelem_faq.html.twig');
    }
}

<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Rpc;

use SonVideo\RpcBundle\Controller\ApiRpcController;

/**
 * IndexController
 *
 * @package   CMS Front-Office
 * @copyright 2016 Son-Video Distribution
 * <AUTHOR> <<EMAIL>>
 */

class DefaultController extends ApiRpcController
{
    /**
     * executeDiscover
     *
     * Discover method for this API.
     */
    public function executeDiscover(): array
    {
        return array_merge(['discover' => []], RouteController::getRpcMethods());
    }
}

<?php
/*
  This file is part of CMS Back-Office package.
  (c) 2016 Son-Video Distribution
  For the full copyright and license information, please view the LICENSE
  file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Stand;

use PommProject\ModelManager\Model\CollectionIterator;
use Symfony\Component\Routing\Annotation\Route;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\Guide\LoadGuideTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\Search\SearchControllerTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\Stand;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18n;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18nModel;
use SonVideo\Cms\FrontOffice\CmsBundle\Controller\TemplateResolverControllerTrait;
use SonVideo\Cms\FrontOffice\CmsBundle\StructuredData\Breadcrumb;
use SonVideo\Cms\FrontOffice\CmsBundle\StructuredData\StandItemList;
use SonVideo\Cms\FrontOffice\CmsBundle\StructuredData\StructuredDataTrait;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context\StandContext;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * StandController
 *
 * @package     SonVideo\Cms\FrontOffice\AppBundle\Controller\Stand
 * @copyright   2016 Son-Video Distribution
 * <AUTHOR> HUBERT <<EMAIL>>
 * @see         BaseController
 */
class StandController extends BaseController
{
    use TemplateResolverControllerTrait;
    use LoadGuideTrait;
    use StandControllerTrait;
    use StructuredDataTrait;
    use SearchControllerTrait;

    /**
     * rootAction
     *
     * Redirect this route to home page
     *
     *
     * @Route("/rayon{trailing_slash}", name="stand_root", requirements={"trailing_slash" = "/|"}, methods={"GET"})
     */
    public function rootAction(): Response
    {
        return $this->redirectToRoute('home', [], Response::HTTP_MOVED_PERMANENTLY);
    }

    /**
     * showStandAction
     *
     * Display stand by slug and language
     *
     *
     *
     * @Route("/rayon/{slug}", name="show_stand", requirements={"slug" = ".+"}, methods={"GET"})
     */
    public function showStandAction(string $slug, Request $request): Response
    {
        if ($slug === Stand::DESTOCK_SLUG) {
            return $this->redirectToRoute('show_destock_stand', [], Response::HTTP_MOVED_PERMANENTLY);
        }

        $_locale = $request->getLocale();
        $stand = $this->loadActiveStand($slug, $_locale);

        $articles = null;
        if ($this->getStandLevel($stand) > 1) {
            $articles = $this->getArticles($stand, $_locale);
        }

        $tag_path = implode('.', $stand['tag_path']);
        $stand_ancestors = $this->getStandAncestors($tag_path, $_locale);

        $stand_url = $this->generateUrl('show_stand', ['slug' => $slug]);
        $structured_data = $this->getStructuredData($stand_ancestors, $stand, $_locale, $stand_url);

        $view_parameters = [
            'stand' => $stand,
            'stand_ancestors' => $stand_ancestors,
            'structured_data' => json_encode($structured_data, JSON_THROW_ON_ERROR),
            'articles' => $articles,
            'search_data' => json_encode(
                [
                    'articles' => $articles instanceof CollectionIterator ? $this->decorateArticles($articles) : [],
                ],
                JSON_THROW_ON_ERROR,
            ),
            'guide' =>
                $stand->has('guide_slug') && $stand->get('guide_slug') !== null
                    ? $this->loadActiveGuide($stand->get('guide_slug'), $_locale)
                    : null,
            'canonical_url' => $stand_url,
            'default_selected_sorting_option' => $stand->get('sort_by'),
        ];
        $this->addStandTags($view_parameters);

        // Template Resolver
        $resolver_response = $this->resolveTemplate(new StandContext($stand), $view_parameters);
        if ($resolver_response instanceof Response) {
            return $resolver_response;
        }

        return $stand->get('use_eav_facets')
            ? $this->render('AppBundle:Stand:show_stand_v2.html.twig', $view_parameters)
            : $this->render('AppBundle:Stand:show_stand.html.twig', $view_parameters);
    }

    private function getArticles(StandI18n $stand, string $_locale): ?CollectionIterator
    {
        $articles = null;
        $tag_path = implode('.', $stand['tag_path']);
        $count_article = $this->getPommDefaultSession()
            ->getModel(ArticleModel::class)
            ->countAllBasketableByTagpath($tag_path);

        if (!($count_article >= 1000 && $this->getStandLevel($stand) === 2)) {
            $articles = $this->getPommDefaultSession()
                ->getModel(ArticleModel::class)
                ->findAllBasketableByTagpath($tag_path, $_locale);
        }
        return $articles;
    }

    private function getStructuredData(
        CollectionIterator $stand_ancestors,
        StandI18n $stand,
        string $_locale,
        string $stand_url
    ): array {
        $breadcrumb_structured_data = new Breadcrumb($this->getBreadcrumbItemList($stand_ancestors, $stand));
        $structured_data = $breadcrumb_structured_data->getData();

        if ($this->getStandLevel($stand) === 2) {
            $stand_children = $this->getPommDefaultSession()
                ->getModel(StandI18nModel::class)
                ->findAllByCultureAndParentTagpath(implode('.', $stand['tag_path']), $_locale);

            $item_list_structured_data = new StandItemList($stand->name, $stand_url, $stand_children, $this->container);

            $structured_data = [$structured_data, $item_list_structured_data->getData()];
        }
        return $structured_data;
    }
}

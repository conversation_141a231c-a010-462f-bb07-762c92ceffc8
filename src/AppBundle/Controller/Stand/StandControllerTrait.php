<?php
namespace SonVideo\Cms\FrontOffice\AppBundle\Controller\Stand;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18n;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandModel;
use SonVideo\Cms\FrontOffice\CmsBundle\StructuredData\StructuredDataTrait;

trait StandControllerTrait
{
    use StructuredDataTrait;

    protected function loadActiveStand(string $slug, string $culture): StandI18n
    {
        $stand = $this->getPommDefaultSession()
            ->getModel(StandI18nModel::class)
            ->findOneBySlugAndCulture($slug, $culture);

        if (is_null($stand)) {
            throw $this->createNotFoundException(
                sprintf("Stand identified by '%s' (locale '%s') does not exist.", $slug, $culture),
            );
        }

        return $stand;
    }

    private function getBreadcrumbItemList(CollectionIterator $stand_ancestors, StandI18n $current_stand): array
    {
        $item_list = [];
        $index = 1;
        // Add n entries for each parents stand
        foreach ($stand_ancestors as $stand) {
            $item_list[] = $this->getBreadcrumbItem(
                $stand['name'],
                sprintf(
                    '%s%s',
                    $this->getParameter('base_url'),
                    $this->generateUrl('show_stand', ['slug' => $stand['slug']]),
                ),
                $index,
            );
            $index++;
        }

        // Add last entry for current stand
        $item_list[] = $this->getBreadcrumbItem(
            $current_stand['name'],
            sprintf(
                '%s%s',
                $this->getParameter('base_url'),
                $this->generateUrl('show_stand', ['slug' => $current_stand['slug']]),
            ),
            $index,
        );

        return $item_list;
    }

    protected function addStandTags(array $parameters): self
    {
        $this->addTags([$parameters['stand']->get('stand_id')], 'stand');
        if (isset($parameters['stand_ancestors'])) {
            $this->addTags(array_column($parameters['stand_ancestors']->extract(), 'stand_id'), 'stand');
        }

        if (isset($parameters['guide'])) {
            $this->addTags([$parameters['guide']->get('guide_id')], 'guide');
        }

        return $this;
    }

    protected function getStandAncestors(string $tag_path, string $_locale): CollectionIterator
    {
        return $this->getPommDefaultSession()
            ->getModel(StandI18nModel::class)
            ->findAllAncestorsInTagpath($tag_path, $_locale);
    }

    protected function getArticleStands(int $article_id, string $_locale): CollectionIterator
    {
        return $this->getPommDefaultSession()
            ->getModel(StandModel::class)
            ->findHierarchyForArticle($article_id, $_locale);
    }

    private function getStandLevel(object $stand): int
    {
        return count($stand->get('tag_path')) - 1;
    }
}

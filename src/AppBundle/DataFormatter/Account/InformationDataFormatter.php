<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\DataFormatter\Account;

use SonVideo\Cms\FrontOffice\AppBundle\DataFormatter\DataFormatterInterface;

class InformationDataFormatter implements DataFormatterInterface
{
    protected static $keep_only = ['title', 'lastname', 'firstname', 'phone', 'cellphone', 'preferences', 'birthday'];

    protected static $capitalize = ['lastname', 'firstname'];

    public function format(array $data): array
    {
        $kept = array_filter(
            $data,
            fn($key): bool => in_array($key, static::$keep_only),
            ARRAY_FILTER_USE_KEY,
        );

        foreach ($kept as $key => $value) {
            if (in_array($key, static::$capitalize)) {
                $value = ucwords($value);
                $value = ucwords($value, '-');
            }

            $kept[$key] = $value;
        }

        return $kept;
    }
}

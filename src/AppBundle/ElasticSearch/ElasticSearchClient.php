<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\ElasticSearch;

use Elasticsearch\Client;
use Elasticsearch\Namespaces\IndicesNamespace;
use Psr\Log\LoggerInterface;

class ElasticSearchClient
{
    private Client $elastic_client;

    private LoggerInterface $logger;

    public function __construct(Client $elastic_client, LoggerInterface $logger)
    {
        $this->elastic_client = $elastic_client;
        $this->logger = $logger;
    }

    /**
     * @return array|callable
     * @throws \Exception
     */
    public function search(array $params = [])
    {
        $result = $this->elastic_client->search($params);

        if (0 === $result['_shards']['successful']) {
            $this->logger->info('No successful shard', $result);

            throw new \Exception('No successful shard');
        }

        return $result;
    }

    /**
     * @return array|callable
     */
    public function index(array $params = [])
    {
        return $this->elastic_client->index($params);
    }

    public function indices(): IndicesNamespace
    {
        return $this->elastic_client->indices();
    }
}

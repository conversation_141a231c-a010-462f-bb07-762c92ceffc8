<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Rule;

use SonVideo\Cms\FrontOffice\AppBundle\Exception\PromoCodeException;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema\PromoCodeInterface;

/**
 * Class ShoppingCartTotalPriceThresholdRule
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Rule
 * <AUTHOR> <<EMAIL>>
 */
class ShoppingCartTotalPriceThresholdRule implements PromoCodeRuleInterface
{
    public $min_threshold;

    /**
     * isEligible
     *
     * @throws PromoCodeException
     */
    public function isEligible(array $cart_summary, PromoCodeInterface $promo_code): bool
    {
        $this->min_threshold = min(array_keys($promo_code->getPromoCodeRules()));

        return $this->min_threshold <= $cart_summary['amount_selling_price_for_promo_code'];
    }

    /**
     * getErrorMessage
     */
    public function getErrorMessage(): string
    {
        return PromoCodeException::PROMO_CODE_THRESHOLD_NOT_REACHED;
    }

    /**
     * getErrorContext
     * @return array{min_threshold: string|false}
     */
    public function getErrorContext(array $options): array
    {
        return [
            'min_threshold' => \NumberFormatter::create(
                \Locale::getDefault(),
                \NumberFormatter::CURRENCY,
            )->formatCurrency($this->min_threshold, $options['currency']),
        ];
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Exception;

/**
 * Class BasketOrderException
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\Exception
 * @copyright 2017 Son-Video Distribution
 * <AUTHOR> <<EMAIL>>
 * @see       AbstractContextException
 */
class BasketOrderException extends AbstractContextException
{
    protected $code = 404;
}

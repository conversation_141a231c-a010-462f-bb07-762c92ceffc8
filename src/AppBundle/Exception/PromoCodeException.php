<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Exception;

/**
 * Class PromoCodeException
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\Exception
 * @copyright 2017 Son-Video Distribution
 * <AUTHOR> <<EMAIL>>
 * @see       AbstractContextException
 */
class PromoCodeException extends AbstractContextException
{
    public const PROMO_CODE_NOT_FOUND = 'PROMO_CODE_NOT_FOUND';
    public const PROMO_CODE_EMPTY_CART = 'PROMO_CODE_EMPTY_CART';
    public const PROMO_CODE_OUTDATED = 'PROMO_CODE_OUTDATED';
    public const PROMO_CODE_NEED_ARTICLE = 'PROMO_CODE_NEED_ARTICLE';
    public const PROMO_CODE_ALREADY_APPLIED = 'PROMO_CODE_ALREADY_APPLIED';
    public const PROMO_CODE_ALREADY_USED = 'PROMO_CODE_ALREADY_USED';
    public const PROMO_CODE_THRESHOLD_NOT_REACHED = 'PROMO_CODE_THRESHOLD_NOT_REACHED';
    public const PROMO_CODE_CART_WITH_QUOTATION = 'PROMO_CODE_CART_WITH_QUOTATION';
    public const PROMO_CODE_CART_WITH_QUOTE = 'PROMO_CODE_CART_WITH_QUOTE';
    public const PROMO_CODE_LOGIN_REQUIRED = 'PROMO_CODE_LOGIN_REQUIRED';
    public const GIFTED_ARTICLE_HAS_NO_STOCK = 'PROMO_CODE_GIFTED_ARTICLE_HAS_NO_STOCK';
}

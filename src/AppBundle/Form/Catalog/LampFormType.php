<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Form\Catalog;

use SonVideo\Cms\FrontOffice\AppBundle\Manager\Catalog\CatalogFetcher;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

/**
 * Class LampFormType
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Form\Catalog
 * <AUTHOR> <<EMAIL>>
 */
class LampFormType extends AbstractType
{
    private CatalogFetcher $catalog_fetcher;

    /**
     * LampFormType constructor.
     */
    public function __construct(CatalogFetcher $catalog_fetcher)
    {
        $this->catalog_fetcher = $catalog_fetcher;
    }

    /**
     * buildForm
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('brand', ChoiceType::class, [
                'label' => 'lamp_brand_select',
                'required' => true,
                'choices' => $this->getCatalogBrands(CatalogFetcher::LAMP_CATALOG_NAME),
            ])
            ->add('model', TextType::class, [
                'label' => 'lamp_model_input',
                'required' => false,
                'constraints' => [
                    new Length([
                        'max' => 70,
                        'maxMessage' => 'lamp.model.max_length',
                    ]),
                ],
            ]);
    }

    /**
     * getCatalogBrands
     *
     *
     */
    protected function getCatalogBrands(string $catalog_name): array
    {
        $brands = $this->catalog_fetcher->getBrandsByCatalog($catalog_name);
        $formatted_brands = [];

        foreach ($brands as $brand) {
            $formatted_brands[$brand['name']] = implode('.', $brand['path']);
        }

        return $formatted_brands;
    }
}

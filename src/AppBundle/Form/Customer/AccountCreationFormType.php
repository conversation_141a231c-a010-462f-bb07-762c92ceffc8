<?php
/*
 * This file is part of Melkart CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Form\Customer;

use SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer\CustomerEmailIsUnique;
use EWZ\Bundle\RecaptchaBundle\Form\Type\EWZRecaptchaType;
use EWZ\Bundle\RecaptchaBundle\Validator\Constraints\IsTrue as RecaptchaTrue;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Class AccountCreationFormType
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Form\Customer
 */
class AccountCreationFormType extends AbstractType
{
    /**
     * configureOptions
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'allow_extra_fields' => true,
            'locale' => APP_DEFAULT_LOCALE,
        ]);

        parent::configureOptions($resolver);
    }

    /**
     * buildForm
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add('email', EmailType::class, [
                'label' => 'Email',
                'constraints' => [
                    new Assert\NotBlank(),
                    new CustomerEmailIsUnique(['is_new_account' => true]),
                    new Assert\Email([
                        'strict' => false,
                    ]),
                ],
            ])
            ->add('password', PasswordType::class, [
                'required' => true,
            ])
            ->add('want_newsletter', CheckboxType::class, [
                'required' => false,
                'label' => 'newsletter-subscription-invitation',
                'translation_domain' => 'account',
                'data' => false,
            ])
            ->add('recaptcha', EWZRecaptchaType::class, [
                'constraints' => [new RecaptchaTrue()],
                'attr' => [
                    'options' => [
                        'theme' => 'light',
                        'type' => 'image',
                        'size' => 'invisible',
                        'defer' => true,
                        'async' => true,
                        'callback' => 'onReCaptchaSuccess',
                        'bind' => 'btnInscription',
                    ],
                ],
                'language' => $options['locale'],
            ]);
    }
}

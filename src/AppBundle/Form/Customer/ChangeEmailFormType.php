<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Form\Customer;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * ChangeEmailFormType
 *
 * Define form used to confirm change of email with a password confirmation.
 * It is called from a reinitialisation link.
 *
 * @package     SonVideo\Cms\FrontOffice\AppBundle\Form\Customer
 * <AUTHOR> Audic <<EMAIL>>
 * @see         AbstractType
 */
class ChangeEmailFormType extends AbstractType
{
    /**
     * configureOptions
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'allow_extra_fields' => true,
        ]);
    }

    /**
     * buildForm
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('password', PasswordType::class, [
            'label' => 'Password',
            'required' => true,
        ]);
    }
}

<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager;

use League\Flysystem\MountManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Class AbstractImageUploader
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager
 */
abstract class AbstractImageUploader
{
    public const MEDIA_FILESYSTEM = 'media_filesystem';
    public const MAX_UPLOADABLE_FILES = 10;

    /**
     * @var array
     */
    protected $images_to_upload = [];

    /**
     * @var array
     */
    protected $uploaded = [];

    private MountManager $mount_manager;

    private LoggerInterface $logger;

    /**
     * InstallationImageManager constructor.
     */
    public function __construct(MountManager $mount_manager, LoggerInterface $logger)
    {
        $this->mount_manager = $mount_manager;
        $this->logger = $logger;
    }

    /**
     * getTargetFilePath
     *
     * @param array        $params Parameters coming from the "upload" call + 'file_index' in the collection.
     *
     */
    abstract protected function getTargetFilePath(UploadedFile $file, array $params): string;

    /**
     * addImageToUpload
     *
     *
     * @return $this
     */
    public function addImageToUpload(UploadedFile $image_to_upload): self
    {
        $this->images_to_upload[] = $image_to_upload;

        return $this;
    }

    /**
     * upload
     *
     * @param array $params Parameters that will be used to define the target file path
     *
     * @throws \Exception
     */
    public function upload(array $params = []): array
    {
        if ($this->images_to_upload === []) {
            throw new \Exception('There is no image to upload');
        }

        if (count($this->images_to_upload) > static::MAX_UPLOADABLE_FILES) {
            throw new \Exception('Maximal amount of uploadable pictures has been reached');
        }

        /**
         * @var UploadedFile $image_file
         */
        foreach ($this->images_to_upload as $index => $image_file) {
            $this->moveUploadFile(
                $image_file->getPathname(),
                $this->getTargetFilePath($image_file, array_merge($params, ['file_index' => $index])),
            );
        }

        $this->cleanUploadedFiles();

        return $this->uploaded;
    }

    /**
     * remove
     */
    public function remove(string $url): bool
    {
        return $this->mount_manager->getFilesystem(static::MEDIA_FILESYSTEM)->delete($url);
    }

    /**
     * cleanUploadedFiles
     *
     * @return static
     */
    protected function cleanUploadedFiles(): self
    {
        foreach ($this->images_to_upload as $image_file) {
            if (file_exists($image_file->getPathname())) {
                unlink($image_file->getPathname());
            }
        }

        $this->images_to_upload = [];

        return $this;
    }

    /**
     * moveUploadFile
     */
    protected function moveUploadFile(string $from, string $to_file_path)
    {
        try {
            // Upload image
            $this->mount_manager
                ->getFilesystem(static::MEDIA_FILESYSTEM)
                ->writeStream($to_file_path, fopen($from, 'r+'));

            $this->uploaded[] = $to_file_path;
        } catch (\Exception $e) {
            $this->logger->error(sprintf('Error while uploading through %s', static::class), ['exception' => $e]);
        }
    }
}

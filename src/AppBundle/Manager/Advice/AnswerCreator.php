<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice;

use Psr\Log\LoggerInterface;
use Ramsey\Uuid\UuidFactoryInterface;
use SonVideo\AdviceClient\Operation\Operation;
use PommProject\ModelManager\Session as PommSession;
use Symfony\Component\Asset\Packages;

/**
 * Class AnswerCreator
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice
 */
class AnswerCreator
{
    use CommentCreatorTrait;

    public const ORIGIN = 'son-video.com';

    /**
     * @var PommSession
     */
    private $pomm_session;

    /**
     * @var CommentImageUploader
     */
    private $image_uploader;

    /**
     * @var Packages
     */
    private $assets_packages;

    private Operation $comment_client;

    /**
     * @var LoggerInterface
     */
    private $logger;

    private UuidFactoryInterface $uuid_factory;

    /**
     * InstallationCreator constructor.
     */
    public function __construct(
        PommSession $pomm_session,
        CommentImageUploader $image_uploader,
        Packages $assets_packages,
        Operation $comment_client,
        LoggerInterface $logger,
        UuidFactoryInterface $uuid_factory
    ) {
        $this->pomm_session = $pomm_session;
        $this->image_uploader = $image_uploader;
        $this->assets_packages = $assets_packages;
        $this->comment_client = $comment_client;
        $this->logger = $logger;
        $this->uuid_factory = $uuid_factory;
    }

    /**
     * create
     *
     *
     * @throws \Exception
     */
    public function create(array $data, string $parent_comment_id, string $locale): array
    {
        $this->logger->info('answer_creator.create.begin', ['data' => $data]);

        $this->saveAccountInformation($data);
        $this->logger->info('answer_creator.create.new_account_information', [
            'account_information' => $data['account_information'],
        ]);

        $comment_uuid = $this->uuid_factory->uuid4();
        $absolute_images_paths = $this->uploadImages($data, $comment_uuid);
        $this->logger->info('answer_creator.create.uploaded_files_paths', ['absolute_images_paths' => $absolute_images_paths]);

        return $this->sendAnswer($comment_uuid, $absolute_images_paths, $data, $parent_comment_id, $locale);
    }

    /**
     * sendAnswer
     *
     *
     */
    protected function sendAnswer(
        string $product_question_uuid,
        array $media_urls,
        array $data,
        string $parent_comment_id,
        string $locale
    ): array {
        $this->logger->info(
            'answer_creator.send_answer.begin',
            ['product_question_uuid' => $product_question_uuid, 'media_urls' => $media_urls, 'data' => $data, 'parent_comment_id' => $parent_comment_id],
        );

        // Format data to submit
        $to_submit = [
            'id' => $product_question_uuid,
            'parent_id' => $parent_comment_id,
            'user_id' => (string) $data['account_information']['customer_id'],
            'origin' => static::ORIGIN,
            'culture_id' => $locale,
            'title' => null,
            'message' => $data['message'],
            'user_location' => $data['location'] ?? null,
            'user_age' => $data['account_information']['age'],
            'associated_products' => $data['associated_products'],
            'media' => $this->formatImageMedias($media_urls),
            'preferences' => [
                'notification' => $data['subscribe_publish'] ?? false,
                'anonymous' => $data['anonymous'] ?? false,
            ],
        ];

        // Call API
        $answer = $this->comment_client->create($to_submit);

        $this->logger->info('answer_creator.send_answer.comment_created', ['answer' => $answer]);

        return $answer;
    }
}

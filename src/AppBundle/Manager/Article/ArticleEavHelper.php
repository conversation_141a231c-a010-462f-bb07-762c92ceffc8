<?php
/*
 * This file is part of FO-CMS package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Article;

use PommProject\ModelManager\Model\FlexibleEntity;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Article;

final class ArticleEavHelper
{
    /**
     *
     * @return mixed|null
     */
    public static function extractEavAttributeValue(int $attribute_id, ?FlexibleEntity $target_entity)
    {
        if (!$target_entity instanceof FlexibleEntity) {
            return null;
        }

        if (!$target_entity->has('eav_attributes')) {
            return null;
        }

        if (null === $target_entity->get('eav_attributes')) {
            return null;
        }

        foreach ($target_entity->get('eav_attributes') as $attribute) {
            if ($attribute_id !== $attribute['attribute_id']) {
                continue;
            }

            if (isset($attribute['values'][0]['value'])) {
                return $attribute['values'][0]['value'];
            }
        }

        return null;
    }

    public static function extractRegulatedAttributesAndAssignTo(?FlexibleEntity $target_entity): ?FlexibleEntity
    {
        if (!$target_entity instanceof FlexibleEntity) {
            return null;
        }

        $target_entity->set('regulated_attributes', [
            'repairability_index' => self::extractEavAttributeValue(
                Article::EAV_REPAIRABILITY_ATTRIBUTE_ID,
                $target_entity,
            ),
            'energy_letter_label' => self::extractEavAttributeValue(
                Article::EAV_ENERGY_LETTER_LABEL_ATTRIBUTE_ID,
                $target_entity,
            ),
            'spare_parts_availability' => self::extractEavAttributeValue(
                Article::EAV_SPARE_PARTS_AVAILABILITY_ATTRIBUTE_ID,
                $target_entity,
            ),
            'sustainability' => self::extractEavAttributeValue(
                Article::EAV_SUSTAINABILITY_ATTRIBUTE_ID,
                $target_entity,
            ),
        ]);

        return $target_entity;
    }

    public static function mergeDestockParentAttributesInArticle(FlexibleEntity $target_entity): FlexibleEntity
    {
        if (!$target_entity->has('parent_destock_eav_attributes')) {
            return $target_entity;
        }

        if (!$target_entity->has('eav_attributes') || is_null($target_entity->get('parent_destock_eav_attributes'))) {
            $target_entity->clear('parent_destock_eav_attributes');

            return $target_entity;
        }

        $indexedEavAttributes = self::createArrayIndexedByAttributeId($target_entity->get('eav_attributes') ?? []);
        $indexedParentEavAttributes = self::createArrayIndexedByAttributeId(
            $target_entity->get('parent_destock_eav_attributes'),
        );
        $target_entity->set(
            'eav_attributes',
            array_values(array_replace($indexedParentEavAttributes, $indexedEavAttributes)),
        );
        $target_entity->clear('parent_destock_eav_attributes');

        return $target_entity;
    }

    private static function createArrayIndexedByAttributeId(array $attributes): array
    {
        return array_reduce(
            $attributes,
            function (array $result, array $attribute) {
                $result[$attribute['attribute_id']] = $attribute;

                return $result;
            },
            [],
        );
    }
}

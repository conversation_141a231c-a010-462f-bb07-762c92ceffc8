<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Article;

use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleWarrantyExtension;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleWarrantyExtensionModel;

/**
 * Class ArticleWarrantyFetcher
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Article
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class ArticleWarrantyFetcher
{
    private PommSession $pomm_session;

    /**
     * ArticleWarrantyFetcher constructor.
     */
    public function __construct(PommSession $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    /**
     * fetchCompleteWarrantyForArticleId
     */
    public function fetchCompleteWarrantyForArticleId(int $article_id, string $locale): CollectionIterator
    {
        return $this->getArticleWarrantyExtensionModel()->fetchCompleteWarrantyForArticleId($article_id, $locale);
    }

    /**
     * fetchOne
     *
     * @return ArticleWarrantyExtension|null
     */
    public function fetchOne(int $article_id, string $warranty_tag_path)
    {
        return $this->getArticleWarrantyExtensionModel()->findByPK([
            'article_id' => $article_id,
            'tag_path' => explode('.', $warranty_tag_path),
        ]);
    }

    /**
     * getArticleWarrantyExtensionModel
     */
    private function getArticleWarrantyExtensionModel(): ArticleWarrantyExtensionModel
    {
        return $this->pomm_session->getModel(ArticleWarrantyExtensionModel::class);
    }
}

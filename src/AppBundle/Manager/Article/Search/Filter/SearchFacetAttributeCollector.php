<?php
/*
 * This file is part of FO CMS package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\Filter;

use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\SearchFacet;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\SearchFacetValue;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryModel;

/**
 * Class SearchFacetAttributeCollector
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\Filter
 */
class SearchFacetAttributeCollector implements SearchFacetCollectorInterface
{
    public const DISPLAY_ORDER = 10;
    public const NAME_MASK = 'attribute';

    /**
     * @var array Previous categories definitions
     */
    protected $categories_facets_definitions = [];

    protected PommSession $pomm_session;

    /**
     * @inheritdoc
     */
    public function __construct(PommSession $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    /**
     * @inheritdoc
     */
    public function collect(array $facets, array $article): array
    {
        // Attributes are composites in order to facilitate display order within the attribute list
        if (!isset($facets[static::NAME_MASK])) {
            $facets[static::NAME_MASK] = new SearchFacet(SearchFacet::COMPOSITE_TYPE, [
                'display_order' => static::DISPLAY_ORDER,
            ]);
        }

        // Retrieving the category's definitions in article query has a huge performance impact, for an obscure reason,
        // unlike a dedicated retrieve.
        $category_definitions = $this->retrieveCategoryDefinitions($article['category_id']);

        foreach ($article['facet_values'] ?? [] as $facet) {
            // retrieve definition
            $attribute_key = sprintf('attribute_id_%s', $facet['attribute_id']);
            $category_definition = $category_definitions[$attribute_key] ?? null;
            if ($category_definition === null) {
                continue;
            }

            $composite = $this->addSearchFacet($facets, $attribute_key, $category_definition);

            // add used values
            foreach ($facet['attribute_values'] as $attribute_value_id) {
                $this->addValueToComposite(
                    $composite,
                    $attribute_value_id,
                    $category_definition,
                    $article['article_id'],
                );
            }
        }

        return $facets;
    }

    /**
     * Retrieve the category's eav_facet_definitions from database or previous result.
     */
    protected function retrieveCategoryDefinitions(int $category_id): ?array
    {
        $key = sprintf('cat_%s', $category_id);
        if (!isset($this->categories_facets_definitions[$key])) {
            $this->categories_facets_definitions[$key] =
                $this->pomm_session->getModel(CategoryModel::class)->findByPK(['category_id' => $category_id])[
                    'eav_facet_definitions'
                ] ?? null;
        }

        return $this->categories_facets_definitions[$key];
    }

    protected function addSearchFacet(array &$facets, string $attribute_key, array $category_definition): SearchFacet
    {
        // shorten the key length to decrease resulting data to send
        $attribute_key = str_replace('attribute_id', 'attribute', $attribute_key);

        // create/retrieve SearchFacet
        $facet_type =
            $category_definition['definition']['type'] === 'numeric'
                ? SearchFacet::NUMERIC_TYPE
                : SearchFacet::DEFAULT_TYPE;
        $composite =
            $facets[static::NAME_MASK]->composite($attribute_key) ??
            new SearchFacet(
                $facet_type,
                array_merge($category_definition['meta'], [
                    'display_order' => $category_definition['display_order'],
                    'is_open' => $category_definition['is_open'],
                ]),
            );

        $facets[static::NAME_MASK]->addChildren($attribute_key, $composite);

        return $composite;
    }

    protected function addValueToComposite(
        SearchFacet $composite,
        int $attribute_value_id,
        array $category_definition,
        int $article_id
    ) {
        // retrieve attribute value definition
        $attribute_value_key = sprintf('attribute_value_id_%d', $attribute_value_id);
        $attribute_value = $category_definition['values'][$attribute_value_key] ?? null;
        if ($attribute_value === null) {
            return;
        }

        // shorten the key length to decrease resulting data to send
        $attribute_value_key = sprintf('a_%s', $attribute_value_id);

        // create/retrieve SearchFacetValue
        if (!$composite->has($attribute_value_key)) {
            $facet_value = new SearchFacetValue(
                $attribute_value_key,
                $attribute_value['value'],
                $attribute_value['meta'],
                $attribute_value['display_order'],
            );
            $composite->addSearchValue($facet_value);
        }

        $composite->addValue($attribute_value_key, $article_id);
    }
}

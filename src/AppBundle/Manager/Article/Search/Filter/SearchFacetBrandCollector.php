<?php
/*
 * This file is part of FO CMS package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\Filter;

use SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\SearchFacet;

/**
 * Class SearchFacetBrandCollector
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\Filter
 */
class SearchFacetBrandCollector extends AbstractSearchFacetSimpleCollector
{
    public const FACET_NAME = 'brand';
    public const FACET_TYPE = SearchFacet::DEFAULT_TYPE;
    public const VALUE_MASK = 'b_%s';
    public const PROPERTY_HOLDING_ID = 'brand_id';
    public const PROPERTY_HOLDING_VALUE = 'brand_name';

    public const METAS = [
        'display_order' => 2,
        'label' => 'Marques',
        'display_options' => [SearchFacet::OPTION_HIDE_IF_SINGLE_VALUE, SearchFacet::OPTION_SORT_BY_OCCURRENCES],
        'internal_filter' => true,
        'is_open' => true,
    ];
}

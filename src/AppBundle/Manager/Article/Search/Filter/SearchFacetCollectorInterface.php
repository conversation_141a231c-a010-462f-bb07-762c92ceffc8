<?php
/*
 * This file is part of FO CMS package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\Filter;

use PommProject\ModelManager\Session as PommSession;

/**
 * Interface SearchFacetCollectorInterface
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\Filter
 */
interface SearchFacetCollectorInterface
{
    /**
     * SearchFacetCollectorInterface constructor.
     */
    public function __construct(PommSession $pomm_session);

    /**
     * Collect in facets the values related to an article.
     *
     * @param array $facets List of facets
     * @param array $article Article's data
     */
    public function collect(array $facets, array $article): array;
}

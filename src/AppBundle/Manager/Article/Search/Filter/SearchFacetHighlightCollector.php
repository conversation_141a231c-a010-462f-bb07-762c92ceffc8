<?php
/*
 * This file is part of FO CMS package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\Filter;

use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\SearchFacet;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\SearchFacetValue;

/**
 * Class SearchFacetHighlightCollector
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\Filter
 */
class SearchFacetHighlightCollector implements SearchFacetCollectorInterface
{
    public const FACET_NAME = 'highlight';
    public const FACET_TYPE = SearchFacet::DEFAULT_TYPE;

    public const METAS = [
        'display_order' => 4,
        'label' => 'À ne pas manquer',
        'is_open' => true,
        'display_options' => [SearchFacet::OPTION_TRANSLATE_VALUE],
    ];

    protected PommSession $pomm_session;

    /**
     * @inheritdoc
     */
    public function __construct(PommSession $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    /**
     * @inheritdoc
     */
    public function collect(array $facets, array $article): array
    {
        if (!isset($facets[static::FACET_NAME])) {
            $facets[static::FACET_NAME] = new SearchFacet(static::FACET_TYPE, static::METAS);
        }

        $highlights = $article['highlights'];

        foreach ($highlights as $highlight) {
            $value = str_replace('article.highlight.', '', $highlight['tag_path']);
            $value_key = 'h_' . $value;

            if ($facets[static::FACET_NAME]->has($value_key) === false) {
                $facet_value = new SearchFacetValue($value_key, $value, [], $highlight['display_order']);
                $facets[static::FACET_NAME]->addSearchValue($facet_value);
            }

            $facets[static::FACET_NAME]->addValue($value_key, $article['article_id']);
        }

        return $facets;
    }
}

<?php
/*
 * This file is part of FO CMS package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search;

/**
 * Class SearchFacetValue
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search
 */
class SearchFacetValue
{
    protected string $key;

    /**
     * @var
     */
    protected $value;

    protected array $meta;

    /**
     * @var array
     */
    protected $article_ids = [];

    protected $display_order;

    /**
     * SearchFacetValue constructor.
     *
     * @param        $value
     */
    public function __construct(string $key, $value, array $meta = [], $display_order = null)
    {
        $this->key = $key;
        $this->value = $value;
        $this->meta = $meta;
        $this->display_order = $display_order;
    }

    public function getKey(): string
    {
        return $this->key;
    }

    public function getMeta(): array
    {
        return $this->meta;
    }

    /**
     * @return mixed
     */
    public function getDisplayOrder()
    {
        return $this->display_order;
    }

    /**
     * @return mixed
     */
    public function getValue()
    {
        return $this->value;
    }

    public function getArticleIds(): array
    {
        return $this->article_ids;
    }

    /**
     * addArticleId
     *
     *
     * @return $this
     */
    public function addArticleId(int $article_id): self
    {
        $this->article_ids[] = $article_id;

        return $this;
    }
}

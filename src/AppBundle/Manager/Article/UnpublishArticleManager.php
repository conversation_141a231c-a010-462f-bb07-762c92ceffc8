<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Article;

use Exception;
use PommProject\ModelManager\Session;
use Psr\Log\LoggerInterface;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrder;
use SonVideo\Synapps\Client\RpcClientService;

class UnpublishArticleManager
{
    public const ARTICLE_UNPUBLISHED_METHOD = 'article:unpublish';

    /**
     * @var Session
     */
    public $pomm_session;
    /**
     * @var LoggerInterface
     */
    public $logger;
    /**
     * @var RpcClientService
     */
    public $rpc_client;

    /**
     * UnpublishedArticleManager constructor.
     */
    public function __construct(Session $session, LoggerInterface $logger, RpcClientService $rpc_client)
    {
        $this->pomm_session = $session;
        $this->logger = $logger;
        $this->rpc_client = $rpc_client;
    }

    /**
     * @throws Exception
     */
    public function checkDestockToUnpublished(CustomerOrder $customerOrder): void
    {
        try {
            $products = $customerOrder->get('products');
            foreach ($products as $product) {
                if (false !== strpos($product['sku'], 'DESTOCK')) {
                    $unpublished_article = $this->rpc_client->call('bo-cms', self::ARTICLE_UNPUBLISHED_METHOD, [
                        $product['article_id'],
                        'unpublished destock',
                    ]);
                    if (null === $unpublished_article) {
                        $this->logger->error('article.unpublished.failed', ['product' => $product]);
                    } else {
                        $this->logger->info('article.unpublished.success', [
                            'unpublished_article' => $unpublished_article,
                        ]);
                    }
                }
            }
        } catch (Exception $e) {
            $this->logger->warning($e->getMessage(), ['exception' => $e]);

            throw new Exception($e->getMessage(), $e->getCode(), $e);
        }
    }
}

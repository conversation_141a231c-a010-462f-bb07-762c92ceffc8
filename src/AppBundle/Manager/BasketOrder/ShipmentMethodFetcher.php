<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder;

use Checkdomain\Holiday\Util;
use PommProject\Foundation\Exception\FoundationException;
use PommProject\Foundation\Pomm;
use PommProject\ModelManager\Model\CollectionIterator;
use Psr\Log\LoggerInterface;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Article;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Product;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\ShipmentMethodModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\ShipmentMethodTagModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Quote;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PublicSchema\CityModel;
use SonVideo\Synapps\Client\RpcClientService;

/**
 * Class ShipmentMethodFetcher
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder
 * <AUTHOR> Marniesse <<EMAIL>>
 */
class ShipmentMethodFetcher
{
    private Pomm $pomm;

    private RpcClientService $rpc_client;

    private BasketOrderManager $basket_order_manager;

    private LoggerInterface $logger;

    /**
     * ShipmentMethodFetcher constructor.
     */
    public function __construct(
        Pomm $pomm,
        RpcClientService $rpc_client,
        BasketOrderManager $basket_order_manager,
        LoggerInterface $logger
    ) {
        $this->pomm = $pomm;
        $this->rpc_client = $rpc_client;
        $this->basket_order_manager = $basket_order_manager;
        $this->logger = $logger;
    }

    public function getShipmentMethodsForBasketOrder(string $customer_id): ?CollectionIterator
    {
        $context = $this->getContextFromBasketOrder($customer_id);
        if ($context === null) {
            return null;
        }

        $bridge_shipment_methods = $this->retrieveEligibleShipmentMethods($context['rpc_payload']);
        if ($bridge_shipment_methods === null) {
            return null;
        }

        return $this->findShipmentsMethods(
            $bridge_shipment_methods,
            $context['shipping_address'],
            $context['basket_order_lines'],
        );
    }

    public function getShipmentMethodsForShoppingCartUsingAddress(array $address): ?CollectionIterator
    {
        $context = $this->getContextFromShoppingCartUsingAddress($address);
        if ($context === null) {
            return null;
        }

        $bridge_shipment_methods = $this->retrieveEligibleShipmentMethods($context['rpc_payload']);
        if ($bridge_shipment_methods === null) {
            return null;
        }

        $address['city'] = '';

        // Using an empty array as $basket_order_lines parameter will
        // always set `is_deliverable_immediately` to true on shipment methods.
        // This data is only used to display a message in the funnel when in Christmas period.
        // We don't care in shopping cart.
        return $this->findShipmentsMethods($bridge_shipment_methods, $address, []);
    }

    private function getContextFromBasketOrder(string $customer_id): ?array
    {
        $basket_order = $this->basket_order_manager->getBasketOrderForCustomer($customer_id);
        if (is_null($basket_order)) {
            $this->logger->error('Basket order can not be found for get_shipment_methods action.', [
                'customer_id' => $customer_id,
            ]);

            return null;
        }

        $basket_order_lines = $this->basket_order_manager->getBasketOrderLinesArticlesForCustomer($customer_id);
        $gift_article = $this->basket_order_manager->getGiftArticle($customer_id);

        $rpc_payload = $this->basket_order_manager
            ->getContextFactory()
            ->load($basket_order, $basket_order_lines)
            ->getArray();
        $rpc_payload['items'] = array_merge(
            $rpc_payload['items'],
            $this->formatGiftArticleAsRegularBasketOrderLine($gift_article),
        );

        // Build array of estimated delivery times for use with getOrderShippingDelay()
        $estimated_delivery_times = [];
        foreach ($basket_order_lines as $basket_order_line) {
            if ($basket_order_line->get('type') === Article::ARTICLE_BASKETABLE_TYPE) {
                $estimated_delivery_times[] = $basket_order_line->get('estimated_delivery_time');
            }
            if ($basket_order_line->get('type') === Quote::QUOTE_BASKETABLE_TYPE) {
                foreach ($basket_order_line->get('extra_data')['items'] as $item) {
                    $estimated_delivery_times[] = $item['estimated_delivery_time'];
                }
            }
        }

        // Add shipping_delay to the payload : number of days until order can be shipped.
        $rpc_payload['shipping_delay'] = $this->getOrderShippingDelay(new \DateTime(), $estimated_delivery_times);

        return [
            'rpc_payload' => $rpc_payload,
            'shipping_address' => $basket_order->get('shipping_address'),
            'basket_order_lines' => $basket_order_lines->extract(),
        ];
    }

    /**
     * @return array{rpc_payload: mixed[]}
     */
    private function getContextFromShoppingCartUsingAddress(array $address): ?array
    {
        $rpc_payload = $this->basket_order_manager
            ->getContextFactory()
            ->loadFromShoppingCartUsingAddress($address)
            ->getArray();

        return [
            'rpc_payload' => $rpc_payload,
        ];
    }

    private function retrieveEligibleShipmentMethods(array $rpc_payload): ?array
    {
        $this->logger->debug('carrier:get_carriers_for_order', $rpc_payload);
        $res = $this->rpc_client->call('bridge', 'carrier:get_carriers_for_order', [$rpc_payload]);

        if (!isset($res['result'])) {
            $this->logger->error('RPC call fails for retrieveEligibleShipmentMethods action.');

            return null;
        }

        return $res['result'];
    }

    private function findShipmentsMethods(
        array $bridge_shipment_methods,
        ?array $shipping_address,
        array $basket_order_lines
    ) {
        $city = $shipping_address
            ? $this->pomm
                ->getDefaultSession()
                ->getModel(CityModel::class)
                ->findByZipOrName(
                    $shipping_address['postal_code'],
                    $shipping_address['city'],
                    $shipping_address['country_code'] ?? ($shipping_address['country_id'] ?? 'FR'),
                )
            : null;
        $shipment_methods = $this->pomm
            ->getDefaultSession()
            ->getModel(ShipmentMethodModel::class)
            ->fetchWithList($bridge_shipment_methods, $city['gps'] ?? null);

        if ($shipment_methods !== null) {
            // Add computed customer order product availability in each returned shipment methods
            $is_deliverable_immediately = $this->isOrderDeliverableImmediately($basket_order_lines);
            foreach ($shipment_methods as $shipment_method) {
                $shipment_method->set('is_deliverable_immediately', $is_deliverable_immediately);
            }

            // reset iterator, in order to not break the current() index
            $shipment_methods->rewind();
        }

        return $shipment_methods;
    }

    /**
     * If one product is not deliverable, then the customer order cannot be deliverable "immediately"
     * (When the deliverability is assured, eg: christmas, etc...)
     */
    private function isOrderDeliverableImmediately(array $basket_order_lines): bool
    {
        foreach ($basket_order_lines as $item) {
            // Only check if the item is an article type (no promo code, accessory, etc...)
            if ($item['type'] !== Article::ARTICLE_BASKETABLE_TYPE) {
                continue;
            }

            // Product is not in stock
            if ($item['informational_stock'] === 0) {
                return false;
            }

            // Minimum availability delay is too long
            if ($item['estimated_delivery_time'] !== Product::AVAILABILITY_1_2_DAYS) {
                return false;
            }
        }

        return true;
    }

    /**
     * Determine the time an order will need to be ready for shipping.
     * @param CollectionIterator $basket_order_lines
     */
    public function getOrderShippingDelay(\DateTime $current_date, array $estimated_delivery_times): ?int
    {
        $holidays_util = new Util();

        //
        // determine the delay of preparation of the order
        //
        $preparation_date = clone $current_date;
        while ($holidays_util->isHoliday('FR', $preparation_date)) {
            $preparation_date->modify('+1 weekday')->setTime(0, 0, 0);
        }
        switch ($preparation_date->format('w')) {
            case '6':
            case '0':
                $preparation_date->modify('+1 weekday');
                break;
            default:
                if ('12' <= $preparation_date->format('H')) {
                    $preparation_date->modify('+1 weekday');
                }
                break;
        }
        while ($holidays_util->isHoliday('FR', $preparation_date)) {
            $preparation_date->modify('+1 weekday');
        }
        $delay = $preparation_date->setTime(0, 0)->diff($current_date->setTime(0, 0))->days;

        //
        // keep the greatest delay between the delay of preparation and the delay of resupply
        //
        $restock_delays = [
            Product::AVAILABILITY_1_2_DAYS => 0,
            Product::AVAILABILITY_3_5_DAYS => 3,
            Product::AVAILABILITY_6_10_DAYS => 6,
            Product::AVAILABILITY_11_15_DAYS => 11,
            Product::AVAILABILITY_16_30_DAYS => 16,
            Product::AVAILABILITY_1_2_MONTHS => 30,
            Product::AVAILABILITY_2_3_MONTHS => 60,
            Product::AVAILABILITY_3_6_MONTHS => 90,
            Product::AVAILABILITY_6_9_MONTHS => 180,
        ];
        foreach ($estimated_delivery_times as $estimated_delivery_time) {
            $restock_delay = $restock_delays[$estimated_delivery_time] ?? null;
            if ($restock_delay === null) {
                return null;
            }

            if ($restock_delay > $delay) {
                $delay = $restock_delay;
            }
        }

        return $delay;
    }

    /**
     * listOfStoreByDistanceByZipCode
     *
     * @return array{stores: mixed, had_fallback_to_department_search: bool}
     * @throws FoundationException
     */
    public function listOfStoreByDistanceByZipCode(string $zip_code): array
    {
        $had_fallback_to_department_search = false;
        if (!preg_match('/^\d{5}$/', $zip_code)) {
            throw new \Exception('Wrong Zip code');
        }

        $city = $this->pomm
            ->getDefaultSession()
            ->getModel(CityModel::class)
            ->findByZipOrName($zip_code);

        if (null === $city) {
            $city = $this->pomm
                ->getDefaultSession()
                ->getModel(CityModel::class)
                ->findByZipOrName(substr($zip_code, 0, 2));
            $had_fallback_to_department_search = true;
        }

        if (null === $city) {
            throw new \Exception('Wrong Zip code');
        }

        return [
            'stores' => $this->pomm
                ->getDefaultSession()
                ->getModel(ShipmentMethodTagModel::class)
                ->fetchStoresWithDistance($city['gps']),
            'had_fallback_to_department_search' => $had_fallback_to_department_search,
        ];
    }

    private function formatGiftArticleAsRegularBasketOrderLine(CollectionIterator $gift_article): array
    {
        $gift_article = $gift_article->extract();

        if ([] === $gift_article) {
            return [];
        }

        $article = $gift_article[0];
        $article['type'] = 'article';
        $article['article_id'] = $article['id'];
        $article['total_price'] = $article['price'] * $article['quantity'];
        unset($article['id']);

        return [$article];
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Account;

use Psr\Log\LoggerInterface;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\Account\PasswordException;
use SonVideo\RpcBundle\Crypter\Crypter;
use SonVideo\Synapps\Client\Manager\SynappsNotifier;
use SonVideo\Synapps\Client\RpcClientService;

/**
 * Class PasswordManager
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Account
 * <AUTHOR> <<EMAIL>>
 */
class PasswordManager
{
    public const SET_PASSWORD_RPC_METHOD = 'customer:set_password';

    public const SET_PASSWORD_SYNAPPS_SUBJECT = 'account.set_password.fo-cms';

    public const SET_PASSWORD_SUCCESS_RPC_RETURN = 0;
    public const SET_PASSWORD_ERROR1_RPC_RETURN = 1;
    public const SET_PASSWORD_ERROR2_RPC_RETURN = 2;
    public const SET_PASSWORD_INSECURE_RPC_RETURN = 3;
    public const SET_PASSWORD_UNKNOWN_RPC_RETURN = -1;

    private RpcClientService $rpc_client;

    private SynappsNotifier $notifier;

    private LoggerInterface $logger;

    private Crypter $crypter;

    /**
     * PasswordModifier constructor.
     */
    public function __construct(RpcClientService $rpc_client, SynappsNotifier $notifier, LoggerInterface $logger, Crypter $crypter)
    {
        $this->rpc_client = $rpc_client;
        $this->notifier = $notifier;
        $this->logger = $logger;
        $this->crypter = $crypter;
    }

    /**
     * changePassword
     * Returns the email of the account.
     *
     * @throws \Exception
     * @throws PasswordException
     */
    public function setPassword(string $token, string $password): string
    {
        $rpc = $this->rpc_client->call('bo-cms', static::SET_PASSWORD_RPC_METHOD, [$token, $this->crypter->encrypt($password)]);
        $result = $rpc['result'];

        switch ($result['pass_changed']) {
            case static::SET_PASSWORD_SUCCESS_RPC_RETURN:
                $this->notifier->notify(static::SET_PASSWORD_SYNAPPS_SUBJECT, ['email' => $result['email']]);

                return $result['email'];

            case static::SET_PASSWORD_ERROR1_RPC_RETURN:
            case static::SET_PASSWORD_ERROR2_RPC_RETURN:
                $this->logger->error(sprintf('Token is invalid. Code error \'%s\'', $result['pass_changed']));

                throw new PasswordException('reset_token_invalid', $result['pass_changed']);

            case static::SET_PASSWORD_INSECURE_RPC_RETURN:
                throw new PasswordException('password_insecure', $result['pass_changed']);
        }

        $error_message = sprintf('Unknown code error \'%s\'', $result['pass_changed']);
        $this->logger->error($error_message);

        throw new PasswordException($error_message);
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder;

use PommProject\Foundation\ConvertedResultIterator;
use PommProject\ModelManager\Exception\ModelException;
use Psr\Log\LoggerInterface;
use SonVideo\Cms\FrontOffice\AppBundle\Factory\CustomerOrder\CustomerOrderContextFactory;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\CustomerOrderManager;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\PaymentClient;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrder;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrderModel;
use SonVideo\Synapps\Client\RpcClientService;
use PommProject\ModelManager\Session as PommSession;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;

/**
 * Class AdditionalPaymentHandler
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder
 * <AUTHOR> Marniesse <<EMAIL>>
 */
class AdditionalPaymentHandler
{
    private AdditionalPaymentHolder $payment_holder;

    private RpcClientService $rpc_client;

    private CustomerOrderContextFactory $context_factory;

    private PommSession $pomm_session;

    private PaymentClient $payment_client;

    private RequestStack $request_stack;

    private RouterInterface $router;

    private LoggerInterface $logger;

    /**
     * AdditionalPaymentHandler constructor.
     */
    public function __construct(
        AdditionalPaymentHolder $payment_holder,
        PommSession $pomm_session,
        RpcClientService $rpc_client,
        CustomerOrderContextFactory $context_factory,
        PaymentClient $payment_client,
        RequestStack $request_stack,
        RouterInterface $router,
        LoggerInterface $logger
    ) {
        $this->payment_holder = $payment_holder;
        $this->rpc_client = $rpc_client;
        $this->context_factory = $context_factory;
        $this->pomm_session = $pomm_session;
        $this->payment_client = $payment_client;
        $this->request_stack = $request_stack;
        $this->router = $router;
        $this->logger = $logger;
    }

    /**
     * getAdditionalPaymentHolder
     */
    public function getAdditionalPaymentHolder(): AdditionalPaymentHolder
    {
        return $this->payment_holder;
    }

    /**
     * needsAdditionalPayment
     *
     * Check customer order balance and new selected payments, and return true if additional payment is required.
     */
    public function needsAdditionalPayment(CustomerOrder $customer_order): bool
    {
        if (!$customer_order->needsAdditionalPayment()) {
            return false;
        }

        return abs($customer_order->getBalance() + $this->payment_holder->getPaymentsTotalAmount()) > 0.0001;
    }

    /**
     * clearPayments
     */
    public function clearPayments(): self
    {
        $this->payment_holder->clearPayments();

        return $this;
    }

    /**
     * addPayment
     *
     * Price is generally negative, but it can be passed as positive. Method will convert it automatically.
     *
     * @param float|null    $price
     */
    public function addPayment(
        CustomerOrder $customer_order,
        int $payment_method_id,
        float $price = null,
        array $extra_data = []
    ): bool {
        $this->payment_holder->clearPayments();

        // If price is not provided, we compute the customer order balance.
        // Notice : this is informational - bridge should re-validate and calculate.
        if ($price === null) {
            $price = ($customer_order->getBalance() + $this->payment_holder->getPaymentsTotalAmount()) * -1;
        } else {
            $price = -1 * abs($price);
        }

        $this->payment_holder->addPayment(
            $customer_order->get('customer_order_id'),
            $payment_method_id,
            $price,
            $extra_data,
        );

        return $this->needsAdditionalPayment($customer_order);
    }

    /**
     * submit
     *
     *
     * @throws \Exception
     */
    public function submit(CustomerOrder $customer_order): self
    {
        // Fetch payment agreement from payment API
        $agreement_id = $this->checkNewPaymentInApi(
            $customer_order,
            $this->context_factory
                ->load($customer_order)
                ->addAdditionalPayments()
                ->getArray(),
        );

        $updated_payments = $this->addPaymentInErp($customer_order);

        // Update the customer order with new payments and update its modification date and status
        $this->pomm_session->getModel(CustomerOrderModel::class)->updateByPK(
            ['customer_order_id' => $customer_order->get('customer_order_id')],
            [
                'payments' => $updated_payments,
                'modified_at' => date('Y-m-d H:i:s'),
                'status' => CustomerOrder::STATUS_AWAITING_PAYMENT,
                'payment_agreement' => $agreement_id,
            ],
        );

        return $this;
    }

    /**
     * fetchPaymentMethods
     *
     * Fetch payment methods for additional payment.
     */
    public function fetchPaymentMethods(string $locale): array
    {
        $payments = $this->payment_holder->fetchPaymentMethods($locale);

        return $payments instanceof ConvertedResultIterator ? $payments->extract() : [];
    }

    /**
     * @throws ModelException
     */
    private function addPaymentInErp(CustomerOrder $customer_order): array
    {
        $request_payload = [
            [
                'customer_order_id' => $customer_order->get('customer_order_id'),
                // format the payment payload
                'payments' => array_map(static function ($payment): array {
                    return [
                        'payment_mean' => trim($payment['code']),
                        'created_at' => (new \DateTime())
                            ->setTimezone(new \DateTimeZone('Europe/Paris'))
                            ->format(\DATE_ATOM),
                        'amount' => abs($payment['price']),
                        'extra_data' => $payment['extra_data'] ?? [],
                    ];
                    // Extract new payment from php session
                }, $this->payment_holder->getPayments()),
                'return_url' => sprintf(
                    '%s%s',
                    $this->request_stack->getCurrentRequest()->getSchemeAndHttpHost(),
                    str_replace(
                        'x',
                        '{code}',
                        $this->router->generate('return_payment_result_with_payment_code', ['code' => 'x']),
                    ),
                ),
            ],
        ];

        $this->logger->debug(
            '[RPC] CustomerOrderManager::RPC_METHOD_CREATE_CUSTOMER_ORDER_PAYMENT_IN_ERP : Request',
            $request_payload,
        );

        // Add a new payment to the customer order
        $response = $this->rpc_client->call(
            'erp',
            CustomerOrderManager::RPC_METHOD_CREATE_CUSTOMER_ORDER_PAYMENT_IN_ERP,
            $request_payload,
        );

        $this->logger->debug(
            '[RPC] CustomerOrderManager::RPC_METHOD_CREATE_CUSTOMER_ORDER_PAYMENT_IN_ERP : Response',
            $response,
        );

        if (!is_array($response['result'] ?? null)) {
            throw new \Exception(
                sprintf('RPC call fails for %s', CustomerOrderManager::RPC_METHOD_CREATE_CUSTOMER_ORDER_PAYMENT_IN_ERP),
            );
        }

        $payment_action = $response['result']['action'] ?? null;
        $this->pomm_session->getModel(CustomerOrderModel::class)->updateByPK(
            ['customer_order_id' => $customer_order->get('customer_order_id')],
            [
                'payment_action' => is_array($payment_action) ? $payment_action : null,
            ],
        );

        $this->logger->debug('[RPC] CustomerOrderManager::RPC_METHOD_FETCH_CUSTOMER_ORDER_FROM_ERP : Request', [
            $customer_order->get('customer_order_id'),
        ]);

        // Get the updated customer order
        $response = $this->rpc_client->call('erp', CustomerOrderManager::RPC_METHOD_FETCH_CUSTOMER_ORDER_FROM_ERP, [
            $customer_order->get('customer_order_id'),
        ]);

        $this->logger->debug(
            '[RPC] CustomerOrderManager::RPC_METHOD_FETCH_CUSTOMER_ORDER_FROM_ERP : Response',
            $response,
        );

        // Only send back the updated payments
        return $response['result']['payments'] ?? [];
    }

    /**
     * checkNewPaymentInApi
     */
    private function checkNewPaymentInApi(CustomerOrder $customer_order, array $context): string
    {
        $agreement_id = $this->payment_client->checkPaymentsForOrder($context);
        if (!is_string($agreement_id)) {
            throw new \RuntimeException(
                sprintf(
                    'Agreement can not be fetched from Payment API for customer_order_id "%d".',
                    $customer_order->get('customer_order_id'),
                ),
            );
        }

        return $agreement_id;
    }
}

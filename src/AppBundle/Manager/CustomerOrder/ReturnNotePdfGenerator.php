<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder;

use Dompdf\Dompdf;
use Picqer\Barcode\BarcodeGeneratorHTML;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrder;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\ReturnNote;
use Twig\Environment;

/**
 * Class ReturnNotePdfGenerator
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class ReturnNotePdfGenerator
{
    private Environment $twig;

    private string $root_dir;

    private array $country_codes = [];

    /**
     * ReturnNotePdfGenerator constructor.
     */
    public function __construct(Environment $twig, string $root_dir)
    {
        $this->twig = $twig;
        $this->root_dir = $root_dir;
    }

    /**
     * setCountryCodes
     */
    public function setCountryCodes(array $country_codes): self
    {
        $this->country_codes = $country_codes;

        return $this;
    }

    /**
     * generate
     */
    public function generate(ReturnNote $return_note, CustomerOrder $customer_order): string
    {
        $html = $this->twig->render('AppBundle:Pdf:ReturnNote/content.html.twig', [
            'return_note' => $return_note->extract(),
            'customer_order' => $customer_order->extract(),
            'bar_code' => $this->getBarCode($return_note->get('return_note_id')),
            'web_dir' => realpath($this->root_dir . '/../web'),
            'countries' => $this->country_codes,
        ]);

        return $this->convertHtmlToPdf($html);
    }

    /**
     * getBarCode
     */
    protected function getBarCode(int $return_note_id): string
    {
        return (new BarcodeGeneratorHTML())->getBarcode(
            (string) $return_note_id,
            BarcodeGeneratorHTML::TYPE_CODE_128_B,
            2,
            90,
        );
    }

    /**
     * convertToPdf
     */
    private function convertHtmlToPdf(string $html): string
    {
        $dompdf = new Dompdf();
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        return $dompdf->output();
    }
}

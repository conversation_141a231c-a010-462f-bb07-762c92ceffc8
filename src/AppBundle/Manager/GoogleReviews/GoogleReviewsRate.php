<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\GoogleReviews;

use PommProject\ModelManager\Session;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\SystemSchema\ParameterModel;

class GoogleReviewsRate
{
    public const DATABASE_PARAMETER_NAME = 'google.reviews.rate';

    private Session $pomm_session;

    public function __construct(Session $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    public function get(): float
    {
        $rates = $this->pomm_session
            ->getModel(ParameterModel::class)
            ->getParameter(self::DATABASE_PARAMETER_NAME)
            ->extract();

        if (1 !== (is_countable($rates) ? count($rates) : 0)) {
            throw new \Exception('Unable to get the Google Reviews rate.');
        }

        return $rates[0]['value'];
    }
}

<?php
/*
 * This file is part of CMS Front Office package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Manager\Paypal;

use Psr\Log\LoggerInterface;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\RpcClientServiceTrait;

/**
 * Class ExpressCheckOutManager
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\Manager\Paypal
 * @copyright 2018 Son-Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 */
class ExpressCheckOutManager
{
    use RpcClientServiceTrait;

    public const RPC_METHOD_DO_PAYPAL_PAYMENT = 'order:do_paypal_express_checkout';

    private LoggerInterface $logger;

    /**
     * ExpressCheckOutManager constructor.
     */
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * doPaypalPaymentExpressCheckout
     *
     * @param  float|null $updated_amount
     * @param  array|null $shipping_address
     *
     * @throws \Exception, ExpressCheckoutException
     */
    public function doPaypalPaymentExpressCheckout(
        string $token,
        float $updated_amount = null,
        array $shipping_address = null
    ): bool {
        // Call bridge API to process payment
        $response = $this->getRpcClient()->call('bridge', static::RPC_METHOD_DO_PAYPAL_PAYMENT, [
            [
                'token' => $token,
                'amount' => $updated_amount,
                'shipping_address' => $shipping_address,
            ],
        ]);

        if (!isset($response['result'])) {
            throw new \Exception(sprintf('RPC call fails for %s', static::RPC_METHOD_DO_PAYPAL_PAYMENT));
        }

        $paypal_response = $response['result'][0]['ACK'] ?? ($response['result'][0] ?? '');
        // unfortunately, the sandbox respond with a boolean while production uses a payload
        // We don't know if that's always the case and PayPal does not recommend the use of the legacy SOAP API that we happen to use...
        $paypal_response = is_bool($paypal_response) && $paypal_response ? 'Success' : $paypal_response;

        if ($paypal_response !== 'Success') {
            $this->logger->error('Paypal payment cannot be finalized', [
                'token' => $token,
                'amount' => $updated_amount,
                'shipping_address' => $shipping_address,
                'result' => $paypal_response,
            ]);

            throw new ExpressCheckoutException('Paypal payment cannot be finalized');
        }

        return true;
    }
}

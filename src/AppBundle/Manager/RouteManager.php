<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Manager;

use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

/**
 * Class RouteManager
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Manager
 * <AUTHOR> <<EMAIL>>
 */
class RouteManager
{
    private RouterInterface $router;

    private ?string $base_url = null;

    /**
     * RouteManager constructor.
     */
    public function __construct(RouterInterface $router)
    {
        $this->router = $router;
    }

    /**
     * setBaseUrl
     */
    public function setBaseUrl(string $base_url): self
    {
        $this->base_url = $base_url;

        return $this;
    }

    /**
     * getRenewPasswordTemplateUrl
     *
     * Get the template to renew password.
     */
    public function getRenewPasswordTemplateUrl(string $culture): string
    {
        return $this->getRenewPasswordUrl('TOKEN', $culture);
    }

    /**
     * getRenewPasswordUrl
     *
     * Get an url to renew password.
     */
    protected function getRenewPasswordUrl(string $token, string $culture): string
    {
        return $this->generateUrlFromPathAndToken('customer_change_password', $token, $culture);
    }

    /**
     * getConfirmNewEmailTemplateUrl
     *
     * Get the template to confirm new email.
     */
    public function getConfirmNewEmailTemplateUrl(string $culture): string
    {
        return $this->getConfirmNewEmailUrl('TOKEN', $culture);
    }

    /**
     * getConfirmNewEmailUrl
     *
     * Get an url to confirm a new email.
     */
    protected function getConfirmNewEmailUrl(string $token, string $culture): string
    {
        return $this->generateUrlFromPathAndToken('customer_change_email', $token, $culture);
    }

    /**
     * generateUrlFromPathAndToken
     *
     * Generate an absolute url from a path with a token string.
     */
    protected function generateUrlFromPathAndToken(string $path, string $token, string $culture): string
    {
        $generated_path = $this->router->generate(
            $path,
            ['token' => $token, '_locale' => $culture],
            UrlGeneratorInterface::ABSOLUTE_PATH,
        );

        return sprintf('%s%s', $this->base_url, str_replace('TOKEN', '{TOKEN}', $generated_path));
    }
}

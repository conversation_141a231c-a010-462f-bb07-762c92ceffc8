<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model;

use PommProject\ModelManager\Model\Model;

/**
 * Class BaseModel
 *
 * Common operations on models.
 *
 * @package CMS Front-Office
 * <AUTHOR> <<EMAIL>>
 * @see     Model
 */
abstract class BaseModel extends Model
{
    /**
     * getModelRelation
     */
    protected function getModelRelation(string $model_class): string
    {
        return $this->getSession()
            ->getModel($model_class)
            ->getStructure()
            ->getRelation();
    }
}

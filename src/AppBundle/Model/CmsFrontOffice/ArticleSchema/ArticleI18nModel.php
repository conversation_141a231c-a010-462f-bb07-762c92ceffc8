<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\Foundation\Where;
use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use PommProject\ModelManager\Model\Projection;
use SonVideo\Cms\FrontOffice\AppBundle\Model\BaseModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema\PromoOfferModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\Tag;
use SonVideo\Cms\FrontOffice\AppBundle\Model\PriceTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\QueryHelperTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\ArticleI18n as ArticleI18nStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\SearchContextInterface;

/**
 * ArticleI18nModel
 *
 * Model class for table article_i18n.
 *
 * @see BaseModel
 */
class ArticleI18nModel extends BaseModel
{
    use WriteQueries;
    use QueryHelperTrait;
    use PriceTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ArticleI18nStructure();
        $this->flexible_entity_class = ArticleI18n::class;
    }

    /**
     * findUrlByPK
     *
     *
     * @throws  \Exception
     */
    public function findUrlByPK(int $article_id, string $culture): string
    {
        $article = $this->findByPK(['article_id' => $article_id, 'supported_culture_id' => $culture]);

        if (!$article instanceof ArticleI18n) {
            throw new \Exception(sprintf('Article %d for supported culture %s does not exists', $article_id, $culture));
        }

        return $article->get('article_url');
    }

    /**
     * searchUsingContext
     *
     * Perform a search on articles.
     */
    public function searchUsingContext(string $culture, SearchContextInterface $context): CollectionIterator
    {
        $condition = new Where();
        $search_query = $this->getSearchQueryProvider()->getSearchQuery($context, $condition, $culture);

        return $this->searchUsingQuery($search_query, $condition, $culture, $context);
    }

    public function searchUsingQuery(
        string $search_query,
        Where $condition,
        string $culture,
        ?SearchContextInterface $context = null
    ): CollectionIterator {
        $projection = $this->createCompleteArticleProjection();

        if ($context instanceof SearchContextInterface) {
            $sql = $this->selectSqlQuery($context, $projection, $condition, $culture);
        } else {
            $sql = $this->getQueryForAllArticles($projection, $condition, $culture);
        }

        $fields_list = implode(
            ",\n",
            array_filter(
                $projection->getFieldsWithTableAlias('ai18n'),
                function ($value, $key) {
                    if (strpos('article_count', (string) $key) === false) {
                        return $value;
                    }
                },
                ARRAY_FILTER_USE_BOTH,
            ),
        );

        $sql = strtr($sql, [
            '{search_query}' => $search_query,
            '{article_tag_query}' => $this->makeArticleTagQuery(),
            '{main_category_query}' => $this->makeMainCategoryQuery(),
            '{article_destock_query}' => $this->makeMainArticleDestockQuery(),
            '{projection}' => $projection->formatFieldsWithFieldAlias('ai18n'),
            '{article_i18n}' => $this->structure->getRelation(),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category}' => $this->getModelRelation(CategoryModel::class),
            '{category_i18n}' => $this->getModelRelation(CategoryI18nModel::class),
            '{stand}' => $this->getModelRelation(StandModel::class),
            '{stand_i18n}' => $this->getModelRelation(StandI18nModel::class),
            '{promo_offer}' => $this->getModelRelation(PromoOfferModel::class),
            '{media_join}' => $this->makeMediaJoinQuery('article_tag'),
            '{highlight_join}' => $this->makeHighlightJoinQuery('search_result'),
            '{color_join}' => $this->makeColorJoinQuery('search_result'),
            '{lengths_join}' => $this->makeLengthsJoinQuery('search_result'),
            '{length_query}' => $this->makeLengthQuery('search_result'),
            '{group_projection}' => $fields_list,
        ]);

        return $this->query($sql, array_merge([$culture], $condition->getValues()), $projection);
    }

    /**
     * selectSqlQuery
     *
     *
     */
    protected function selectSqlQuery(
        SearchContextInterface $context,
        Projection $projection,
        Where $condition,
        string $culture
    ): string {
        return $context->isQueryInAllArticles()
            ? $this->getQueryForAllArticles($projection, $condition, $culture)
            : $this->getQueryForMainArticles($projection);
    }

    /**
     * createCompleteArticleProjection
     */
    private function createCompleteArticleProjection(): Projection
    {
        return $this->createProjection()
            ->setField('sku', 'a.sku', 'text')
            ->setField('name', 'a.name', 'text')
            ->setField('unbasketable_reason', 'a.unbasketable_reason', 'text')
            ->setField('selling_price', static::getPriceIfNoHidden('selling_price'), 'numeric')
            ->setField('reference_price', static::getPriceIfNoHidden('reference_price'), 'numeric')
            ->setField('price', static::getPriceIfNoHidden(), 'jsonb')
            ->setField('estimated_delivery_time', 'p.estimated_delivery_time', 'article.availability_delay')
            ->setField('informational_stock', 'p.informational_stock', 'integer')
            ->setField('display_mode', 'cc.display_mode', 'article.display_mode')
            ->setField('score', 'ROUND(cc.review_score, 1)', 'float')
            ->setField(
                'review_score_index',
                'CASE WHEN cc.review_score IS NOT NULL THEN GREATEST(cc.review_score::int, 1) ELSE null END',
                'int',
            )
            ->setField('cc_short_description', 'cci18n.editorial_content->>\'short_description\'', 'text')
            ->setField('brand_name', 'b.name', 'text')
            ->setField('article_url', 'a.article_url', 'public.citext')
            ->setField('media_uri', 'media.media_uri', 'public.citext')
            ->setField('media_meta', 'media.meta', 'jsonb')
            ->setField('category_id', 'c.category_id', 'int4')
            ->setField('category_slug', 'c.slug', 'text')
            ->setField('category_name', 'ci18n.name', 'text')
            ->setField('category_parent_id', 'c.category_parent_id', 'int4')
            ->setField('article_attributes', 'ac.attributes->(parameters.culture)', 'jsonb')
            ->setField('article_tag', 'at.article_tag', 'text[]')
            ->setField('main_category_name', 'ci18n_main.name', 'text')
            ->setField('parent_destock_price', 'article_destock.price', 'jsonb')
            ->setField('main_category_aliases', 'c_main.aliases', 'hstore')
            ->setField('packaged_articles', 'a.packaged_articles', 'int[]')
            ->setField('highlights', 'h.highlights', 'jsonb')
            ->setField('ranking', 'sr.ranking', 'float')
            ->setField('common_content_name', 'cci18n.name', 'text')
            ->setField('common_content_id', 'cci18n.common_content_id', 'text')
            ->setField('color_indexes', 'co.color_indexes', 'text[]')
            ->setField('color_labels', 'co.color_labels', 'text[]')
            ->setField('is_default_article', 'a.article_id = cc.default_article_id', 'boolean')
            ->setField('article_lengths', 'al.article_lengths', 'json')
            ->setField('article_length', 'l.article_length', 'text')
            ->setField('article_color', 'ac.attributes -> (parameters.culture) ->> \'couleur\'', 'text')
            ->setField('main_stand_slug', 's.slug', 'text')
            ->setField('main_stand_name', 'si18n.name', 'text')
            ->setField('promo_codes_description', 'pcc.promo_short_description', 'text[]')
            ->setField('sort_by', 's.sort_by', 'integer')
            ->setField('has_an_ongoing_supplier_order', 'p.has_an_ongoing_supplier_order', 'bool');
    }

    /**
     * getQueryForAllArticles
     *
     * Returns the query that fetchs products directly.
     */
    private function getQueryForAllArticles(Projection $projection, Where $condition, string $culture): string
    {
        $condition->andWhere('', [$culture]);
        $projection->setField('article_count', '1::int4', 'int4');

        return <<<SQL
        WITH
          parameters(culture) AS (SELECT $*::bpchar),
          search_result       AS ({search_query}),
          article_tag         AS ({article_tag_query}),
          index_category      AS ({main_category_query}),
          article_destock     AS ({article_destock_query}),
          media               AS ({media_join}),
          highlight           AS ({highlight_join}),
          colors              AS ({color_join}),
          article_lengths     AS ({lengths_join}),
          article_length     AS ({length_query}),
          promo_code_contents AS (
            SELECT
              sr.article_id,
              array_agg(po.article_short_description_i18n -> p.culture ORDER BY upper(po.lifetime) DESC)
              filter (WHERE po.article_short_description_i18n -> p.culture IS NOT NULL) AS promo_short_description
            FROM
              search_result sr
              INNER JOIN {promo_offer} po ON sr.article_id = ANY(po.computed_article_ids)
              CROSS JOIN parameters p
            WHERE
              po.lifetime @> now()
              AND po.article_short_description_i18n ? p.culture
              AND po.deactivated_reason IS NULL
            GROUP BY sr.article_id
          )
        SELECT {projection}
        FROM {article_i18n} ai18n
          CROSS JOIN parameters
          INNER JOIN article_tag at        USING (article_id)
          INNER JOIN {article} a           ON ai18n.article_id = a.article_id
          INNER JOIN {brand} b             ON a.brand_id = b.brand_id
          INNER JOIN {product} p           USING (sku)
          INNER JOIN {common_content} cc   ON a.common_content_id = cc.common_content_id
          INNER JOIN {common_content_i18n} cci18n   ON cc.common_content_id = cci18n.common_content_id
            AND cci18n.supported_culture_id = ai18n.supported_culture_id
          INNER JOIN {article_category} ac ON a.article_id = ac.article_id
          INNER JOIN {category} c          ON ac.category_id = c.category_id
          INNER JOIN {category_i18n} ci18n ON c.category_id = ci18n.category_id
            AND ci18n.supported_culture_id = ai18n.supported_culture_id
          INNER JOIN search_result sr          ON sr.article_id = ai18n.article_id
          LEFT JOIN media                      ON media.article_id = ai18n.article_id
          LEFT JOIN index_category ic          ON ic.category_id = ac.category_id
          LEFT JOIN {category} c_main          ON c_main.category_id = ic.main_category_id
          LEFT JOIN {category_i18n} ci18n_main ON ci18n_main.category_id = ic.main_category_id
          LEFT JOIN article_destock ad         ON ad.article_id = ai18n.article_id
          LEFT JOIN {article} article_destock  ON article_destock.article_id = ad.parent_destock_id
          LEFT JOIN highlight h                ON h.article_id = ai18n.article_id
          LEFT JOIN colors co                  ON co.article_id = ai18n.article_id
          LEFT JOIN article_lengths al         ON al.article_id = ai18n.article_id
          LEFT JOIN article_length l           ON l.article_id = ai18n.article_id
          LEFT JOIN {stand} s                  ON a.default_stand_id = s.stand_id
          LEFT JOIN {stand_i18n} si18n         ON s.stand_id = si18n.stand_id
                                               AND si18n.supported_culture_id = ai18n.supported_culture_id
          LEFT JOIN promo_code_contents pcc    ON pcc.article_id = a.article_id
        WHERE ai18n.supported_culture_id = $*::text
        GROUP BY {group_projection}
        SQL;
    }

    /**
     * getQueryForMainArticles
     *
     * Returns the query that fetchs the main product of the matching common content pages
     */
    private function getQueryForMainArticles(Projection $projection): string
    {
        $projection->setField('article_count', 'count(a.article_id)', 'int4');

        return <<<SQL
        WITH
          parameters(culture) AS (SELECT $*::bpchar),
          search_result       AS ({search_query}),
          article_tag         AS ({article_tag_query}),
          index_category      AS ({main_category_query}),
          article_destock     AS ({article_destock_query}),
          media               AS ({media_join}),
          highlight           AS ({highlight_join}),
          colors              AS ({color_join}),
          article_lengths     AS ({lengths_join}),
          article_length      AS ({length_query}),
          promo_code_contents AS (
            SELECT
              sr.article_id,
              array_agg(po.article_short_description_i18n -> p.culture ORDER BY upper(po.lifetime) DESC)
              filter (WHERE po.article_short_description_i18n -> p.culture IS NOT NULL) AS promo_short_description
            FROM
              search_result sr
              INNER JOIN {promo_offer} po ON sr.article_id = ANY(po.computed_article_ids)
              CROSS JOIN parameters p
            WHERE
              po.lifetime @> now()
              AND po.article_short_description_i18n ? p.culture
              AND po.deactivated_reason IS NULL
            GROUP BY sr.article_id
          )
        SELECT {projection}
        FROM   {article_i18n} ai18n
          CROSS JOIN parameters
          INNER JOIN {common_content} cc   ON cc.default_article_id = ai18n.article_id
          INNER JOIN article_tag at        ON cc.default_article_id = at.article_id
          INNER JOIN {common_content_i18n} cci18n   ON cc.common_content_id = cci18n.common_content_id
            AND cci18n.supported_culture_id = ai18n.supported_culture_id
          INNER JOIN {article} a           ON ai18n.article_id = a.article_id
          INNER JOIN {brand} b             ON a.brand_id = b.brand_id
          INNER JOIN {product} p           USING (sku)
          INNER JOIN {article_category} ac ON a.article_id = ac.article_id
          INNER JOIN {category} c          ON ac.category_id = c.category_id
          INNER JOIN {category_i18n} ci18n ON c.category_id = ci18n.category_id
                                           AND ci18n.supported_culture_id = ai18n.supported_culture_id
          INNER JOIN search_result sr          ON sr.article_id = ai18n.article_id
          LEFT JOIN media                      ON media.article_id = ai18n.article_id
          LEFT JOIN index_category ic          ON ic.category_id = ac.category_id
          LEFT JOIN {category} c_main          ON c_main.category_id = ic.main_category_id
          LEFT JOIN {category_i18n} ci18n_main ON ci18n_main.category_id = ic.main_category_id
          LEFT JOIN article_destock ad         ON ad.article_id = ai18n.article_id
          LEFT JOIN {article} article_destock  ON article_destock.article_id = ad.parent_destock_id
          LEFT JOIN highlight h                ON h.article_id = ai18n.article_id
          LEFT JOIN colors co                  ON co.article_id = ai18n.article_id
          LEFT JOIN article_lengths al         ON al.article_id = ai18n.article_id
          LEFT JOIN article_length l           ON l.article_id = ai18n.article_id
          LEFT JOIN {stand} s                  ON a.default_stand_id = s.stand_id
          LEFT JOIN {stand_i18n} si18n         ON s.stand_id = si18n.stand_id
                                               AND si18n.supported_culture_id = ai18n.supported_culture_id
          LEFT JOIN promo_code_contents pcc    ON pcc.article_id = a.article_id
        GROUP BY {group_projection}
        SQL;
    }

    /**
     * makeArticleTagQuery
     *
     * Returns the query to get all tags (in one array) by article_id.
     */
    private function makeArticleTagQuery(): string
    {
        $sql = <<<SQL
        SELECT
            sr.article_id,
            array_agg(at.tag_path) as article_tag
        FROM search_result sr
          LEFT JOIN {article_tag} at ON sr.article_id = at.article_id
        GROUP BY sr.article_id
        SQL;

        return strtr($sql, [
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
        ]);
    }

    /**
     * makeMainArticleDestockQuery
     */
    private function makeMainArticleDestockQuery(): string
    {
        $sql = <<<SQL
        SELECT
          sr.article_id,
          min(subltree(at.tag_path, {article_id_position_start}, {article_id_position_end})::TEXT)::int AS parent_destock_id
        FROM search_result sr
          LEFT JOIN {article_tag} at USING (article_id)
        WHERE at.tag_path <@ '{destock_tag}'::ltree
        GROUP BY sr.article_id
        SQL;

        return strtr($sql, [
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{destock_tag}' => Tag::DESTOCK_BASE_TAG,
            '{article_id_position_start}' => count(explode('.', Tag::DESTOCK_BASE_TAG)),
            '{article_id_position_end}' => count(explode('.', Tag::DESTOCK_BASE_TAG)) + 1,
        ]);
    }

    /**
     * makeMainCategoryQuery
     */
    private function makeMainCategoryQuery(): string
    {
        $sql = <<<SQL
        WITH RECURSIVE index_category (category_id, main_category_id) as (
            SELECT
                category_id as category_id,
                category_id as main_category_id
            FROM {category}
            WHERE category_parent_id IS NULL

          UNION ALL

            SELECT
                c.category_id,
                ic.main_category_id
            FROM {category} c
              JOIN index_category ic on c.category_parent_id = ic.category_id
        )
        SELECT
          category_id,
          main_category_id
        FROM index_category
        SQL;

        return strtr($sql, [
            '{category}' => $this->getModelRelation(CategoryModel::class),
        ]);
    }

    /**
     * findBestSellerForStand
     *
     *
     */
    public function findBestSellerForStand(string $stand_tag, string $culture, int $limit = 0): CollectionIterator
    {
        $sql = <<<SQL
        WITH
          parameters AS (
            SELECT $*::char(2) AS culture
          ),
          article_by_stand AS (
            SELECT
              a.article_id        AS article_id,
              p.invoiced_quantity AS invoiced_quantity,
              COALESCE(p.invoiced_quantity, 0) * COALESCE((a.price->>'selling_price')::numeric(10,2), 0) AS turnover,
              s.stand_id          AS stand_id,
              s.slug              AS stand_slug,
              si18n.name          AS stand_name,
              -- row_number is used to keep only one article_id in the next query
              row_number() OVER (PARTITION BY a.article_id ORDER BY s.stand_id DESC ) AS row_number
            FROM {article_i18n} ai18n
              CROSS JOIN parameters param
              INNER JOIN {article} a         ON a.article_id = ai18n.article_id
              INNER JOIN {article_tag} at    ON at.article_id = a.article_id
              INNER JOIN {product} p         ON p.sku = a.sku
              INNER JOIN {stand} s           ON s.tag_path = at.tag_path
              INNER JOIN {stand_i18n} si18n  ON si18n.stand_id = s.stand_id
                                             AND si18n.supported_culture_id = ai18n.supported_culture_id
              INNER JOIN {common_content_i18n} cci18n
                                             ON cci18n.common_content_id = a.common_content_id
                                             AND cci18n.supported_culture_id = ai18n.supported_culture_id
            WHERE
              {condition}
            ORDER BY p.invoiced_quantity DESC
        ),
        -- Get only an unique article_id (stand is choosen arbitrary) and calculate its position in stand (best seller first)
        article_unique_by_stand AS (
          SELECT
            article_id,
            invoiced_quantity,
            turnover,
            stand_slug,
            stand_name,
            row_number() OVER (PARTITION BY stand_id ORDER BY invoiced_quantity DESC) AS row_number
          FROM article_by_stand
          WHERE row_number = 1
        ),
        media AS ({media_join}),
        article_highlight AS ({highlight_query})
        SELECT {projection}
        FROM   article_unique_by_stand aubs
          CROSS JOIN parameters param
          INNER JOIN {article_i18n} ai18n  ON ai18n.article_id = aubs.article_id
          INNER JOIN {article} a           ON ai18n.article_id = a.article_id
          INNER JOIN {brand} b             ON b.brand_id = a.brand_id
          INNER JOIN {common_content_i18n} cci18n
                                           ON cci18n.common_content_id = a.common_content_id
                                           AND cci18n.supported_culture_id = ai18n.supported_culture_id
          INNER JOIN {common_content} cc   ON cc.common_content_id = a.common_content_id
          LEFT JOIN {article_category} ac ON a.article_id = ac.article_id
          LEFT JOIN {category} c          ON ac.category_id = c.category_id
          LEFT JOIN {category_i18n} ci18n ON c.category_id = ci18n.category_id
                                           AND ci18n.supported_culture_id = ai18n.supported_culture_id
          LEFT JOIN media                  ON media.article_id = ai18n.article_id
          LEFT JOIN article_highlight      ON article_highlight.article_id = aubs.article_id
        WHERE ai18n.supported_culture_id = param.culture
        ORDER BY aubs.row_number, aubs.turnover DESC
        {limit}
        SQL;

        $where = Where::create('ai18n.supported_culture_id = param.culture AND at.tag_path <@ $*', [$stand_tag])
            ->andWhere('a.unbasketable_reason is null')
            ->andWhere('p.estimated_delivery_time = $*', [Product::AVAILABILITY_1_2_DAYS]);
        $projection = $this->createProjection()
            ->setField('brand_name', 'b.name', 'text')
            ->setField('display_mode', 'cc.display_mode', 'text')
            ->setField(
                'note',
                'CASE WHEN cc.review_score IS NOT NULL THEN ROUND(cc.review_score, 1) ELSE null END',
                'int',
            )
            ->setField('price', static::getPriceIfNoHidden(), 'jsonb')
            ->setField('name', 'a.name', 'text')
            ->setField('cc_short_description', 'cci18n.editorial_content->>\'short_description\'', 'text')
            ->setField('article_url', 'a.article_url', 'public.citext')
            ->setField('image_url', 'media.media_uri', 'public.citext')
            ->setField('media_meta', 'media.meta', 'jsonb')
            ->setField('stand_slug', 'aubs.stand_slug', 'text')
            ->setField('stand_name', 'aubs.stand_name', 'text')
            ->setField('highlights', 'article_highlight.highlights', 'jsonb')
            ->setField('common_content_name', 'cci18n.name', 'text')
            ->setField('category_name', 'ci18n.name', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('ai18n'),
            '{article_i18n}' => $this->structure->getRelation(),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{stand}' => $this->getModelRelation(StandModel::class),
            '{stand_i18n}' => $this->getModelRelation(StandI18nModel::class),
            '{media_join}' => $this->makeMediaJoinQuery('article_unique_by_stand'),
            '{highlight_query}' => $this->makeHighlightJoinQuery('article_unique_by_stand'),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category}' => $this->getModelRelation(CategoryModel::class),
            '{category_i18n}' => $this->getModelRelation(CategoryI18nModel::class),
            '{condition}' => $where,
            '{limit}' => $limit !== 0 ? sprintf('LIMIT %d', $limit) : '',
        ]);

        return $this->query($sql, array_merge([$culture], $where->getValues()), $projection);
    }

    /**
     * findTouchScreenSelection
     *
     * Return articles with needed data to display a touch screen store.
     * Based on the request generated by search engine on a selection.
     */
    public function findTouchScreenSelection(string $tag, string $culture = APP_DEFAULT_LOCALE): CollectionIterator
    {
        $sql = <<<SQL
        WITH
          parameters(tag_path, culture) AS (SELECT $*::text::ltree, $*::bpchar),
          search_result AS (
            SELECT
              at.article_id
            FROM
              {article_tag} at
              CROSS JOIN parameters
              JOIN {article} a USING (article_id)
            WHERE
              at.tag_path <@ parameters.tag_path
              AND a.unbasketable_reason is null
            GROUP BY
              article_id
          ),
          article_tag AS (
            SELECT
              sr.article_id,
              array_agg(at.tag_path) as article_tag
            FROM
              search_result sr
              LEFT JOIN {article_tag} at ON sr.article_id = at.article_id
            GROUP BY
              sr.article_id
          ),
          media AS (
            WITH parent_destock AS (
              SELECT
                sr.article_id,
                min(subltree(at.tag_path, 2, 3):: TEXT):: int AS parent_article_id
              FROM
                article_tag sr
                INNER JOIN {article_tag} at ON at.article_id = sr.article_id
              WHERE
                at.tag_path <@ 'article.destock'::ltree
              GROUP BY
                sr.article_id
            )
            SELECT
              sr.article_id,
              COALESCE(am.media_variation, am2.media_variation) AS media_variation
            FROM
              article_tag sr
              CROSS JOIN parameters
              LEFT JOIN parent_destock pd ON pd.article_id = sr.article_id
              LEFT JOIN {article_media_i18n} ami18n ON ami18n.article_id = sr.article_id
                                                       AND ami18n.supported_culture_id = parameters.culture
                                                       AND ami18n.display_order = 1
                                                       AND ami18n.type = 'IMAGE'::article.article_media_type
              LEFT JOIN {article_media} am ON am.media_id = ami18n.media_id
              LEFT JOIN {article_media_i18n} ami18n2 ON ami18n2.article_id = pd.parent_article_id
                                                        AND ami18n2.supported_culture_id = parameters.culture
                                                        AND ami18n2.display_order = 1
                                                        AND ami18n2.type = 'IMAGE'::article.article_media_type
              LEFT JOIN {article_media} am2 ON am2.media_id = ami18n2.media_id
          )
        SELECT
          {projection}
        FROM
          {article_i18n} ai18n
          CROSS JOIN parameters
          INNER JOIN article_tag at USING (article_id)
          INNER JOIN {article} a ON ai18n.article_id = a.article_id
          INNER JOIN {brand} b ON a.brand_id = b.brand_id
          INNER JOIN {common_content} cc ON a.common_content_id = cc.common_content_id
          LEFT JOIN media ON media.article_id = ai18n.article_id
        WHERE
          ai18n.supported_culture_id = parameters.culture
        ORDER BY a.article_id ASC
        SQL;
        $projection = $this->createProjection()
            ->setField('name', 'a.name', 'text')
            ->setField('display_mode', 'cc.display_mode', 'text')
            ->setField('brand_name', 'b.name', 'text')
            ->setField('media_variation', 'media.media_variation', 'jsonb')
            ->setField('selling_price', static::getPriceIfNoHidden('selling_price'), 'numeric')
            ->setField('price', static::getPriceIfNoHidden(), 'jsonb');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('ai18n'),
            '{article_i18n}' => $this->structure->getRelation(),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
        ]);

        return $this->query($sql, [$tag, $culture], $projection);
    }
}

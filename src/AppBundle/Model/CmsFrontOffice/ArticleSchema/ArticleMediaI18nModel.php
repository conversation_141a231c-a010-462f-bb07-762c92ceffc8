<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\ArticleMediaI18n as ArticleMediaI18nStructure;

/**
 * ArticleMediaI18nModel
 *
 * Model class for table article_media_i18n.
 *
 * @see Model
 */
class ArticleMediaI18nModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ArticleMediaI18nStructure();
        $this->flexible_entity_class = ArticleMediaI18n::class;
    }
}

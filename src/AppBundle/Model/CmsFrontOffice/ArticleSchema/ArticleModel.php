<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\Foundation\ResultIterator;
use PommProject\Foundation\Where;
use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use PommProject\ModelManager\Model\Projection;
use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\Article as ArticleStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\CommonContentQuestionModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\GuideAssociatedStandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema\PromoOfferModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\Tag;
use SonVideo\Cms\FrontOffice\AppBundle\Model\PriceTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\QueryHelperTrait;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\SearchQueriesMakerTrait;

/**
 * ArticleModel
 *
 * Model class for table article.
 *
 * @see Model
 */
class ArticleModel extends Model
{
    use WriteQueries;
    use SearchQueriesMakerTrait;
    use QueryHelperTrait;
    use PriceTrait;

    public const RELATED_LIMIT = 4;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ArticleStructure();
        $this->flexible_entity_class = Article::class;
    }

    /**
     * getPommSession
     */
    public function getPommSession(): PommSession
    {
        return $this->session;
    }

    /**
     * findByUriAndCulture
     *
     *
     * @return array|null
     */
    public function findByUriAndCulture(string $uri, string $culture)
    {
        $parameters_query = <<<SQL
        SELECT $*::public.citext AS article_url, $*::char(2) AS culture
        SQL;
        $get_article_query = <<<SQL
        SELECT
          article_id,
          common_content_id
        FROM {article} a
          CROSS JOIN parameters p
        WHERE a.article_url = p.article_url
        SQL;
        $projection = $this->createProjection()
            ->setField('editorial_content', 'ai.editorial_content', 'json')
            ->setField('common_content', 'cmsi', 'article.common_content_i18n')
            ->setField('display_mode', 'cms.display_mode', 'article.display_mode')
            ->setField('brand', 'ab', 'article.brand')
            ->setField('estimated_delivery_time', 'p.estimated_delivery_time', 'article.availability_delay')
            ->setField('informational_stock', 'p.informational_stock', 'integer')
            ->setField('press_articles', 'pa.press_articles', 'jsonb')
            ->setField('article_medias', 'am.article_medias', 'jsonb[]')
            ->setField('article_documents', 'adoc.article_documents', 'jsonb[]')
            ->setField('technology', 'full_technology.technology', 'article.technology[]')
            ->setField('technology_i18n', 'full_technology.technology_i18n', 'article.technology_i18n[]')
            ->setField('declination_type', 'cms.declination_type', 'article.declination_type')
            ->setField('highlights', 'highlight.highlights', 'jsonb')
            ->setField('parent_destock_id', 'ad.article_id', 'integer')
            ->setField('parent_destock_unbasketable_reason', 'ad.unbasketable_reason', 'text')
            ->setField('parent_destock_price', 'ad.price', 'jsonb')
            ->setField('parent_destock_url', 'ad.article_url', 'text')
            ->setField('parent_destock_eav_attributes', 'ad.eav_attributes', 'jsonb')
            ->setField('parent_destock_editorial_content', 'adi.editorial_content', 'jsonb')
            ->setField('main_article_url', 'ma.article_url', 'text')
            ->setField('promo_code_contents', 'pcc.article_editorial_content', 'text[]')
            ->setField('review_score', 'cms.review_score', 'float')
            ->setField('nb_advices', 'COALESCE(cms.nb_advices, 0)', 'integer')
            ->setField('nb_recommendations', 'COALESCE(cms.nb_recommendations, 0)', 'integer')
            ->setField('ean', 'p.ean', 'text')
            ->setField('has_an_ongoing_supplier_order', 'p.has_an_ongoing_supplier_order', 'bool')
            ->setField('reference_price', "a.price->>'reference_price'", 'float')
            ->setField(
                'price_discount_percent',
                "(1 - ((a.price->>'selling_price')::numeric / (a.price->>'reference_price')::numeric)) * 100",
                'float',
            );

        $sql = $this->makeArticleQuery($parameters_query, $get_article_query, $projection);

        return $this->query($sql, [$uri, $culture], $projection)->current();
    }

    public function findByUriAndCultureForReviewAndQuestionAnswer(string $uri, string $culture)
    {
        $parameters_query = <<<SQL
        SELECT $*::public.citext AS article_url, $*::char(2) AS culture
        SQL;
        $get_article_query = <<<SQL
        SELECT
          article_id,
          common_content_id
        FROM {article} a
          CROSS JOIN parameters p
        WHERE a.article_url = p.article_url
        SQL;
        $projection = $this->createProjection()
            ->setField('editorial_content', 'ai.editorial_content', 'json')
            ->setField('common_content', 'cmsi', 'article.common_content_i18n')
            ->setField('display_mode', 'cms.display_mode', 'article.display_mode')
            ->setField('brand', 'ab', 'article.brand')
            ->setField('informational_stock', 'p.informational_stock', 'integer')
            ->setField('article_medias', 'am.article_medias', 'jsonb[]')
            ->setField('highlights', 'highlight.highlights', 'jsonb')
            ->setField('parent_destock_price', 'ad.price', 'jsonb')
            ->setField('parent_destock_url', 'ad.article_url', 'text')
            ->setField('parent_destock_editorial_content', 'adi.editorial_content', 'jsonb')
            ->setField('main_article_url', 'ma.article_url', 'text')
            ->setField('review_score', 'cms.review_score', 'float')
            ->setField('nb_advices', 'COALESCE(cms.nb_advices, 0)', 'integer')
            ->setField('nb_recommendations', 'COALESCE(cms.nb_recommendations, 0)', 'integer')
            ->setField('ean', 'p.ean', 'text')
            ->setField('brand_name', 'ab.name', 'text')
            ->setField('has_an_ongoing_supplier_order', 'p.has_an_ongoing_supplier_order', 'bool');

        $sql = $this->makeArticleQuery($parameters_query, $get_article_query, $projection);

        return $this->query($sql, [$uri, $culture], $projection)->current();
    }

    /**
     * findByIdWithCulture
     *
     *
     * @return array|null
     */
    public function findByIdWithCulture(int $article_id, string $culture)
    {
        $parameters_query = <<<SQL
        SELECT $*::int AS article_id, $*::char(2) AS culture
        SQL;
        $get_article_query = <<<SQL
        SELECT
          a.article_id,
          common_content_id
        FROM {article} a
          CROSS JOIN parameters p
        WHERE a.article_id = p.article_id
        SQL;
        $projection = $this->createProjection()
            ->setField('brand', 'ab', 'article.brand')
            ->setField('article_medias', 'am.article_medias', 'jsonb[]');

        $sql = $this->makeArticleQuery($parameters_query, $get_article_query, $projection);

        return $this->query($sql, [$article_id, $culture], $projection)->current();
    }

    /**
     * makeArticleQuery
     *
     *
     */
    protected function makeArticleQuery(
        string $parameters_query,
        string $get_article_query,
        Projection $projection
    ): string {
        $sql = <<<SQL
        WITH
          parameters AS (
            {parameters_query}
          ),
          get_article AS (
            {get_article_query}
          ),
          get_article_i18n AS (
            SELECT
              ai18n.article_id,
              ai18n.supported_culture_id
            FROM article.article_i18n ai18n
              INNER JOIN get_article ga ON ga.article_id = ai18n.article_id
              CROSS JOIN parameters p
            WHERE ai18n.supported_culture_id = p.culture
          ),
          get_article_tags AS (
            SELECT
              a.article_id,
              at.tag_path
            FROM get_article a
              INNER JOIN {article_tag} AS at ON at.article_id = a.article_id
          ),
          press_articles AS (
            SELECT
              ga.article_id,
              jsonb_agg(row_to_json(apa) :: JSONB - 'press_award_logo_url' :: TEXT || jsonb_build_object('press_award_logo_url', COALESCE(pma.logo_uri, apa.press_award_logo_url))) AS press_articles
            FROM get_article ga
              INNER JOIN {press_article} apa ON apa.common_content_id = ga.common_content_id
              LEFT JOIN {press_magazine_award} pma ON apa.press_award_tag_path = pma.tag_path
            GROUP BY ga.article_id
          ),
          destock AS (
            SELECT
              article_id,
              (subltree(tag_path, {article_id_position_start}, {article_id_position_end})::TEXT)::int AS parent_destock_id
            FROM get_article_tags
            WHERE tag_path <@ '{destock_tag}'::ltree
            LIMIT 1
          ),
          article_medias AS (
            -- Get medias from the product or its parent if destock.
            -- Keep destock's own medias, marked as second_life.
            SELECT
              gai18n.article_id,
              array_agg(
                  json_build_object(
                      'media_id', am.media_id,
                      'article_id', am.article_id,
                      'media_variation', am.media_variation,
                      'meta', ami18n.meta,
                      'display_order', ami18n.display_order,
                      'url', {largest_image_url},
                      'is_second_life', pd.parent_destock_id is not null and am.article_id = gai18n.article_id
                  )
              ORDER BY am.article_id = gai18n.article_id, ami18n.display_order ASC
              ) AS article_medias
            FROM {article_media} am
              CROSS JOIN get_article_i18n gai18n
              JOIN {article_media_i18n} ami18n
                ON am.media_id = ami18n.media_id AND ami18n.supported_culture_id = gai18n.supported_culture_id
                  AND ami18n.type = 'IMAGE'::article.article_media_type
              LEFT JOIN destock pd ON pd.article_id = gai18n.article_id
            WHERE
              am.article_id = gai18n.article_id OR am.article_id = pd.parent_destock_id
            GROUP BY gai18n.article_id
          ),
        article_documents AS (
            SELECT
              gai18n.article_id,
              array_agg(
                  json_build_object(
                      'media_id', am.media_id,
                      'article_id', am.article_id,
                      'media_variation', am.media_variation,
                      'meta', ami18n.meta,
                      'display_order', ami18n.display_order
                  )
              ORDER BY am.article_id = gai18n.article_id, ami18n.display_order ASC
              ) AS article_documents
            FROM {article_media} am
              CROSS JOIN get_article_i18n gai18n
              JOIN {article_media_i18n} ami18n
                ON am.media_id = ami18n.media_id AND ami18n.supported_culture_id = gai18n.supported_culture_id
                  AND ami18n.type = 'DOCUMENT'::article.article_media_type
              LEFT JOIN destock pd ON pd.article_id = gai18n.article_id
            WHERE
              am.article_id = gai18n.article_id OR am.article_id = pd.parent_destock_id
            GROUP BY gai18n.article_id
          ),
          full_technology AS (
            SELECT
              gat.article_id,
              array_agg(al ORDER BY ali.expression) AS technology,
              array_agg(ali ORDER BY ali.expression) AS technology_i18n
            FROM get_article_tags gat
              JOIN {technology} al USING (tag_path)
              JOIN {technology_i18n} ali USING (technology_id)
            GROUP BY gat.article_id
          ),
          highlight AS ({highlight_query}),
          promo_code_contents AS (
            SELECT
              ga.article_id,
              array_agg(po.article_editorial_content_i18n -> p.culture)
              filter (WHERE po.article_editorial_content_i18n -> p.culture IS NOT NULL) AS article_editorial_content,
              array_agg(po.article_short_description_i18n -> p.culture ORDER BY upper(po.lifetime) DESC)
              filter (WHERE po.article_short_description_i18n -> p.culture IS NOT NULL) AS promo_short_description
            FROM
              get_article ga
              INNER JOIN {promo_offer} po ON ga.article_id = ANY(po.computed_article_ids)
              CROSS JOIN parameters p
            WHERE
              po.lifetime @> now()
              AND po.deactivated_reason IS NULL
              AND (po.article_editorial_content_i18n ? p.culture
              OR po.article_short_description_i18n ? p.culture)
            GROUP BY ga.article_id
          )
        SELECT
          {projection}
        FROM
          get_article_i18n gai18n
          INNER JOIN {article} a ON a.article_id = gai18n.article_id
          INNER JOIN {article_i18n} ai ON ai.article_id = gai18n.article_id
                                       AND ai.supported_culture_id = gai18n.supported_culture_id
          INNER JOIN {common_content} cms
            ON a.common_content_id = cms.common_content_id
          INNER JOIN article.common_content_i18n cmsi
            ON cms.common_content_id = cmsi.common_content_id
               AND cmsi.supported_culture_id = ai.supported_culture_id
          LEFT JOIN {article} ma
            ON cms.default_article_id = ma.article_id
          INNER JOIN {brand} ab
            ON a.brand_id = ab.brand_id
          INNER JOIN {product} p ON a.sku = p.sku
          LEFT JOIN press_articles pa ON pa.article_id = a.article_id
          LEFT JOIN article_medias am ON am.article_id = a.article_id
          LEFT JOIN article_documents adoc ON adoc.article_id = a.article_id
          LEFT JOIN full_technology ON full_technology.article_id = a.article_id
          LEFT JOIN highlight ON highlight.article_id = a.article_id
          LEFT JOIN promo_code_contents pcc ON pcc.article_id = a.article_id
          LEFT JOIN destock ON destock.article_id = a.article_id
          LEFT JOIN {article} ad ON ad.article_id = destock.parent_destock_id
          LEFT JOIN {article_i18n} adi ON adi.article_id = destock.parent_destock_id
        SQL;

        $sql = strtr($sql, [
            '{parameters_query}' => $parameters_query,
            '{get_article_query}' => $get_article_query,
        ]);

        return strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{article}' => $this->structure->getRelation(),
            '{article_i18n}' => $this->getModelRelation(ArticleI18nModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{press_article}' => $this->getModelRelation(PressArticleModel::class),
            '{press_magazine_award}' => $this->getModelRelation(PressMagazineAwardModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{technology}' => $this->getModelRelation(TechnologyModel::class),
            '{technology_i18n}' => $this->getModelRelation(TechnologyI18nModel::class),
            '{promo_offer}' => $this->getModelRelation(PromoOfferModel::class),
            '{destock_tag}' => Tag::DESTOCK_BASE_TAG,
            '{highlight_query}' => $this->makeHighlightJoinQuery('get_article_i18n'),
            '{article_id_position_start}' => count(explode('.', Tag::DESTOCK_BASE_TAG)),
            '{article_id_position_end}' => count(explode('.', Tag::DESTOCK_BASE_TAG)) + 1,
            '{largest_image_url}' => ArticleMediaModel::getSqlForLargestUsingAlias('am'),
        ]);
    }

    /**
     * createProjectionForListing
     */
    public function createBaseProjectionForListing(): Projection
    {
        return $this->createProjection()
            ->unsetField('brand_id')
            ->unsetField('weight_g')
            ->unsetField('batch_sale')
            ->unsetField('internal_unbasketable_comment')
            ->unsetField('embargo_date')
            ->unsetField('created_at')
            ->unsetField('replaced_by');
    }

    /**
     * countAllBasketableByTagpath
     *
     * Retrieve number of articles from a tag path
     */
    public function countAllBasketableByTagpath(string $tag_path): int
    {
        $sql = <<<SQL
        SELECT
          COUNT(aa.article_id) AS count_article
          FROM {article} aa
              INNER JOIN {article_tag} aat
        ON aa.article_id = aat.article_id
          LEFT JOIN {article_tag} at ON aa.article_id = at.article_id
          AND at.tag_path <@ '{destock_base_tag}' :: ltree
          WHERE
            aat.tag_path <@ $*::ltree
            AND aa.unbasketable_reason IS NULL
            AND at.article_id IS NULL
        SQL;

        $sql = strtr($sql, [
            '{article}' => $this->structure->getRelation(),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{destock_base_tag}' => Tag::DESTOCK_BASE_TAG,
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, [explode('.', $tag_path)])
            ->current()['count_article'];
    }

    /**
     * findAllBasketableByTagpath
     *
     * Retrieve all articles from a specific culture and a tag path
     */
    public function findAllBasketableByTagpath(
        string $tag_path,
        string $culture = APP_DEFAULT_LOCALE
    ): CollectionIterator {
        $sql = <<<SQL
        WITH
          articles_in_path AS (
            SELECT
              aa.article_id,
              aa.common_content_id
            FROM {article} aa
              INNER JOIN {article_tag} aat ON aa.article_id = aat.article_id
              LEFT JOIN {article_tag} at ON aa.article_id = at.article_id
                AND at.tag_path <@ '{destock_base_tag}' :: ltree
            WHERE
              aat.tag_path <@ $*::ltree AND aa.unbasketable_reason IS NULL AND at.article_id IS NULL
          ),
          articles_in_path_with_declinations AS (
            SELECT DISTINCT
              aa.article_id
            FROM articles_in_path aip
              JOIN {article} aa      ON aip.common_content_id = aa.common_content_id
              JOIN {article_tag} aat ON aip.article_id = aat.article_id
            WHERE
              aa.unbasketable_reason IS NULL
          ),
          article_lengths AS ({lengths_join})
        SELECT
            aa.article_id,
            l.article_lengths AS article_lengths
        FROM
          articles_in_path aip
          CROSS JOIN parameters
          JOIN {article} aa                 ON aip.article_id = aa.article_id
          JOIN {common_content} cc          ON cc.common_content_id = aa.common_content_id
          JOIN {article_i18n} aai           ON aa.article_id = aai.article_id
                                            AND aai.supported_culture_id = parameters.culture
          JOIN {article_media_i18n} ami18n  ON ami18n.article_id = aa.article_id
                                            AND ami18n.supported_culture_id = aai.supported_culture_id
                                            AND ami18n.display_order = 1
                                            AND ami18n.type = 'IMAGE'::article.article_media_type
          JOIN {article_media} aam          ON aam.media_id = ami18n.media_id
          LEFT JOIN article_lengths l       ON l.article_id = aa.article_id
          LEFT JOIN {article_category} ac       ON aa.article_id = ac.article_id
          LEFT JOIN {category} c                ON ac.category_id = c.category_id
          LEFT JOIN {category_i18n} ci18n       ON c.category_id = ci18n.category_id
                                                AND ci18n.supported_culture_id = aai.supported_culture_id

        GROUP BY aa.common_content_id, aa.article_id, l.article_lengths
        SQL;

        $sql = strtr($sql, [
            '{article}' => $this->structure->getRelation(),
            '{article_i18n}' => $this->getModelRelation(ArticleI18nModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category}' => $this->getModelRelation(CategoryModel::class),
            '{category_i18n}' => $this->getModelRelation(CategoryI18nModel::class),
            '{promo_offer}' => $this->getModelRelation(PromoOfferModel::class),
            '{destock_base_tag}' => Tag::DESTOCK_BASE_TAG,
            '{lengths_join}' => $this->makeLengthsJoinQuery('articles_in_path_with_declinations'),
        ]);

        return $this->getCommonArticleInformationFromStandArticles($sql, [$culture, explode('.', $tag_path)]);
    }

    private function getCommonArticleInformationFromStandArticles(
        string $sql_query,
        array $values = []
    ): CollectionIterator {
        $projection = $this->createBaseProjectionForListing()
            ->setField('editorial_content', 'ai.editorial_content', 'json')
            ->setField('cc_short_description', "cci18n.editorial_content->>'short_description'", 'text')
            ->setField('brand_name', 'ab.name', 'text')
            ->setField('estimated_delivery_time', 'p.estimated_delivery_time', 'article.availability_delay')
            ->setField('informational_stock', 'p.informational_stock', 'integer')
            ->setField('display_mode', 'cc.display_mode', 'article.display_mode')
            ->setField(
                'review_score_index',
                'CASE WHEN cc.review_score IS NOT NULL THEN GREATEST(cc.review_score::int, 1) ELSE null END',
                'int',
            )
            ->setField('selling_price', static::getPriceIfNoHidden('selling_price'), 'text')
            ->setField('reference_price', static::getPriceIfNoHidden('reference_price'), 'text')
            ->setField('category_slug', 'c.slug', 'text')
            ->setField('category_name', 'ci18n.name', 'text')
            ->setField('article_attributes', 'ac.attributes->(parameters.culture)', 'json')
            ->setField('article_lengths', 's.article_lengths', 'json')
            ->setField('media_uri', ArticleMediaModel::getSqlForLargestUsingAlias('am'), 'public.citext')
            ->setField('score', 'ROUND(cc.review_score, 1)', 'float')
            ->setField('highlights', 'h.highlights', 'jsonb')
            ->setField('common_content_name', 'cci18n.name', 'text')
            ->setField('color_indexes', 'co.color_indexes', 'text[]')
            ->setField('color_labels', 'co.color_labels', 'text[]')
            ->setField('is_default_article', 'a.article_id = cc.default_article_id', 'boolean')
            ->setField('article_length', 'al.article_length', 'text')
            ->setField('article_color', 'ac.attributes -> (parameters.culture) ->> \'couleur\'', 'text')
            ->setField('promo_codes_description', 'pcc.promo_short_description', 'text[]')
            ->setField('has_an_ongoing_supplier_order', 'p.has_an_ongoing_supplier_order', 'bool')
            ->setField(
                'second_life',
                "CASE WHEN slo.article_id IS NULL THEN NULL ELSE jsonb_build_object('count', slo.count, 'minimum_price', slo.minimum_price) END",
                'jsonb',
            );

        return $this->query(
            $this->getCommonArticleInformationQuery($projection, $sql_query, null),
            $values,
            $projection,
        );
    }

    /**
     * getAllUrl
     *
     * Get the urls of all articles in stock that are not destock
     * along with the url of their main image in default culture at the largest size.
     */
    public function getAllUrl(): ResultIterator
    {
        $sql = <<<SQL
        WITH media AS (
          SELECT
            article_id,
            largest_media_uri
          FROM (
            SELECT
              am.article_id,
              {largest_image_uri} AS largest_media_uri,
              RANK() OVER (PARTITION BY am.article_id ORDER BY ami.display_order ASC) AS rank
            FROM article.article_media am
            INNER JOIN article.article_media_i18n ami
              ON ami.media_id = am.media_id
              AND ami.supported_culture_id = '{supported_culture_id}'
              AND ami.display_order IS NOT NULL
          ) m
          WHERE m.rank = 1 AND largest_media_uri IS NOT NULL
        )
        SELECT
          a.article_url AS url,
          m.largest_media_uri As image_url,
          GREATEST(
            a.created_at,
            a.updated_at,
            CASE
              WHEN cci.updated_at::DATE >= '2024-11-19' THEN cci.updated_at
              ELSE NULL
            END
          ) As lastmod
        FROM {relation} a
            INNER JOIN article.common_content cc ON a.article_id = cc.default_article_id
            INNER JOIN article.common_content_i18n cci ON cci.common_content_id = cc.common_content_id AND cci.supported_culture_id = '{supported_culture_id}'
          LEFT JOIN media m ON a.article_id = m.article_id
        WHERE a.unbasketable_reason IS NULL AND a.sku NOT LIKE 'DESTOCK%'
        ORDER BY a.article_url
        SQL;

        $sql = strtr($sql, [
            '{relation}' => $this->structure->getRelation(),
            '{largest_image_uri}' => ArticleMediaModel::getSqlForLargestUsingAlias('am'),
            '{supported_culture_id}' => APP_DEFAULT_LOCALE,
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql);
    }

    /**
     * This is mainly used for sitemap purpose
     */
    public function getUrlHistoricReview(): ResultIterator
    {
        $sql = <<<sql
        SELECT
          CONCAT(a.article_url,'/avis') as url
          FROM {relation} a
        INNER JOIN {common_content} cc on a.article_id = cc.default_article_id and a.common_content_id = cc.common_content_id
          WHERE cc.nb_advices IS NOT NULL
          AND cc.nb_advices > 0
          AND a.unbasketable_reason IS NULL AND a.sku NOT LIKE 'DESTOCK%'
        ORDER BY a.article_url
        sql;

        $sql = strtr($sql, [
            '{relation}' => $this->structure->getRelation(),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql);
    }

    public function getUrHistoricQuestionAnswer(): ResultIterator
    {
        $sql = <<<sql
        SELECT
            CONCAT(a.article_url,'/questions-reponses') as url
        FROM {relation} a
            INNER JOIN {common_content} cc on a.article_id = cc.default_article_id and a.common_content_id = cc.common_content_id
            INNER JOIN {common_content_question} ccq on ccq.common_content_id = cc.common_content_id
        WHERE a.unbasketable_reason IS NULL AND a.sku NOT LIKE 'DESTOCK%'
        GROUP BY a.article_url
        HAVING count(ccq.common_content_id) > 0
        ORDER BY a.article_url
        sql;

        $sql = strtr($sql, [
            '{relation}' => $this->structure->getRelation(),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_question}' => $this->getModelRelation(CommonContentQuestionModel::class),
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql);
    }

    /**
     * getComplementaryArticles
     *
     * Retrieve the principal articles of the related "complementary" common contents of an article
     */
    public function getComplementaryArticles(
        int $article_id,
        string $culture,
        int $limit = self::RELATED_LIMIT
    ): CollectionIterator {
        $sql = <<<SQL
        WITH
          parameters (article_id, culture) AS (
            SELECT $*::int4, $*::bpchar
          ),
          complementary_article AS (
            SELECT
              ra.article_id,
              ra.common_content_id
            FROM {article} a
              CROSS JOIN parameters
              JOIN {related_common_content} rcc ON (
                rcc.common_content_id = a.common_content_id
                AND rcc.relation_type = '{relation_type}'
              )
              JOIN {common_content} cc ON (cc.common_content_id = rcc.related_common_content_id)
              JOIN {article} ra ON (ra.article_id = cc.default_article_id)
            WHERE a.article_id = parameters.article_id AND ra.unbasketable_reason IS NULL
          ),
          highlight AS ({highlight_query})
        SELECT
          {projection}
        FROM
          complementary_article ca
          CROSS JOIN parameters
          JOIN {common_content} cc        ON cc.common_content_id = ca.common_content_id
          JOIN {article} ra               ON ra.article_id = cc.default_article_id
          JOIN {article_i18n} dai18n      ON dai18n.article_id = ra.article_id
                                          AND dai18n.supported_culture_id = '{default_culture}'
          LEFT JOIN {article_i18n} ai18n  ON ai18n.article_id = ra.article_id
                                          AND ai18n.supported_culture_id = parameters.culture
          JOIN {product} p                ON ra.sku = p.sku
          LEFT JOIN {article_media_i18n} ami18n ON ami18n.article_id = ra.article_id
                                                AND ami18n.supported_culture_id = parameters.culture
                                                AND ami18n.display_order = 1
                                                AND ami18n.type = 'IMAGE'::article.article_media_type
          LEFT JOIN {article_media} am    ON am.media_id = ami18n.media_id
          LEFT JOIN highlight h           ON h.article_id = ra.article_id
          LEFT JOIN {stand} s             ON ra.default_stand_id = s.stand_id
          LEFT JOIN {stand_i18n} si18n    ON ra.default_stand_id = si18n.stand_id
                                          AND si18n.supported_culture_id = parameters.culture
          LEFT JOIN {brand} b            ON ra.brand_id = b.brand_id
          LEFT JOIN {article_category} ac ON ra.article_id = ac.article_id
          LEFT JOIN {category} c          ON ac.category_id = c.category_id
          LEFT JOIN {category_i18n} ci18n ON c.category_id = ci18n.category_id
                                           AND ci18n.supported_culture_id = ai18n.supported_culture_id

        LIMIT {limit}
        SQL;

        $projection = $this->createBaseProjectionForListing()
            ->setField('editorial_content', 'COALESCE(ai18n.editorial_content, dai18n.editorial_content)', 'jsonb')
            ->setField('image_url', ArticleMediaModel::getSqlForLargestUsingAlias('am'), 'text')
            ->setField('estimated_delivery_time', 'p.estimated_delivery_time', 'article.availability_delay')
            ->setField('display_mode', 'cc.display_mode', 'article.display_mode')
            ->setField('declination_type', 'cc.declination_type', 'article.declination_type')
            ->setField('highlights', 'h.highlights', 'jsonb')
            // Substract the following aggregate from the group projection below
            ->setField('stand', 'jsonb_build_object(\'name\', si18n.name, \'slug\', s.slug)', 'json')
            ->setField('has_an_ongoing_supplier_order', 'p.has_an_ongoing_supplier_order', 'bool')
            ->setField('brand_name', 'b.name', 'text')
            ->setField('category_name', 'ci18n.name', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('ra'),
            '{article}' => $this->structure->getRelation(),
            '{article_i18n}' => $this->getModelRelation(ArticleI18nModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{related_common_content}' => $this->getModelRelation(RelatedCommonContentModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{stand}' => $this->getModelRelation(StandModel::class),
            '{stand_i18n}' => $this->getModelRelation(StandI18nModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category}' => $this->getModelRelation(CategoryModel::class),
            '{category_i18n}' => $this->getModelRelation(CategoryI18nModel::class),
            '{relation_type}' => RelatedCommonContent::COMPLEMENTARY_RELATION,
            '{highlight_query}' => $this->makeHighlightJoinQuery('complementary_article'),
            '{default_culture}' => APP_DEFAULT_LOCALE,
            '{limit}' => $limit,
        ]);

        return $this->query($sql, [$article_id, $culture], $projection);
    }

    /**
     * findArticlesByGuideIdAndCulture
     *
     *
     */
    public function findArticlesByGuideIdAndCulture(int $guide_id, string $_locale, int $limit): CollectionIterator
    {
        $sql = <<<SQL
        WITH
          associated_stand_article AS (
              SELECT
                a.article_id,
                min(s.stand_id) as stand_id,
                bool_or(ash.article_id is not null) as headline,
                max(p.informational_stock) as informational_stock
              FROM
                {guide_associated_stand} gas
                CROSS JOIN parameters
                INNER JOIN {stand} s
                  ON gas.stand_id = s.stand_id
                INNER JOIN {article_tag} at
                  ON s.tag_path = at.tag_path
                LEFT JOIN {article_stand_headline} ash
                  ON at.article_id = ash.article_id AND s.stand_id = ash.stand_id
                INNER JOIN {article} a
                  ON at.article_id = a.article_id
                INNER JOIN {product} p
                  ON a.sku = p.sku
                INNER JOIN {common_content} cc
                  ON a.common_content_id = cc.common_content_id
                  AND a.article_id = cc.default_article_id
                INNER JOIN {article_media} am
                  ON a.article_id = am.article_id
                INNER JOIN {article_media_i18n} ami18n
                  ON am.media_id = ami18n.media_id
                  AND ami18n.supported_culture_id = parameters.culture
                  AND ami18n.display_order = 1
                  AND ami18n.type = 'IMAGE'::article.article_media_type
              WHERE
                gas.guide_id = $*
                AND a.unbasketable_reason IS NULL
              GROUP BY a.article_id
        ),
          ordered_associated_stand_article as (
              SELECT
               *,
               row_number()
               OVER (
                 PARTITION BY asa.stand_id
                 ORDER BY asa.headline DESC, asa.informational_stock DESC ) AS rn
              FROM associated_stand_article asa
          )
        SELECT
          oasa.article_id,
          oasa.stand_id,
          oasa.headline
        FROM ordered_associated_stand_article oasa
        WHERE rn <= $*
        SQL;

        $sql = strtr($sql, [
            '{article}' => $this->structure->getRelation(),
            '{guide_associated_stand}' => $this->getModelRelation(GuideAssociatedStandModel::class),
            '{stand}' => $this->getModelRelation(StandModel::class),
            '{stand_i18n}' => $this->getModelRelation(StandI18nModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{article_stand_headline}' => $this->getModelRelation(ArticleStandHeadlineModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{exclude_destock_query}' => $this->makeExcludeDestockQuery(),
        ]);

        return $this->getCommonArticleInformationFromGuideAssociatedStandArticles(
            $sql,
            [$_locale, $guide_id, $limit],
            'ORDER BY stand_id, headline DESC, price->\'selling_price\', informational_stock DESC',
        );
    }

    /**
     * getCommonArticleInformationFromArticleList
     *
     * Get article information from a list of article_id given by sql_query param.
     *
     * @param string|null $suffix
     */
    private function getCommonArticleInformationFromGuideAssociatedStandArticles(
        string $sql_query,
        array $values = [],
        string $suffix = null
    ): CollectionIterator {
        $projection = $this->createBaseProjectionForListing()
            // projection fields from sql_query, needed to order the result
            ->setField('stand_id', 's.stand_id', 'integer')
            ->setField('headline', 's.headline', 'boolean')
            // projection fields to enrich article
            ->setField('brand_name', 'ab.name', 'text')
            ->setField('estimated_delivery_time', 'p.estimated_delivery_time', 'article.availability_delay')
            ->setField('informational_stock', 'p.informational_stock', 'integer')
            ->setField('is_main_article', 'a.article_id = cc.default_article_id', 'boolean')
            ->setField('editorial_content', 'ai.editorial_content', 'jsonb')
            ->setField('image_url', ArticleMediaModel::getSqlForLargestUsingAlias('am'), 'text')
            ->setField('category_name', 'ci18n.name', 'text')
            ->setField('category_slug', 'c.slug', 'text')
            ->setField('display_mode', 'cc.display_mode', 'article.display_mode')
            ->setField('declination_type', 'cc.declination_type', 'article.declination_type')
            ->setField('highlights', 'h.highlights', 'jsonb')
            ->setField('color_indexes', 'co.color_indexes', 'text[]')
            ->setField('color_labels', 'co.color_labels', 'text[]')
            ->setField('is_default_article', 'a.article_id = cc.default_article_id', 'boolean')
            ->setField('has_an_ongoing_supplier_order', 'p.has_an_ongoing_supplier_order', 'bool')
            ->setField('reference_price', "a.price->>'reference_price'", 'float');

        return $this->query(
            $this->getCommonArticleInformationQuery($projection, $sql_query, $suffix),
            $values,
            $projection,
        );
    }

    /**
     * getDeclinationByCommonContentId
     *
     *
     */
    public function getDeclinationByCommonContentId(int $common_content_id, string $culture): CollectionIterator
    {
        $sql = <<<SQL
        WITH
          parameters (common_content_id, culture) AS (
            SELECT $*::int4, $*::bpchar
          ),
          common_content_article AS (
            SELECT
              a.article_id
            FROM {article} a
              CROSS JOIN parameters p
            WHERE a.common_content_id = p.common_content_id
          ),
          highlight AS ({highlight_query})
        SELECT
          {projection}
        FROM
          {common_content} cc
          CROSS JOIN parameters param
          INNER JOIN {article} a
            ON cc.common_content_id = a.common_content_id
          INNER JOIN {product} p
            ON a.sku = p.sku
          INNER JOIN {brand} b
            ON a.brand_id = b.brand_id
          INNER JOIN {article_category} ac
            ON a.article_id = ac.article_id
          LEFT JOIN {color} c
            ON (ac.attributes#>>(ARRAY[param.culture, 'couleur']))::ltree = c.color_path
          LEFT JOIN highlight h ON h.article_id = a.article_id
          LEFT JOIN LATERAL (
            SELECT
              (subltree(at.tag_path, {article_id_position_start}, {article_id_position_end})::TEXT)::int AS parent_destock_id
            FROM
              {article_tag} at
            WHERE
              at.article_id = a.article_id
              AND at.tag_path <@ '{destock_tag}'::ltree
            LIMIT 1
          )  destock ON TRUE
          LEFT JOIN {article} ad       ON ad.article_id = destock.parent_destock_id
        WHERE
          cc.common_content_id = param.common_content_id
          AND a.unbasketable_reason IS NULL
          AND a.price ? 'selling_price'
        ORDER BY {order_by_selling_price} ASC, is_main_article DESC
        SQL;

        $projection = $this->createBaseProjectionForListing()
            ->setField('brand_name', 'b.name', 'text')
            ->setField('estimated_delivery_time', 'p.estimated_delivery_time', 'article.availability_delay')
            ->setField('informational_stock', 'p.informational_stock', 'integer')
            ->setField('is_main_article', 'a.article_id = cc.default_article_id', 'boolean')
            ->setField('display_mode', 'cc.display_mode', 'article.display_mode')
            ->setField('declination_type', 'cc.declination_type', 'article.declination_type')
            ->setField('color', 'c.label_i18n->(param.culture)', 'text')
            ->setField('color_image', 'c.image', 'text')
            ->setField('length', 'ac.attributes#>>(ARRAY[param.culture, \'longueur\'])', 'text')
            ->setField('parent_destock_price', 'ad.price', 'jsonb')
            ->setField('parent_destock_id', 'destock.parent_destock_id', 'integer')
            ->setField('highlights', 'h.highlights', 'jsonb')
            ->setField('has_an_ongoing_supplier_order', 'p.has_an_ongoing_supplier_order', 'bool');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{article}' => $this->structure->getRelation(),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{color}' => $this->getModelRelation(ColorModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{destock_tag}' => Tag::DESTOCK_BASE_TAG,
            '{highlight_query}' => $this->makeHighlightJoinQuery('common_content_article'),
            '{article_id_position_start}' => count(explode('.', Tag::DESTOCK_BASE_TAG)),
            '{article_id_position_end}' => count(explode('.', Tag::DESTOCK_BASE_TAG)) + 1,
            '{order_by_selling_price}' => static::getOrderBySellingPrice(),
        ]);

        return $this->query($sql, [$common_content_id, $culture], $projection);
    }

    /**
     * getDeclinationByCommonContentIdForReviews
     *
     *
     */
    public function getDeclinationByCommonContentIdForReviews(
        int $common_content_id,
        string $culture
    ): CollectionIterator {
        $sql = <<<SQL
        WITH
          parameters (common_content_id, culture) AS (
            SELECT $*::int4, $*::bpchar
          ),
          common_content_article AS (
            SELECT
              a.article_id
            FROM {article} a
              CROSS JOIN parameters p
            WHERE a.common_content_id = p.common_content_id
          ),
          highlight AS ({highlight_query})
        SELECT
          {projection}
        FROM
          {common_content} cc
          CROSS JOIN parameters param
          INNER JOIN {article} a
            ON cc.common_content_id = a.common_content_id
          INNER JOIN {product} p
            ON a.sku = p.sku
          INNER JOIN {brand} b
            ON a.brand_id = b.brand_id
          INNER JOIN {article_category} ac
            ON a.article_id = ac.article_id
          LEFT JOIN {color} c
            ON (ac.attributes#>>(ARRAY[param.culture, 'couleur']))::ltree = c.color_path
          LEFT JOIN highlight h ON h.article_id = a.article_id
          LEFT JOIN LATERAL (
            SELECT
              (subltree(at.tag_path, {article_id_position_start}, {article_id_position_end})::TEXT)::int AS parent_destock_id
            FROM
              {article_tag} at
            WHERE
              at.article_id = a.article_id
              AND at.tag_path <@ '{destock_tag}'::ltree
            LIMIT 1
          )  destock ON TRUE
          LEFT JOIN {article} ad       ON ad.article_id = destock.parent_destock_id
        WHERE
          cc.common_content_id = param.common_content_id
          AND a.price ? 'selling_price'
        ORDER BY {order_by_selling_price} ASC, is_main_article DESC
        SQL;

        $projection = $this->createBaseProjectionForListing()
            ->setField('estimated_delivery_time', 'p.estimated_delivery_time', 'article.availability_delay')
            ->setField('informational_stock', 'p.informational_stock', 'integer')
            ->setField('is_main_article', 'a.article_id = cc.default_article_id', 'boolean')
            ->setField('display_mode', 'cc.display_mode', 'article.display_mode')
            ->setField('declination_type', 'cc.declination_type', 'article.declination_type')
            ->setField('color', 'c.label_i18n->(param.culture)', 'text')
            ->setField('color_image', 'c.image', 'text')
            ->setField('length', 'ac.attributes#>>(ARRAY[param.culture, \'longueur\'])', 'text')
            ->setField('parent_destock_price', 'ad.price', 'jsonb')
            ->setField('highlights', 'h.highlights', 'jsonb')
            ->setField('has_an_ongoing_supplier_order', 'p.has_an_ongoing_supplier_order', 'bool');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{article}' => $this->structure->getRelation(),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{color}' => $this->getModelRelation(ColorModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{destock_tag}' => Tag::DESTOCK_BASE_TAG,
            '{highlight_query}' => $this->makeHighlightJoinQuery('common_content_article'),
            '{article_id_position_start}' => count(explode('.', Tag::DESTOCK_BASE_TAG)),
            '{article_id_position_end}' => count(explode('.', Tag::DESTOCK_BASE_TAG)) + 1,
            '{order_by_selling_price}' => static::getOrderBySellingPrice(),
        ]);

        return $this->query($sql, [$common_content_id, $culture], $projection);
    }

    /**
     * getCommonArticleInformationQuery
     *
     * @param string|null $suffix
     *
     */
    private function getCommonArticleInformationQuery(
        Projection $projection,
        string $sql_query,
        string $suffix = null
    ): string {
        $sql = <<<SQL
        WITH
          parameters (culture) AS (
            SELECT $*::bpchar
          ),
          selected_article as (
            {sql_query}
          ),
          promo_code_contents AS (
            SELECT
              a.article_id,
              array_agg(po.article_short_description_i18n -> p.culture ORDER BY upper(po.lifetime) DESC)
              filter (WHERE po.article_short_description_i18n -> p.culture IS NOT NULL) AS promo_short_description
            FROM
              {article} a
              INNER JOIN {promo_offer} po ON a.article_id = ANY(po.computed_article_ids)
              CROSS JOIN parameters p
            WHERE
              po.lifetime @> now()
              AND po.article_short_description_i18n ? p.culture
              AND po.deactivated_reason IS NULL
            GROUP BY a.article_id
          ),
          highlight AS ({highlight_query}),
          colors AS ({color_join}),
          article_length AS ({length_query}),
          second_life_offer AS (
            SELECT
              a.article_id,
              COUNT(a2.article_id) as count,
              MIN(a2.price->>'selling_price') as minimum_price
            FROM
              selected_article s
              INNER JOIN article.article a ON s.article_id = a.article_id
              INNER JOIN article.common_content cc ON cc.common_content_id = a.common_content_id
              INNER JOIN article.article a2 ON a.common_content_id = a2.common_content_id
              INNER JOIN article.article_tag a2t ON a2t.article_id = a2.article_id
            WHERE
              a2.destock IS NOT NULL
              AND a2.unbasketable_reason IS NULL
              AND (
                cc.declination_type = 'COLOR'
                OR a2t.tag_path = ('article.destock.' || a.article_id)::public.ltree
              )
            GROUP BY a.article_id
          )
        SELECT
          {projection}
        FROM
          selected_article s
          CROSS JOIN parameters
          INNER JOIN {article} a
            ON s.article_id = a.article_id
          INNER JOIN {article_i18n} ai
            ON a.article_id = ai.article_id
            AND ai.supported_culture_id = parameters.culture
          INNER JOIN {brand} ab
            ON a.brand_id = ab.brand_id
          INNER JOIN {product} p USING (sku)
          INNER JOIN {common_content} cc
            ON a.common_content_id = cc.common_content_id
          INNER JOIN {common_content_i18n} cci18n
            ON cc.common_content_id = cci18n.common_content_id
            AND cci18n.supported_culture_id = parameters.culture
          INNER JOIN {article_media} am
            ON a.article_id = am.article_id
          INNER JOIN {article_media_i18n} ami18n
            ON am.media_id = ami18n.media_id
            AND ami18n.supported_culture_id = parameters.culture
            AND ami18n.display_order = 1
            AND ami18n.type = 'IMAGE'::article.article_media_type
          INNER JOIN {article_category} ac
            ON a.article_id = ac.article_id
          INNER JOIN {category} c
            ON ac.category_id = c.category_id
          INNER JOIN {category_i18n} ci18n
            ON ac.category_id = ci18n.category_id
            AND ci18n.supported_culture_id = parameters.culture
          LEFT JOIN highlight h             ON h.article_id = s.article_id
          LEFT JOIN colors co               ON co.article_id = s.article_id
          LEFT JOIN article_length al       ON al.article_id = s.article_id
          LEFT JOIN promo_code_contents pcc ON pcc.article_id = a.article_id
          LEFT JOIN second_life_offer slo ON slo.article_id = a.article_id
        {suffix}
        SQL;

        return strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{article}' => $this->structure->getRelation(),
            '{article_i18n}' => $this->getModelRelation(ArticleI18nModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category}' => $this->getModelRelation(CategoryModel::class),
            '{category_i18n}' => $this->getModelRelation(CategoryI18nModel::class),
            '{highlight_query}' => $this->makeHighlightJoinQuery('selected_article'),
            '{color_join}' => $this->makeColorJoinQuery('selected_article'),
            '{length_query}' => $this->makeLengthQuery('selected_article'),
            '{promo_offer}' => $this->getModelRelation(PromoOfferModel::class),
            '{sql_query}' => $sql_query,
            '{suffix}' => $suffix ?? '',
        ]);
    }

    /**
     * fetchArticlesBySkus
     *
     *
     * @throws \RuntimeException
     */
    public function fetchArticlesBySkus(array $skus): CollectionIterator
    {
        if ($skus === []) {
            throw new \RuntimeException('Array skus can not be empty.');
        }

        $sql = <<<SQL
        SELECT
          {projection}
        FROM {relation} a
          INNER JOIN {brand} b USING (brand_id)
        WHERE
          {condition}
        SQL;
        $projection = $this->createProjection()->setField('name_with_brand', 'b.name || \' \' || a.name', 'text');
        $where = Where::createWhereIn('sku', $skus);
        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{relation}' => $this->structure->getRelation(),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{condition}' => $where,
        ]);

        return $this->query($sql, $where->getValues(), $projection);
    }

    /**
     * getArticlesByBrandCategory
     */
    public function getArticlesByBrandCategory(int $brand_id, int $category_id): CollectionIterator
    {
        $sql = <<<SQL
        SELECT
          {projection}
        FROM
          {relation} a
          INNER JOIN {article_category} ac USING (article_id)
          INNER JOIN {brand} b             USING (brand_id)
          LEFT JOIN LATERAL (
            SELECT
              at.article_id
            FROM
              {article_tag} at
            WHERE
              at.article_id = a.article_id
              AND at.tag_path <@ '{destock_tag}'::ltree
            GROUP BY at.article_id
          ) article_destock ON TRUE
        WHERE
          {condition}
        ORDER BY a.name
        SQL;
        $where = Where::create('a.brand_id = $* AND ac.category_id = $* AND article_destock.article_id IS NULL', [
            $brand_id,
            $category_id,
        ]);
        $projection = $this->createProjection()->setField('brand_name', 'b.name', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{relation}' => $this->structure->getRelation(),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{condition}' => $where,
            '{destock_tag}' => Tag::DESTOCK_BASE_TAG,
        ]);

        return $this->query($sql, $where->getValues(), $projection);
    }

    /**
     * getPackagedArticlesEditorialContentsByArticleId
     *
     *
     * @return CollectionIterator
     */
    public function getPackagedArticlesEditorialContentsByArticleId(
        int $article_id,
        string $culture = APP_DEFAULT_LOCALE
    ) {
        $sql = <<<SQL
        WITH packaged_articles AS (
            SELECT unnest(packaged_articles) AS article_id
            FROM {relation} a
            WHERE a.article_id = $*::int4
          )
        SELECT
          {projection}
        FROM {relation} a
          INNER JOIN packaged_articles pa
            ON a.article_id = pa.article_id
          INNER JOIN {brand} b
            ON a.brand_id = b.brand_id
          INNER JOIN {article_category} ac
            ON a.article_id = ac.article_id
          INNER JOIN {category_i18n} ci18n
            ON ac.category_id = ci18n.category_id
            AND ci18n.supported_culture_id = $*::text
          INNER JOIN {common_content_i18n} cci18n
            ON a.common_content_id = cci18n.common_content_id
            AND cci18n.supported_culture_id = $*::text
        SQL;

        $projection = (new Projection($this->flexible_entity_class))
            ->setField('article_id', 'a.article_id', 'int4')
            ->setField('sku', 'a.sku', 'text')
            ->setField('brand_name', 'b.name', 'text')
            ->setField('name', 'a.name', 'text')
            ->setField('category_name', 'ci18n.name', 'text')
            ->setField('specifications', 'cci18n.editorial_content ->> \'caracteristiques\'', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{relation}' => $this->structure->getRelation(),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category_i18n}' => $this->getModelRelation(CategoryI18nModel::class),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
        ]);

        return $this->query($sql, [$article_id, $culture, $culture], $projection);
    }

    /**
     * getArticleForShoppingCart
     *
     * @return Article|null
     */
    public function getArticleForShoppingCart(int $article_id)
    {
        $sql = <<<SQL
        SELECT {projection}
        FROM {relation} a
          INNER JOIN {common_content} cc USING (common_content_id)
        WHERE {condition}
        SQL;
        $condition = <<<SQL
        article_id = $* AND a.unbasketable_reason IS NULL AND (cc.display_mode IS NULL OR cc.display_mode != $*)
        SQL;

        $where = Where::create($condition, [$article_id, CommonContent::DISPLAY_MODE_CONFIDENTIAL_PRICE]);
        $projection = $this->createProjection();

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{relation}' => $this->structure->getRelation(),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{condition}' => $where,
        ]);

        return $this->query($sql, $where->getValues(), $projection)->current();
    }

    /**
     * getArticleForComplementaryPopin
     *
     * Get an Article with just small set of additional data related to the "add to basket popin".
     *
     *
     * @return Article|null
     */
    public function getArticleForComplementaryPopin(int $article_id)
    {
        $sql = <<<SQL
        SELECT
          {projection}
        FROM
          {article} a
          JOIN {brand} b ON b.brand_id = a.brand_id
          JOIN {product} p ON a.sku = p.sku
        WHERE
          a.article_id = $*
        SQL;

        $projection = $this->createProjection()
            ->setField('full_name', "CONCAT_WS(' ', b.name, a.name)", 'text')
            ->setField('estimated_delivery_time', 'p.estimated_delivery_time', 'article.availability_delay')
            ->setField('has_an_ongoing_supplier_order', 'p.has_an_ongoing_supplier_order', 'bool');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{article}' => $this->structure->getRelation(),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
        ]);

        return $this->query($sql, [$article_id], $projection)->current();
    }

    /**
     * getArticleSubstitute
     *
     * Retrieve the Article which replace the one in parameter,
     * ie. first in substitution chain that is still sell
     */
    public function getArticleSubstitute(int $article_id): ?Article
    {
        $sql = <<<SQL
        WITH RECURSIVE substitute AS (
          SELECT article_id, replaced_by, unbasketable_reason
          FROM {article}
          WHERE article_id = $*
            AND unbasketable_reason IS NOT NULL

          UNION

          SELECT a.article_id, a.replaced_by, a.unbasketable_reason
          FROM {article} a
            INNER JOIN substitute s ON s.replaced_by = a.article_id
        )
        SELECT {projection}
        FROM substitute
          INNER JOIN {article}             a2 USING (article_id)
          INNER JOIN {common_content_i18n} cci ON a2.common_content_id = cci.common_content_id
        WHERE substitute.unbasketable_reason IS NULL
        LIMIT 1
        SQL;

        $projection = $this->createProjection()->setField('full_name', 'cci.name', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a2'),
            '{article}' => $this->structure->getRelation(),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
        ]);

        return $this->query($sql, [$article_id], $projection)->current();
    }

    /**
     * getArticleSuggestWithSameStandAndBrand
     *
     * return article with same stand and brand it is still sell
     * and that has a minimum price difference ( +/- 15% )
     */
    public function getArticleSuggestWithSameStandAndBrand(int $article_id): ?Article
    {
        $sql = <<<SQL
        WITH
          parameters  AS (
            SELECT article_id, brand_id, default_stand_id, (price ->> 'selling_price')::DECIMAL AS selling_price
              FROM {article}
              WHERE article_id = $*
                AND unbasketable_reason IS NOT NULL
            ),
          substitutes AS (
            SELECT
              a.article_id,
              abs(((a.price ->> 'selling_price')::DECIMAL - p.selling_price) / p.selling_price * 100) AS dif_pour
              FROM {article}    a
                INNER JOIN parameters p ON a.brand_id = p.brand_id AND a.default_stand_id = p.default_stand_id
              WHERE a.unbasketable_reason IS NULL
                AND packaged_articles IS NULL
            )
        SELECT {projection}
          FROM substitutes
            INNER JOIN {article}             a2 USING (article_id)
            INNER JOIN {common_content_i18n} cci ON a2.common_content_id = cci.common_content_id
          WHERE substitutes.dif_pour < 15
          ORDER BY dif_pour
          LIMIT 1
        SQL;

        $projection = $this->createProjection()->setField('full_name', 'cci.name', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a2'),
            '{article}' => $this->structure->getRelation(),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
        ]);

        return $this->query($sql, [$article_id], $projection)->current();
    }

    /**
     * getArticleSuggestWithSameStand
     *
     * return article with same stand it is still sell
     * and that has a minimum price difference ( +/- 15% )
     */
    public function getArticleSuggestWithSameStand(int $article_id): ?Article
    {
        $sql = <<<SQL
        WITH
          parameters  AS (
            SELECT article_id, default_stand_id, (price ->> 'selling_price')::DECIMAL AS selling_price
              FROM {article}
              WHERE article_id = $*
                AND unbasketable_reason IS NOT NULL
            ),
          substitutes AS (
            SELECT
              a.article_id,
              abs(((a.price ->> 'selling_price')::DECIMAL - p.selling_price) / p.selling_price * 100) AS dif_pour
              FROM {article}    a
                INNER JOIN parameters p ON a.default_stand_id = p.default_stand_id
              WHERE a.unbasketable_reason IS NULL
                AND packaged_articles IS NULL
            )
        SELECT {projection}
          FROM substitutes
            INNER JOIN {article}             a2 USING (article_id)
            INNER JOIN {common_content_i18n} cci ON a2.common_content_id = cci.common_content_id
          WHERE substitutes.dif_pour < 15
          ORDER BY dif_pour
          LIMIT 1
        SQL;

        $projection = $this->createProjection()->setField('full_name', 'cci.name', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a2'),
            '{article}' => $this->structure->getRelation(),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
        ]);

        return $this->query($sql, [$article_id], $projection)->current();
    }

    /**
     * getStandSuggest
     *
     * return article stand if there is at least one more item for sale
     *
     *
     * @return array|null
     */
    public function getStandSuggest(int $article_id, string $_locale = APP_DEFAULT_LOCALE)
    {
        $sql = <<<SQL
        WITH
          parameters AS (
            SELECT default_stand_id
              FROM {article}
              WHERE article_id = $*
                AND unbasketable_reason IS NOT NULL
            )
        SELECT {projection}
          FROM {stand}              s
            INNER JOIN {stand_i18n} si18n ON s.stand_id = si18n.stand_id
            INNER JOIN {article_tag} a_t ON s.tag_path = a_t.tag_path
            INNER JOIN {article}     a2 ON a_t.article_id = a2.article_id
            CROSS JOIN parameters          p
          WHERE p.default_stand_id = s.stand_id
            AND a2.unbasketable_reason IS NULL
            AND si18n.supported_culture_id = $*
          LIMIT 1
        SQL;

        $projection = (new Projection($this->flexible_entity_class))
            ->setField('article_id', 'a2.article_id', 'text')
            ->setField('stand_name', 'si18n.name', 'text')
            ->setField('stand_slug', 's.slug', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a2'),
            '{article}' => $this->structure->getRelation(),
            '{article_tag}' => $this->getModelRelation(ArticleTagModel::class),
            '{stand}' => $this->getModelRelation(StandModel::class),
            '{stand_i18n}' => $this->getModelRelation(StandI18nModel::class),
        ]);

        return $this->query($sql, [$article_id, $_locale], $projection)->current();
    }

    /**
     * @return array|null
     */
    public function getBrandIfContainsBasketable(int $article_id)
    {
        $sql = <<<SQL
        WITH
          parameters AS (
            SELECT brand_id
              FROM {article}
              WHERE article_id = $*
                AND unbasketable_reason IS NOT NULL
            )
        SELECT {projection}
          FROM {brand} b
            INNER JOIN {article}     a2 ON b.brand_id = a2.brand_id
            CROSS JOIN parameters          p
          WHERE p.brand_id = b.brand_id
            AND a2.unbasketable_reason IS NULL
          LIMIT 1
        SQL;

        $projection = (new Projection($this->flexible_entity_class))
            ->setField('article_id', 'a2.article_id', 'text')
            ->setField('brand_name', 'b.name', 'text')
            ->setField('brand_slug', 'b.slug', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a2'),
            '{article}' => $this->structure->getRelation(),
            '{brand}' => $this->getModelRelation(BrandModel::class),
        ]);

        return $this->query($sql, [$article_id], $projection)->current();
    }

    public function hasStock(string $sku): bool
    {
        $sql = <<<SQL
        SELECT
          p.informational_stock > 0 AS is_available
          FROM article.product p
          WHERE p.sku = TRIM($*)
        SQL;

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, [$sku])
            ->current()['is_available'] ?? false;
    }
}

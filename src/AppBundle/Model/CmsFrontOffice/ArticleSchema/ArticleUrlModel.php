<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\ArticleUrl as ArticleUrlStructure;

/**
 * ArticleUrlModel
 *
 * Model class for table article_url.
 *
 * @see Model
 */
class ArticleUrlModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ArticleUrlStructure();
        $this->flexible_entity_class = ArticleUrl::class;
    }

    /**
     * fetchRedirectInformation
     *
     * @param   string $url
     * @return  ArticleUrl
     */
    public function fetchRedirectInformation($url)
    {
        $article_model = $this->getSession()->getModel(ArticleModel::class);
        $sql = <<<SQL
        select
          au.article_id,
          a.article_url
        from
          :article_url au
          join :article a
            using (article_id)
        where
          :conditions
        SQL;

        $conditions = new Where('au.article_url = $*', [$url]);
        $sql = strtr($sql, [
            ':article_url' => $this->structure->getRelation(),
            ':article' => $article_model->getStructure()->getRelation(),
            ':conditions' => $conditions,
        ]);

        return $this->query($sql, $conditions->getValues())->current();
    }
}

<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\ArticleWarrantyExtension as ArticleWarrantyExtensionStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ModelRelationTrait;

/**
 * ArticleWarrantyExtensionModel
 *
 * Model class for table article_warranty_extension.
 *
 * @see Model
 */
class ArticleWarrantyExtensionModel extends Model
{
    use WriteQueries;
    use ModelRelationTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ArticleWarrantyExtensionStructure();
        $this->flexible_entity_class = ArticleWarrantyExtension::class;
    }

    /**
     * fetchWarrantyForArticleId
     */
    public function fetchCompleteWarrantyForArticleId(
        int $article_id,
        string $culture = APP_DEFAULT_LOCALE
    ): CollectionIterator {
        $sql = <<<SQL
        SELECT
          {projection}
        FROM
          {relation} r
          INNER JOIN {waranty_extension} we USING (tag_path)
        WHERE
          {condition}
        ORDER BY tag_path
        SQL;
        $projection = $this->createProjection()
            ->setField('label', 'COALESCE(we.label->$*, we.label->$*)', 'text')
            ->setField('description', 'COALESCE(we.description->$*, we.description->$*)', 'text')
            ->setField('tag_path', 'r.tag_path', 'text')
            ->setField('type', 'subltree(r.tag_path, 1, 2)', 'text');
        $values = [$culture, APP_DEFAULT_LOCALE, $culture, APP_DEFAULT_LOCALE];
        $where = Where::create('r.article_id = $*', [$article_id]);

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('r'),
            '{relation}' => $this->structure->getRelation(),
            '{waranty_extension}' => $this->getModelRelation(WarrantyExtensionModel::class),
            '{condition}' => $where,
        ]);

        return $this->query($sql, array_merge($values, $where->getValues()), $projection);
    }
}

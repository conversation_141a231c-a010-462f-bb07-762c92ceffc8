<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * ArticleI18n
 *
 * Structure class for relation article.article_i18n.
 * Article language dependent attributes
 *
 * editorial_content:
 * Descriptions, comments etc.
 *
 * @see RowStructure
 */
class ArticleI18n extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('article.article_i18n')
            ->setPrimaryKey(['supported_culture_id', 'article_id'])
            ->addField('article_id', 'int4')
            ->addField('supported_culture_id', 'bpchar')
            ->addField('editorial_content', 'jsonb')
            ->addField('updated_at', 'timestamptz');
    }
}

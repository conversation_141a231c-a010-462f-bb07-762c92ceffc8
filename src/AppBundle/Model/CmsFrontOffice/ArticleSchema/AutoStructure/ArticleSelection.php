<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * ArticleSelection
 *
 * Structure class for relation article.article_selection.
 * An article's selection use a search context to define which articles are in
 * the selection or not. The selection has optionnaly a tag_path in order to
 * define easily a static selection.
 *
 * tag_path:
 * The tag_path is used to create static articles selection.
 * search_context:
 * The search context define which articles are in the selection.
 * created_by:
 * The creator of the selection.
 *
 * @see RowStructure
 */
class ArticleSelection extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('article.article_selection')
            ->setPrimaryKey(['selection_id'])
            ->addField('selection_id', 'uuid')
            ->addField('name', 'text')
            ->addField('description', 'text')
            ->add<PERSON>ield('tag_path', 'public.ltree')
            ->addField('search_context', 'article.search_context')
            ->addField('created_by', 'text')
            ->addField('created_at', 'timestamptz');
    }
}

<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * ArticleTag
 *
 * Structure class for relation article.article_tag.
 * List of tags of articles
 *
 * created_at:
 * a timestamp indicating when the tag was created
 *
 * @see RowStructure
 */
class ArticleTag extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('article.article_tag')
            ->setPrimaryKey(['tag_path', 'article_id'])
            ->addField('article_id', 'int4')
            ->addField('tag_path', 'public.ltree')
            ->addField('created_at', 'timestamptz');
    }
}

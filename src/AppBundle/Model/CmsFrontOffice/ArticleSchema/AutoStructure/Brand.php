<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * Brand
 *
 * Structure class for relation article.brand.
 * Brand information related to articles
 *
 * brand_id:
 * Unique database identifier for brands.
 * name:
 * Brand name.
 * slug:
 * Brand slug
 * logo_url:
 * Link to the logo image.
 * is_active:
 * true if the brand is always used
 * nb_advices:
 * Number of advices for the brand
 * avg_advices:
 * Average notation of a brand
 * nb_recommendations:
 * Number of reccomendations for the brand
 *
 * @see RowStructure
 */
class Brand extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('article.brand')
            ->setPrimaryKey(['brand_id'])
            ->addField('brand_id', 'int4')
            ->addField('name', 'text')
            ->addField('slug', 'text')
            ->addField('logo_url', 'public.citext')
            ->addField('svd_source_doc', 'text')
            ->addField('svd_source_image', 'text')
            ->addField('is_active', 'bool')
            ->addField('country', 'text')
            ->addField('nb_advices', 'int4')
            ->addField('avg_advices', 'float8')
            ->addField('nb_recommendations', 'int4');
    }
}

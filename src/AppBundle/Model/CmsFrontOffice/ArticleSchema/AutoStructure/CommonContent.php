<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * CommonContent
 *
 * Structure class for relation article.common_content.
 * When searching on global attributes, results point to common contents.
 * Therefore, each common content must display a default article from the
 * attached list
 *
 * common_content_id:
 * Unique identifier for Common contents, the value is defined by
 * draft_common_content sequence
 * default_article_id:
 * When searching on global attributes, results point to some common contents.
 * Therefore, each common content must must have a default article.
 * display_mode:
 * Common content could have a specific display mode
 * declination_type:
 * The type of relation between articles which have the same common content.
 * nb_advices:
 * Number of advices for the common content.
 * nb_recommendations:
 * Number of recommendations for common content.
 *
 * @see RowStructure
 */
class CommonContent extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('article.common_content')
            ->setPrimaryKey(['common_content_id'])
            ->addField('common_content_id', 'int4')
            ->addField('default_article_id', 'int4')
            ->addField('display_mode', 'article.display_mode')
            ->addField('declination_type', 'article.declination_type')
            ->addField('review_score', 'numeric')
            ->addField('nb_advices', 'int4')
            ->addField('nb_recommendations', 'int4');
    }
}

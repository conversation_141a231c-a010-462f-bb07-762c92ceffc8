<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * CommonContentI18n
 *
 * Structure class for relation article.common_content_i18n.
 * Language variations for common content
 *
 * editorial_content:
 * Editorial content attributes in JSON format
 * name:
 * Represent the shared description/name of the common content
 * title:
 * Meta-title used for SEO
 * description:
 * Meta-description used for SEO
 *
 * @see RowStructure
 */
class CommonContentI18n extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('article.common_content_i18n')
            ->setPrimaryKey(['supported_culture_id', 'common_content_id'])
            ->addField('common_content_id', 'int4')
            ->addField('supported_culture_id', 'bpchar')
            ->addField('editorial_content', 'jsonb')
            ->addField('name', 'text')
            ->addField('title', 'text')
            ->addField('description', 'text')
            ->addField('updated_at', 'timestamptz');
    }
}

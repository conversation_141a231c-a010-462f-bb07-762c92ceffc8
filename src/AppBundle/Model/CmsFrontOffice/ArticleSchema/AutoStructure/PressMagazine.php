<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * PressMagazine
 *
 * Structure class for relation article.press_magazine.
 * Press magazine that can have labels.
 *
 * magazine_id:
 * Unique identifier for magazine.
 * culture:
 * ISO639-1 culture of the magazine.
 * url_info_svd:
 * External url of the original content.
 *
 * @see RowStructure
 */
class PressMagazine extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('article.press_magazine')
            ->setPrimaryKey(['magazine_id'])
            ->addField('magazine_id', 'int4')
            ->addField('name', 'text')
            ->addField('culture', 'bpchar')
            ->addField('url_info_svd', 'text');
    }
}

<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\BrandI18n as BrandI18nStructure;

/**
 * BrandI18nModel
 *
 * Model class for table brand_i18n.
 *
 * @see Model
 */
class BrandI18nModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new BrandI18nStructure();
        $this->flexible_entity_class = BrandI18n::class;
    }
}

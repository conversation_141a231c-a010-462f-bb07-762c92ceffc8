<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\ModelManager\Model\FlexibleEntity;

/**
 * CommonContent
 *
 * Flexible entity for relation
 * article.common_content
 *
 * @see FlexibleEntity
 */
class CommonContent extends FlexibleEntity
{
    // value of enum common_content.declination_type
    public const DECLINATION_COLOR = 'COLOR';
    public const DECLINATION_LENGTH = 'LENGTH';
    public const DECLINATION_ACCESSORY = 'ACCESSORY';

    // value of enum common_content.display_mode
    public const DISPLAY_MODE_WITHOUT_INSTEAD_OF = 'WITHOUT_INSTEAD_OF';
    public const DISPLAY_MODE_STORE_EXCLUSIVITY = 'STORE_EXCLUSIVITY';
    public const DISPLAY_MODE_CONFIDENTIAL_PRICE = 'CONFIDENTIAL_PRICE';
}

<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\CommonContentI18n as CommonContentI18nStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\CommonContentI18n;

/**
 * CommonContentI18nModel
 *
 * Model class for table common_content_i18n.
 *
 * @see Model
 */
class CommonContentI18nModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new CommonContentI18nStructure();
        $this->flexible_entity_class = CommonContentI18n::class;
    }
}

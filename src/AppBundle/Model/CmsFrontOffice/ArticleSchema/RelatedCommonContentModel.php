<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\RelatedCommonContent as RelatedCommonContentStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\PriceTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\QueryHelperTrait;

/**
 * RelatedCommonContentModel
 *
 * Model class for table related_common_content.
 *
 * @see Model
 */
class RelatedCommonContentModel extends Model
{
    use WriteQueries;
    use QueryHelperTrait;
    use PriceTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new RelatedCommonContentStructure();
        $this->flexible_entity_class = RelatedCommonContent::class;
    }
}

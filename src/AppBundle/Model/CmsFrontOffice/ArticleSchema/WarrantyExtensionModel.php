<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\AutoStructure\WarrantyExtension as WarrantyExtensionStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\WarrantyExtension;

/**
 * WarrantyExtensionModel
 *
 * Model class for table warranty_extension.
 *
 * @see Model
 */
class WarrantyExtensionModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new WarrantyExtensionStructure();
        $this->flexible_entity_class = WarrantyExtension::class;
    }
}

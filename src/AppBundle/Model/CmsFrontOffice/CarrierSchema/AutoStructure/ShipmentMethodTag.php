<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * ShipmentMethodTag
 *
 * Structure class for relation carrier.shipment_method_tag.
 * Associate tag to shipment method
 *
 * extra_data:
 * Store extra data values defined in tag
 *
 * @see RowStructure
 */
class ShipmentMethodTag extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('carrier.shipment_method_tag')
            ->setPrimaryKey(['tag_name', 'shipment_method_id'])
            ->addField('shipment_method_id', 'int4')
            ->addField('tag_name', 'text')
            ->addField('extra_data', 'jsonb');
    }
}

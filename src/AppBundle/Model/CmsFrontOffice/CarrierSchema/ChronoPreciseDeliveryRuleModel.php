<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema;

use PommProject\ModelManager\Exception\ModelException;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\AutoStructure\ChronoPreciseDeliveryRule as ChronoPreciseDeliveryRuleStructure;

/**
 * ChronoPreciseDeliveryRuleModel
 *
 * Model class for table chrono_precise_delivery_rule.
 *
 * @see Model
 */
class ChronoPreciseDeliveryRuleModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ChronoPreciseDeliveryRuleStructure();
        $this->flexible_entity_class = ChronoPreciseDeliveryRule::class;
    }

    /**
     * @return mixed
     * @throws ModelException
     */
    public function getFirstDeliveryDayPossible(int $order_day, string $order_hour)
    {
        $sql = <<<SQL
        WITH
          parameters AS (
            SELECT $*::bpchar AS selected_day, $*::bpchar AS current_hour
          )
        SELECT {projection}
          FROM carrier.chrono_precise_delivery_rule dr
            CROSS JOIN parameters p
          WHERE dr.day = p.selected_day::int
        ;
        SQL;

        $projection = $this->createProjection()->setField(
            'next_delivery_slot',
            'CASE WHEN p.current_hour::TIME WITH TIME ZONE AT TIME ZONE \'Europe/Paris\' < hour AT TIME ZONE \'Europe/Paris\' THEN as_soon_as_before_hour :: INT  ELSE as_soon_as_after_hour :: INT END ',
            'int4',
        );

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('dr'),
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, [$order_day, $order_hour])
            ->current();
    }
}

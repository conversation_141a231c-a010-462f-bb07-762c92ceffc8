<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema;

use PommProject\Foundation\Converter\Type\Point;
use PommProject\ModelManager\Exception\ModelException;
use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\AutoStructure\ShipmentMethod as ShipmentMethodStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\ShipmentMethod;

/**
 * ShipmentMethodModel
 *
 * Model class for table shipment_method.
 *
 * @see Model
 */
class ShipmentMethodModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ShipmentMethodStructure();
        $this->flexible_entity_class = ShipmentMethod::class;
    }

    /**
     * fetchWithList
     *
     * Retrieve info for available shipment methods.
     * Shipment methods which are in $shipment_methods but not in DB are not returned.
     *
     * @param Point|null $gps
     *
     * @throws ModelException
     */
    public function fetchWithList(
        array $shipment_methods,
        Point $gps = null,
        string $locale = 'fr'
    ): ?CollectionIterator {
        if ($shipment_methods === []) {
            return null;
        }

        $shipment_method_tag_model = $this->getSession()->getModel(ShipmentMethodTagModel::class);
        $carrier_model = $this->getSession()->getModel(CarrierModel::class);
        $tag_model = $this->getSession()->getModel(TagModel::class);

        $sql = <<<SQL
        WITH
            parameters (city_gps, culture) AS (
                SELECT $*::point, $*::bpchar
            ),
            remote (shipment_method_id, cost, shipping_delay) AS (VALUES {values}),
            store AS (
                SELECT
                    smt.shipment_method_id,
                    (smt.extra_data->>'gps')::point <-> p.city_gps::point  AS distance
                FROM {shipment_method_tag} AS smt
                    INNER JOIN remote ON remote.shipment_method_id = smt.shipment_method_id
                    CROSS JOIN parameters AS p
                WHERE smt.tag_name = 'store'
            )

        SELECT
            {projection}
        FROM
            {relation} AS sm
        CROSS JOIN parameters AS p
        INNER JOIN
            remote ON remote.shipment_method_id = sm.shipment_method_id
        INNER JOIN
            {carrier} c ON c.carrier_id = sm.carrier_id
        LEFT JOIN
            store ON store.shipment_method_id = sm.shipment_method_id
        LEFT JOIN
            {shipment_method_tag} AS smt ON (smt.shipment_method_id = sm.shipment_method_id)
        LEFT JOIN
            {tag} AS t USING (tag_name)
        WHERE
            sm.is_online
        GROUP BY sm.shipment_method_id, store.distance, remote.cost, remote.shipping_delay, c.code, p.culture
        ORDER BY store.distance, coalesce(bool_or(smt.tag_name = 'recommended'), false) DESC, remote.cost
        ;
        SQL;

        $tags_projection = <<<SQL
        COALESCE(jsonb_object_agg(smt.tag_name,
            jsonb_build_object(
                'description', {tag_description},
                'extra_data', smt.extra_data
            )
        ) FILTER (WHERE smt.tag_name IS NOT NULL), '{}')
        SQL;

        $tags_projection = strtr($tags_projection, [
            '{tag_description}' => 't.description_i18n->(p.culture)',
        ]);

        $projection = $this->createProjection()
            ->unsetField('is_online')
            ->unsetField('title_i18n')
            ->unsetField('description_i18n')
            ->unsetField('no_delay_fallback_i18n')
            ->setField('title', 'sm.title_i18n->(p.culture)', 'text')
            ->setField('description', 'sm.description_i18n->(p.culture)', 'text')
            ->setField('no_delay_fallback', 'sm.no_delay_fallback_i18n->(p.culture)', 'text')
            ->setField('cost', 'remote.cost', 'float')
            ->setField('shipping_delay', 'remote.shipping_delay', 'int')
            ->setField('is_express', "coalesce(bool_or(smt.tag_name = 'express'), false)", 'bool')
            ->setField('is_store', "coalesce(bool_or(smt.tag_name = 'store'), false)", 'bool')
            ->setField('is_relay', "coalesce(bool_or(smt.tag_name = 'pickup_location'), false)", 'bool')
            ->setField('is_shipment_quotation', "(c.code = 'IDFN')", 'bool')
            ->setField('distance', 'store.distance', 'float')
            ->setField('tags', $tags_projection, 'jsonb')
            ->setField('carrier_code', 'c.code', 'bpchar');

        $sql_values = [];
        $values = [$gps, $locale];
        foreach ($shipment_methods as $one_shipment) {
            $sql_values[] = '($*::integer, $*::float, $*::integer)';
            // Three parts in $values : shipment_method_id, cost and shipping_delay
            $values[] = $one_shipment['shipment_method_id'];
            if (is_array($one_shipment) && array_key_exists('cost', $one_shipment)) {
                // Data from RPC call
                $values[] = $one_shipment['cost'];
                $values[] = $one_shipment['shipping_delay'] ?? null;
            } else {
                // Data from DB (BasketOrderLine)
                $values[] = $one_shipment['price'] * $one_shipment['quantity'];
                $values[] = null;
            }
        }

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('sm'),
            '{values}' => $sql_values !== [] ? implode(', ', $sql_values) : '(0, 0)',
            '{relation}' => $this->structure->getRelation(),
            '{shipment_method_tag}' => $shipment_method_tag_model->getStructure()->getRelation(),
            '{carrier}' => $carrier_model->getStructure()->getRelation(),
            '{tag}' => $tag_model->getStructure()->getRelation(),
        ]);

        return $this->query($sql, $values, $projection);
    }
}

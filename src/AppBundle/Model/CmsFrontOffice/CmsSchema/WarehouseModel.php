<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CmsSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CmsSchema\AutoStructure\Warehouse as WarehouseStructure;

/**
 * WarehouseModel
 *
 * Model class for table warehouse.
 *
 * @see Model
 */
class WarehouseModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new WarehouseStructure();
        $this->flexible_entity_class = Warehouse::class;
    }

    /**
     * findByShipmentMethodId
     *
     * @return array
     */
    public function findByShipmentMethodId(int $shipment_method_id)
    {
        return $this->findWhere('shipment_method_id = $*', [$shipment_method_id])->extract();
    }
}

<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * Invoice
 *
 * Structure class for relation customer_order.invoice.
 * Invoice associated to an order
 *
 * customer_order_id:
 * Foreign key to order.
 * products:
 * List of products included in the current invoice (a customer order can have
 * several invoices)
 *
 * @see RowStructure
 */
class Invoice extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this
            ->setRelation('customer_order.invoice')
            ->setPrimaryKey(['invoice_id'])
            ->addField('invoice_id', 'int4')
            ->addField('customer_order_id', 'int4')
            ->addField('created_at', 'timestamptz')
            ->addField('products', 'jsonb')
            ;
    }
}

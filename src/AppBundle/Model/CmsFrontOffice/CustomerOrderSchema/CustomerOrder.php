<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema;

use PommProject\ModelManager\Exception\ModelException;
use PommProject\ModelManager\Model\FlexibleEntity;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CarrierSchema\ShipmentMethod;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentMethod;

/**
 * CustomerOrder
 *
 * Flexible entity for relation
 * customer_order.customer_order
 *
 * @see FlexibleEntity
 */
class CustomerOrder extends FlexibleEntity
{
    public const STATUS_AWAITING_PAYMENT = 'AWAITING_PAYMENT';
    public const STATUS_AWAITING_PROCESSING = 'AWAITING_PROCESSING';
    public const STATUS_BEING_PROCESSED = 'BEING_PROCESSED';
    public const STATUS_BEING_PREPARED = 'BEING_PREPARED';
    public const STATUS_PARTIALLY_SHIPPED = 'PARTIALLY_SHIPPED';
    public const STATUS_SHIPPED = 'SHIPPED';
    public const STATUS_PARTIALLY_AVAILABLE = 'PARTIALLY_AVAILABLE';
    public const STATUS_AVAILABLE = 'AVAILABLE';
    public const STATUS_PARTIALLY_PICKED_UP = 'PARTIALLY_PICKED_UP';
    public const STATUS_PICKED_UP = 'PICKED_UP';
    public const STATUS_CANCELLED = 'CANCELLED';
    public const STATUS_DELIVERED = 'DELIVERED';

    public const ESTIMATED_SHIPPING_DATE_TYPE = 'shipping';
    public const ESTIMATED_DELIVERY_DATE_TYPE = 'delivery';

    public const CHECKOUT_VERSION_v2 = 'v2';

    /**
     * @throws ModelException
     */
    public function getEstimatedShippingDate(): ?string
    {
        return $this->getEstimatedDate(static::ESTIMATED_SHIPPING_DATE_TYPE);
    }

    /**
     * @throws ModelException
     */
    public function getEstimatedDeliveryDate(): ?string
    {
        if ($this->get('estimated_delivery_date') instanceof \DateTimeInterface) {
            return $this->get('estimated_delivery_date')->format('Y-m-d');
        }

        if (is_string($this->get('estimated_delivery_date'))) {
            return (new \DateTimeImmutable($this->get('estimated_delivery_date')))->format('Y-m-d');
        }

        return null;
    }

    /**
     *
     * @throws ModelException
     */
    protected function getEstimatedDate(string $mode): ?string
    {
        if (!in_array($mode, static::getEstimatedDateTypes())) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Invalid mode "%s", must be one of following values : "%s"',
                    $mode,
                    implode(', ', static::getEstimatedDateTypes()),
                ),
            );
        }

        $payments = $this->get('payments');

        if (count($payments ?? []) === 0) {
            return null;
        }

        $payment_methods = array_column($payments, 'code');
        $created_date = $this->get('created_at');

        $created_date_formatted = $created_date->format('Y-m-d');
        $dates = [
            static::ESTIMATED_SHIPPING_DATE_TYPE => date('Y-m-d', strtotime($created_date_formatted . ' +10 weekday')),
            static::ESTIMATED_DELIVERY_DATE_TYPE => date('Y-m-d', strtotime($created_date_formatted . ' +12 weekday')),
        ];

        foreach ($payment_methods as $payment_method) {
            if (!in_array($payment_method, PaymentMethod::ELIGIBLE_TO_EXPRESS_DELIVERY_CODES, true)) {
                return $dates[$mode];
            }
        }

        $dates = [
            static::ESTIMATED_SHIPPING_DATE_TYPE => date('Y-m-d', strtotime($created_date_formatted . ' +2 weekday')),
            static::ESTIMATED_DELIVERY_DATE_TYPE => date('Y-m-d', strtotime($created_date_formatted . ' +3 weekday')),
        ];

        if (!in_array($this->get('shipment_method_id'), ShipmentMethod::ELIGIBLE_TO_EXPRESS_DELIVERY_IDS, true)) {
            return $dates[$mode];
        }

        $dates = [
            static::ESTIMATED_SHIPPING_DATE_TYPE => date('Y-m-d', strtotime($created_date_formatted . ' +1 weekday')),
            static::ESTIMATED_DELIVERY_DATE_TYPE => date('Y-m-d', strtotime($created_date_formatted . ' +1 weekday')),
        ];

        return $dates[$mode];
    }

    /**
     * getOrderAmountWithoutShippingCost
     */
    public function getOrderAmountWithoutShippingCost(): string
    {
        $order_amount = $this->get('total_price') - $this->get('shipping_price');

        return is_null($order_amount) || $order_amount <= 0 ? 0 : $order_amount;
    }

    /**
     * getOrderTaxeAmount
     */
    public function getOrderTaxeAmount(): string
    {
        return $this->get('total_price') - $this->get('total_price_vat_excluded');
    }

    /**
     * getEstimatedDateTypes
     */
    protected static function getEstimatedDateTypes(): array
    {
        return [static::ESTIMATED_SHIPPING_DATE_TYPE, static::ESTIMATED_DELIVERY_DATE_TYPE];
    }

    /**
     * needsAdditionalPayment
     */
    public function needsAdditionalPayment(): bool
    {
        if ($this->get('status') !== static::STATUS_AWAITING_PAYMENT) {
            return false;
        }

        return abs($this->getBalance()) > 0.0001;
    }

    public function containsGiftCard(): bool
    {
        $payments = $this->extract()['payments'];
        foreach ($payments as $payment) {
            if (in_array($payment['code'], PaymentMethod::PARTIALLY_PAID_PAYMENT_METHODS)) {
                return true;
            }
        }
        return false;
    }

    /**
     * getBalance
     *
     * @throws ModelException
     */
    public function getBalance(): float
    {
        return $this->get('total_price') -
            array_reduce(
                $this->get('payments') ?? [],
                fn(float $sum, $payment): float => $sum + (float) ($payment['amount'] ?? 0),
                0,
            );
    }

    /**
     * getDiscountAmount
     *
     * @throws ModelException
     */
    public function getDiscountAmount(): float
    {
        $discounts = array_filter(
            $this->get('products'),
            fn($value): bool => \in_array($value['sku'], ['REMISE-COMMANDE', 'REMISE-DEVIS']),
        );

        return array_sum(array_column($discounts, 'total_amount'));
    }

    /**
     * getDiscountAmountVatExcluded
     *
     * @throws ModelException
     */
    public function getDiscountAmountVatExcluded(): float
    {
        return round($this->getDiscountAmount() / (1 + APP_DEFAULT_VAT_RATE), 3);
    }
}

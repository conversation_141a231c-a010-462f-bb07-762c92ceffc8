<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema;

use PommProject\Foundation\Where;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\AutoStructure\ReturnNote as ReturnNoteStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ModelRelationTrait;

/**
 * ReturnNoteModel
 *
 * Model class for table return_note.
 *
 * @see Model
 */
class ReturnNoteModel extends Model
{
    use WriteQueries;
    use ModelRelationTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ReturnNoteStructure();
        $this->flexible_entity_class = ReturnNote::class;
    }

    /**
     * getReturnNote
     *
     * @return ReturnNote|null
     */
    public function getReturnNote(int $return_note_id, int $customer_id)
    {
        $sql = <<<SQL
        SELECT
          {projection}
        FROM
          {relation} r
          INNER JOIN {customer_order} co ON co.customer_order_id = r.customer_order_id
        WHERE
          {condition}
        SQL;
        $projection = $this->createProjection();
        $where = Where::create('r.return_note_id = $* and co.customer_id = $*', [$return_note_id, $customer_id]);

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('r'),
            '{relation}' => $this->structure->getRelation(),
            '{customer_order}' => $this->getModelRelation(CustomerOrderModel::class),
            '{condition}' => $where,
        ]);

        return $this->query($sql, $where->getValues(), $projection)->current();
    }
}

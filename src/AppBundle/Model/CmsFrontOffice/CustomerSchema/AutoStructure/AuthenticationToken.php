<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * AuthenticationToken
 *
 * Structure class for relation customer.authentication_token.
 * This is where tokens used for authentication are stored.
 *
 * purpose:
 * purpose of token generation
 * expire_at:
 * TTL
 *
 * @see RowStructure
 */
class AuthenticationToken extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this
            ->setRelation('customer.authentication_token')
            ->setPrimaryKey(['token'])
            ->addField('token', 'bpchar')
            ->addField('customer_id', 'int4')
            ->addField('salt', 'text')
            ->addField('purpose', 'text')
            ->addField('expire_at', 'timestamptz')
            ->addField('extra', 'jsonb')
            ;
    }
}

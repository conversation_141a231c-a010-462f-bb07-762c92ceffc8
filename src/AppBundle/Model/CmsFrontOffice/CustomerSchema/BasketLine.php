<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema;

use PommProject\ModelManager\Model\FlexibleEntity;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleEavDecoratorForFlexibleEntityTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleWarrantyExtension;

/**
 * BasketLine
 *
 * Flexible entity for relation
 * customer.basket_line
 *
 * @see FlexibleEntity
 */
class BasketLine extends FlexibleEntity
{
    use ArticleEavDecoratorForFlexibleEntityTrait;

    /**
     * getWarranties
     */
    public function getWarranties(): array
    {
        return $this->get('extra_data')['warranties'] ?? [];
    }

    /**
     * addWarranty
     */
    public function addWarranty(ArticleWarrantyExtension $article_warranty): self
    {
        $extra_data = $this->get('extra_data');

        $tag_path = $article_warranty->get('tag_path');
        if (is_array($tag_path)) {
            $tag_path = implode('.', $tag_path);
        }

        if (isset($extra_data['warranties'])) {
            $extra_data['warranties'][] = ['tag_path' => $tag_path];
        } else {
            $extra_data['warranties'] = [['tag_path' => $tag_path]];
        }

        return $this->set('extra_data', $extra_data);
    }

    /**
     * removeWarranty
     */
    public function removeWarranty(string $warranty_tag_path): self
    {
        $extra_data = $this->get('extra_data');

        if (!isset($extra_data['warranties'])) {
            return $this;
        }

        foreach ($extra_data['warranties'] as $key => $warranty) {
            if ($warranty['tag_path'] === $warranty_tag_path) {
                unset($extra_data['warranties'][$key]);
            }
        }

        $extra_data['warranties'] = array_values($extra_data['warranties']);

        return $this->set('extra_data', $extra_data);
    }
}

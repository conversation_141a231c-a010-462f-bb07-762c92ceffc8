<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * InstallationArticle
 *
 * Structure class for relation customer_social.installation_article.
 * Articles associated to customer installations
 *
 * installation_article_id:
 * Installation article unique identifier
 * name:
 * Article name when article does not exist in database.
 *
 * @see RowStructure
 */
class InstallationArticle extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('customer_social.installation_article')
            ->setPrimaryKey(['installation_article_id'])
            ->addField('installation_article_id', 'uuid')
            ->addField('installation_id', 'int4')
            ->addField('article_id', 'int4')
            ->addField('name', 'text')
            ->add<PERSON>ield('quantity', 'int4');
    }
}

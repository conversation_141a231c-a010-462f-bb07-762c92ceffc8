<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema;

use PommProject\ModelManager\Exception\ModelException;
use PommProject\Foundation\ConvertedResultIterator;
use PommProject\ModelManager\Model\ModelTrait\ReadQueries;
use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\Projection;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMediaI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMediaModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleUrlModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\CommonContentI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\CommonContentModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ModelRelationTrait;
use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountInformationModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\AutoStructure\CommonContentQuestion as CommonContentQuestionStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\CommonContentQuestion;

/**
 * CommonContentQuestionModel
 *
 * Model class for table common_content_question.
 *
 * @see Model
 */
class CommonContentQuestionModel extends Model
{
    use ReadQueries;
    use ModelRelationTrait;

    public const MORE_ANSWER = 'more_answer';
    public const NO_ANSWER = 'no_answer';
    public const MOST_USEFULL = 'most_usefull';
    public const MOST_RECENT = 'most_recent';

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new CommonContentQuestionStructure();
        $this->flexible_entity_class = CommonContentQuestion::class;
    }

    /**
     * getCompleteProjection
     */
    protected function getCompleteProjection(): Projection
    {
        $DEFAULT_ORIGIN = CommonContentReview::DEFAULT_ORIGIN;

        return $this->createProjection()
            ->setField('answers', 'COALESCE(ccq.answers, \'[]\'::jsonb)', 'jsonb')
            ->setField('created_at', 'ccq.created_at', 'text')
            ->setField(
                'username',
                "CASE 
                    WHEN ccq.origin != '$DEFAULT_ORIGIN' THEN '__external__'
                    WHEN ccq.preferences @> '{\"anonymous\": true}' THEN '__anonymous__' 
                    ELSE COALESCE(ai.pseudonym, '__anonymous__') 
                END",
                'text',
            );
    }

    /**
     * findQuestionsForCommonContentId
     */
    public function findQuestionsForCommonContentId(
        int $common_content_id,
        string $culture,
        ?string $filtre
    ): CollectionIterator {
        switch ($filtre) {
            case self::MORE_ANSWER:
                $order_by = 'nb_answers DESC';

                break;
            case self::NO_ANSWER:
                $order_by = 'nb_answers ASC';
                break;
            case self::MOST_RECENT:
                $order_by = 'ccq.created_at DESC';
                break;
            case self::MOST_USEFULL:
            default:
                $order_by = 'is_usefull DESC';
                break;
        }

        $sql = <<<SQL
        WITH
            parameters AS (
              SELECT $*::int AS common_content_id, $*::text as culture, $*::text as origin
            ),
            associated_products_by_question AS (
              SELECT
                ccq.common_content_question_id,
                unnest(ccq.associated_products) AS common_content_id
              FROM {relation} ccq
              CROSS JOIN parameters p
              WHERE p.common_content_id = ccq.common_content_id
            ),
            associated_products_info AS (
              SELECT
                apbq.common_content_question_id,
                cc.common_content_id,
                cc.default_article_id,
                cci.name,
                au.article_url,
                am.media_variation,
                a.unbasketable_reason IS NOT NULL as is_unbasketable
              FROM associated_products_by_question apbq
              CROSS JOIN parameters p
              INNER JOIN {common_content} cc          ON cc.common_content_id = apbq.common_content_id
              INNER JOIN {common_content_i18n} cci    ON cci.common_content_id = apbq.common_content_id
                                                      AND cci.supported_culture_id = p.culture
              INNER JOIN {article} a                  ON a.article_id = cc.default_article_id
              INNER JOIN {article_media_i18n} ami18n  ON ami18n.article_id = a.article_id
                                                      AND ami18n.display_order = 1
                                                      AND ami18n.type = 'IMAGE'::article.article_media_type
              INNER JOIN {article_media} am           ON am.media_id = ami18n.media_id
              INNER JOIN {article_url} au             ON au.article_id = a.article_id
            ),
            associated_products_info_regroup AS (
              SELECT
                common_content_question_id,
                JSONB_AGG(associated_products_info) AS associated_products
              FROM associated_products_info
              GROUP BY common_content_question_id
            ),
            count_answer AS (
            SELECT ccq.common_content_question_id, COUNT(*) AS nb_answers
            FROM {relation} ccq
                CROSS JOIN LATERAL jsonb_array_elements(ccq.answers) k
                CROSS JOIN parameters p
            WHERE ccq.common_content_id = p.common_content_id
            GROUP BY ccq.common_content_question_id
            )
        SELECT
          {projection}
        FROM {relation} ccq
          CROSS JOIN parameters p
          LEFT JOIN associated_products_info_regroup apir ON apir.common_content_question_id = ccq.common_content_question_id
          LEFT JOIN {account_information} ai ON (ccq.username = ai.customer_id::text AND ccq.origin = p.origin)
          LEFT JOIN count_answer ca ON ca.common_content_question_id = ccq.common_content_question_id
        WHERE ccq.common_content_id = p.common_content_id
        ORDER BY {order_by}
        SQL;

        $projection = $this->getCompleteProjection()
            ->setField('associated_products', 'apir.associated_products', 'jsonb')
            ->setField('is_usefull', 'coalesce(((ccq.vote_useful * 3) - ccq.vote_useless),0)', 'int')
            ->setField('nb_answers', 'coalesce(ca.nb_answers,0)', 'int');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('ccq'),
            '{relation}' => $this->structure->getRelation(),
            '{account_information}' => $this->getModelRelation(AccountInformationModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_url}' => $this->getModelRelation(ArticleUrlModel::class),
            '{order_by}' => $order_by,
        ]);

        return $this->query($sql, [$common_content_id, $culture, CommonContentQuestion::DEFAULT_ORIGIN], $projection);
    }

    /**
     * getAllAnswersAssociatedProductsFromQuestion
     */
    public function getAllAnswersAssociatedProductsFromQuestion(
        int $common_content_id,
        string $culture
    ): ConvertedResultIterator {
        $sql = <<<SQL
        WITH
            parameters AS (
              SELECT $*::int AS common_content_id, $*::text as culture
            ),
            answers_from_question AS (
              SELECT
                ccq.common_content_question_id,
                regexp_matches(ccq.answers::text, '"associated_products": \[([^\]]*)\]', 'g') as associated_products
              FROM {relation} ccq
                CROSS JOIN parameters p
              WHERE p.common_content_id = ccq.common_content_id
            ),

            associated_products AS (
              SELECT
                common_content_question_id,
                btrim(unnest(string_to_array(associated_products[1], ',')), '" ')::text as common_content_id
              FROM answers_from_question
            ),
            checked_associated_products AS (
              SELECT
                common_content_question_id,
                common_content_id::int
              FROM associated_products
              WHERE common_content_id ~ '^\d+$'
            )
        SELECT
          {projection}
        FROM checked_associated_products ap
          CROSS JOIN parameters p
          INNER JOIN {common_content} cc         ON cc.common_content_id = ap.common_content_id
          INNER JOIN {common_content_i18n} cci   ON cci.common_content_id = ap.common_content_id
                                                 AND cci.supported_culture_id = p.culture
          INNER JOIN {article} a                 ON a.article_id = cc.default_article_id
          INNER JOIN {article_media_i18n} ami18n ON ami18n.article_id = a.article_id
                                                 AND ami18n.display_order = 1
                                                 AND ami18n.type = 'IMAGE'::article.article_media_type
          INNER JOIN {article_media} am          ON am.media_id = ami18n.media_id
          INNER JOIN {article_url} au            ON au.article_id = a.article_id

        SQL;

        $projection = (new Projection(''))
            ->setField('common_content_id', 'cc.common_content_id', 'text')
            ->setField('common_content_question_id', 'ap.common_content_question_id', 'text')
            ->setField('default_article_id', 'cc.default_article_id', 'text')
            ->setField('name', 'cci.name', 'text')
            ->setField('article_url', 'au.article_url', 'text')
            ->setField('media_variation', 'am.media_variation', 'text')
            ->setField('is_unbasketable', 'a.unbasketable_reason IS NOT NULL', 'boolean');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('ccq'),
            '{relation}' => $this->structure->getRelation(),
            '{account_information}' => $this->getModelRelation(AccountInformationModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_url}' => $this->getModelRelation(ArticleUrlModel::class),
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, [$common_content_id, $culture], $projection);
    }

    /**
     * getAllQuestionResponders
     */
    public function getAllQuestionResponders(int $common_content_id): ConvertedResultIterator
    {
        $sql = <<<SQL
        WITH
          parameters AS (SELECT $*::int AS common_content_id),
          users_from_question AS (
            SELECT
              ccq.common_content_question_id,
              unnest(regexp_matches(ccq.answers::text, '"user_id": "(.*?)"', 'g')) as user_id
            FROM customer_social.common_content_question ccq
              CROSS JOIN parameters p
            WHERE p.common_content_id = ccq.common_content_id
          )

        SELECT
          {projection}
        FROM users_from_question ufq
          LEFT JOIN {account_information} ai ON ai.customer_id = ufq.user_id::int
        WHERE ufq.user_id ~ '^\d+$'
        SQL;

        $projection = (new Projection(''))
            ->setField('common_content_question_id', 'ufq.common_content_question_id', 'text')
            ->setField('user_id', 'ufq.user_id', 'text')
            ->setField('pseudonym', 'ai.pseudonym', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('ccq'),
            '{relation}' => $this->structure->getRelation(),
            '{account_information}' => $this->getModelRelation(AccountInformationModel::class),
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, [$common_content_id], $projection);
    }

    /**
     * findOneComplete
     *
     * @return CommonContentQuestion|null
     * @throws ModelException
     */
    public function findOneComplete(string $common_content_question_id)
    {
        $sql = <<<SQL

                WITH
            parameters AS (
              SELECT $*::text AS common_content_question_id
            ),
            associated_products_by_question AS (
              SELECT
                ccq.common_content_question_id,
                unnest(ccq.associated_products) AS common_content_id
              FROM {relation} ccq
              CROSS JOIN parameters p
              WHERE p.common_content_question_id::uuid = ccq.common_content_question_id
            ),
            associated_products_info AS (
              SELECT
                apbq.common_content_question_id,
                cc.common_content_id,
                cc.default_article_id,
                cci.name,
                au.article_url,
                am.media_variation,
                a.unbasketable_reason IS NOT NULL as is_unbasketable
              FROM associated_products_by_question apbq
              CROSS JOIN parameters p
              INNER JOIN {common_content} cc          ON cc.common_content_id = apbq.common_content_id
              INNER JOIN {common_content_i18n} cci    ON cci.common_content_id = apbq.common_content_id
              INNER JOIN {article} a                  ON a.article_id = cc.default_article_id
              INNER JOIN {article_media_i18n} ami18n  ON ami18n.article_id = a.article_id
                                                      AND ami18n.display_order = 1
                                                      AND ami18n.type = 'IMAGE'::article.article_media_type
              INNER JOIN {article_media} am           ON am.media_id = ami18n.media_id
              INNER JOIN {article_url} au             ON au.article_id = a.article_id
            ),
            associated_products_info_regroup AS (
              SELECT
                common_content_question_id,
                JSONB_AGG(associated_products_info) AS associated_products
              FROM associated_products_info
              GROUP BY common_content_question_id
            )

        SELECT
          {projection}
        FROM {relation} ccq
          LEFT JOIN {account_information} ai ON ccq.origin = '{default_origin}'
                                             AND ccq.username = ai.customer_id::text
         LEFT JOIN associated_products_info_regroup apir ON apir.common_content_question_id = ccq.common_content_question_id
        WHERE {condition}
        SQL;
        $projection = $this->getCompleteProjection()
            ->unsetField('origin')
            ->setField('associated_products', 'apir.associated_products', 'jsonb');
        $where = Where::create('ccq.common_content_question_id = $*', [$common_content_question_id]);

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('ccq'),
            '{relation}' => $this->structure->getRelation(),
            '{account_information}' => $this->getModelRelation(AccountInformationModel::class),
            '{default_origin}' => CommonContentQuestion::DEFAULT_ORIGIN,
            '{condition}' => $where,
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_url}' => $this->getModelRelation(ArticleUrlModel::class),
        ]);

        return $this->query(
            $sql,
            array_merge([$common_content_question_id], $where->getValues()),
            $projection,
        )->current();
    }

    /**
     * findOneCompleteOrAnswer
     *
     * Retrieve an answer out of a question's answers, recursively
     *
     *
     * @throws \Exception
     */
    public function findOneCompleteAnswer(string $common_content_question_id, string $answer_id): array
    {
        $common_content_question = $this->findOneComplete($common_content_question_id);

        if (!$common_content_question instanceof CommonContentQuestion) {
            throw new \Exception(sprintf('Common Content Question %s does not exists', $common_content_question_id));
        }

        $answer = $this->findAnswerInAnswersRecursively($answer_id, $common_content_question->extract()['answers']);

        if ($answer === null) {
            throw new \Exception(
                sprintf('Answer %s not found in Common Content Question %s', $answer_id, $common_content_question_id),
            );
        }

        // set additional data on the answer
        $answer['common_content_id'] = $common_content_question['common_content_id'];

        return $answer;
    }

    /**
     * findAnswerInAnswersRecursively
     *
     *
     * @return array|null
     */
    protected function findAnswerInAnswersRecursively(string $answer_id, array $answers)
    {
        if ($answers === []) {
            return null;
        }

        $answer_index = array_search($answer_id, array_column($answers, 'comment_id'));
        if ($answer_index !== false) {
            return $answers[$answer_index];
        }

        $answer_index = array_search($answer_id, array_column($answers, 'id'));
        if ($answer_index !== false) {
            return $answers[$answer_index];
        }

        // Recursive search in answers
        foreach ($answers as $answer) {
            $found_answer = $this->findAnswerInAnswersRecursively($answer_id, $answer['answers']);

            if ($found_answer !== null) {
                return $found_answer;
            }
        }

        return null;
    }
}

<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema;

use PommProject\Foundation\Pager;
use PommProject\Foundation\Where;
use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use SonVideo\Cms\FrontOffice\AppBundle\Model\BaseModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\AutoStructure\Installation as InstallationStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleCategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\BrandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountInformationModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ModelRelationTrait;

/**
 * InstallationModel
 *
 * Model class for table installation.
 *
 * @see Model
 */
class InstallationModel extends BaseModel
{
    use WriteQueries;
    use ModelRelationTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new InstallationStructure();
        $this->flexible_entity_class = Installation::class;
    }

    /**
     * fetchOne
     *
     * @return null|Installation
     */
    public function fetchOne(int $installation_id, string $supported_culture_id, bool $only_accepted = true)
    {
        $sql = <<<SQL
        WITH installation_article_name AS (
          SELECT
            ia.installation_id,
            CASE
              WHEN (a.name IS NOT NULL) THEN COALESCE(b.name, '') || ' ' || a.name
              ELSE ia.name
            END AS name,
            ia.quantity,
            s.slug                                      AS stand_slug,
            COALESCE(si18n.name, si18n_default.name)    AS stand_name
          FROM {installation_article} ia
            LEFT JOIN {article} a                   ON a.article_id = ia.article_id
            LEFT JOIN {brand} b                     ON b.brand_id = a.brand_id
            LEFT JOIN {stand} s                     ON s.stand_id = a.default_stand_id
            LEFT JOIN {stand_i18n} si18n            ON si18n.stand_id = s.stand_id
                                                    AND si18n.supported_culture_id = $*
            LEFT JOIN {stand_i18n} si18n_default    ON si18n_default.stand_id = s.stand_id
                                                    AND si18n_default.supported_culture_id = $*
          WHERE {condition_articles}
        )
        SELECT
          {projection}
        FROM
          {relation} r
          INNER JOIN installation_article_name ian ON ian.installation_id = r.installation_id
          LEFT JOIN {account_information} ai       ON ai.customer_id = r.customer_id
        WHERE
          {condition}
        GROUP BY
          {group_projection}
        SQL;
        $extra_values = [$supported_culture_id, APP_DEFAULT_LOCALE];
        $where_articles = Where::create('ia.installation_id = $*', [$installation_id]);
        $where = Where::create('r.installation_id = $*', [$installation_id]);
        if ($only_accepted) {
            $where->andWhere('r.is_accepted');
        }

        $projection = $this->createProjection()
            ->setField('account_firstname', 'ai.firstname', 'text')
            ->setField('account_lastname', 'ai.lastname', 'text')
            ->setField('articles', 'jsonb_agg(ian)', 'jsonb');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('r'),
            '{relation}' => $this->structure->getRelation(),
            '{installation_article}' => $this->getModelRelation(InstallationArticleModel::class),
            '{account_information}' => $this->getModelRelation(AccountInformationModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{stand}' => $this->getModelRelation(StandModel::class),
            '{stand_i18n}' => $this->getModelRelation(StandI18nModel::class),
            '{condition}' => $where,
            '{condition_articles}' => $where_articles,
            '{group_projection}' => implode(', ', range(1, count($projection->getFieldsWithTableAlias()) - 1)),
        ]);

        return $this->query(
            $sql,
            array_merge($extra_values, $where_articles->getValues(), $where->getValues()),
            $projection,
        )->current();
    }

    /**
     * getPaginatedInstallations
     *
     * @param string|null $type
     *
     */
    public function getPaginatedInstallations(int $page, int $item_per_page, $type = null): Pager
    {
        $offset = $item_per_page * ($page - 1);
        $limit = $item_per_page;
        $sql = <<<SQL
        SELECT
          {projection}
        FROM
          {installation} i
          LEFT JOIN {account_information} ai ON ai.customer_id = i.customer_id
        WHERE
          {condition}
        ORDER BY
          i.created_at DESC
        OFFSET {offset}
        LIMIT {limit}
        SQL;
        $projection = $this->createProjection()
            ->setField('customer_firstname', 'ai.firstname', 'text')
            ->setField('customer_lastname', 'ai.lastname', 'text')
            ->setField('main_media', "i.medias->'main'", 'jsonb')
            ->unsetField('description')
            ->unsetField('medias')
            ->unsetField('budget')
            ->unsetField('surface_area_m2');
        $where = Where::create("is_accepted AND medias ? 'main'");
        if ($type !== null) {
            $where->andWhere('installation_type = $*::customer_social.installation_type', [$type]);
        }

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('i'),
            '{installation}' => $this->structure->getRelation(),
            '{account_information}' => $this->getModelRelation(AccountInformationModel::class),
            '{condition}' => $where,
            '{offset}' => $offset,
            '{limit}' => $limit,
        ]);

        return new Pager(
            $this->query($sql, $where->getValues(), $projection),
            $this->countWhere($where),
            $item_per_page,
            $page,
        );
    }

    /**
     * getAllForCustomer
     */
    public function getAllForCustomer(
        int $customer_id,
        string $supported_culture_id = APP_DEFAULT_LOCALE
    ): CollectionIterator {
        $sql = <<<SQL
        WITH
        parameters AS (SELECT $*::integer AS customer_id, $*::text AS supported_culture_id),
        installation_article_name AS (
          SELECT
            ia.installation_id,
            ia.installation_article_id,
            ia.article_id,
            CASE
              WHEN (a.name IS NOT NULL) THEN COALESCE(b.name, '') || ' ' || a.name
              ELSE ia.name
            END AS name,
            ia.quantity,
            c.slug                                   AS category_slug,
            COALESCE(ci18n.name, ci18n_default.name) AS category_name
          FROM {relation} r
            CROSS JOIN parameters param
            INNER JOIN {installation_article} ia    ON ia.installation_id = r.installation_id
            LEFT JOIN {article} a                   ON a.article_id = ia.article_id
            LEFT JOIN {brand} b                     ON b.brand_id = a.brand_id
            LEFT JOIN {article_category} ac         ON ac.article_id = a.article_id
            LEFT JOIN {category} c                  ON c.category_id = ac.category_id
            LEFT JOIN {category_i18n} ci18n         ON ci18n.category_id = c.category_id
                                                    AND ci18n.supported_culture_id = param.supported_culture_id
            LEFT JOIN {category_i18n} ci18n_default ON ci18n_default.category_id = c.category_id
                                                    AND ci18n_default.supported_culture_id = param.supported_culture_id
          WHERE r.customer_id = param.customer_id
        )
        SELECT {projection}
        FROM {relation} i
          INNER JOIN installation_article_name ia ON ia.installation_id = i.installation_id
          CROSS JOIN parameters param
        WHERE i.customer_id = param.customer_id
        GROUP BY {group_projection}
        ORDER BY created_at DESC
        SQL;
        $projection = $this->createProjection()->setField('articles', 'jsonb_agg(ia)', 'jsonb');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('i'),
            '{relation}' => $this->structure->getRelation(),
            '{installation_article}' => $this->getModelRelation(InstallationArticleModel::class),
            '{account_information}' => $this->getModelRelation(AccountInformationModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category}' => $this->getModelRelation(CategoryModel::class),
            '{category_i18n}' => $this->getModelRelation(CategoryI18nModel::class),
            '{group_projection}' => implode(', ', range(1, count($projection->getFieldsWithTableAlias()) - 1)),
        ]);

        return $this->query($sql, [$customer_id, $supported_culture_id], $projection);
    }
}

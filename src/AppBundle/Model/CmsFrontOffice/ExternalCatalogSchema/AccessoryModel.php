<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\Projection;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use PommProject\Foundation\Where;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\AutoStructure\Accessory as AccessoryStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\Accessory;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\BrandModel as ArticleBrandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ModelRelationTrait;

/**
 * AccessoryModel
 *
 * Model class for table accessory.
 *
 * @see Model
 */
class AccessoryModel extends Model
{
    use WriteQueries;
    use ModelRelationTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new AccessoryStructure();
        $this->flexible_entity_class = Accessory::class;
    }

    /**
     * findByProduct
     *
     *
     */
    public function findByProduct(string $product_path, string $culture): CollectionIterator
    {
        $where = Where::create('mpa.main_product_path = $*', [$product_path]);

        return $this->findCompletedAccessories($where, $culture);
    }

    /**
     * findByBrand
     *
     *
     */
    public function findByBrand(string $brand_path, string $culture): CollectionIterator
    {
        $where = Where::create('mp.brand_path = $*', [$brand_path]);

        return $this->findCompletedAccessories($where, $culture);
    }

    /**
     * findByBrandAndProduct
     *
     *
     */
    public function findByBrandAndProduct(string $brand_path, string $product_name, string $culture): CollectionIterator
    {
        $where = Where::create('mp.brand_path = $* AND coalesce(aa.name, mp.name) ~* $*', [$brand_path, $product_name]);

        return $this->findCompletedAccessories($where, $culture);
    }

    /**
     * findById
     *
     * @return null|\SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\Accessory
     */
    public function findSimpleById(int $accessory_id, string $culture)
    {
        $projection = $this->createProjection()
            ->setField('short_description', 'COALESCE(short_description->$*, \'\')', 'text')
            ->setField('created_at', 'created_at', 'text');

        $sql = strtr('SELECT {projection} FROM {relation} WHERE {condition}', [
            '{projection}' => $projection->formatFieldsWithFieldAlias(),
            '{relation}' => $this->structure->getRelation(),
            '{condition}' => Where::create('accessory_id = $*'),
        ]);

        return $this->query($sql, [$culture, $accessory_id], $projection)->current();
    }

    /**
     * findCompletedAccessories
     *
     *
     */
    protected function findCompletedAccessories(Where $where, string $culture): CollectionIterator
    {
        $sql = <<<SQL
        WITH parameters as (
          SELECT $*::text as culture, $*::text as default_culture
        )
        SELECT
          {projection}
        FROM
          {accessory} a
          CROSS JOIN parameters p
          INNER JOIN {main_product_accessory} mpa
            ON mpa.accessory_id = a.accessory_id
          INNER JOIN {main_product} mp
            ON mp.path = mpa.main_product_path
          INNER JOIN {external_brand} ecb
            ON mp.brand_path = ecb.path
          LEFT JOIN {brand} b
            ON ecb.brand_id = b.brand_id
          LEFT JOIN {article} aa
            ON mp.article_id = aa.article_id
        WHERE
        {condition}
        ORDER BY coalesce(aa.name, mp.name) ASC
        SQL;

        $projection = $this->getCompleteAccessoryProjection($culture);

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('a'),
            '{accessory}' => $this->structure->getRelation(),
            '{main_product_accessory}' => $this->getModelRelation(MainProductAccessoryModel::class),
            '{main_product}' => $this->getModelRelation(MainProductModel::class),
            '{external_brand}' => $this->getModelRelation(BrandModel::class),
            '{brand}' => $this->getModelRelation(ArticleBrandModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{condition}' => $where,
        ]);

        return $this->query($sql, array_merge([$culture, APP_DEFAULT_LOCALE], $where->getValues()), $projection);
    }

    /**
     * getCompleteAccessoryProjection
     *
     *
     */
    protected function getCompleteAccessoryProjection(string $culture): Projection
    {
        return $this->createProjection()
            ->unsetField('short_description')
            ->unsetField('created_at')
            ->setField(
                'short_description',
                $culture === APP_DEFAULT_LOCALE
                    ? 'a.short_description->p.culture'
                    : 'coalesce(a.short_description->p.culture, a.short_description->p.default_culture)',
                'text',
            )
            ->setField('brand_name', 'coalesce(b.name, ecb.name)', 'text')
            ->setField('product_name', 'coalesce(aa.name, mp.name)', 'text');
    }
}

<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * MainProductAccessory
 *
 * Structure class for relation external_catalog.main_product_accessory.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 * @see http://www.postgresql.org/docs/9.0/static/sql-comment.html
 *
 *
 *
 * @see RowStructure
 */
class MainProductAccessory extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('external_catalog.main_product_accessory')
            ->setPrimaryKey(['accessory_id', 'main_product_path'])
            ->addField('main_product_path', 'public.ltree')
            ->add<PERSON><PERSON>('accessory_id', 'int4');
    }
}

<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use PommProject\Foundation\Where;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\AutoStructure\Brand as BrandStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\Brand;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\BrandModel as ArticleBrandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ModelRelationTrait;

/**
 * BrandModel
 *
 * Model class for table brand.
 *
 * @see Model
 */
class BrandModel extends Model
{
    use WriteQueries;
    use ModelRelationTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new BrandStructure();
        $this->flexible_entity_class = Brand::class;
    }

    /**
     * findByCatalog
     *
     *
     */
    public function findByCatalog(string $catalog_name): CollectionIterator
    {
        $where = Where::create('ecb.path <@ $*', [$catalog_name]);

        $sql = <<<SQL
        SELECT
          {projection}
        FROM {relation} ecb
          LEFT JOIN {brand} b
          ON ecb.brand_id = b.brand_id
        WHERE
        {condition}
        ORDER BY name ASC
        SQL;

        $projection = $this->createProjection()
            ->unsetField('name')
            ->unsetField('brand_id')
            ->setField('name', 'coalesce(b.name, ecb.name)', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('ecb'),
            '{relation}' => $this->structure->getRelation(),
            '{brand}' => $this->getModelRelation(ArticleBrandModel::class),
            '{condition}' => $where,
        ]);

        return $this->query($sql, $where->getValues(), $projection);
    }
}

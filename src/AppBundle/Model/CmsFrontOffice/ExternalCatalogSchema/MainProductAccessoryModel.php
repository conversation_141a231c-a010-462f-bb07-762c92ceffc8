<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\AutoStructure\MainProductAccessory as MainProductAccessoryStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\MainProductAccessory;

/**
 * MainProductAccessoryModel
 *
 * Model class for table main_product_accessory.
 *
 * @see Model
 */
class MainProductAccessoryModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new MainProductAccessoryStructure();
        $this->flexible_entity_class = MainProductAccessory::class;
    }
}

<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * FamilyI18n
 *
 * Structure class for relation guide.family_i18n.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 * @see http://www.postgresql.org/docs/9.0/static/sql-comment.html
 *
 *
 *
 * @see RowStructure
 */
class FamilyI18n extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('guide.family_i18n')
            ->setPrimaryKey(['supported_culture_id', 'family_id'])
            ->add<PERSON>ield('family_id', 'int4')
            ->add<PERSON>ield('supported_culture_id', 'bpchar')
            ->add<PERSON>ield('name', 'text')
            ->add<PERSON>ield('title', 'text')
            ->add<PERSON>ield('keywords', 'text[]')
            ->addField('description', 'text');
    }
}

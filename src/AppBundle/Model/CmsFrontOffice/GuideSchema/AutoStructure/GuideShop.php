<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * GuideShop
 *
 * Structure class for relation guide.guide_shop.
 * The guide associated to the shop to qualify its "understand to choose". A
 * shop could have only one association.
 *
 *
 *
 * @see RowStructure
 */
class GuideShop extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('guide.guide_shop')
            ->setPrimaryKey(['shop_id'])
            ->addField('shop_id', 'int4')
            ->addField('guide_id', 'int4');
    }
}

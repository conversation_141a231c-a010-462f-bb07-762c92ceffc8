<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * GuideTechno
 *
 * Structure class for relation guide.guide_techno.
 * The guide used to describe the technology. A technology could have only one
 * association.
 *
 *
 *
 * @see RowStructure
 */
class GuideTechno extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('guide.guide_techno')
            ->setPrimaryKey(['technology_id'])
            ->addField('technology_id', 'int4')
            ->addField('guide_id', 'int4');
    }
}

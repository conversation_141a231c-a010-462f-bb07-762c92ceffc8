<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\ReadQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\AutoStructure\FamilyI18n as FamilyI18nStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\FamilyI18n;

/**
 * FamilyI18nModel
 *
 * Model class for table family_i18n.
 *
 * @see Model
 */
class FamilyI18nModel extends Model
{
    use ReadQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new FamilyI18nStructure();
        $this->flexible_entity_class = FamilyI18n::class;
    }
}

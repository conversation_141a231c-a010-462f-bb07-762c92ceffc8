<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema;

use PommProject\Foundation\Where;
use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\ModelTrait\ReadQueries;
use PommProject\ModelManager\Model\Projection;
use SonVideo\Cms\FrontOffice\AppBundle\Model\BaseModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\AutoStructure\Family as FamilyStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandModel;

/**
 * FamilyModel
 *
 * Model class for table family.
 *
 * @see BaseModel
 */
class FamilyModel extends BaseModel
{
    use ReadQueries;

    public const AMOUNT_OF_LATEST_GUIDES = 5;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new FamilyStructure();
        $this->flexible_entity_class = Family::class;
    }

    /**
     * findAllOrderedWithGuides
     *
     * Retrieve all the families ordered by display_order,
     * with a "guides" property containing the self::AMOUNT_OF_LATEST_GUIDES most recent active guides slug and name
     */
    public function findAllOrderedWithGuides(string $culture): CollectionIterator
    {
        $sql = <<<SQL
        WITH
          parameters(culture) AS (SELECT $*::bpchar),
          family_with_guides_positioned AS (
            SELECT
              family_id,
              g.guide_id,
              g.slug AS slug,
              row_number() OVER (PARTITION BY family_id ORDER BY created_at DESC) AS pos
            FROM
              {guide_family} gf
              JOIN {guide} g ON (g.guide_id = gf.guide_id AND g.is_active)
          ),
          last_guides AS (
            SELECT
              family_id,
              array_agg(
                json_build_object(
                  'guide_id', fgo.guide_id,
                  'name', COALESCE(gi18n.name, dgi18n.name),
                  'slug', fgo.slug
                )
              ) AS guides
            FROM
              family_with_guides_positioned fgo
              CROSS JOIN parameters AS p
              JOIN {guide_i18n} dgi18n ON (dgi18n.guide_id = fgo.guide_id AND dgi18n.supported_culture_id = '{default_culture}')
              LEFT JOIN {guide_i18n} gi18n ON (gi18n.guide_id = fgo.guide_id AND gi18n.supported_culture_id = p.culture)
            WHERE fgo.pos <= {AMOUNT_OF_LATEST_GUIDES}
            GROUP BY family_id
          )

        SELECT
          {projection}
        FROM
          {family} f
          CROSS JOIN parameters AS p
          JOIN {family_i18n} dfi18n ON (dfi18n.family_id = f.family_id AND dfi18n.supported_culture_id = '{default_culture}')
          LEFT JOIN {family_i18n} fi18n ON (fi18n.family_id = f.family_id AND fi18n.supported_culture_id = p.culture)
          JOIN last_guides lg ON (lg.family_id = f.family_id)
        ORDER BY f.display_order ASC
        SQL;

        $projection = $this->createProjection()
            ->setField('name', 'COALESCE(fi18n.name, dfi18n.name)', 'text')
            ->setField('guides', 'lg.guides', 'jsonb[]');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('f'),
            '{family}' => $this->structure->getRelation(),
            '{family_i18n}' => $this->getModelRelation(FamilyI18nModel::class),
            '{guide}' => $this->getModelRelation(GuideModel::class),
            '{guide_family}' => $this->getModelRelation(GuideFamilyModel::class),
            '{guide_i18n}' => $this->getModelRelation(GuideI18nModel::class),
            '{default_culture}' => APP_DEFAULT_LOCALE,
            '{AMOUNT_OF_LATEST_GUIDES}' => self::AMOUNT_OF_LATEST_GUIDES,
        ]);

        return $this->query($sql, [$culture], $projection);
    }

    /**
     * getQueryFindBySlugWithGuides
     *
     * Return a query to get family information with guides list.
     * {projection} and {guide_projection} must be set in call function
     */
    private function getQueryFindBySlugWithGuides(Where $condition, string $slug, string $culture): string
    {
        $sql = <<<SQL
        WITH
          parameters(slug, culture) AS (SELECT $*::text, $*::bpchar)
        SELECT
          {projection}
        FROM
          parameters AS p
          JOIN {family} f               ON f.slug = p.slug
          JOIN {family_i18n} dfi18n     ON dfi18n.family_id = f.family_id AND dfi18n.supported_culture_id = '{default_culture}'
          LEFT JOIN {family_i18n} fi18n ON fi18n.family_id = f.family_id AND fi18n.supported_culture_id = p.culture
          LEFT JOIN LATERAL (
            SELECT
              array_agg(
                {guide_projection}
                ORDER BY g.created_at DESC
              ) AS guides
            FROM
              {guide_family} gf
              JOIN {guide} g               ON g.guide_id = gf.guide_id
              JOIN {guide_i18n} dgi18n     ON dgi18n.guide_id = g.guide_id AND dgi18n.supported_culture_id = '{default_culture}'
              LEFT JOIN {guide_i18n} gi18n ON gi18n.guide_id = g.guide_id AND gi18n.supported_culture_id = p.culture
              LEFT JOIN LATERAL (
                SELECT s.stand_id, s.slug
                FROM
                  {guide_stand} gs
                  JOIN {stand} s          ON gs.stand_id = s.stand_id
                WHERE gs.guide_id = g.guide_id
                ORDER BY gs.stand_id
                LIMIT 1
              ) s ON TRUE
            WHERE
              {condition}
          ) g ON TRUE
        SQL;

        $condition->andWhere('gf.family_id = f.family_id AND g.is_active', [$slug, $culture]);
        $condition->setStack(array_reverse($condition->stack));

        return strtr($sql, [
            '{family}' => $this->structure->getRelation(),
            '{family_i18n}' => $this->getModelRelation(FamilyI18nModel::class),
            '{guide}' => $this->getModelRelation(GuideModel::class),
            '{guide_family}' => $this->getModelRelation(GuideFamilyModel::class),
            '{guide_i18n}' => $this->getModelRelation(GuideI18nModel::class),
            '{guide_stand}' => $this->getModelRelation(GuideStandModel::class),
            '{stand}' => $this->getModelRelation(StandModel::class),
            '{default_culture}' => APP_DEFAULT_LOCALE,
            '{condition}' => $condition,
        ]);
    }

    /**
     * findBySlugWithGuides
     *
     *
     * @return null|Family
     */
    public function findBySlugWithGuides(string $slug, string $culture)
    {
        $where = Where::create();
        $sql = $this->getQueryFindBySlugWithGuides($where, $slug, $culture);

        $projection = $this->createProjection()
            ->setField('name', 'COALESCE(fi18n.name, dfi18n.name)', 'text')
            ->setField('title', 'COALESCE(fi18n.title, dfi18n.title)', 'text')
            ->setField('keywords', 'COALESCE(fi18n.keywords, dfi18n.keywords)', 'text[]')
            ->setField('description', 'COALESCE(fi18n.description, dfi18n.description)', 'text')
            ->setField('guides', 'g.guides', 'jsonb[]');

        $guide_projection = (new GuideModel())
            ->createProjection()
            ->unsetField('banner_image')
            ->unsetField('main_family_id')
            ->unsetField('created_at')
            ->unsetField('is_active')
            ->setField('name', 'COALESCE(gi18n.name, dgi18n.name)', 'text')
            ->setField(
                'resume',
                "COALESCE(gi18n.editorial_content->>'resume', dgi18n.editorial_content->>'resume')",
                'text',
            )
            ->setField('is_choice_guide', 's.stand_id IS NOT NULL', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('f'),
            '{guide_projection}' => $this->formatJsonBuildObjectFromProjection($guide_projection, 'g'),
        ]);

        return $this->query($sql, $where->getValues(), $projection)->current();
    }

    /**
     * findBySlugWithGuides
     *
     *
     * @return null|Family
     */
    public function findBySlugWithLimitedGuides(string $slug, int $guide_id, string $culture, int $limit = null)
    {
        $where = Where::create('g.guide_id != $*', [$guide_id]);
        $sql = $this->getQueryFindBySlugWithGuides($where, $slug, $culture);

        $projection = $this->createProjection()
            ->setField('name', 'COALESCE(fi18n.name, dfi18n.name)', 'text')
            ->setField('guides', 'g.guides', 'jsonb[]');
        if ($limit !== null) {
            $projection->setField('guides', sprintf('g.guides[1:%d]', $limit), 'jsonb[]');
        }

        $guide_projection = (new GuideModel())
            ->createProjection()
            ->unsetField('banner_image')
            ->unsetField('main_family_id')
            ->unsetField('created_at')
            ->unsetField('is_active')
            ->setField('name', 'COALESCE(gi18n.name, dgi18n.name)', 'text')
            ->setField('canonical_stand_slug', 's.slug', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('f'),
            '{guide_projection}' => $this->formatJsonBuildObjectFromProjection($guide_projection, 'g'),
        ]);

        return $this->query($sql, $where->getValues(), $projection)->current();
    }

    /**
     * findAllWithI18n
     *
     *
     */
    public function findAllActiveWithI18n(string $culture): CollectionIterator
    {
        $sql = <<<SQL
        WITH
          parameters(culture) AS (SELECT $*::bpchar)

        SELECT
          {projection}
        FROM
          {family} f
          CROSS JOIN parameters AS p
          JOIN {family_i18n} dfi18n ON (dfi18n.family_id = f.family_id AND dfi18n.supported_culture_id = '{default_culture}')
          LEFT JOIN {family_i18n} fi18n ON (fi18n.family_id = f.family_id AND fi18n.supported_culture_id = p.culture)
        WHERE
          EXISTS (
            SELECT family_id
            FROM
              {guide_family} gf
              JOIN {guide} g ON (g.guide_id = gf.guide_id AND g.is_active)
            WHERE gf.family_id = f.family_id
          )
        ORDER BY f.display_order ASC
        SQL;

        $projection = $this->createProjection()->setField('name', 'COALESCE(fi18n.name, dfi18n.name)', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('f'),
            '{family}' => $this->structure->getRelation(),
            '{family_i18n}' => $this->getModelRelation(FamilyI18nModel::class),
            '{guide_family}' => $this->getModelRelation(GuideFamilyModel::class),
            '{guide}' => $this->getModelRelation(GuideModel::class),
            '{default_culture}' => APP_DEFAULT_LOCALE,
        ]);

        return $this->query($sql, [$culture], $projection);
    }

    /**
     * formatJsonBuildObjectFromProjection
     *
     * Return a formatted string with fields from a projection like
     * json_build_object('field1', a.field1, 'field2', a.field2 … )
     *
     * @param string|null $alias
     */
    protected function formatJsonBuildObjectFromProjection(Projection $projection, string $alias = null): string
    {
        $fields = $projection->getFieldsWithTableAlias($alias);

        return sprintf(
            'json_build_object(%s)',
            implode(
                ', ',
                array_map(
                    fn($field_alias, $field_definition): string => sprintf(
                        "'%s', %s",
                        addcslashes($field_alias, '"\\'),
                        $field_definition,
                    ),
                    array_keys($fields),
                    $fields,
                ),
            ),
        );
    }
}

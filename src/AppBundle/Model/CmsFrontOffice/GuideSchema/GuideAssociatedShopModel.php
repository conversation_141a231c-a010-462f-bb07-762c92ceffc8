<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\BaseModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\AutoStructure\GuideAssociatedShop as GuideAssociatedShopStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ShopSchema\ShopI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ShopSchema\ShopModel;

/**
 * GuideAssociatedShopModel
 *
 * Model class for table guide_associated_shop.
 *
 * @see BaseModel
 */
class GuideAssociatedShopModel extends BaseModel implements GuideAssociationModelInterface
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new GuideAssociatedShopStructure();
        $this->flexible_entity_class = GuideAssociatedShop::class;
    }

    /**
     * getAssociationsForGuideId
     */
    public function getAssociationsForGuideId(int $guide_id, string $culture_id): CollectionIterator
    {
        $shop_model = $this->getSession()->getModel(ShopModel::class);

        $sql = <<<SQL
        SELECT
          {projection}
        FROM
          {shop} s
          JOIN {relation} r ON r.shop_id = s.shop_id
          LEFT JOIN {shop_i18n} si ON si.shop_id = s.shop_id AND si.supported_culture_id = $*
          LEFT JOIN {shop_i18n} si_default ON si_default.shop_id = s.shop_id AND si_default.supported_culture_id = $*
        WHERE
          {condition}
        SQL;
        $projection = $shop_model->createProjection()->setField('name', 'COALESCE(si.name, si_default.name)', 'text');

        $where = Where::create('r.guide_id = $*', [$guide_id]);
        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('s'),
            '{relation}' => $this->structure->getRelation(),
            '{shop}' => $shop_model->structure->getRelation(),
            '{shop_i18n}' => $this->getModelRelation(ShopI18nModel::class),
            '{condition}' => $where,
        ]);

        return $this->query($sql, [$culture_id, APP_DEFAULT_LOCALE, $guide_id], $projection);
    }
}

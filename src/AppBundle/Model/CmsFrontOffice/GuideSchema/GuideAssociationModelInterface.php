<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema;

use PommProject\ModelManager\Model\CollectionIterator;

/**
 * Interface GuideAssociationModelInterface
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema
 */
interface GuideAssociationModelInterface
{
    /**
     * getAssociationsForGuideId
     *
     * Returns a collection of element associated to the specified guide.
     */
    public function getAssociationsForGuideId(int $guide_id, string $culture_id): CollectionIterator;
}

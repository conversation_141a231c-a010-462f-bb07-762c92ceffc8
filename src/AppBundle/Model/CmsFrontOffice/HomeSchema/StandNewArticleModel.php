<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\HomeSchema;

use PommProject\Foundation\ConvertedResultIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\HomeSchema\AutoStructure\StandNewArticle as StandNewArticleStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\QueryHelperTrait;

/**
 * StandNewArticleModel
 *
 * Model class for table stand_new_article.
 *
 * @see Model
 */
class StandNewArticleModel extends Model
{
    use QueryHelperTrait;
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new StandNewArticleStructure();
        $this->flexible_entity_class = StandNewArticle::class;
    }

    public function fetchHomeNewArticles(string $culture, int $limit = 0): ConvertedResultIterator
    {
        $sql = <<<SQL
        WITH
          parameters AS (
            SELECT $*::bpchar AS culture
            ),
          article_from_stand_pool AS (
            SELECT
              s.stand_id AS stand_id,
              s.slug AS stand_slug,
              si18n.name AS stand_name,
              a.article_id AS article_id,
              a.created_at AS created_at
              FROM home.stand_new_article sna
                CROSS JOIN parameters param
                INNER JOIN taxonomy.stand s ON s.stand_id = sna.stand_id
                INNER JOIN taxonomy.stand_i18n si18n ON si18n.stand_id = s.stand_id
                INNER JOIN article.article a ON a.default_stand_id = s.stand_id
                INNER JOIN article.common_content cc ON cc.common_content_id = a.common_content_id
              WHERE si18n.supported_culture_id = param.culture
                AND a.unbasketable_reason is null
                AND (cc.display_mode NOT IN ('CONFIDENTIAL_PRICE') OR cc.display_mode IS NULL)
            ),
          article_sorted_by_creation_date AS (
            SELECT *, ROW_NUMBER() OVER (PARTITION BY stand_id ORDER BY created_at DESC) AS position_in_stand
              FROM article_from_stand_pool
            ),
          new_article_filtered AS (
            SELECT *
              FROM article_sorted_by_creation_date asbcd
              WHERE asbcd.position_in_stand = 1
              ORDER BY position_in_stand
              {limit}
            ),
          -- Load the media only from the list of filtered articles
          media AS ({media_join}),
          -- Load the highlight only from the list of filtered articles
          highlight AS ({highlight_join})
        SELECT
          ai18n.article_id AS article_id,
          ai18n.supported_culture_id AS supported_culture_id,
          ai18n.editorial_content AS editorial_content,
          b.name AS brand_name,
          cc.display_mode AS display_mode,
          CASE WHEN cc.review_score IS NOT NULL
                 THEN ROUND(cc.review_score, 1)
            END AS note,
          a.price AS price,
          a.name AS name,
          cci18n.editorial_content ->> 'short_description' AS cc_short_description,
          a.article_url AS article_url,
          media.media_uri AS image_url,
          media.meta AS media_meta,
          naf.stand_slug AS stand_slug,
          naf.stand_name AS stand_name,
          naf.created_at AS created_at,
          cci18n.name AS common_content_name,
          ci18n.name AS category_name,
          h.highlights::jsonb
          FROM new_article_filtered naf
            CROSS JOIN parameters p
            INNER JOIN article.article_i18n ai18n
              ON ai18n.article_id = naf.article_id AND ai18n.supported_culture_id = p.culture
            INNER JOIN article.article a ON ai18n.article_id = a.article_id
            INNER JOIN article.brand b ON b.brand_id = a.brand_id
            INNER JOIN article.common_content cc ON cc.common_content_id = a.common_content_id
            INNER JOIN article.common_content_i18n cci18n ON cci18n.common_content_id = a.common_content_id AND
                                                             cci18n.supported_culture_id = ai18n.supported_culture_id
            LEFT JOIN article.article_category ac ON a.article_id = ac.article_id
            LEFT JOIN taxonomy.category_i18n ci18n
              ON ac.category_id = ci18n.category_id AND ci18n.supported_culture_id = cci18n.supported_culture_id
            LEFT JOIN media ON media.article_id = naf.article_id
            LEFT JOIN highlight h ON h.article_id = a.article_id
          ORDER BY RANDOM()
        SQL;

        $sql = strtr($sql, [
            '{limit}' => $limit !== 0 ? sprintf('LIMIT %d', $limit) : '',
            '{media_join}' => $this->makeMediaJoinQuery('new_article_filtered'),
            '{highlight_join}' => $this->makeHighlightJoinQuery('new_article_filtered'),
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, [$culture]);
    }
}

<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\NavigationSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\NavigationSchema\AutoStructure\BurgerMenuSection as BurgerMenuSectionStructure;

/**
 * BurgerMenuSectionModel
 *
 * Model class for table burger_menu_section.
 *
 * @see Model
 */
class BurgerMenuSectionModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new BurgerMenuSectionStructure();
        $this->flexible_entity_class = BurgerMenuSection::class;
    }

    public function removeEmptySections(array $sections): array
    {
        return array_reduce(
            $sections,
            function ($kept_sections, array $section) {
                $section['entries'] = $this->removeEmptyEntries($section['entries']);
                $section['groups'] = $this->removeEmptyGroups($section['groups']);

                if ((is_countable($section['entries']) ? count($section['entries']) : 0) > 0 || $section['groups'] !== []) {
                    $kept_sections[] = $section;
                }

                return $kept_sections;
            },
            [],
        );
    }

    private function removeEmptyEntries(array $entries): array
    {
        return array_reduce(
            $entries,
            function ($kept_entries, array $entry) {
                $entry['sections'] = $this->removeEmptySections($entry['sections']);

                if (
                    $entry['is_active'] &&
                    (($entry['url'] !== null && $entry['url'] !== '') || $entry['sections'] !== [])
                ) {
                    $kept_entries[] = $entry;
                }

                return $kept_entries;
            },
            [],
        );
    }

    private function removeEmptyGroups(array $groups): array
    {
        return array_reduce(
            $groups,
            function ($kept_groups, array $group) {
                $group['entries'] = $this->removeEmptyEntries($group['entries']);

                if ($group['entries'] !== []) {
                    $kept_groups[] = $group;
                }

                return $kept_groups;
            },
            [],
        );
    }
}

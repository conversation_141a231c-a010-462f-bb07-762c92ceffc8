<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\OptInSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\Projection;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use PommProject\Foundation\Where;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\OptInSchema\AutoStructure\Consent as ConsentStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\OptInSchema\Consent;

/**
 * ConsentModel
 *
 * Model class for table consent.
 *
 * @see Model
 */
class ConsentModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new ConsentStructure();
        $this->flexible_entity_class = Consent::class;
    }
}

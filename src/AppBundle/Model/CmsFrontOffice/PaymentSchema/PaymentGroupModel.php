<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\AutoStructure\PaymentGroup as PaymentGroupStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentGroup;

/**
 * PaymentGroupModel
 *
 * Model class for table payment_group.
 *
 * @see Model
 */
class PaymentGroupModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new PaymentGroupStructure();
        $this->flexible_entity_class = PaymentGroup::class;
    }
}

<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema;

use PommProject\Foundation\ConvertedResultIterator;
use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use PommProject\Foundation\Where;

use PommProject\ModelManager\Model\Projection;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketOrderLine;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketOrderLineModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\AutoStructure\PaymentMethod as PaymentMethodStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ModelRelationTrait;

/**
 * PaymentMethodModel
 *
 * Model class for table payment_method.
 *
 * @see Model
 */
class PaymentMethodModel extends Model
{
    use WriteQueries;
    use ModelRelationTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new PaymentMethodStructure();
        $this->flexible_entity_class = PaymentMethod::class;
    }

    /**
     * fetchWithList
     *
     * Retrieve info for available payments.
     * Payments which are in $available_payments but not in DB are not returned.
     *
     * @return CollectionIterator|null
     */
    public function fetchWithList(array $available_payments, string $locale = 'FR')
    {
        if ($available_payments === []) {
            return null;
        }

        $sql = <<<SQL
        WITH parameters (culture) AS (
          SELECT $*::bpchar
        )
        SELECT {projection}
        FROM
            (VALUES {values} ) as p (payment_method_id, total_cost, down_payment_amount)
        CROSS JOIN parameters
        INNER JOIN
            {relation} as pm USING (payment_method_id)
        INNER JOIN
            payment.payment_group as pg USING (payment_group_id)
        ORDER BY
            payment_group_display_order, display_order;
        SQL;
        $projection = $this->createProjection()
            ->unsetField('description_i18n')
            ->unsetField('label_i18n')
            ->setField('description', 'description_i18n->(parameters.culture)', 'text')
            ->setField('label', 'label_i18n->(parameters.culture)', 'text')
            ->setField('total_cost', 'p.total_cost', 'float')
            ->setField('down_payment_amount', 'p.down_payment_amount', 'float')
            ->setField('payment_group_name', 'pg.name_i18n->(parameters.culture)', 'text')
            ->setField('payment_group_display_order', 'pg.display_order', 'text')
            ->setField('payment_group_logos', 'pg.logos', 'jsonb');

        $sql_values = [];
        $values = [$locale];

        $already_collected = [];
        foreach ($available_payments as $one_payment) {
            // prevent duplicates
            if (in_array($one_payment['payment_method_id'], $already_collected)) {
                continue;
            }
            $already_collected[] = $one_payment['payment_method_id'];

            $sql_values[] = '($*::integer, $*::float, $*::float)';
            $values[] = $one_payment['payment_method_id'];
            if (isset($one_payment['total_cost'])) {
                // Data from RPC call
                $values[] = $one_payment['total_cost'];
            } else {
                // Data from DB (BasketOrderLine)
                $values[] = $one_payment['price'] * $one_payment['quantity'];
            }
            // Add down payment cost (only computed in Bridge if order is in shipment quotation)
            $values[] = $one_payment['down_payment_amount'] ?? 0;
        }

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('pm'),
            '{values}' => implode(', ', $sql_values),
            '{relation}' => $this->structure->getRelation(),
        ]);

        return $this->query($sql, $values, $projection);
    }

    /**
     * fetchForBasketOrder
     *
     * Retrieve all payments added to order (basket_order_line)
     *
     *
     * @return null|CollectionIterator
     */
    public function fetchForBasketOrder(int $customer_id, string $locale = APP_DEFAULT_COUNTRY)
    {
        $sql = <<<SQL
        WITH parameters (culture) AS (
          SELECT $*::bpchar
        )
        SELECT
          {projection}
        FROM {basket_order_line} bol
          CROSS JOIN parameters
          INNER JOIN {relation} pm
            ON bol.id = pm.payment_method_id
          INNER JOIN {payment_group} pg
            ON pm.payment_group_id = pg.payment_group_id
        WHERE {conditions};
        SQL;
        $projection = $this->createProjection()
            ->unsetField('description_i18n')
            ->setField('name', 'name_i18n->(parameters.culture)', 'text')
            ->setField('customer_id', 'bol.customer_id', 'int')
            ->setField('price', 'bol.price', 'float')
            ->setField('extra_data', 'bol.extra_data', 'jsonb');

        $conditions = Where::create('customer_id = $* and type = $*::regclass', [
            $customer_id,
            BasketOrderLine::TYPE_PAYMENT,
        ]);

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('pm'),
            '{relation}' => $this->structure->getRelation(),
            '{basket_order_line}' => $this->getSession()
                ->getModel(BasketOrderLineModel::class)
                ->getStructure()
                ->getRelation(),
            '{payment_group}' => $this->getSession()
                ->getModel(PaymentGroupModel::class)
                ->getStructure()
                ->getRelation(),
            '{conditions}' => $conditions,
        ]);

        return $this->query($sql, array_merge([$locale], $conditions->getValues()), $projection);
    }

    /**
     * fetchForPaymentMethodList
     *
     * Retrieve all payments info with list
     *
     *
     * @return null|ConvertedResultIterator
     */
    public function fetchForPaymentMethodList(array $payments, string $locale)
    {
        if ($payments === []) {
            return null;
        }

        $values = [];
        $payments_list = implode(
            ',',
            array_map(function ($payment) use (&$values): string {
                $values[] = $payment['extra_data'] ?? [];

                return sprintf(
                    '(%d::integer, %s::float, $*::jsonb)',
                    $payment['payment_method_id'] ?? null,
                    $payment['price'] ?? 0,
                );
            }, $payments),
        );

        $sql = <<<SQL
        SELECT
          {projection}
        FROM
          (VALUES {payments}) AS p(payment_method_id, price, extra_data)
          INNER JOIN {payment_method} pm ON p.payment_method_id = pm.payment_method_id
          INNER JOIN {payment_group} pg ON pm.payment_group_id = pg.payment_group_id
        SQL;

        $projection = new Projection('');
        $projection
            ->setField('payment_method_id', 'p.payment_method_id', 'integer')
            ->setField(
                'name',
                sprintf("COALESCE(pg.name_i18n->'%s', pg.name_i18n->'%s')", $locale, APP_DEFAULT_COUNTRY),
                'text',
            )
            ->setField(
                'description',
                sprintf("COALESCE(pm.description_i18n->'%s', pm.description_i18n->'%s')", $locale, APP_DEFAULT_COUNTRY),
                'text',
            )
            ->setField('price', 'p.price', 'float')
            ->setField('extra_data', 'p.extra_data', 'jsonb');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('pm'),
            '{payment_method}' => $this->structure->getRelation(),
            '{payment_group}' => $this->getModelRelation(PaymentGroupModel::class),
            '{payments}' => $payments_list,
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, $values, $projection);
    }
}

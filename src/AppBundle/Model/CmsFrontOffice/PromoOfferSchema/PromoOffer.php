<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema;

use PommProject\ModelManager\Model\FlexibleEntity;

/**
 * PromoOffer
 *
 * Flexible entity for relation
 * promo_offer.promo_offer
 *
 * @see FlexibleEntity
 */
class PromoOffer extends FlexibleEntity implements PromoCodeInterface
{
    /**
     * getPromoId
     *
     * Return the id of the entity
     */
    public function getPromoId(): int
    {
        return $this->get('promo_offer_id');
    }

    /**
     * getPromoCodeRules
     */
    public function getPromoCodeRules(): array
    {
        $promo_code_definition = $this->get('definition')['promo_code'] ?? [];

        return $promo_code_definition['rules'] ?? [];
    }

    /**
     * getStringPromoCode
     */
    public function getStringPromoCode(): string
    {
        $promo_code_definition = $this->get('definition')['promo_code'] ?? [];

        return $promo_code_definition['code'] ?? '';
    }

    public function allArticlesAreConcerned(): bool
    {
        return $this->get('selection_id') === null;
    }

    /**
     * getConcernedArticleIds
     */
    public function getConcernedArticleIds(): array
    {
        return $this->get('computed_article_ids') ?? [];
    }
}

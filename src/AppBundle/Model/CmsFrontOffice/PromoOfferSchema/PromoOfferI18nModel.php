<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema;

use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use SonVideo\Cms\FrontOffice\AppBundle\Model\BaseModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleSelectionModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema\AutoStructure\PromoOfferI18n as PromoOfferI18nStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema\PromoOfferI18n;

/**
 * PromoOfferI18nModel
 *
 * Model class for table promo_offer_i18n.
 *
 * @see BaseModel
 */
class PromoOfferI18nModel extends BaseModel
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new PromoOfferI18nStructure();
        $this->flexible_entity_class = PromoOfferI18n::class;
    }

    /**
     * fetchOneCompleteBySlug
     *
     *
     */
    public function fetchOneCompleteBySlug(string $slug, string $culture)
    {
        $sql = <<<SQL
        WITH parameters(slug, culture) AS (
          SELECT $*::text, $*::text
        )
        SELECT
          {projection}
        FROM
          parameters p
          INNER JOIN {promo_offer} po ON po.slug = p.slug
          INNER JOIN {promo_offer_i18n} poi18nd ON poi18nd.slug = p.slug AND poi18nd.supported_culture_id = $*
          LEFT JOIN {promo_offer_i18n} poi18n ON poi18n.slug = p.slug AND poi18n.supported_culture_id = p.culture
          LEFT JOIN {article_selection} s ON s.selection_id = po.selection_id
        SQL;
        $projection = $this->createProjection()
            ->setField('title', 'coalesce(poi18n.title, poi18nd.title)', 'text')
            ->setField('editorial_content', 'coalesce(poi18n.editorial_content, poi18nd.editorial_content)', 'text')
            ->setField('meta_title', 'coalesce(poi18n.meta_title, poi18nd.meta_title)', 'text')
            ->setField('meta_description', 'coalesce(poi18n.meta_description, poi18nd.meta_description)', 'text')
            ->setField('meta_keywords', 'coalesce(poi18n.meta_keywords, poi18nd.meta_keywords)', 'text[]')
            ->setField('promo_offer_id', 'po.promo_offer_id', 'int4')
            ->setField('is_active', 'po.lifetime @> now() AND po.deactivated_reason IS NULL', 'boolean')
            ->setField('search_context', 's.search_context', 'jsonb')
            ->setField('has_static_selection', 's.tag_path is not null', 'boolean')
            ->setField('image_banner', 'po.image_banner', 'text');

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('poi18nd'),
            '{promo_offer}' => $this->getModelRelation(PromoOfferModel::class),
            '{promo_offer_i18n}' => $this->getStructure()->getRelation(),
            '{article_selection}' => $this->getModelRelation(ArticleSelectionModel::class),
        ]);

        return $this->query($sql, [$slug, $culture, APP_DEFAULT_LOCALE], $projection)->current();
    }
}

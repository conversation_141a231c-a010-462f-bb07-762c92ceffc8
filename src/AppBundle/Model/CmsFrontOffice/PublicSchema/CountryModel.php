<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PublicSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PublicSchema\AutoStructure\Country as CountryStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PublicSchema\Country;

/**
 * CountryModel
 *
 * Model class for table country.
 *
 * @see Model
 */
class CountryModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new CountryStructure();
        $this->flexible_entity_class = Country::class;
    }
    /**
     * findAllOrderByContryName
     *
     * @return  array|null
     */
    public function findAllOrderByContryName()
    {
        $sql = <<<SQL
        SELECT
          c.country_id,
          c.name
        FROM country c
          order by 2
        SQL;
        return $this->query($sql)->extract();
    }
}

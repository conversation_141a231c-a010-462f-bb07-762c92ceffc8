<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2015 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\SearchSchema;

use PommProject\Foundation\Where;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\ReadQueries;
use PommProject\Foundation\ConvertedResultIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMediaI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMediaModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ProductModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\SearchSchema\AutoStructure\Autocomplete as AutocompleteStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ModelRelationTrait;

/**
 * AutocompleteModel
 *
 * Model class for materialized view autocomplete.
 *
 * @see Model
 */
class AutocompleteModel extends Model
{
    use ReadQueries;
    use ModelRelationTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     */
    public function __construct()
    {
        $this->structure = new AutocompleteStructure();
        $this->flexible_entity_class = Autocomplete::class;
    }

    /**
     * Returns proposals for words beginning by the given argument.
     *
     * @param string|null $item_type (optional)
     *
     */
    public function findLike(
        string $word,
        string $culture,
        int $limit = 0,
        ?string $item_type = null
    ): ConvertedResultIterator {
        $sql = <<<SQL
        WITH
          params (term, supported_culture_id) AS (SELECT regexp_replace(lower(unaccent($*::text)), '[^a-zA-Z0-9_\s]+', '', 'g'), $*::char(2)),
          found_item AS (
            SELECT
              a.item_id,
              a.item_type,
              a.item_slug,
              a.title
            FROM
              {autocomplete} a
              CROSS JOIN params p
            WHERE
              regexp_replace(lower(unaccent(a.title)), '[^a-zA-Z0-9_\s]+', '', 'g') ~* ('^' || p.term || '|\s' || p.term)
              AND a.supported_culture_id = p.supported_culture_id
              AND a.is_active
              AND {conditions}
            ),
          weighted_item AS (
            SELECT
              fi.item_id,
              fi.item_type,
              fi.item_slug,
              fi.title,
              similarity(regexp_replace(lower(unaccent(fi.title)), '[^a-zA-Z0-9_\s]+', '', 'g'), p.term) AS score,
              CASE
                WHEN fi.item_type != 'article' THEN true
                ELSE a.unbasketable_reason IS NULL AND prod.informational_stock > 0
              END AS is_available
            FROM
              found_item fi
              CROSS JOIN params p
              LEFT JOIN {article} a    ON fi.item_id = a.article_id AND fi.item_type = 'article'
              LEFT JOIN {product} prod ON a.sku = prod.sku
          ),
          score_avg AS (SELECT round(avg(score)::numeric, 2) AS average FROM weighted_item)
        SELECT
          wi.item_id,
          wi.item_type,
          wi.item_slug,
          wi.title,
          wi.score
        FROM
          weighted_item wi
          CROSS JOIN score_avg sa
        ORDER BY is_available desc, wi.score desc, wi.title
        {limit}
        SQL;
        $parameters = [$word, $culture];

        $conditions = $item_type !== null ? new Where('item_type = $*', [$item_type]) : null;
        if ($conditions instanceof Where) {
            $parameters = array_merge($parameters, $conditions->getValues());
        }

        $sql = strtr($sql, [
            '{autocomplete}' => $this->getStructure()->getRelation(),
            '{conditions}' => $conditions ?? 'TRUE',
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{limit}' => $limit !== 0 ? sprintf('LIMIT %d', $limit) : '',
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, $parameters);
    }

    /**
     * findProductsLike
     *
     * Return proposals for words beginning by the given argument.
     * Only articles are returned, but unbasketable too and with additional data.
     */
    public function findProductsLike(string $word, string $culture, int $limit = 0): ConvertedResultIterator
    {
        $sql = <<<SQL
        WITH
          params (term, supported_culture_id) AS (SELECT regexp_replace(lower(unaccent($*::text)), '[^a-zA-Z0-9_\s]+', '', 'g'), $*::char(2)),
          found_item AS (
            SELECT
              a.item_id,
              a.item_slug,
              a.title,
              a.is_active
            FROM
              {autocomplete} a
              CROSS JOIN params p
            WHERE
              regexp_replace(lower(unaccent(a.title)), '[^a-zA-Z0-9_\s]+', '', 'g') ~* ('^' || p.term || '|\s' || p.term)
              AND a.supported_culture_id = p.supported_culture_id
              AND a.item_type = 'article'
            ),
          weighted_item AS (
            SELECT
              fi.item_id,
              fi.item_slug,
              fi.title,
              is_active,
              similarity(regexp_replace(lower(unaccent(fi.title)), '[^a-zA-Z0-9_\s]+', '', 'g'), p.term) AS score
            FROM
              found_item fi
              CROSS JOIN params p
          )
        SELECT
          wi.item_id,
          wi.item_slug,
          wi.title,
          wi.score,
          a.common_content_id,
          am.media_variation
        FROM
          weighted_item wi
          CROSS JOIN params p
          JOIN {article} a ON a.article_id = wi.item_id
          LEFT JOIN {article_media_i18n} ami18n ON (
            ami18n.article_id =  wi.item_id
            AND ami18n.supported_culture_id = p.supported_culture_id
            AND ami18n.display_order = 1
            AND ami18n.type = 'IMAGE'::article.article_media_type
          )
          LEFT JOIN {article_media} am ON am.media_id = ami18n.media_id
        ORDER BY wi.is_active desc, wi.score desc, wi.title
        {limit}
        SQL;
        $sql = strtr($sql, [
            '{autocomplete}' => $this->getStructure()->getRelation(),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{limit}' => $limit !== 0 ? sprintf('LIMIT %d', $limit) : '',
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, [$word, $culture]);
    }
}

<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2015 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice;

use PommProject\Foundation\Converter\ConverterHolder;
use PommProject\Foundation\Converter\PgHstore;
use PommProject\Foundation\Converter\PgLtree;
use PommProject\Foundation\Session\Session;
use PommProject\ModelManager\Converter\PgEntity;
use PommProject\ModelManager\Model\RowStructure;
use PommProject\ModelManager\SessionBuilder as ModelManagerSessionBuilder;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Basketable;
use SonVideo\Cms\FrontOffice\AppBundle\Model\Converter\PgTimestampWithTimezoneConverter;

/**
 * Description of SessionBuilder
 *
 * <AUTHOR>
 */
class SessionBuilder extends ModelManagerSessionBuilder
{
    protected function initializeConverterHolder(ConverterHolder $converter_holder): self
    {
        parent::initializeConverterHolder($converter_holder);
        $converter_holder
            ->addTypeToConverter('String', 'article.product_unbasketable_reason')
            ->addTypeToConverter('String', 'article.related_common_content_type')
            ->addTypeToConverter('String', 'article.display_mode')
            ->addTypeToConverter('String', 'product_unbasketable_reason')
            ->addTypeToConverter('String', 'article.availability_delay')
            ->addTypeToConverter('String', 'availability_delay')
            ->addTypeToConverter('String', 'article.declination_type')
            ->addTypeToConverter('String', 'display_mode')
            ->addTypeToConverter('String', 'public.sku')
            ->addTypeToConverter('String', 'public.citext')
            ->addTypeToConverter('String', 'citext')
            ->addTypeToConverter('String', 'public.trigram')
            ->addTypeToConverter('String', 'public.code')
            ->addTypeToConverter('String', 'lquery')
            ->addTypeToConverter('String', 'customer_title')
            ->addTypeToConverter('String', 'customer.customer_title')
            ->addTypeToConverter('String', 'customer.basket_order_line_type')
            ->addTypeToConverter('String', 'navigation.main_menu_node_type')
            ->addTypeToConverter('String', 'customer_order.message_sender')
            ->addTypeToConverter('String', 'customer_order.delivery_note_status')
            ->addTypeToConverter('String', 'customer_order.return_note_status')
            ->addTypeToConverter('String', 'customer_social.installation_type')
            ->addTypeToConverter('String', 'customer_order.customer_order_origin')
            ->addTypeToConverter('String', 'customer_order.customer_order_checkout_version')
            ->addTypeToConverter('String', 'promo_offer.deactivated_reason')
            ->addTypeToConverter('String', 'customer.quote_type')
            ->addTypeToConverter('String', 'customer.quote_status_type')
            ->addTypeToConverter('JSON', 'article.search_context')
            ->addTypeToConverter('String', 'timetz')
            ->addTypeToConverter('JSON', 'promo_offer.promo_offer_definition')
            ->registerConverter('HSTore', new PgHstore(), ['public.hstore', 'hstore'])
            ->registerConverter('Ltree', new PgLtree(), ['public.ltree', 'ltree'])
            ->registerConverter(
                'Basketable',
                new PgEntity(
                    Basketable::class,
                    (new RowStructure())->setDefinition([
                        'id' => 'int4',
                        'type' => 'regclass',
                        'url' => 'text',
                        'description' => 'text',
                    ]),
                ),
                ['customer.basketable', Basketable::class],
            )
            ->registerConverter('DateTz', new PgTimestampWithTimezoneConverter(), ['datetz']);

        return $this;
    }

    protected function postConfigure(Session $session): self
    {
        parent::postConfigure($session);
        $session->getListener('query')->attachAction(function ($name, array $data, $session): void {
            if ($session->hasLogger()) {
                if ($name == 'query:pre') {
                    // log query...
                    $session
                        ->getLogger()
                        ->debug(sprintf('[%s] -- %s', $data['session_stamp'], $data['sql']), ['_channel' => 'pomm']);

                    // ... on separated line from the parameters
                    $data['parameters']['_channel'] = 'pomm';
                    $session->getLogger()->debug('Parameters', $data['parameters']);
                } elseif ($name == 'query:post') {
                    $session
                        ->getLogger()
                        ->debug(sprintf('Result count : %d - Time : %s ms', $data['result_count'], $data['time_ms']), [
                            '_channel' => 'pomm',
                        ]);
                }
            }
        });

        return $this;
    }
}

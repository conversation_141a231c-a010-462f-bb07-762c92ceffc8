<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ShopSchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * ShopI18n
 *
 * Structure class for relation shop.shop_i18n.
 * decline shops in several supported cultures
 *
 * shop_id:
 * shop identifier
 * supported_culture_id:
 * unique identifier for culture
 * name:
 * Stand name in supported culture
 * title:
 * Alternative title to use for the article selection
 * editorial_content:
 * Shop presentation
 * keywords:
 * Keywords for SEO
 * description:
 * Meta-description used for SEO
 *
 * @see RowStructure
 */
class ShopI18n extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('shop.shop_i18n')
            ->setPrimaryKey(['supported_culture_id', 'shop_id'])
            ->addField('shop_id', 'int4')
            ->addField('supported_culture_id', 'bpchar')
            ->addField('name', 'text')
            ->addField('title', 'text')
            ->addField('editorial_content', 'jsonb')
            ->addField('keywords', 'text[]')
            ->addField('description', 'text');
    }
}

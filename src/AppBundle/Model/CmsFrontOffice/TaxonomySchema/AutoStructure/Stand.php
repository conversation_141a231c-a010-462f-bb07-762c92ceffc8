<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * Stand
 *
 * Structure class for relation taxonomy.stand.
 * Default sorting mode
 *
 * slug:
 * url friendly representation of the stand
 * logo_uri:
 * Url of the main illustration. Useful for stands on level 2.
 * aliases:
 * Aliases for others entities (like trackers)
 * is_linkable:
 * Determine if links to the stand must be displayed or not.
 * use_eav_facets:
 * the field which condition the use of the eav facets instead of the original
 * attributes
 * sort_by:
 * Default sorting mode
 *
 * @see RowStructure
 */
class Stand extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('taxonomy.stand')
            ->setPrimaryKey(['stand_id'])
            ->addField('stand_id', 'int4')
            ->addField('tag_path', 'public.ltree')
            ->addField('slug', 'text')
            ->addField('logo_uri', 'public.citext')
            ->addField('aliases', 'public.hstore')
            ->addField('is_linkable', 'bool')
            ->addField('use_eav_facets', 'bool')
            ->addField('sort_by', 'int4');
    }
}

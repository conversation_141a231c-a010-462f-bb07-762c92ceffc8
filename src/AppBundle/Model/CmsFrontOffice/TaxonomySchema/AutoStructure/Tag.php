<?php
/**
 * This file has been automatically generated by <PERSON><PERSON>'s generator.
 * You MIGHT NOT edit this file as your changes will be lost at next
 * generation.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\AutoStructure;

use PommProject\ModelManager\Model\RowStructure;

/**
 * Tag
 *
 * Structure class for relation taxonomy.tag.
 * Define terms to caracterize elements
 *
 *
 *
 * @see RowStructure
 */
class Tag extends RowStructure
{
    /**
     * __construct
     *
     * Structure definition.
     *
     * @access public
     */
    public function __construct()
    {
        $this->setRelation('taxonomy.tag')
            ->setPrimaryKey(['path'])
            ->addField('path', 'public.ltree')
            ->addField('is_searchable', 'bool');
    }
}

<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2015 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema;

use PommProject\Foundation\ConvertedResultIterator;
use PommProject\Foundation\Where;
use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\Projection;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

use SonVideo\Cms\FrontOffice\AppBundle\Model\BaseModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMediaI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMediaModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\BrandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\CommonContentI18nModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\CommonContentModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ProductModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\AutoStructure\Category as CategoryStructure;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleCategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\PriceTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\QueryHelperTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\SlugQueryTrait;

/**
 * CategoryModel
 *
 * Model class for table category.
 *
 * @see Model
 */
class CategoryModel extends BaseModel
{
    use WriteQueries;
    use SlugQueryTrait;
    use PriceTrait;
    use QueryHelperTrait;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new CategoryStructure();
        $this->flexible_entity_class = Category::class;
    }

    /**
     * findByBrandIdAndCulture
     *
     * Find all categories for a given brand and culture
     */
    public function findByBrandIdAndCulture(int $brand_id, string $culture): ConvertedResultIterator
    {
        $sql = <<<SQL
        WITH parameters as (
          SELECT $*::text as culture, $*::int as brand_id
        ),
        brand_article AS (
          SELECT
            a.article_id
          FROM {article} a
            CROSS JOIN parameters
          WHERE
            a.brand_id = parameters.brand_id
            AND a.unbasketable_reason IS NULL
            AND a.article_url IS NOT NULL
            AND a.article_url::text <> ''
        ),
        highlight AS ({highlight_query}),
        promo_code_contents AS ({make_promo_code_query}),
        second_life_offer AS (
          SELECT
            a.article_id,
            COUNT(a2.article_id) as count,
            MIN(a2.price->>'selling_price') as minimum_price
          FROM
            article.article a
            INNER JOIN article.common_content cc ON cc.common_content_id = a.common_content_id
            INNER JOIN article.article a2 ON a.common_content_id = a2.common_content_id
            INNER JOIN article.article_tag a2t ON a2t.article_id = a2.article_id
          WHERE
            a2.destock IS NOT NULL
            AND a2.unbasketable_reason IS NULL
            AND (
              cc.declination_type = 'COLOR'
              OR a2t.tag_path = ('article.destock.' || a.article_id)::public.ltree
            )
          GROUP BY a.article_id
        ),
        article_with_tags as (
          SELECT
            aa.article_id                                AS article_id,
            aa.name                                      AS name,
            aa.sku                                       AS sku,
            aa.article_url                               AS article_url,
            ab.name                                      AS brand_name,
            aa.unbasketable_reason                       AS unbasketable_reason,
            aai.editorial_content                        AS editorial_content,
            {image_url}                                  AS image_url,
            coalesce(cc.display_mode :: TEXT, 'DEFAULT') AS display_mode,
            pdt.estimated_delivery_time                  AS estimated_delivery_time,
            {price}                                      AS price,
            h.highlights                                 AS highlights,
            cci.name                                     AS common_content_name,
            pcc.promo_short_description                  AS promo_codes_description,
            pdt.has_an_ongoing_supplier_order            AS has_an_ongoing_supplier_order,
            aa.price->>'reference_price' AS reference_price,
            CASE WHEN slo.article_id IS NULL THEN NULL
              ELSE jsonb_build_object('count', slo.count, 'minimum_price', slo.minimum_price) END AS second_life
          FROM brand_article ba
            CROSS JOIN parameters as p
            INNER JOIN {article} aa ON aa.article_id = ba.article_id
            JOIN {article_i18n} aai
              ON aa.article_id = aai.article_id
              AND aai.supported_culture_id = p.culture
            INNER JOIN {product} pdt USING (sku)
            INNER JOIN {brand} ab
              ON aa.brand_id = ab.brand_id
            JOIN {article_media_i18n} ami18n
              ON aai.article_id = ami18n.article_id
              AND aai.supported_culture_id = ami18n.supported_culture_id
              AND ami18n.display_order = 1
              AND ami18n.type = 'IMAGE'::article.article_media_type
            LEFT JOIN {article_media} am
              ON ami18n.media_id = am.media_id
            JOIN {common_content} cc
              ON aa.common_content_id = cc.common_content_id
            LEFT JOIN {common_content_i18n} cci
              ON cc.common_content_id = cci.common_content_id
              AND cci.supported_culture_id = p.culture
            LEFT JOIN highlight h ON h.article_id = ba.article_id
            LEFT JOIN promo_code_contents pcc ON pcc.article_id = aa.article_id
            LEFT JOIN second_life_offer slo ON slo.article_id = aa.article_id
          ORDER BY {order_by_selling_price}
        )
        SELECT
          {projection}
        FROM
          {category} tc
          CROSS JOIN parameters as p
          JOIN {article_category} aac
            ON tc.category_id = aac.category_id
          JOIN article_with_tags awt
            ON aac.article_id = awt.article_id
          JOIN {category_i18n} tci
            ON tc.category_id = tci.category_id
            AND tci.supported_culture_id = p.culture
        GROUP BY
          tc.category_id, tci.name, tc.slug
        ORDER BY tci.name
        SQL;
        $projection = new Projection('');
        $projection
            ->setField('category_id', 'tc.category_id', 'int')
            ->setField('name', 'tci.name', 'text')
            ->setField('slug', 'tc.slug', 'text')
            ->setField('min_price', 'min((awt.price ->> \'selling_price\')::DECIMAL)', 'decimal')
            ->setField(
                'articles',
                sprintf('jsonb_agg(awt order by %s)', static::getOrderBySellingPrice('awt', 'awt')),
                'jsonb',
            )
            ->setField('nb_articles', 'count(awt.article_id)', 'jsonb');
        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('tci'),
            '{category}' => $this->structure->getRelation(),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_i18n}' => $this->getModelRelation(ArticleI18nModel::class),
            '{product}' => $this->getModelRelation(ProductModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{category_i18n}' => $this->getModelRelation(CategoryI18nModel::class),
            '{article_media_i18n}' => $this->getModelRelation(ArticleMediaI18nModel::class),
            '{article_media}' => $this->getModelRelation(ArticleMediaModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_i18n}' => $this->getModelRelation(CommonContentI18nModel::class),
            '{price}' => static::getPriceIfNoHidden('', 'aa'),
            '{order_by_selling_price}' => static::getOrderBySellingPrice('aa', 'cc'),
            '{image_url}' => ArticleMediaModel::getSqlForLargestUsingAlias('am'),
            '{highlight_query}' => $this->makeHighlightJoinQuery('brand_article'),
            '{make_promo_code_query}' => $this->makePromoCodeQuery('brand_article'),
        ]);

        return $this->getSession()
            ->getQueryManager()
            ->query($sql, [$culture, $brand_id], $projection);
    }

    /**
     * findSimpleByBrandIdAndCulture
     *
     * Find all categories for a given brand and culture, with simple projection: category_id, name and slug.
     */
    public function findSimpleByBrandIdAndCulture(int $brand_id, string $supported_culture_id): CollectionIterator
    {
        $sql = <<<SQL
        SELECT
          {projection}
        FROM
          {category} tc
          JOIN {article_category} aac
            ON tc.category_id = aac.category_id
          JOIN {article} a
            ON aac.article_id = a.article_id
          LEFT JOIN {category_i18n} tci
            ON tc.category_id = tci.category_id AND tci.supported_culture_id = $*
          JOIN {category_i18n} tci_default
            ON tc.category_id = tci_default.category_id AND tci_default.supported_culture_id = $*
        WHERE
          {condition}
        GROUP BY
          tc.category_id, tc.slug, tci.name, tci_default.name
        ORDER BY tci.name
        SQL;
        $where = Where::create('a.brand_id = $*', [$brand_id]);
        $projection = new Projection(Category::class);
        $projection
            ->setField('category_id', 'tc.category_id', 'int')
            ->setField('name', 'COALESCE(tci.name, tci_default.name)', 'text')
            ->setField('slug', 'tc.slug', 'text');
        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias(),
            '{category}' => $this->structure->getRelation(),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{category_i18n}' => $this->getModelRelation(CategoryI18nModel::class),
            '{condition}' => $where,
        ]);

        return $this->query(
            $sql,
            array_merge([$supported_culture_id, APP_DEFAULT_LOCALE], $where->getValues()),
            $projection,
        );
    }
}

imports:
    - { resource: 'validator/services.yml' }

services:
    ###############
    ##  Vendors  ##
    ###############

    Ramsey\Uuid\UuidFactory: ~

    ###############
    ##  Manager  ##
    ###############

    app.router_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\RouteManager
        arguments: ['@router']
        calls:
            - [setBaseUrl, ['%base_url%']]

    app.manager.basket_order_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder\BasketOrderManager
        arguments:
            - '@pomm'
            - '@app.factory.order_context_factory'
            - '@app.manager.customer.shopping_cart'
            - '@app.manager.customer.shopping_cart_catalog_manager'
            - '@app.manager.customer.shopping_cart_magazine_manager'

    app.manager.basket_order.payment_fetcher:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder\PaymentFetcher
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@sonvideo.synapps.rpc_client'
            - '@app.manager.basket_order_manager'
            - '@app.factory.customer_order_context_factory'
            - '@SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\PaymentClient'
            - '@SonVideo\Cms\FrontOffice\Application\Client\PaymentV2Client'
            - '@SonVideo\Cms\FrontOffice\Application\Manager\Payment\PaymentV2EligibilityContextBuilder'
            - '@logger'
            - '@SonVideo\Cms\FrontOffice\Application\Manager\Feature\FeatureStatus'

    app.manager.basket_order.svd_gift_card_payment_handler:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder\SvdGiftCardPaymentHandler
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@sonvideo.synapps.rpc_client'
            - '@app.manager.basket_order_manager'

    app.manager.basket_order.payment_handler:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder\PaymentHandler
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@app.manager.basket_order_manager'
            - '@request_stack'
            - '@sonvideo.synapps.rpc_client'
            - '@SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\PaymentClient'
            - '@logger'
            - '@SonVideo\Cms\FrontOffice\Application\Manager\Feature\FeatureStatus'

    SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder\ShipmentMethodFetcher:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder\ShipmentMethodFetcher
        arguments:
            - '@pomm'
            - '@sonvideo.synapps.rpc_client'
            - '@app.manager.basket_order_manager'
            - '@logger'

    app.manager.customer_order_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\CustomerOrderManager
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@sonvideo.synapps.rpc_client'
            - '@SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\PaymentClient'
            - '@SonVideo\Cms\FrontOffice\Application\Client\PaymentV2ClientInterface'
            - '@logger'

    app.manager.article_unpublished_article_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\UnpublishArticleManager
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@logger'
            - '@sonvideo.synapps.rpc_client'
    app.manager.customer_order_message_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\CustomerOrderMessageManager
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@sonvideo.synapps.rpc_client'

    app.manager.account.email_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Account\EmailManager
        arguments:
            $rpc_client: '@sonvideo.synapps.rpc_client'
            $notifier: '@sonvideo.synapps.synapps_notifier'
            $logger: '@logger'
            $crypter: '@rpc.crypter'

    app.manager.account.password_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Account\PasswordManager
        arguments:
            $rpc_client: '@sonvideo.synapps.rpc_client'
            $notifier: '@sonvideo.synapps.synapps_notifier'
            $logger: '@logger'
            $crypter: '@rpc.crypter'

    SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Account\CustomerOrder\CustomerOrderForCheckoutRetriever:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Account\CustomerOrder\CustomerOrderForCheckoutRetriever
        arguments:
            - "@=service('pomm').getDefaultSession()"

    app.manager.account_creator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\AccountCreator
        arguments:
            $rpc_client: '@sonvideo.synapps.rpc_client'
            $logger: '@logger'
            $crypter: '@rpc.crypter'
        calls:
            - [
                  setConfirmAccountUrlPattern,
                  ['@=service("router").generate("customer_creation_confirm", {token: "0TOKEN0"}, 0)'],
              ]

    app.manager.token_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\TokenManager
        arguments: ['@sonvideo.synapps.rpc_client']

    app.manager.customer.shopping_cart_entity_provider:
        class: SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\ShoppingCartEntityProvider
        arguments: ["@=service('pomm').getDefaultSession()", '@app.manager.article.article_warranty_fetcher']

    app.manager.customer.shopping_cart:
        class: SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\ShoppingCartManager
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@session'
            - '@app.manager.customer.shopping_cart_entity_provider'
            - '@app.manager.customer.session_shopping_cart'
            - '@app.manager.customer.authenticated_shopping_cart'
            - '@app.manager.customer.quote_manager'
            - '@logger'
        calls:
            - [setEventDispatcher, ['@event_dispatcher']]
            - [setPromoCodeResolver, ['@app.manager.promo_code_resolver']]
            - [setShoppingCartCatalogManager, ['@app.manager.customer.shopping_cart_catalog_manager']]
            - [setShoppingCartMagazineManager, ['@app.manager.customer.shopping_cart_magazine_manager']]
            - [setCustomerPreferenceManager, ['@app.manager.customer_preference']]
            - [setLocale, ['%locale%']]

    app.manager.customer.quote_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\QuoteManager
        arguments:
            - "@=service('pomm').getDefaultSession()"

    app.manager.customer.session_shopping_cart_merge_holder:
        class: SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Session\ShoppingCartMergeHolder
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@session'

    app.manager.customer.shopping_cart_converter:
        class: SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\ShoppingCartConverter
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@app.manager.customer.shopping_cart'
            - '@app.manager.customer.session_shopping_cart_merge_holder'
            - '@session'
            - '@logger'

    app.manager.customer.session_shopping_cart:
        class: SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Repository\ShoppingCartSessionRepository
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@session'
            - '%locale%'

    app.manager.customer.authenticated_shopping_cart:
        class: SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Repository\ShoppingCartAuthenticatedRepository
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@app.manager.customer.quote_manager'
            - '@security.token_storage'
            - '%locale%'

    app.sitemap.generator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Sitemap\SitemapGenerator
        arguments: ['@app.sitemap.url_generator_collection', '@oneup_flysystem.mount_manager', '@twig']
        calls:
            - [setBaseUrl, ['%base_url%']]

    app.sitemap.url_generator_collection:
        class: SonVideo\Cms\FrontOffice\AppBundle\Sitemap\UrlGeneratorCollection
        arguments: ['@pomm', '@router', '@app.static_page_router', '@assets.packages']

    app.manager.guide.guide_association_fetcher:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Guide\GuideAssociationFetcher
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.manager.customer.document.invoice_fetcher:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\Document\InvoiceFetcher
        arguments:
            - '@oneup_flysystem.mount_manager'
            - '@sonvideo.synapps.rpc_client'

    app.manager.contact_us_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\ContactUsManager
        arguments:
            - '@sonvideo.synapps.rpc_client'

    app.manager.article.article_warranty_fetcher:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\ArticleWarrantyFetcher
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.manager.immediate_recall:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerService\ImmediateRecallManager
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@app.validation.validator'
            - '@app.client.axialys_api'
            - '@logger'
            - '%kernel.environment%'

    app.manager.tracker:
        class: SonVideo\Cms\FrontOffice\AppBundle\Tracker\TrackerManager
        arguments: ['%tracker%', '@app.manager.tracker_provider_container']

    app.manager.tracker_provider_container:
        class: SonVideo\Cms\FrontOffice\AppBundle\Tracker\TrackerProviderContainer
        calls:
            - [addTrackerProvider, ['@app.order_confirm_provider']]
            - [addTrackerProvider, ['@app.provider.guide_family']]
            - [addTrackerProvider, ['@app.provider.stand_headline']]
            - [addTrackerProvider, ['@app.provider.shopping_cart']]

    SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\CheckoutProcess\CustomerOrderContextForPaymentApiBuilder:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\CheckoutProcess\CustomerOrderContextForPaymentApiBuilder
        arguments:
            - '@app.manager.basket_order_manager'
            - '@logger'

    SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\CheckoutProcess\CustomerOrderCreator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\CheckoutProcess\CustomerOrderCreator
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\CheckoutProcess\CustomerOrderContextForPaymentApiBuilder'
            - '@sonvideo.synapps.rpc_client'
            - '@SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\PaymentClient'
            - '@app.manager.basket_order_manager'
            - '@router'
            - '@logger'
        calls:
            - [setSynappsNotifier, ['@sonvideo.synapps.synapps_notifier']]

    app.manager.customer_order.additional_payment_handler:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\AdditionalPaymentHandler
        arguments:
            - '@app.manager.customer_order.additional_payment_holder'
            - "@=service('pomm').getDefaultSession()"
            - '@sonvideo.synapps.rpc_client'
            - '@app.factory.customer_order_context_factory'
            - '@SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\PaymentClient'
            - '@request_stack'
            - '@router'
            - '@logger'

    app.manager.customer_order.additional_payment_holder:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\AdditionalPaymentHolder
        arguments: ['@session', "@=service('pomm').getDefaultSession()"]

    app.manager.customer_order.return_note_creator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\ReturnNoteCreator
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@sonvideo.synapps.rpc_client'
            - '@sonvideo.synapps.synapps_notifier'
            - '@logger'

    app.manager.customer_order.return_note_pdf_generator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\ReturnNotePdfGenerator
        arguments: ['@twig', '%kernel.root_dir%']

    app.manager.customer_order.svd_gift_card_payment_handler:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\SvdGiftCardPaymentHandler
        arguments:
            - '@sonvideo.synapps.rpc_client'
            - '@app.factory.customer_order_context_factory'
            - '@app.manager.customer_order.additional_payment_handler'
            -
    SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\CustomerOrderTrackingManager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerOrder\CustomerOrderTrackingManager
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.manager.article.article_fetcher:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\ArticleFetcher
        arguments: ["@=service('pomm').getDefaultSession()", '@translator']

    app.manager.article.search.search_facet_builder:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\Search\SearchFacetBuilder
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.manager.article.article_redirection_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\ArticleRedirectionManager
        arguments: ["@=service('pomm').getDefaultSession()", '@translator', '@app.router']

    app.manager.abstract_image_uploader:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\AbstractImageUploader
        abstract: true
        arguments: ['@oneup_flysystem.mount_manager', '@logger']

    app.manager.event.installation_image_uploader:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Event\InstallationImageUploader
        parent: 'app.manager.abstract_image_uploader'

    app.manager.event.comment_image_uploader:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\CommentImageUploader
        parent: 'app.manager.abstract_image_uploader'

    app.manager.event.installation_creator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Event\InstallationCreator
        arguments:
            [
                "@=service('pomm').getDefaultSession()",
                '@app.manager.event.installation_image_uploader',
                '@sonvideo.synapps.synapps_notifier',
                '@logger',
            ]

    app.manager.event.installation_edit:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Event\InstallationEdit
        arguments:
            [
                "@=service('pomm').getDefaultSession()",
                '@app.manager.event.installation_image_uploader',
                '@sonvideo.synapps.synapps_notifier',
                '@logger',
            ]

    app.manager.catalog.catalog_fetcher:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Catalog\CatalogFetcher
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.manager.category.category_fetcher:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Category\CategoryFetcher
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.manager.stand.stand_fetcher:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Stand\StandFetcher
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.manager.store.store_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Store\StoreManager
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.manager.customer_preference:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\CustomerPreferenceManager
        arguments: ["@=service('pomm').getDefaultSession()", '@security.token_storage']

    app.manager.customer.address:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\AddressManager
        arguments: ['@pomm', '@app.validation.validator', '@sonvideo.synapps.synapps_notifier', '@logger']
        calls:
            - [setRpcClient, ['@sonvideo.synapps.rpc_client']]

    app.manager.customer.shopping_cart_catalog_holder:
        class: SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Catalog\ShoppingCartCatalogHolder
        arguments: ['@session']

    app.manager.customer.shopping_cart_magazine_holder:
        class: SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Magazine\ShoppingCartMagazineHolder
        arguments: ['@session']

    app.manager.customer.shopping_cart_catalog_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Catalog\ShoppingCartCatalogManager
        arguments:
            - '@app.manager.customer_preference'
            - '@app.manager.customer.shopping_cart_catalog_holder'
            - "@=service('pomm').getDefaultSession()"
            - '@logger'
            - '%catalog_in_basket%'

    app.manager.customer.shopping_cart_magazine_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Magazine\ShoppingCartMagazineManager
        arguments:
            - '@app.manager.customer_preference'
            - '@app.manager.customer.shopping_cart_magazine_holder'
            - "@=service('pomm').getDefaultSession()"
            - '@logger'

    app.manager.promo_code_resolver:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\PromoCode\PromoCodeResolver
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '%currency%'
            - '@sonvideo.synapps.rpc_client'

    app.manager.basket_order_summary:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\BasketOrder\BasketOrderSummary
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@app.manager.promo_code_resolver'

    SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\ThirdPartyResult\PaymentResultHandler:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\ThirdPartyResult\PaymentResultHandler
        calls:
            - [
                  addThirdPartyHandler,
                  [
                      '@SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\ThirdPartyResult\ThirdPartyHandler\FullCBHandler',
                  ],
              ]

    SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\ThirdPartyResult\ThirdPartyHandler\FullCBHandler:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\ThirdPartyResult\ThirdPartyHandler\FullCBHandler

    SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\ArticleRecommendationFetcher:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Article\ArticleRecommendationFetcher
        arguments: ["@=service('pomm').getDefaultSession()"]

    SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\CustomerManager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\CustomerManager
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@security.token_storage'
            - '@session'
            - '@app.manager.customer.shopping_cart_converter'
            - '@app.manager.account_creator'
        calls:
            - [setEventDispatcher, ['@event_dispatcher']]
            - [setRequestStack, ['@request_stack']]
            - [setRpcClient, ['@sonvideo.synapps.rpc_client']]
            - [setCustomerResetPasswordUrl, ['@app.router_manager', '@translator']]

    app.manager.comment.review_creator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\ReviewCreator
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@app.manager.event.comment_image_uploader'
            - '@assets.packages'
            - '@advice_client.operation.site_review'
            - '@advice_client.operation.product_review'
            - '@logger'
            - '@Ramsey\Uuid\UuidFactory'
        calls:
            - [setRpcClient, ['@sonvideo.synapps.rpc_client']]

    SonVideo\Cms\FrontOffice\AppBundle\Manager\Paypal\ExpressCheckOutManager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Paypal\ExpressCheckOutManager
        arguments:
            - '@logger'
        calls:
            - [setRpcClient, ['@sonvideo.synapps.rpc_client']]

    'SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\QuestionCreator':
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\QuestionCreator
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@app.manager.event.comment_image_uploader'
            - '@assets.packages'
            - '@advice_client.operation.product_question'
            - '@logger'
            - '@Ramsey\Uuid\UuidFactory'
        calls:
            - [setRpcClient, ['@sonvideo.synapps.rpc_client']]

    'SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\AnswerCreator':
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\AnswerCreator
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@app.manager.event.comment_image_uploader'
            - '@assets.packages'
            - '@advice_client.operation.comment'
            - '@logger'
            - '@Ramsey\Uuid\UuidFactory'
        calls:
            - [setRpcClient, ['@sonvideo.synapps.rpc_client']]

    'SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\AdviceUserIdGenerator':
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\AdviceUserIdGenerator
        arguments:
            - '@security.token_storage'
            - '@request_stack'
            - '@SonVideo\Cms\FrontOffice\AppBundle\EventListener\CookieListener'

    'SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\ReviewVoteSubmit':
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\ReviewVoteSubmit
        arguments:
            - '@advice_client.operation.comment_vote'
            - '@SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\AdviceUserIdGenerator'
            - '@logger'

    app.manager.question_interest_creator:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\QuestionInterestCreator
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@logger'

    app.manager.question_manager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Advice\QuestionManager
        arguments:
            - "@=service('pomm').getDefaultSession()"

    SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\NewsletterManager:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\NewsletterManager
        arguments:
            - '@sonvideo.synapps.rpc_client'
    app.newsletter_manager:
        alias: SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\NewsletterManager

    ###############
    ##  Clients  ##
    ###############

    app.client.so_colissimo:
        class: SonVideo\Cms\FrontOffice\AppBundle\Client\SoColissimoSoapClient
        arguments: ['%soap.so_colissimo.account_number%', '%soap.so_colissimo.password%']

    app.client.mondial_relay:
        class: SonVideo\Cms\FrontOffice\AppBundle\Client\MondialRelaySoapClient
        arguments:
            - '%soap.mondial_relay.wdsl%'
            - '%soap.mondial_relay.shop_identifier%'
            - '%soap.mondial_relay.private_key%'

    app.client.chronopost_pickup:
        class: SonVideo\Cms\FrontOffice\AppBundle\Client\ChronopostSoapClient
        arguments:
            - '%soap.chronopost_pickup.wdsl%'
            - '%soap.chronopost_pickup.account%'
            - '%soap.chronopost_pickup.password%'
            - '@logger'

    app.client.chronopost_precise:
        class: SonVideo\Cms\FrontOffice\AppBundle\Client\ChronopostPreciseSoapClient
        arguments:
            - '%soap.chronopost_precise.wdsl%'
            - '%soap.chronopost_precise.account%'
            - '%soap.chronopost_precise.password%'
            - '@logger'

    app.client.axialys_api:
        class: SonVideo\Cms\FrontOffice\AppBundle\Client\AxialysApiClient
        arguments:
            - '%axialys.username%'
            - '%axialys.password%'

    SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\PaymentClient:
        class: SonVideo\Cms\FrontOffice\AppBundle\Manager\Payment\PaymentClient
        arguments: ['%api.payment.url%', '%api.payment.authentication_key%', '@logger']

    ###############
    ##  Factory  ##
    ###############

    app.factory.order_context_factory:
        class: SonVideo\Cms\FrontOffice\AppBundle\Factory\Customer\OrderContextFactory
        arguments: ['@pomm', '@app.manager.customer.shopping_cart', '@security.token_storage', '@session', '@logger']
        calls:
            - [setIpAddress, ['@request_stack']]

    app.factory.customer_order_context_factory:
        class: SonVideo\Cms\FrontOffice\AppBundle\Factory\CustomerOrder\CustomerOrderContextFactory
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@security.token_storage'
            - '@session'
            - '@app.manager.customer_order.additional_payment_holder'
            - '@logger'
        calls:
            - [setRequestStack, ['@request_stack']]

    ###############
    ##  Routing  ##
    ###############

    app.router:
        class: SonVideo\Cms\FrontOffice\AppBundle\Routing\Router
        decorates: router
        arguments: ['@app.router.inner', '@app.static_page_router', '@router.request_context', '@logger']

    app.static_page_router:
        class: SonVideo\Cms\FrontOffice\AppBundle\Routing\StaticPageRouter
        arguments: ['@cms.static_page.resolver', '@router.request_context', '%locale%']

    ######################
    ##  Twig extension  ##
    ######################

    app.twig_extension.load_brand_data:
        class: SonVideo\Cms\FrontOffice\AppBundle\Twig\LoadBrandDataExtension
        arguments: ["@=service('pomm').getDefaultSession()"]
        tags:
            - { name: twig.extension }

    app.twig_extension.route:
        class: SonVideo\Cms\FrontOffice\AppBundle\Twig\RouteExtension
        arguments: ['@router']
        tags:
            - { name: twig.extension }

    app.twig.environment_extension:
        class: SonVideo\Cms\FrontOffice\AppBundle\Twig\EnvironmentExtension
        tags:
            - { name: twig.extension }

    ############
    ##  Form  ##
    ############

    SonVideo\Cms\FrontOffice\AppBundle\Form\ToggleType:
        class: SonVideo\Cms\FrontOffice\AppBundle\Form\ToggleType
        tags:
            - { name: form.type }

    app.form.account_information:
        class: SonVideo\Cms\FrontOffice\AppBundle\Form\Customer\AccountInformationFormType
        arguments: ["@=service('pomm').getDefaultSession()"]
        tags:
            - { name: form.type }

    app.form.account_confirmation:
        class: SonVideo\Cms\FrontOffice\AppBundle\Form\Customer\AccountCreationFormType
        arguments: ["@=service('pomm').getDefaultSession()"]
        tags:
            - { name: form.type }

    app.form.installation_creation:
        class: SonVideo\Cms\FrontOffice\AppBundle\Form\CustomerSocial\InstallationCreationFormType
        arguments: ['@translator']
        tags:
            - { name: form.type }

    app.form.catalog_lamp:
        class: SonVideo\Cms\FrontOffice\AppBundle\Form\Catalog\LampFormType
        arguments: ['@app.manager.catalog.catalog_fetcher']
        tags:
            - { name: form.type }

    app.form.review:
        class: SonVideo\Cms\FrontOffice\AppBundle\Form\ReviewFormType
        arguments:
            - "@=service('pomm').getDefaultSession()"
            - '@app.manager.article.article_fetcher'
        tags:
            - { name: form.type }

    SonVideo\Cms\FrontOffice\AppBundle\Form\Customer\CommunicationPreferencesFormType:
        class: SonVideo\Cms\FrontOffice\AppBundle\Form\Customer\CommunicationPreferencesFormType
        arguments:
            - "@=service('pomm').getDefaultSession()"
        tags:
            - { name: form.type }

    ################
    ##  Provider  ##
    ################

    app.order_confirm_provider:
        class: SonVideo\Cms\FrontOffice\AppBundle\Tracker\Providers\OrderConfirmProvider
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.provider.guide_family:
        class: SonVideo\Cms\FrontOffice\AppBundle\Tracker\Providers\GuideFamilyProvider
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.provider.stand_headline:
        class: SonVideo\Cms\FrontOffice\AppBundle\Tracker\Provider\StandHeadlineArticleProvider
        arguments: ["@=service('pomm').getDefaultSession()"]

    app.provider.shopping_cart:
        class: SonVideo\Cms\FrontOffice\AppBundle\Tracker\Providers\ShoppingCartProvider
        arguments: ['@app.manager.customer.shopping_cart']

    ###############
    ##  Elector  ##
    ###############

    app.elector.return_note:
        class: SonVideo\Cms\FrontOffice\AppBundle\Elector\ReturnNoteElector
        arguments: ['%withdrawal_period_in_days%']

    ################
    ##  Listener  ##
    ################

    app.listener.cache_control_before:
        class: SonVideo\Cms\FrontOffice\AppBundle\EventListener\CacheControlListenerBefore
        tags: [{ name: 'kernel.event_subscriber' }]

    app.listener.cache_control_after:
        arguments:
            - '@app.listener.cache_control_before'
            - '@session'
        class: SonVideo\Cms\FrontOffice\AppBundle\EventListener\CacheControlListenerAfter
        tags: [{ name: 'kernel.event_subscriber' }]

    'SonVideo\Cms\FrontOffice\AppBundle\EventListener\CookieListener':
        class: SonVideo\Cms\FrontOffice\AppBundle\EventListener\CookieListener
        tags: [{ name: 'kernel.event_subscriber' }]

    SonVideo\Cms\FrontOffice\AppBundle\Controller\Customer\Account\PasswordController:
        autowire: true

    SonVideo\Cms\FrontOffice\AppBundle\Controller\Api\Customer\NewsletterController:
        autowire: true

    SonVideo\Cms\FrontOffice\AppBundle\Controller\Api\External\Prospect\ProspectConsentController:
        autowire: true

@import '../../../Resources/scss/style';

/*********************************/
/*         ARTICLE PAGE          */
/*********************************/

.product-header {
    margin-bottom: 30px;

    .container {
        max-width: none;
    }
}

.product-second-life-content {
    display: flex;
    flex-direction: column;
    gap: $space_32;
    margin-bottom: $space_48;
    font-family: $font_open_sans;
    font-size: $height_16;
    line-height: $height_26;
    font-weight: $font_normal;
    color: $color_grey_typo;
    .product-second-life-warning {
        display: flex;
        flex-direction: column;
        background-color: $color_green_light;
        color: $color_green_dark;
        padding: $space_16;
        border-radius: $radius_8;
        .product-second-life-title {
            display: flex;
            flex-direction: row;
            gap: $space_10;
            font-weight: $font_bold;
        }
    }
    .product-second-life-grade {
        span {
            font-weight: $font_bold;
        }
    }
    .product-second-life-image {
        display: flex;
        flex-direction: row;
        gap: $space_8;
        flex-wrap: wrap;
        img {
            width: 150px;
            height: 150px;
            border: solid 1px $color_grey_default;
            border-radius: $radius_8;
            padding: $space_4;
            cursor: pointer;
        }
    }
    .product-second-life-other {
        border: solid 1px $color_grey_default;
        border-radius: $radius_8;
        padding: $space_16;
        width: fit-content;
        a {
            font-size: $height_16;
            line-height: $height_26;
        }
        ul {
            padding: 0;
            margin: 0;
        }
    }
}

.container-installments {
    display: flex;
    flex-direction: column;
    gap: $space_8;
    margin-bottom: $space_16;
    padding-top: $space_16;
    border-top: solid 1px #e5e5e5;

    .installments-title {
        font-size: $font_size_13;
        line-height: $height_20;
        font-weight: $font_bold;
    }

    .installments-options {
        display: flex;
        gap: $space_4;
    }

    .installment-option {
        width: 36px;
        height: 36px;
        border: 2px solid $color_grey_default;
        border-radius: 50%;
        color: $color_grey_typo;
        cursor: pointer;
        display: flex;
        gap: $space_4;
        justify-content: center;
        align-items: center;
        text-align: center;
        font-size: $font_size_13;
        line-height: $height_20;
        font-weight: $font_bold;
        &:hover {
            background-color: $color_blue_default;
            color: $color_white;
            border-color: $color_blue_default;
        }
    }
}

.product-header-content {
    border: 1px solid $sv-border-color-base;
    padding: 20px;
    position: relative;
    font-size: 14px;

    &.article {
        &.fixed {
            position: fixed;
            top: 90px;
            right: calc(50% - 490px);
            width: 300px;
            animation: product-header-content 0.3s ease forwards;

            @media (min-width: 1365px) {
                width: 400px;
                right: calc(50% - 630px);
            }

            .product-header-brand-logo,
            .product-header-promotions,
            .product-header-recall,
            .product-header-share,
            .product-header-regulated-attributes {
                display: none;
            }
        }
        &.out {
            position: fixed;
            top: 90px;
            right: calc(50% - 490px);
            width: 300px;
            @media (min-width: 1365px) {
                width: 400px;
                right: calc(50% - 630px);
            }
            animation: product-header-content-reverse 0.3s ease forwards;
        }
    }

    @keyframes product-header-content {
        from {
            opacity: 0;
            transform: translateY(-100%);
        }

        to {
            opacity: 1;
            transform: translateY(0%);
        }
    }

    @keyframes product-header-content-reverse {
        from {
            opacity: 1;
            transform: translateY(0%);
        }

        to {
            opacity: 0;
            transform: translateY(-100%);
        }
    }
}

#article-second-life {
    display: flex;
    flex-direction: row;
    gap: $space_8;
    padding-top: $space_16;
    padding-bottom: $space_16;
    border-top: solid 1px $color_skeleton_default;
    font-size: $font_size_13;
    line-height: $height_20;
    font-weight: $font_bold;
    align-items: center;
    cursor: pointer;
    .focus {
        color: $color_blue_web_safe;
    }
}

.product-header-section {
    padding: 15px 0;
    border-top: 1px solid #e5e5e5;

    &:first-child {
        border: 0;
        padding-top: 0;
    }

    &:last-child {
        padding-bottom: 0;
    }

    &.product-header-section-noborder {
        padding-top: 0;
        border: 0;
    }

    .product-header-brand-logo {
        padding: 0;
    }

    &.with-sustainability {
        padding-top: 32px;
    }
}

.product-header-promotion-section {
    margin-bottom: 15px;
}

.product-header-section-row {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;

    .product-header-brand-logo {
        max-width: 45%;
        padding-left: 15px;
        text-align: center;
        align-self: flex-start;

        img {
            max-width: 100% !important;
            height: auto !important;
            max-height: 80px;
        }
    }

    .product-header-common {
        flex-grow: 1;
    }

    @media (max-width: 322px), (min-width: 640px) and (max-width: 820px) {
        flex-direction: column;
        align-items: flex-start;

        .product-header-brand-logo {
            max-width: 100%;
            padding-bottom: 15px;
            padding-left: 0;
        }
    }
}

.product-header-reviews .product-header-review-score {
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;

    .score-item-text {
        font-size: 13px;
        line-height: 16px;
        & + .score-item-text {
            margin-left: 25px;
        }
    }
}

.icon-questions {
    display: inline-block;
    width: 19px;
    height: 16px;
    background: transparent url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat -344px -140px;
    margin-right: 10px;
}

.product-header-header {
    margin-bottom: 30px;
}

h1.product-header-title {
    border: 0;
    line-height: 1;
    margin: 0 0 5px 0;
    padding: 0;

    .product-header-title-model {
        font-weight: 700;
        font-size: $sv-font-size-xl;
        line-height: $sv-line-height-xl;
        color: $sv-font-color;
        text-transform: uppercase;
    }

    .product-header-title-stand {
        font-weight: 700;
        font-size: $sv-font-size-lg;
        line-height: $sv-line-height-lg;
        a {
            color: $sv-font-color-light;
        }
    }
}

.product-header-media {
    margin-bottom: 30px;

    @media (min-width: 640px) {
        margin-bottom: 0;
    }
}

.product-header-highlight .product-highlight {
    white-space: normal;
}

/******************/
/*  MAIN PICTURE  */
/******************/

.product-header-mainpicture {
    text-align: center;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    height: 450px;
    @media (min-width: 992px) {
        height: 450px;
    }
    @media (min-width: 1365px) {
        height: 450px;
    }
}

.product-header-mainpicture-picture {
    width: 100%;
    max-width: 600px;
    height: 450px !important;
    object-fit: contain;
    margin: 0 auto;
    @media (min-width: 992px) {
        height: 450px !important;
    }
    @media (min-width: 1365px) {
        height: 450px !important;
    }
}

.pin-it-button {
    position: absolute;
    bottom: 10px;
    right: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-header-mainpicture:hover .pin-it-button {
    opacity: 1;
}

/*********************/
/*  IMMEDIAT RECALL  */
/*********************/

.product-header-recall .widget-rappel-immediat {
    cursor: pointer;

    .btn-primary.btn-outline:hover,
    .btn-primary.btn-outline:focus,
    .btn-primary.btn-outline.focus {
        color: #fff !important;
        background-color: $sv-color-primary;
        border-color: $sv-color-primary;
    }
}

.product-header-recall-text,
.product-header-recall-btn {
    height: 100%;
    align-self: center;
}

.product-header-recall-text {
    font-size: 13px;
    color: $sv-font-color;

    strong {
        font-size: 14px;
        transition: color 0.3s;

        &:hover {
            color: $sv-link-color;
        }
    }

    p {
        margin-bottom: 10px;
    }
}

/******************/
/*  Technologies  */
/******************/

.logos-list.logos-list-technologies {
    margin-bottom: 30px;
}

.logos-list-body {
    margin-bottom: -15px;
}

.logos-list-item {
    position: relative;
    margin-bottom: 15px;
    overflow: hidden;

    &:before {
        content: '';
        float: left;
        padding-top: 100%;
    }

    &.logos-list-item-border {
        border-radius: 5px;
        border: 1px solid #e5e5e5;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
    }

    img {
        width: 100% !important;
        height: 100% !important;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        object-fit: contain;
        border: 5px solid transparent;
    }
}

/******************/
/*      Share     */
/******************/

.product-header-share a:last-child {
    margin-right: 0;
}

/******************/
/*       Sku      */
/******************/

.product-header-content .sku {
    position: absolute;
    bottom: -15px;
    right: 0;
    font-size: 10px;
    line-height: 10px;
    color: #999;
}

/**********************************/
/*    MINIATURES FICHE PRODUIT    */
/**********************************/

.product-header-thumbnails,
.product-header-thumbnails *,
.product-header-thumbnails *:before,
.product-header-thumbnails *:after {
    box-sizing: border-box;
}

.product-header-thumbnails {
    @media (min-width: 1355px) {
        width: 700px;
    }
    @media (min-width: 600px) and (max-width: 1355px) {
        width: 360px;
    }
    @media (min-width: 340px) and (max-width: 600px) {
        width: 275px;
    }
    width: 135px;
    max-width: 100%;
    margin: 30px auto 0 auto;
    position: relative;
    white-space: nowrap;
}

.product-header-thumbnails-list {
    position: relative;
    display: block;
    @media (min-width: 1355px) {
        height: 110px;
    }

    @media (min-width: 600px) and (max-width: 1355px) {
        height: 84px;
    }

    height: 64px;

    overflow: hidden;
    margin: 0 auto;
}

// fleches

.product-header-thumbnails-arrow {
    display: block;
    position: absolute;
    top: 0;
    width: 26px;
    height: 64px;
    cursor: pointer;
    @media (min-width: 1355px) {
        top: 24px;
    }
    @media (min-width: 600px) and (max-width: 1355px) {
        top: 10px;
    }

    &:after {
        position: absolute;
        content: '';
        top: 19px;
        left: 0;
        width: 26px;
        height: 26px;
        background: transparent url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat;

        transition: left 0.3s, right 0.3s;
    }
}

.product-header-thumbnails-arrow-left {
    left: -26px;

    &:after {
        background-position: -170px -67px;
    }

    &:hover:after {
        left: -2px;
    }
}

.product-header-thumbnails-arrow-right {
    right: -26px;

    &:after {
        background-position: -204px -67px;
    }

    &:hover:after {
        left: 2px;
    }
}

.product-header-thumbnails .slick-slide {
    display: inline-block;
    position: relative;
    border: 2px solid $sv-border-color-base;
    padding: 2px;
    margin: 0 7px 0 0;
    transition: border-color 0.3s;
    background: #fff;
    overflow: hidden;
    text-align: center;

    div {
        @media (min-width: 1355px) {
            height: 102px !important;
            width: 102px !important;
        }

        @media (min-width: 600px) and (max-width: 1355px) {
            height: 75px !important;
            width: 75px !important;
        }

        width: 55px !important;
        height: 55px !important;
        background: #fff;
        overflow: hidden;
    }

    img {
        @media (min-width: 1355px) {
            height: 102px !important;
            width: 102px !important;
        }

        @media (min-width: 600px) and (max-width: 1355px) {
            height: 75px !important;
            width: 75px !important;
        }

        width: 55px !important;
        height: 55px !important;
        object-fit: contain;
    }

    &.slick-current,
    &:hover {
        border-color: $sv-color-primary;
    }

    &:hover {
        cursor: pointer;
    }
}

/************************/
/*     Declinations     */
/************************/

.product-header-declination-color-list {
    padding: 0;
    margin: 0;
    overflow: auto;

    .product-header-declination-color-item {
        display: block;
        list-style-type: none;
        margin: 0 7px 7px 0;
        float: left;

        a {
            display: block;
            border: 2px solid $sv-border-color-base;
            padding: 2px;
            transition: border-color 0.3s;
        }

        &-current a,
        a:hover {
            border-color: $sv-color-primary;
        }

        img {
            width: 55px !important;
            height: 55px !important;
        }
    }
}

.product-header-select-declination {
    color: $sv-font-color;
    font-size: 12px;
    line-height: 18px;
    margin-right: 10px;
    max-width: 100%;
}

/************************/
/*        Price         */
/************************/

.product-header-pricing {
    // Price container
    .product-header-price {
        margin-bottom: 10px;
        .reference-price {
            display: flex;
            flex-direction: row;
            gap: $space_10;
            padding-bottom: $space_8;
            .percent {
                background-color: $color_blue_web_safe;
                padding: 1px 6px 2px 6px;
                font-family: Open Sans;
                font-size: $font_size_15;
                font-weight: $font_bold;
                line-height: $height_24;
                color: $color_white;
                height: $height_27;
            }
            .reference-price-cross {
                display: flex;
                flex-direction: row;
                gap: $space_4;
                cursor: pointer;
                .crossed {
                    position: relative;
                    font-size: $font_size_17;
                    font-weight: $font_bold;
                    line-height: $height_25;
                    color: $color_grey_typo;
                    cursor: pointer;
                    &:after {
                        content: '';
                        position: absolute;
                        left: 0;
                        right: 0;
                        top: 12px;
                        height: 2px;
                        background: $color_grey_typo;
                    }
                }
            }
        }
    }

    // Price
    .SVDv3_zonePrix_prix {
        font-size: 32px;
        line-height: 1;
        color: $sv-color-primary;
    }

    .SVDv3_zonePrix_prixConfidentiel {
        font-size: 24px;
        text-transform: uppercase;
        color: $sv-font-color;
        word-break: break-word;
    }

    // Price strike
    .SVDv3_zonePrix_ald {
        font-size: 14px;
        line-height: 22px;
        font-weight: 700;
        margin: 0;
    }

    // Ecotaxe
    .SVDv3_zonePrix_ecotaxe {
        font-size: 12px;
        line-height: 18px;
    }

    // Availability
    .product-header-availability .SVDv3_dispo {
        display: inline-block;
        font-size: 14px;
        line-height: 22px;
        margin: 0;
    }

    & + .product-header-warranties {
        margin-top: 15px;
    }
}

.mobile-add-to-basket {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    transition: all 0.2s ease;
    transform: translateY(100%);
    z-index: 20;
    padding: 10px 25px;
    background: #fff;
    box-shadow: $sv-box-shadow-md;

    @media (min-width: 992px) {
        display: none;
    }

    &.active {
        transform: translateY(0);
    }

    .container-price-add-to-basket {
        display: flex;

        .SVDv3_zonePrix_prix {
            line-height: 43px;
            color: $sv-color-primary;
            flex-basis: 35%;
            @media (min-width: 390px) {
                flex-basis: 50%;
            }
            @media (min-width: 769px) {
                flex-basis: 75%;
            }

            &.SVDv3_zonePrix_prixConfidentiel {
                font-size: 16px;
                align-self: center;
                line-height: normal;
                @media (min-width: 992px) {
                    font-size: 24px;
                }
            }
        }

        .SVDv3_bouton_ajoutPanier {
            flex-basis: 65%;
            font-size: 14px;

            padding: 7px 14px;

            @media (min-width: 390px) {
                flex-basis: 50%;
            }
            @media (min-width: 769px) {
                flex-basis: 25%;
            }
        }
    }

    .product-header-button .SVDv3_bouton_ajoutPanier {
        padding: 9px;
    }
}

/**********************/
/*       BUTTON       */
/**********************/

.product-header-button {
    .SVDv3_bouton_ajoutPanier,
    .SVDv3_bouton_prixConfidentiel,
    .SVDv3_bouton_exclusifMagasin {
        font-size: 14px;
        margin: 0 auto;
        padding: 13px;
        display: block;
        font-weight: 700;
        text-transform: uppercase;
        cursor: pointer;
        border-radius: 4px;
        width: 100%;
    }
}

/**********************/
/*     WARRANTIES     */
/**********************/

.product-header-warranties {
    .product-header-warranty {
        font-size: 13px;
        margin-bottom: 5px;
        padding-left: 20px;
        position: relative;

        &:after {
            position: absolute;
            content: '';
            top: 1px;
            left: 0;
            width: 16px;
            height: 16px;
            background: transparent url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat -310px -72px;
        }

        &:last-child {
            margin-bottom: 0;
        }

        .SVDv3_dispo {
            font-weight: bold;
            margin-bottom: 0;
        }
    }
}

.product-header-warranty .find-store-nearby {
    color: #3366cc;
    cursor: pointer;
    text-align: left;
    &:hover {
        color: #003399;
    }
}

//TEMP display none

.SVDv3_produit_avantageSVD {
    display: none;
}

/*****************************/
/* BEFORE CHRISTMAS DELIVERY */
/*****************************/

.product-header-before-christmas-body {
    padding-bottom: 8px;
}

.delivery-before-chistmas {
    display: block;
    line-height: 1.3;
    font-size: 13px;
    text-align: center;
    color: $sv-font-color;
    border: 1px solid transparent;
    background-color: #fff;
    transition: background-color 0.3s, border-color 0.3s, color 0.3s;

    strong {
        text-transform: uppercase;
        display: inline-block;
        position: relative;
    }
}

.delivery-before-chistmas-wrapper {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
}

.delivery-before-chistmas-yes {
    color: $sv-christmas;
    border-color: $sv-christmas;

    strong {
        padding-left: 10px;
    }

    svg {
        width: 26px;
        height: 20px;
        fill: $sv-christmas;
        transition: fill 0.3s;
    }
}

a.delivery-before-chistmas-yes,
.christmas-card .delivery-before-chistmas-yes {
    background-color: #fff;
    color: $sv-christmas;

    strong:before {
        background-position: -310px -4px;
    }
}

.christmas-card .delivery-before-chistmas-yes {
    display: block;
    margin-bottom: 10px;
}

a.delivery-before-chistmas-yes:hover {
    background-color: $sv-christmas;
    border-color: $sv-christmas;
    color: #fff;
    cursor: pointer;

    svg {
        fill: #fff;
    }
}

.swal2-content .delivery-before-chistmas {
    display: block;
    margin: 0 auto 10px auto;
    max-width: 70%;
}

.delivery-before-chistmas-no {
    border-color: $sv-border-color-light;
    line-height: 1.5;
    padding: 0.5rem 1rem;
}

/**************/
/* HIGHLIGHTS */
/**************/

.product-highlight-list {
    margin-bottom: -4px;
}

/*************/
/*  ADVICES  */
/*************/

#advice_histogram_picto {
    cursor: pointer;
    width: 12px;
    height: 12px;
    margin-left: 5px;
}

#advice_histogram {
    position: absolute;
    height: auto;
    overflow: hidden;
    z-index: 10000;
    top: 16px;
}

#advice_histogram .popover-inner {
    width: 240px;
    box-sizing: border-box;
    padding: 10px 15px 4px;
}

.question-media,
.review-media,
.answer-media {
    width: 125px;
    max-width: 125px;
    height: 125px !important;
    max-height: 125px;
    object-fit: cover;
    margin-bottom: 8px;
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .question-media,
    .review-media,
    .answer-media {
        max-width: 125px;
        width: auto;
        height: 100%;
    }
}

/*************************/
/*  ARTICLE DECLINATION  */
/*************************/

#product-header-declination-body {
    font-size: 13px;
    line-height: 20px;
    position: relative;
    min-height: 42px;
}

.current-selected-declination {
    border: 1px solid $sv-border-color-base;
    opacity: 1;
    z-index: 2;
    display: block;
    width: 100%;
    padding: 10px 36px 10px 10px;
    background: #fff;
    transition: opacity 0.3s ease, border 0.3s, box-shadow 0.3s;
    cursor: pointer;
    position: relative;
    box-shadow: inset 0 0 0 transparent;

    &.active {
        opacity: 0;
        z-index: 1;
        position: absolute;
        top: 0;
        left: 0;
    }

    &:after {
        position: absolute;
        content: '';
        top: 50%;
        right: 4px;
        width: 26px;
        height: 26px;
        margin-top: -13px;
        background: transparent url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat -272px -67px;
        transition: margin 0.5s;
    }

    &:hover {
        border-color: $sv-color-grey;
        box-shadow: inset 4px 4px 8px rgba(0, 0, 0, 0.15);

        &:after {
            margin-top: -11px;
        }
    }
}

.product-header-declination-list {
    display: none;
    z-index: 1;
    list-style: none;
    padding-bottom: 0;
    margin-bottom: 0;
    position: relative;

    li {
        display: block;
        border: 1px solid $sv-color-grey;

        &:not(:last-child) {
            border-bottom: 0;
        }

        &:not(:first-child) {
            border-top: 0;
        }

        & + li a.declination-link {
            border-top: 1px solid $sv-border-color-base;
        }

        a.declination-link {
            color: $sv-font-color;
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 10px;
            transition: background-color 0.3s;

            &:hover {
                background-color: $sv-color-light;
            }

            .SVDv3_dispo {
                cursor: pointer;
            }
        }
    }

    &.active {
        display: block;
        z-index: 2;
    }

    .price {
        font-weight: 700;
        text-align: right;
        white-space: nowrap;

        .SVDv3_zonePrix_prix,
        .SVDv3_zonePrix_ald {
            font-size: 13px;
            color: $sv-font-color;
            line-height: 20px;
        }

        .SVDv3_zonePrix_ald {
            font-weight: 500;
            display: none;
        }
    }

    .image {
        margin-right: 10px;
        display: block;
        position: relative;
        flex: 0 0 38px;

        img {
            width: 38px !important;
            height: 38px !important;
            display: block;
            border-radius: 3px;
        }

        &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }
    }

    .info {
        flex-grow: 1;
        margin-right: 10px;

        .name,
        .availability {
            display: block;

            p {
                margin: 0;
            }

            .SVDv3_dispo,
            .SVDv3_dispo_stock {
                font-size: 13px;
            }
        }
    }
}

.product-promo-offer {
    box-shadow: $sv-box-shadow-sm;
    padding: 0;
    margin: 0 auto 30px auto;
    @media (min-width: 640px) and (max-width: 991px) {
        max-width: 720px;
    }
    .SVDv3_hr {
        display: none;
    }

    .grid_container_12 {
        @extend .container;
        width: 100% !important;
        max-width: 100% !important;
        &:after {
            display: none;
        }
    }

    .grid_row {
        @extend .row;
        display: flex;
        flex-wrap: wrap;
        @media (min-width: 640px) {
            flex-wrap: nowrap;
        }
        .row-no-gutter {
            margin: 0;
        }

        .row-no-gutter > * {
            padding: 0;
        }
        &:after {
            display: none;
        }
        align-items: center;
    }

    .col_6_col {
        margin-bottom: 0 !important;
        margin-right: 0;
        float: none;
        height: 100%;
        min-height: 100%;
        @media (min-width: 640px) {
            flex: 0 0 50%;
            max-width: 50%;
        }
        &:first-child {
            order: 2;
        }
        &:last-child {
            order: 1;
        }

        @media (min-width: 640px) {
            &:first-child {
                order: 1;
            }
            &:last-child {
                order: 2;
            }
        }

        p {
            margin: 0 0 20px 0;
            &:last-child {
                margin: 0;
            }
        }

        &:first-child p {
            padding-left: 20px;
            padding-right: 20px;
            &:first-child {
                padding-top: 20px;
            }
            &:last-child {
                padding-bottom: 20px;
            }
        }

        &:last-child img {
            width: 100%;
            height: auto;
        }
    }
}

/*********************************/
/*  ARTICLE PAYMENT CALCULATION  */
/*********************************/

.container-payment-calculation {
    .container-payment-calculation-title {
        font-size: 14px;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .monthly-payments {
        list-style: none;
        display: flex;
        margin-bottom: 20px;
        @media (min-width: 992px) {
            margin-bottom: 0;
            flex-basis: auto;
        }

        li + li {
            margin-left: 10px;
        }
    }

    .monthly-payments-multiplicator {
        display: block;
        border-radius: 50%;
        border: 1px solid $sv-border-color-base;
        color: $sv-color-grey;
        width: 36px;
        height: 36px;
        text-align: center;
        line-height: 36px;
        transition: all 0.2s ease;
        font-size: 14px;
        &.active {
            border: 2px solid $sv-color-primary;
            line-height: 32px;
            color: $sv-color-primary;
        }
    }

    .content-simulator {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        @media (min-width: 992px) {
            flex-wrap: nowrap;
        }

        p {
            margin-bottom: 0;
        }

        .free-of-charge {
            font-size: 13px;
            line-height: 13px;
        }
    }

    .simulator-calcul-target {
        line-height: 20px;
        cursor: pointer;
        .calcul {
            color: $sv-color-primary;
            font-size: 16px;
        }
        .calcul,
        .free-of-charge {
            display: block;
            text-align: right;
        }
        .free-of-charge {
            font-size: 13px;
            &:after {
                content: '?';
                border: 1px solid #d0d0d0;
                border-radius: 50%;
                padding: 0;
                height: 12px;
                width: 12px;
                line-height: 10px;
                display: inline-block;
                vertical-align: middle;
                box-sizing: border-box;
                text-align: center;
                font-size: 8px;
                margin-left: 5px;
            }
        }
    }
}

/*****************************/
/*  ARTICLE SLIDER SKELETON  */
/*****************************/

.slider-skeleton {
    margin-top: 50px;
    margin-bottom: 30px;
    @media (min-width: 992px) {
        margin-top: 0px;
        margin-bottom: 9px;
    }
}

.slider-img {
    height: 457px;
    width: 100%;
    margin-bottom: 30px;
}

.slider-nav li {
    width: 63px;
    height: 63px;
    & + li {
        margin-left: 7px;
    }
}

.slider-nav {
    display: flex;
    justify-content: center;
}

.slider-img,
.slider-nav li {
    display: inline-block;
    position: relative;
    vertical-align: middle;
    overflow: hidden;
    background-color: #dddbdd;
    &::after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        transform: translateX(-100%);
        background-image: linear-gradient(
            90deg,
            rgba(#fff, 0) 0,
            rgba(#fff, 0.2) 20%,
            rgba(#fff, 0.5) 60%,
            rgba(#fff, 0)
        );
        animation: shimmer 5s infinite;
        content: '';
    }
    @keyframes shimmer {
        100% {
            transform: translateX(100%);
        }
    }
}
/**************************************/
/*          ARTICLE COLONNES          */
/**************************************/

.SVDv3_colonnes_2colonnes1 .SVDv3_colonnes_colonne_gauche_article {
    width: 100%;
    float: none;
    margin: 0 auto;
    @media (min-width: 992px) {
        width: 660px;
        float: left;
    }
}
.SVDv3_colonnes_2colonnes1 .SVDv3_colonnes_colonne_droite_article {
    display: none;
    @media (min-width: 992px) {
        display: block;
        width: 300px;
        float: right;
    }
}

/**************************************/
/*         ARTICLE DESCRIPTIF         */
/**************************************/

.SVDv3_ficheProduit_descriptif {
    padding-top: 30px;
}

/**************************************/
/*            POINTS FORTS            */
/**************************************/

.SVDv3_ficheProduit_pointsForts {
    ul {
        margin: 0 0 10px 0;
        padding: 0;
        li {
            padding-left: 26px;
            margin: 0 0 4px 0;
            list-style-type: none;
            position: relative;
            text-align: left;

            &:before {
                font-family: 'icomoon-svd';
                content: '\e90c';
                color: #3366cc;
                font-size: 18px;
                position: absolute;
                display: block;
                top: 0;
                left: 1px;
                font-weight: 700;
                speak: none;
                font-style: normal;
                font-variant: normal;
                text-transform: none;
                line-height: 1;
                text-rendering: auto;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }
    }
}

.container-presssheet-strongpoints {
    display: flex;
    flex-wrap: wrap;
    @media (min-width: 992px) {
        flex-wrap: nowrap;
        gap: 30px;
    }

    .SVDv3_CmsFiche_press-logo {
        border: 0;
    }
}

.content-presssheet-strongpoints {
    flex-basis: 100%;
    @media (min-width: 992px) {
        .content-presssheet-strongpoints {
            flex-basis: 50%;
        }
    }

    & + .content-presssheet-strongpoints {
        margin-top: 20px;
        @media (min-width: 992px) {
            margin-top: 0px;
        }
    }
}
/*********************/
/*  ARTICLE ADVICES  */
/*********************/

.product-review-slide-container {
    height: 100%;
    margin-bottom: 20px;
    @media (min-width: 992px) {
        margin-bottom: 0;
    }

    .product-info-review {
        min-height: 201px;
        @media (min-width: 992px) {
            min-height: 105px;
        }
    }

    .article-media {
        display: flex;
        flex-wrap: wrap;
        @media (min-width: 992px) {
            flex-wrap: nowrap;
        }
        .container-info {
            padding-bottom: 15px;
            flex-basis: 100%;
            order: 2;
            @media (min-width: 992px) {
                flex-basis: 60%;
                padding-right: 15px;
                padding-bottom: 15px;
                order: 1;
            }
        }
        .container-img {
            flex-basis: 100%;
            margin-bottom: 20px;
            order: 1;
            @media (min-width: 992px) {
                flex-basis: 40%;
                margin-bottom: 0px;
                order: 2;
            }
        }
        .SVDv3_produit_modele {
            font-size: 18px;
            text-transform: uppercase;
            margin-bottom: 10px;
        }
        .SVDv3_produit_basket_description {
            color: #999;
            font-weight: normal;
            margin-bottom: 10px;
        }
        .SVDv3_produit_modele,
        .SVDv3_produit_basket_description {
            font-weight: 700;
        }
        .SVDv3_produit_basket_description,
        .SVDv3_produit_description {
            font-size: 14px;
            line-height: normal;
        }
    }

    .container-img {
        width: 100%;
        text-align: center;
        padding-bottom: 10px;
        img {
            max-height: 95px;
            display: inline-block;
        }
    }

    .container-info,
    .container-img {
        align-self: center;
    }
}

.product-review {
    position: relative;
    position: -webkit-sticky;
    position: sticky;
    top: 20px;
    &.fixed {
        @media (min-width: 992px) {
            position: fixed;
            top: 20px;
            width: 391px;
        }
        @media (min-width: 1365px) {
            position: fixed;
            top: 20px;
            width: 400px;
        }
    }

    .SVDv3_bouton_gris {
        font-size: 14px;
        margin: 0 auto;
        padding: 13px;
        display: block;
        font-weight: 700;
        text-transform: uppercase;
        cursor: pointer;
        border-radius: 4px;
        width: 100%;
        border: 2px solid #444;
        color: #444;
        background: transparent;
        &:hover {
            background: $sv-color-light;
        }
    }

    .container-info {
        .SVDv3_produit_basket_description {
            font-weight: 700;
        }
    }
}
.container-reviews {
    .product-reviews {
        list-style: none;
    }
}

.order-reviews {
    order: 2;
    @media (min-width: 640px) {
        order: 1;
    }

    #product-questions-list.questions-answers .row.product-review-item {
        border-bottom: 1px solid #d0d0d0;
    }

    #product-questions-list.questions-answers .product-review-item:last-child,
    .product-reviews li:last-child .product-review-item {
        border-bottom: 0px;
    }

    .product-review-item {
        h5 {
            font-size: 16px;
            font-weight: 700;
            line-height: 22px;
            margin: 0 0 10px 0;
            padding: 0;
            color: #444;
            background: none;
        }
    }

    .global-stats {
        .progress {
            min-width: 75px;
        }
    }

    .progress-bar-title,
    .score-number {
        font-size: 14px;
    }
}

.order-product {
    order: 1;
    @media (min-width: 640px) {
        order: 2;
    }
}

.product-reviews-page-app-container {
    .global-stats {
        border: 3px solid $sv-color-light;
    }
}

.product-reviews-page-app {
    min-height: 550px;
    @media (min-width: 640px) {
        min-height: 398px;
    }
    @media (min-width: 992px) {
        min-height: 267px;
    }
}

.product-header-reviews-questions {
    padding-bottom: 30px;
    margin-bottom: 30px;
}

.score-item-row .icon-questions {
    margin-left: 25px;
    flex-shrink: 0;
}

.questions-product-answers-item {
    display: none;
    &.active {
        display: block;
    }
}

/*********************/
/*  ARTICLE ADVICES  */
/*********************/

.img-france-marker {
    width: 119px;
    height: 119px;
}

/*****************************/
/*  MODAL FIND STORE NEARBY  */
/*****************************/

.sweet_container_store_nearby {
    @media (min-width: 992px) {
        overflow-x: initial;
        overflow-y: initial;
    }
}

.container-modal-find-store {
    .SVDv3_texteRouge,
    .SVDv3_texteBleu {
        margin-top: 10px;
        margin-bottom: 0;
        text-align: left;
    }
}

.find-store-nearby {
    font-weight: 500;
    font-size: 13px;
    .form {
        width: 100%;
        display: flex;
        margin-top: 15px;
        flex-wrap: wrap;
        @media (min-width: 992px) {
            margin-top: 30px;
            flex-wrap: nowrap;
        }

        .container-input {
            flex-basis: 100%;
            position: relative;
            input {
                border: 1px solid $sv-border-color-base;
                height: 35px;
                line-height: 33px;
                padding: 10px;
                width: 100%;
                display: block;
                font-size: 14px;

                &::placeholder {
                    font-size: 14px;
                }
            }
        }

        .button-find {
            height: 35px;
            line-height: 35px;
            text-align: center;
            text-transform: uppercase;
            color: #fff;
            flex-basis: 100%;
            background: $sv-color-primary;
            transition: background 0.2s ease;

            &:disabled {
                background: lighten($sv-color-primary, 30%);
            }
            @media (min-width: 992px) {
                flex-basis: 140px;
            }
        }
    }

    .list-stores {
        list-style: none;
        margin-top: 15px;
        margin-bottom: 0;
        @media (min-width: 992px) {
            margin-top: 30px;
        }
        li {
            border-top: 1px solid #d0d0d0;
        }
    }

    .container-store-info {
        display: flex;
        align-items: center;
        padding: 22px 0px;
        font-size: 12px;
        .content-address {
            flex-basis: 42%;
            padding-left: 5px;
        }
        .content-distance {
            flex-basis: 34%;
        }
        .content-free-withdrawal {
            flex-basis: 25%;
        }

        .content-address {
            p {
                margin-bottom: 0;
                line-height: 22px;
                text-align: left;
            }
        }
    }

    .content-free-withdrawal,
    .content-distance,
    .store-name {
        font-weight: bold;
    }

    .phone {
        color: $sv-color-primary;
        margin-left: 5px;
    }

    .icon-phone-store,
    .phone {
        display: inline-block;
        vertical-align: middle;
    }

    .store-name {
        text-transform: uppercase;
    }
}

.icon-phone-store {
    width: 14px;
    height: 14px;
    display: inline-block;
    background: transparent url('https://image.son-video.com/images/ui/icons-svg/icon-phone.svg') center center/14px
        14px no-repeat;
}

/************************/
/*  ASSOCIATED PRODUCT  */
/************************/

.SVDv3_produitsAssocies {
    margin: 0;
    display: flex;
    list-style: none;
    width: 100%;
    flex-wrap: wrap;

    li {
        flex-basis: 100%;
        text-align: center;
        border-bottom: 0;

        & + li {
            &:before {
                content: '';
                width: 70%;
                height: 1px;
                background: #f0f0f0;
                position: absolute;
                top: 0px;
                left: 50%;
                transform: translateX(-50%);
            }
            @media (min-width: 640px) {
                &:before {
                    display: none;
                }
            }
        }

        @media (min-width: 640px) {
            flex-basis: 50%;
        }

        @media (min-width: 992px) {
            flex-basis: 25%;
        }

        a {
            text-align: left;
        }
    }

    ul {
        width: 100%;
    }

    .link-product-element {
        display: block;
        padding: 0px 10px;
    }
}

.SVDv3_list_products {
    padding-top: 40px;
    margin-bottom: 40px;
    border-top: 1px solid $sv-border-color-base;

    .SVDv3_produit_modele {
        margin-bottom: 5px;
        text-align: center;
    }

    .SVDv3_produit_description {
        color: $sv-color-grey;
        text-align: center;
        margin-bottom: 0;
    }

    .SVDv3_produit_image {
        width: 100%;
        text-align: center;
        height: 140px;
        display: flex;
        justify-content: center;
        @media (min-width: 992px) {
            margin-bottom: 30px;
        }

        img {
            max-width: 140px;
            max-height: 140px;
            display: inline-block;
            align-self: center;
        }
    }

    .SVDv3_zonePrix_prix {
        text-align: center;
    }

    .SVDv3_produitAssocie {
        padding-top: 15px;
        padding-bottom: 45px;
        position: relative;
        font-size: 14px;
        @media (min-width: 992px) {
            margin-bottom: 30px;
        }

        .SVDv3_bouton_ajoutPanier {
            position: static;
            bottom: auto;
            right: auto;
        }

        .SVDv3_produit_boutonCommander {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
        }

        .SVDv3_dispo {
            width: 100%;
            text-align: center;
            color: $sv-color-grey;
            cursor: pointer;
            margin-bottom: 5px;
        }
    }
}

.title-associated-products {
    font-size: 20px;
}

/************************/
/*   ADVISED ARTICLES   */
/************************/

.advised_articles {
    clear: both;
    float: none;
}

/**************************************/
/*   APPELS BOUTIQUES FICHE ARTICLE   */
/**************************************/

.list-store-calls {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    margin-bottom: 0;
    margin-top: 40px;

    li {
        flex-basis: 100%;
        padding-right: 0px;
        box-sizing: border-box;
        margin-bottom: 20px;
        @media (min-width: 640px) {
            flex-basis: 50%;
            padding-right: 30px;
            &:nth-child(even) {
                padding-right: 0px;
            }
        }
        @media (min-width: 992px) {
            flex-basis: 33.33%;
            &:nth-child(even) {
                padding-right: 30px;
            }
            &:nth-child(3n) {
                padding-right: 0px;
            }
        }
        @media (min-width: 1366px) {
            flex-basis: 25%;
            padding-right: 30px;
            &:nth-child(3n) {
                padding-right: 30px;
            }
            &:nth-child(4n) {
                padding-right: 0px;
            }
        }
    }

    a {
        display: flex;
        transition: background 0.2s ease;
        &:hover {
            background: $sv-color-light;
            p {
                color: #444;
            }
            .store-call-button {
                &:after {
                    transform: translateX(4px);
                    color: #000;
                }
            }
        }
    }

    .content-img {
        flex-basis: 100px;
        flex-shrink: 0;
        height: 100px;
        background: $sv-color-light;
    }

    .content-text {
        flex-basis: 100%;
        padding-left: 15px;
        align-self: center;
    }

    p {
        margin: 0;
        transition: color 0.2s ease;
    }

    .content-img-background {
        background-position: bottom 0px right 0px;
        height: 100px;
        background-size: cover;
    }

    .store-call-title {
        font-size: 16px;
        color: $sv-color-grey;
        font-weight: bold;
        margin-bottom: 6px;
    }

    .store-call-content,
    .store-call-button {
        font-size: 13px;
    }

    .store-call-button {
        color: $sv-color-grey-lighter;
        position: relative;
        span {
            display: inline-block;
            vertical-align: middle;
            &:first-letter {
                text-transform: uppercase;
            }
        }
        &:after {
            font-family: 'icomoon-svd';
            display: inline-block;
            content: '\e902';
            color: $sv-color-grey-lighter;
            font-weight: 400;
            font-size: 9px;
            -webkit-transition: all 0.3s ease-out;
            transition: all 0.3s ease-out;
            position: relative;
            top: 2px;
            margin-left: 10px;
        }
    }

    .store-call-content {
        color: $sv-color-grey;
        margin-bottom: 6px;
        &:first-letter {
            text-transform: uppercase;
        }
    }
}

/********************/
/*  LIST DOCUMENTS  */
/********************/
.SVDv3_article_element {
    .list-documents {
        list-style: none;
        .document {
            display: block;
        }

        li {
            padding-left: 0;
            &:before {
                display: none;
            }
        }
        .txt,
        .icon-document {
            display: inline-block;
            vertical-align: middle;
        }

        .txt {
            margin-left: 10px;
        }
    }
}

.icon-document {
    display: inline-block;
    width: 12px;
    height: 16px;
    background: transparent url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat -414px -106px;
}

/*******************************/
/*  LIST MAGASIN DE PROXIMITE  */
/*******************************/

.postal-code {
    margin-bottom: 0;
    position: absolute;
    top: 34px;
    left: 0;
    right: 0;
    border: 1px solid #e0e0e0;
    max-height: 149px;
    overflow-y: auto;
    z-index: 10;
    text-align: left;
    li {
        background: #fff;
        list-style: none;
        padding: 10px;
        transition: all 0.2s ease;
        cursor: pointer;
        &:hover {
            background: $sv-color-light;
        }
        & + li {
            border-top: 1px solid #e0e0e0;
        }
    }
}

/***************************/
/*  ARTICLE REPAIRABILITY  */
/***************************/
#repairability-index {
    margin-bottom: 30px;
    margin-right: 10px;
}

// red by default
.icon-sprite-repairability-index-picto {
    width: 94px;
    height: 56px;
    position: relative;

    background: url('https://image.son-video.com/images/ui/uiV3/icones/repairability_index_picto.svg') no-repeat 0 0 /
        cover;

    &.orange {
        background-position: -94px 0px;
    }

    &.yellow {
        background-position: -188px 0px;
    }

    &.light-green {
        background-position: -282px 0px;
    }

    &.green {
        background-position: -376px 0px;
    }

    .ct-txt {
        font-family: 'Open Sans', sans-serif;
        color: #444;
        position: absolute;
        top: 0;
        right: 0;
        font-size: 26px;
        font-weight: bold;
        width: 47px;
        height: 47px;
        text-align: center;
        line-height: 47px;
    }
}

/******************************/
/*  ARTICLE ENERGY LABELLING  */
/******************************/

// "a" by default
.icon-sprite-energy-labelling-picto {
    width: 87px;
    height: 48px;
    position: relative;

    background: url('https://image.son-video.com/images/ui/uiV3/icones/classe_eco_picto_plain.svg') no-repeat 0 0 /
        cover;

    &.label-b {
        background-position: -88px 0px;
    }

    &.label-c {
        background-position: -176px 0px;
    }

    &.label-d {
        background-position: -263px 0px;
    }

    &.label-e {
        background-position: -351px 0px;
    }

    &.label-f {
        background-position: -438px 0px;
    }

    &.label-g {
        background-position: -525px 0px;
    }

    &[data-energy-label] {
        cursor: pointer;
    }

    .ct-txt {
        text-transform: uppercase;
        font-family: 'Open Sans', sans-serif;
        color: #fff;
        position: absolute;
        top: 0;
        right: 17px;
        font-size: 26px;
        font-weight: bold;
        width: 47px;
        height: 47px;
        text-align: center;
        line-height: 47px;
    }
}

.sustainability-picture {
    height: 120px;
    clip-path: inset(12px 0 0 0);
    margin: -30px -10px -12px -10px;
}

.side-panel-container {
    .energy-label-details {
        @media (min-width: 640px) {
            width: 420px;
        }
    }
    .second-life-offers {
        @media (min-width: 640px) {
            width: 450px;
        }
    }
}

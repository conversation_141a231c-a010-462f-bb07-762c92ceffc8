.breadcrumb {
    padding: 20px 0;
    line-height: 20px;
    font-size: 13px;
    color: #444;

    @media (min-width: 992px) {
        padding: 10px 0;
    }

    ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-content: flex-start;
        align-items: flex-start;
        height: 20px;

        li {
            flex-direction: row;
            line-height: 20px;
            padding-left: 10px;
            gap: 10px;
            a {
                color: #444;
                transition: color 0.3s;

                &:hover {
                    color: #3366cc;
                }
            }

            @media (max-width: 992px) {
                &:nth-last-child(2) {
                    display: flex;
                }
                a {
                    display: flex;
                    flex-direction: row;
                    gap: 10px;
                    color: #444;
                    transition: color 0.3s;

                    &:hover {
                        color: #3366cc;
                    }
                    &:before {
                        display: flex;
                        flex-direction: row;
                        content: '';
                        width: 10px;
                        height: 20px;
                        background: transparent
                            url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat -41px -70px;
                    }
                }

                &:not(a) {
                    display: none;
                }
                & a[href='/'] {
                    display: none;
                }
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-content: flex-start;
                align-items: flex-start;
                position: relative;
                left: -10px;
            }
            @media (min-width: 992px) {
                display: flex;

                &:before {
                    display: flex;
                    flex-direction: row;
                    content: '';
                    width: 10px;
                    height: 20px;
                    background: transparent url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg')
                        no-repeat -41px -70px;
                }

                .breadcrumb-home-txt {
                    display: none;
                }

                &.breadcrumb-home {
                    padding-left: 0;

                    a {
                        display: block;
                        width: 12px;
                        height: 20px;
                        background: transparent
                            url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat -6px -70px;
                    }

                    &:before {
                        display: none;
                    }
                }
                &:before {
                    transform: rotate(180deg);
                }
            }
        }
    }
}

.body-basket .breadcrumb {
    display: none;

    @media (min-width: 992px) {
        display: block;
    }
}

// fil ariane dans masthead

.SVDv3_masthead > .SVDv3_masthead_wrapper > .breadcrumb {
    position: absolute;
    top: 0;
    left: 0;
}

.SVDv3_masthead_black .breadcrumb {
    color: #fff;

    ul li {
        &:before {
            background-position: -41px -3px;
        }

        @media (min-width: 992px) {
            &.breadcrumb-home a {
                background-position: -6px -3px;
            }

            &:before {
                background-position: -75px -3px;
            }
        }

        a {
            color: #fff;

            &:hover {
                color: #f0f0f0;
            }
        }
    }
}

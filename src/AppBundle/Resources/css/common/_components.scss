/*****************************************/
/*               LINK ARROW              */
/*****************************************/

.link-arrow {
    color: $sv-link-color;
    display: inline-flex;
    align-items: center;

    &.link-arrow-after:after,
    &.link-arrow-before:before {
        display: inline-block;
        content: '';
        width: 16px;
        height: 16px;
    }

    &.link-arrow-after:after {
        margin-left: 5px;
    }

    &.link-arrow-before:before {
        margin-right: 5px;
    }

    &.link-arrow-after.link-arrow-right:after {
        background: transparent url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat -70px -106px;
    }

    &.link-arrow-after.link-arrow-left:after {
        background: transparent url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat -40px -106px;
    }

    &.link-arrow-before.link-arrow-right:before {
        background: transparent url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat -70px -106px;
    }

    &.link-arrow-before.link-arrow-left:before {
        background: transparent url('https://image.son-video.com/images/ui/icons-svg/layout-sprites.svg') no-repeat -40px -106px;
    }
}

/**************************************/
/*              TAG LIST              */
/**************************************/

ul.tag-list {
    padding: 0;
    margin: 0;
    font-size: 0;
    list-style: none;

    li {
        display: inline-block;
        padding: 0;
        margin-bottom: 5px;
        margin-right: 5px;

        &:before {
            display: none;
        }

        &:last-child {
            margin-right: 0 !important;
        }
    }

    &.tag-list-lg li {
        margin-bottom: 10px;
        margin-right: 10px;
    }
}

.tag-list-wrapper {
    &.tag-list-inline {
        position: relative;

        &:before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #f0f0f0;
        }

        ul.tag-list {
            background-color: #fff;
            padding: 0 20px;
            display: inline-block;
            position: relative;
            margin: 0 auto;

            li {
                display: block;

                @media (max-width: 639px) {
                    margin-right: 0 !important;
                }

                &:last-child {
                    margin-bottom: 0 !important;
                }

                @media (min-width: 640px) {
                    margin-bottom: 0;
                    display: inline-block;
                }

                .btn {
                    width: 240px;
                    max-width: 100%;

                    @media (min-width: 640px) {
                        width: auto;
                    }
                }
            }
        }
    }
}

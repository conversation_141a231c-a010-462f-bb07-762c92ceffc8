.full-width-dropdown {
    .overlay {
        z-index: 30;
        background-color: #000000;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        animation: overlay-opacity 0.2s ease-in-out;
    }

    .content {
        z-index: 90;
        position: absolute;
        left: 0;
        right: 0;

        > div {
            color: #101214;
            background-color: #ffffff;
            overflow: hidden;
            box-shadow: 0 0 #0000, 0 0 #0000, 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
        }
    }
}
@keyframes overlay-opacity {
    from {
        opacity: 0;
    }
    to {
        opacity: 66%;
    }
}

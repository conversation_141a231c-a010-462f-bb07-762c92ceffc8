.shipment-content {
    display: inline-block;

    p {
        line-height: 17px;
        font-size: 13px;
        margin-bottom: 0;
        color: #444;
    }
    .guaranteed-delivery-before-christmas {
        color: $sv-christmas;
        font-weight: 700;
    }
}
@media (max-width: 480px) {
    .shipment-content {
        display: block;
    }
}

.shipment-method .logo-legend {
    line-height: 12px;
    font-size: 10px;
    margin-bottom: 0;
    color: #999;
}
.shipment-border {
    border-top: 2px solid #d0d0d0;
}

.shipment-price {
    font-weight: bold;
    &.free {
        text-transform: uppercase;
        color: #393;
    }
}

@media (max-width: 575px) {
    .shipment-method .basket-col-1 {
        text-align: center;
    }
}

.img_payment_detail {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.container-button-text-loader {
    display: block;
    width: 100%;
    position: relative;
    span.hidden-opacity {
        opacity: 0;
    }
    .loader-button {
        display: inline-block;
        position: absolute;
        width: 80px;
        height: 15px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        span {
            position: absolute;
            top: 50%;
            width: 13px;
            height: 13px;
            border-radius: 50%;
            background: #fff;
            animation-timing-function: cubic-bezier(0, 1, 1, 0);
            &:nth-child(1) {
                left: 8px;
                animation: loader-button1 0.6s infinite;
            }
            &:nth-child(2) {
                left: 8px;
                animation: loader-button2 0.6s infinite;
            }
            &:nth-child(3) {
                left: 32px;
                animation: loader-button2 0.6s infinite;
            }
            &:nth-child(4) {
                left: 56px;
                animation: loader-button3 0.6s infinite;
            }
        }

        @keyframes loader-button1 {
            0% {
                transform: scale(0) translateY(-50%);
            }
            100% {
                transform: scale(1) translateY(-50%);
            }
        }
        @keyframes loader-button3 {
            0% {
                transform: scale(1) translateY(-50%);
            }
            100% {
                transform: scale(0) translateY(-50%);
            }
        }
        @keyframes loader-button2 {
            0% {
                transform: translate(0, -50%);
            }
            100% {
                transform: translate(24px, -50%);
            }
        }
    }
}

@function force-rgba($r, $g, $b, $a) {
    @return unquote("rgba(#{$r}, #{$g}, #{$b}, #{$a})");
}

.popover {
    z-index: 10000;
    margin-top: 5px;

    .popover-arrow {
        width: 0;
        height: 0;
        position: absolute;
        border: 5px solid transparent;
        border-top-width: 0;
        border-bottom-color: #d0d0d0;
        top: -5px;
        left: calc(50% - 5px);
        margin-top: 0;
        margin-bottom: 0;
    }

    .popover-inner {
        background: #fff;
        border: 2px solid #d0d0d0;
        border-radius: 5px;
        padding: 5px 10px 4px;
        box-shadow: 0 5px 10px force-rgba(0, 0, 0, 15%);

        .popover-group {
            display: flex;
            flex-direction: column;
            padding: 10px 8px 0 8px;

            .popover-group-item {
                font-weight: 400;
                font-size: 13px;
                color: hsl(0, 0%, 40%);
            }

            a.popover-group-item {
                padding-bottom: 14px;
                line-height: 1;

                &:hover {
                    text-decoration: underline;
                    color: hsl(0, 0%, 20%);
                }
            }
        }
    }
}

.account-popover {
    min-width: 180px;
}

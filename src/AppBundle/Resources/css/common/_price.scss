@import '../../../Resources/scss/style';

/**************************************/
/*              ZONE PRIX             */
/**************************************/

.SVDv3_zonePrix {
    position: relative;
    margin: 0 0 10px 0;
}

.SVDv3_zonePrix_prix {
    font-weight: 700;
    font-size: 28px;
    color: #000;
    margin: 0;
}
.reference-price {
    display: flex;
    flex-direction: column;
    gap: $space_10;
    .percent {
        font-family: Open Sans;
        font-size: $font_size_15;
        font-weight: $font_bold;
        line-height: $height_24;
        color: $color_white;
        .back {
            background-color: $color_blue_web_safe;
            padding: 1px 6px 2px 6px;
            text-wrap: nowrap;
        }
    }
    &.old {
        .crossed {
            font-size: $font_size_14;
            font-weight: $font_semi_bold;
            line-height: $height_24;
            color: $color_grey_typo;
            cursor: pointer;
            display: flex;
            align-items: center;
            &.article {
                font-size: $font_size_17;
                font-weight: $font_bold;
                line-height: $height_25;
            }
            @include media_min($media_lg) {
                margin-left: auto;
                margin-right: auto;
            }

            .line-through {
                background-image: linear-gradient(110deg, $color_grey_typo 50%, transparent 50%);
                background-repeat: no-repeat;
                background-size: 200% 2px;
                background-position: 0 11px;
                margin-right: 6px;
            }

            .confirm {
                display: inline-flex;
                font-size: 12px;
                line-height: 12px;
                width: 12px;
                height: 12px;
                color: $color_grey_border_enabled;
                border: solid 1px $color_grey_border_enabled;
                border-radius: 50px;
                align-items: center;
                justify-content: center;
                padding: .5px;
                box-sizing: content-box;
                scale: 0.8;
                &.article {
                    padding: 1px;
                }
            }
        }
    }
}

.SVDv3_zonePrix_second_life {
    color: $color_grey_typo;
    font-family: $font_open_sans;
    font-weight: $font_normal;
    line-height: $height_18;
}

.SVDv3_produitAssocie .SVDv3_zonePrix_prix,
.SVDv3_appelsB .SVDv3_zonePrix_prix,
.SVDv3_rayon_listingProduits_grille .SVDv3_zonePrix_prix,
.SVDv3_rayon_listingProduits_prix .SVDv3_zonePrix_prix,
.SVDv3_calculEcran .SVDv3_zonePrix_prix,
.SVDv3_zonePrix_longueur .SVDv3_zonePrix_prix {
    font-size: 20px;
    line-height: 1.2;
    margin: 0;
}

.SVDv3_reduction {
    font-size: 14px;
}

.SVDv3_zonePrix_ald {
    font-weight: 700;
    margin: 0;
    color: #444;
}

.SVDv3_zonePrix_ecotaxe {
    color: #999;
}

ul.SVDv3_zonePrix_ecotaxe {
    margin: 0;
}

.SVDv3_zonePrix_ecotaxe li {
    list-style-type: none;
}

.SVDv3_dispo {
    cursor: default;
    display: inline-block;
}

.SVDv3_dispo_stock {
    color: #339933;
    font-weight: 700;
    font-size: 14px;
}

.SVDv3_dispo_outofstock {
    color: #e2020d;
}
.SVDv3_dispo_unavailable {
    color: #d37700;
    font-weight: 700;
    font-size: 14px;
}

p.SVDv3_rayon_prixConfidentiel {
    font-size: 11px;
    font-weight: bold;
    color: #000;
    text-transform: uppercase;
    margin: 0;
    padding: 0 10px;
}

.SVDv3_produitAssocie p.SVDv3_rayon_prixConfidentiel,
.SVDv3_appelsB p.SVDv3_rayon_prixConfidentiel {
    font-size: 12px;
}

/**************************************/
/*            BLOC PRODUIT            */
/**************************************/

.SVDv3_produit_image {
    overflow: hidden;
}

.SVDv3_produit_modele {
    font-weight: bold;
}

/* btn cmd */

.SVDv3_produit_boutonCommander {
    margin: 0;
}

.SVDv3_produit_miseEnAvant {
    display: block;
    padding: 3px 0;
}

.SVDv3_produit_miseEnAvant img {
    vertical-align: bottom;
}

/* Icones/boutons avantages SVD - zone prix fiche produit */

.SVDv3_produit_avantageSVD {
    position: absolute;
    right: 0;
    bottom: 25px;
    margin: 0;
}

.SVDv3_zonePrix_pasDeclinaison .SVDv3_produit_avantageSVD {
    bottom: 57px;
}

.SVDv3_produit_avantageSVD_vignette {
    display: block;
    width: 105px;
    height: 19px;
    background: url('https://image.son-video.com/images/ui/uiV3/boutons/uiV3_boutons_zonePrix_avantages.gif') no-repeat;
    margin: 2px 0 0 0;
}

.SVDv3_produit_avantageSVD_vignette span {
    display: none;
}

.SVDv3_produit_avantageSVD_vignette_garantie1 {
    background-position: 0 0;
}

.SVDv3_produit_avantageSVD_vignette_garantie2 {
    background-position: 0 -21px;
}

.SVDv3_produit_avantageSVD_vignette_garantie3 {
    background-position: 0 -42px;
}

.SVDv3_produit_avantageSVD_vignette_garantie4 {
    background-position: 0 -63px;
}

.SVDv3_produit_avantageSVD_vignette_garantie5 {
    background-position: 0 -84px;
}

.SVDv3_produit_avantageSVD_vignette_garantie6 {
    background-position: 0 -105px;
}

.SVDv3_produit_avantageSVD_vignette_garantie7 {
    background-position: 0 -126px;
}

.SVDv3_produit_avantageSVD_vignette_garantie8 {
    background-position: 0 -147px;
}

.SVDv3_produit_avantageSVD_vignette_garantie9 {
    background-position: 0 -168px;
}

.SVDv3_produit_avantageSVD_vignette_garantie10 {
    background-position: 0 -189px;
}

.SVDv3_produit_avantageSVD_vignette_garantie20 {
    background-position: 0 -210px;
}

.SVDv3_produit_avantageSVD_vignette_garantie25 {
    background-position: 0 -231px;
}

.SVDv3_produit_avantageSVD_vignette_garantie99 {
    background-position: 0 -252px;
}

.SVDv3_produit_avantageSVD_vignette_livraison {
    background-position: 0 -273px;
}

.SVDv3_produit_avantageSVD_vignette_garantieSvd {
    background-position: 0 -294px;
    height: 24px;
}

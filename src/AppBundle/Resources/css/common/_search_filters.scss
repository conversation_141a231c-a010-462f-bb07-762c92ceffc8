.disabled-filter {
    opacity: 0.5;
    pointer-events: none;
}

.internal-filter {
    input {
        display: block;
        width: 100%;
        padding-left: 30px;
        transition: border 0.2s ease;
        font-size: 13px;

        @media (max-width: 991px) {
            .internal-filter input {
                padding: 7px 30px;
            }
        }

        &:focus {
            border: 1px solid #36c;
        }
    }

    span:after {
        content: '';
        position: absolute;
        background: url('https://image.son-video.com/images/ui/uiV3/icones/uiV3_headfoot_icon_sprite.png') no-repeat -217px
            10px;
        width: 13px;
        height: 30px;
        top: auto;
        left: 40px;
        z-index: 80;

        @media (max-width: 991px) {
            background: transparent
                url('https://image.son-video.com/images/ui/uiV3/responsive/uiV3_resp_head_icons_recherche.png')
                no-repeat 0 center;
            background-size: 18px 18px;
            width: 18px;
            top: 36px;
        }
    }
}

.search-remaining-articles {
    margin: 20px 0;
}

.article-pagination {
    margin: 20px 0;
    text-align: center;
    white-space: nowrap;

    li {
        display: inline-block;
        font-size: 14px;
        line-height: 22px;
        padding: 5px 10px;
        border-radius: 4px;
        margin: 0 2px;

        @include media_min($media_md) {
            padding: 10px 20px;
        }

        a {
            color: $color_dark_default;
        }
    }

    .pagination-item {
        transition: background $animation_duration;

        &:hover {
            background: $color_grey_light;
        }
        &.hidden {
            display: none;
        }
        &.active {
            background: $color_blue_web_safe;
            color: $color_white;
        }

        img {
            display: inline-block;
        }
    }

    .ellipsis {
        padding: 5px;
        margin: 0 2px;
        color: $color_grey_default;
        @include media_min($media_md) {
            padding: 10px 10px;
        }
    }
}

.SVDv3_colonne_element.search-filters {
    line-height: 22px;
    padding: 0;

    @media (min-width: 1365px) {
        font-size: 14px;
        line-height: 24px;
    }
}

.search-filters {
    #mobile-toggle-filters {
        display: none;
    }

    #search-filters-block {
        display: block;

        .search-filters-checkboxes {
            padding-bottom: 0;
        }

        .search-filters-block:first-child {
            @media (max-width: 991px) {
                border-top: 1px solid #d0d0d0;
            }
        }
    }

    .search-filters-block {
        border-top: 1px solid #d0d0d0;

        &:first-child {
            border-top: none;
        }

        .search-filters-heading {
            padding: 15px 20px;
            transition: color 0.3s;

            & + .search-filters-body {
                padding-top: 0;
            }

            &.cursor-pointer:hover {
                color: #36c;
            }
        }

        .icon-chevron-bottom {
            transition: transform 0.3s;
        }

        .search-filters-body {
            padding: 15px 20px;
        }

        &.is-open .btn .icon-chevron-bottom,
        &.is-open .search-filters-heading .icon-chevron-bottom {
            transform: rotate(180deg);
        }

        .search-filters-result {
            font-size: 18px;
            line-height: 26px;
            &.loading {
                height: 46px;
                line-height: 46px;
            }
        }

        .search-filters-remove {
            font-size: 12px;
            line-height: 20px;
            color: #999;
            text-decoration: underline;
        }

        .search-filters-title {
            text-transform: uppercase;
            font-weight: 700;
            display: block;
        }

        .search-filters-selected {
            display: inline-block;
            margin: 0 5px 5px 0;
            border: 1px solid #d0d0d0;
            border-radius: 15px;
            padding: 5px 30px 5px 10px;
            cursor: pointer;
            box-sizing: border-box;
            height: 29px;
            line-height: 16px;
            transition: background 0.35s;
            position: relative;

            &:hover {
                background: #f0f0f0;
            }

            > span {
                display: inline-block;
                white-space: nowrap;
                overflow: hidden;
                max-width: 215px;
                text-overflow: ellipsis;
            }

            .score-item {
                height: 16px;
            }

            i {
                width: 12px;
                height: 12px;
                color: #444;
                position: absolute;
                top: 7px;
                right: 10px;
            }
        }

        .search-filters-checkboxes {
            list-style: none;
            padding-bottom: 10px;
            margin: -5px 0 0 0;

            li {
                position: relative;

                .filter-count {
                    color: $sv-color-grey-lighter;
                }

                input[type='checkbox'] {
                    position: absolute;
                    opacity: 0;
                    cursor: pointer;
                    height: 0;
                    width: 0;

                    &:checked {
                        & ~ .checkmark {
                            background: $sv-color-primary-lighter;
                            border: 1px solid $sv-color-primary-lighter;
                            &:after {
                                display: block;
                            }
                        }
                    }
                }

                .checkmark {
                    position: absolute;
                    top: 4px;
                    left: 0;
                    height: 12px;
                    width: 12px;
                    border-radius: 2px;
                    border: 1px solid #d0d0d0;
                    transition: background 0.35s;
                    box-sizing: content-box;

                    &:after {
                        content: '';
                        position: absolute;
                        display: none;
                        left: 4px;
                        top: 2px;
                        width: 2px;
                        height: 5px;
                        border: solid white;
                        border-width: 0 2px 2px 0;
                        transform: rotate(45deg);
                    }

                    & + span {
                        margin-left: 21px;
                    }
                }
            }

            li.disabled-filter {
                opacity: 1;
                color: #d0d0d0;

                .filter-count {
                    color: #d0d0d0;
                }

                .product-score {
                    opacity: 0.5;
                }

                input[type='checkbox'] {
                    & ~ .checkmark {
                        background: #d0d0d0;
                        border: 1px solid #d0d0d0;

                        &:after {
                            display: none;
                        }
                    }
                }
            }
        }

        .filter-collapse {
            margin: 15px 0 0 0;
            height: 30px;
            background: #f0f0f0;
            border-radius: 3px;

            .expand-filters {
                width: 100%;
                height: 30px;
                display: flex;
                justify-content: center;
            }
        }

        form {
            input {
                font-family: 'Open Sans', sans-serif;
                font-size: 13px !important;
                width: 72px;
                height: 32px;
                text-align: right;
                margin: 0 4px;
            }

            button {
                font-family: 'Open Sans', sans-serif;
                padding: 0;
                height: 30px;
                width: 40px;
                font-size: 11px;
                margin-left: 4px;
                display: inline;
                vertical-align: bottom;
            }

            #input-format-min,
            #input-format-max {
                width: 68px;
                text-align: center;
                transition: border 0.2s ease;

                &:focus {
                    border: 1px solid #36c;
                }
            }
        }

        .internal-filter {
            margin-bottom: 20px;

            input {
                height: 32px;
            }

            @media (max-width: 991px) {
                span:after {
                    background: transparent
                        url('https://image.son-video.com/images/ui/uiV3/icones/uiV3_headfoot_icon_sprite.png') no-repeat -217px
                        10px;
                    background-size: auto;
                    width: 13px;
                    top: auto;
                }
            }
        }
    }

    /*Styles for animations for expanded filters */

    .search-filters-checkboxes {
        max-height: 192px;
        overflow: hidden;
        transition: max-height 0.45s cubic-bezier(1, -0.17, 0, 1.07);

        li + span {
            display: none;
        }

        &.filters_expanded {
            max-height: none;

            li + span {
                display: block;
            }
        }
    }

    .expand-filters {
        transition: background 0.35s;

        &:active {
            background: #d0d0d0;
        }
    }
}

.btn-toggle-filters {
    width: 265px;
    margin: 1rem auto;
}

@media (max-width: 991px) {
    .search-filters {
        #search-filters-block {
            display: none;
        }

        #mobile-toggle-filters {
            display: block;
        }
    }

    #search_app .SVDv3_colonnes_colonne_gauche {
        margin-top: 20px;
    }
}

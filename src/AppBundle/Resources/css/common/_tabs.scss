ul.ui-tabs-nav li {
    line-height: 38px;
}

.tabs-section {
    position: relative;

    &.fixed {
        position: fixed;
        right: 0;
        z-index: 99;
        transition: all 0.2s ease;
        background: #f0f0f0;
        width: 100%;
        @media (max-width: 767px) {
            top: 60px;
            left: 0;
            z-index: 100;
            padding: 0 45px;
        }
        @media (min-width: 767px), (orientation: landscape) {
            top: 0;
            padding: 0 45px;
        }

        .tabs-section-tabs-list {
            padding: 0;
            margin: 0 !important;
            @media (min-width: 992px) {
                width: 980px;
                left: calc(50% - 490px);
                position: relative;
            }
            @media (min-width: 1365px) {
                width: 980px;
                left: calc(50% - 630px);
                position: relative;
            }
        }
    }

    &:before {
        content: '';
        position: absolute;
        bottom: 0px;
        right: 0px;
        left: 0px;
        height: 1px;
        background: #d0d0d0;
    }

    .left,
    .right {
        font-family: 'icomoon-svd' !important;
        speak: none;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        display: inline-block;
        position: absolute;
        top: 7px;
        width: 40px;
        height: 45px;
        text-align: center;
        line-height: 40px;
    }
    .left {
        left: -5px;
        &:after {
            content: '\e901';
        }
    }
    .right {
        right: -5px;
        &:after {
            content: '\e902';
        }
    }

    .arrow-tab {
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: auto;
        display: block;
        z-index: 9;
        @media (min-width: 640px) {
            display: none;
        }
    }
}

.tabs-section-item {
    padding-top: 30px;
}

.tabs-section-tabs-list {
    margin: 0 40px 10px;
    overflow-x: auto;
    white-space: nowrap;
    list-style-type: none;
    position: relative;

    @media (min-width: 640px) {
        padding: 0;
        margin: 0;
    }

    @media (min-width: 992px) {
        overflow-x: initial;
        white-space: normal;
    }
}

.tabs-section-tabs-item {
    display: inline-block;
    white-space: nowrap;
    position: relative;
    z-index: 2;
    font-weight: 600;
    .SVDv3_titres_noStyle {
        margin-bottom: 0;
    }
    &:not(:last-child) {
        margin-right: 15px;
    }
    a {
        display: inline-block;
        padding: 15px 0;
        color: #444;
        text-decoration: none;
        &:hover {
            text-decoration: none;
            color: inherit;
        }
    }
    &.active,
    &.ui-tabs-selected {
        border-bottom: 1px solid #3366cc;
        a {
            color: #3366cc;
        }
    }
}

.tabs-section-body {
    padding: 30px 0;
}

.tabs-section-height {
    height: 78px;
    background: #fff;
    @media (min-width: 640px) {
        height: 50px;
    }
}

.tabs-section-list div.tab_content_wrapper {
    margin-bottom: 30px;
}

.vertical-tabs {
    ul {
        border: 1px solid #d0d0d0;
        list-style: none;
        text-transform: uppercase;

        > li {
            border-bottom: 1px solid #d0d0d0;

            &:last-child {
                border-bottom: none;
            }

            > a {
                color: #666;
                display: block;
                font-weight: 700;
                font-size: 12px;
                outline: 0 none;
                padding: 7px 10px;
                text-decoration: none;
                white-space: nowrap;

                .current {
                    background: #666 none repeat scroll 0 0;
                    color: #fff;
                }

                .disabled > a {
                    background: #eee none repeat scroll 0 0;
                    user-select: none;
                    cursor: not-allowed;
                }
            }
        }
    }
}

.container-tab-title {
    border-bottom: 1px solid $sv-border-color-base;
    margin-bottom: 30px;
    .tab-title {
        font-size: 26px;
        display: inline-block;
        color: #36c;
        margin: 0;
        position: relative;
        padding: 15px 0px;
        font-weight: 700;
        &:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 1px;
            background: $sv-color-primary;
        }
    }
}

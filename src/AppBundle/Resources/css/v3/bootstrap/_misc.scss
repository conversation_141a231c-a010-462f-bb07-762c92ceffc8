/* Bootstrap container fixes */

.container,
.container *,
.container-fluid,
.container-fluid * {
    box-sizing: border-box;
}

.SVDv3_content_content .container {
    padding-left: 0;
    padding-right: 0;
}

/* Extend bootstrap grid gutters */

$grid-gutter-width: 30px;
$grid-gutter-small: 7.5px;
$grid-gutter-large: 50px;

.row-no-gutter {
    margin: 0;
}

.row-no-gutter>* {
    padding: 0;
}

.row-small-gutter {
    margin-left: -$grid-gutter-small;
    margin-right: -$grid-gutter-small;
}

.row-small-gutter>* {
    padding-left: $grid-gutter-small;
    padding-right: $grid-gutter-small;
}

.row-large-gutter {
    margin-left: -$grid-gutter-large;
    margin-right: -$grid-gutter-large;
}

.row-large-gutter>* {
    padding-left: $grid-gutter-large;
    padding-right: $grid-gutter-large;
}

/*  Sections */

h2.section-title {
    color: #555;
    font-size: 22px;
    font-weight: 700;

    &.mr-auto {
        margin: 0;
        padding: 0;
    }
}

.section-box {
    padding: 20px;
    margin-bottom: 20px;

    .form-item {
        margin-bottom: 20px;
    }

    .author {
        font-size: 14px;
        margin-bottom: 10px;

        .timestamp {
            color: #999;
        }
    }

    .message {
        margin-bottom: 20px;
        color: #666;
        font-size: 14px;
        line-height: 1.5;
    }
}

.section-bordered {
    border: 1px solid #d0d0d0;
    border-radius: 5px;
    margin-bottom: 5px;
    padding: 20px!important;

    &.has-errors {
        border-color: #c90101;
    }
}

.section-with-background {
    background-color: #EEE;
}

.section-sub-title {
    color: #555;
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px;
    padding: 0;
}

.section-agreement {
    padding: 20px 0 18px;

    > .d-flex {
        margin-bottom: 2px;
    }

    .tiny-column {
        width: 20px;
    }
}

.with-top-separator {
    border-top: 1px solid #E6E6E6;
}

.push-bottom {
    display: block;
    margin-bottom: 20px;
}

.associated-products {
    &:not(:last-child) {
        margin: 0 0 20px;
    }

    .section-bordered {
        margin-bottom: 10px;
    }

    a {
        color: #666;
    }

    .associated-product-preview {
        max-width: 100%;
        max-height: 90px;
        overflow-wrap: break-word;

        img {
            max-width: 100%;
            max-height: 95px;
        }
    }

    .flex-grow {
        flex-grow: 1;
    }

    .text-on-bottom {
        font-weight: bold;
        font-size: 12px;
        font-family: "Open Sans", sans-serif;
    }

    .squared-placeholder {
        background-color: #FFFFFF;
        margin-right: 10px;
        padding: 10px;
    }
    .associated-product-title {
        width: 90px;
    }

    .clickable {
        touch-action: manipulation;
        cursor: pointer;
    }
}

.multiselect.product-picker {
    .multiselect__element {
        padding: 0;

        &::before {
            content: '';
        }
    }

    .option__image-wrapper {
        width: 90px;
        height: 90px;
        margin-right: 12px;

        .option__image {
            max-width: 100%;
            max-height: 100%;
        }
    }

    .multiselect__option--highlight {
        background-color: #466edc;
    }
}

.section-with-background {
    .squared-placeholder {
        border: 3px solid #D0D0D0;
    }
}

/* Utilities */

.gap-1 {
     gap: .25rem;
 }

.gap-2 {
     gap: .5rem;
 }

.gap-3 {
     gap: .75rem;
 }

@media (max-width: 991px)
{

    *
    {
        box-sizing: border-box;
    }

    body
    {
        -webkit-text-size-adjust: 100%;
    }

    #sfWebDebug{ display: none; }

    .SVDv3_colonnes_2colonnes1 .SVDv3_colonnes_colonne_gauche
    {
        display: none;
    }

    /* On retabli le background par defaut, en cas de background evenementiel */
    #SVDv3_head_container,
    #SVDv3_content_container
    {
        background: #fff none;
        padding: 0;
    }

    /**************************************/
    /* TYPOGRAPHIE / MISE EN FORME TEXTE  */
    /**************************************/
    .SVDv3_alignGauche
    {
        float: none;
    }

    .text-sm
    {
        font-size: 14px;
        line-height: 22px;
    }

    /**********************************/
    /*            CONTENU             */
    /**********************************/
    #SVDv3_content_container .SVDv3_content_content
    {
        margin: 0 auto;
        width: 90%; /* largeur responsive */
        max-width: 980px;
    }

    /**********************************/
    /*    RAYONS LISTING PRODUITS     */
    /**********************************/

    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_dispo p
    {
        margin-bottom: 10px;
    }

    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_dispo .label
    {
        display: inline-block;
    }

    #SVDv3_CmsRayon_orderBy
    {
        margin: 0;
        text-align: center;
    }

    /**********************************/
    /*            A RANGER            */
    /**********************************/
    .col_4_col img
    {
        display: block;
        margin: 0 auto;
    }

    /**********************************/
    /*          GRID COLUMNS          */
    /**********************************/
    /* Colonnes gauche / droite */
    .SVDv3_colonnes_2colonnes1 .SVDv3_colonnes_colonne_droite,
    .SVDv3_colonnes_2colonnes1 .SVDv3_colonnes_colonne_gauche
    {
        width: 100%;
        float: none;
        margin: 0 auto;
    }

    /*****************************************/
    /*                GRILLE                 */
    /*****************************************/

    .grid_container_12 .grid_row *[class*='col_'],
    .grid_container_10 .grid_row *[class*='col_']
    {
        width: 100% !important;
        margin-right: 0;
        margin-bottom: 20px;
        float: none;
    }

    *[class*='pull-'],
    *[class*='push-']
    {
        left: auto !important;
        right: auto !important;
    }

    *[class*='col-offset-']
    {
        margin-left: 0 !important;
    }

    /*****************************************/
    /*         A VERIFIER ET RANGER          */
    /*****************************************/

    #BVQANoQuestionsID a
    {
        background: none !important;
        border: 0;
    }

    /*****************************************/
    /*                TITLE                  */
    /*****************************************/
    .SVDv3_titres_noStyle
    {
        margin-top: 0 !important;
    }

    h1
    {
        background: none !important;
        border: 0;
    }

    .SVDv3_body_mod_PctCompte h1,
    .SVDv3_body_mod_PctCompte .SVDv3_content_content h2,
    .SVDv3_body_act_installationForm .SVDv3_boite_roundCorner h2,
    #comprendrePourChoisir h2,
    .SVDv3_panier_container:before,
    .SVDv3_mobile_navbar,
    .title-mobile-bg
    {
        display: block;
        height: auto;
        padding: 10px 5% !important;
        text-align: center !important;
        font-size: 14px !important;
        line-height: 18px !important;
        font-weight: bold !important;
        text-transform: uppercase;
        color: #fff !important;
        border: 0 !important;
        width: 111.1111% !important;
        margin-left: -5.55555% !important;
        border-radius: 0 !important;
    }

    .title-mobile-bg small
    {
        text-transform: none;
    }

    /* Titres Noirs */
    .SVDv3_body_mod_PctCompte h1,
    .SVDv3_body_mod_PctCompte .SVDv3_content_content h2,
    .SVDv3_body_act_installationForm .SVDv3_boite_roundCorner h2,
    #comprendrePourChoisir h2,
    .SVDv3_panier_container:before,
    .SVDv3_mobile_navbar,
    .title-mobile-bg-black
    {
        background: #000 none !important;
    }

    .SVDv3_content_content h2 span
    {
        display: inline;
        background: none;
        padding: 0;
    }

    .responsive-full-width
    {
        width: 111.1111% !important;
        margin-left: -5.55555% !important;
    }

    /* Titres Gris */
    #comprendrePourChoisir h2,
    .title-mobile-bg-grey
    {
        background-color: #666 !important;
    }

    /*.SVDv3_content_content h2,*/
    .SVDv3_produit_categorie a,
    .SVDv3_colonnes_colonne_gauche h3 span
    {
        padding: 0;
    }

    /*****************************************/
    /*            MODULE RECHERCHE           */
    /*****************************************/

    #Recherche_resultats_produits,
    #Recherche_resultats_marques,
    #Recherche_resultats_categorie
    {
        padding: 10px 0;
    }

    /*********************************/
    /*              RIB              */
    /*********************************/

    .rib-svd-domi,
    .rib-svd-iban
    {
        width: auto;
    }

    .rib-svd-number
    {
        padding: 0 2px;
    }

    /*****************************************/
    /*     GUIDES / SOMMAIRES CATEGORIES     */
    /*****************************************/

    .SVDv3_sommaireCategorie_cellule
    {
        width: 47.9591836734694%;
        margin-right: 4.08163265306122%;
        min-height: 280px;
    }

    .SVDv3_sommaireCategorie_cellule:nth-child(2n)
    {
        margin-right: 0;
    }

    .SVDv3_sommaireCategorie_cellule_image p
    {
        padding: 0 10px;
    }

    /* NUANCIER DES PLACAGES BOIS */

    .wood-color-chart-grid .bg-offwhite
    {
        background-color: #fff;
    }

    .wood-color-chart-grid .grid_row.bg-offwhite .wood-color-chart-item
    {
        border: 2px solid #f0f0f0;
    }

    .wood-color-chart-grid .grid_row:not(:first-child)
    {
        padding-top: 0;
    }

    /*****************************************/
    /*            FICHES PRODUIT             */
    /*****************************************/

    .body-article .SVDv3_colonnes_colonne_gauche
    {
        display: none;
    }

    .body-article #SVDv3_content_container .SVDv3_content_content
    {
        padding-bottom: 0;
    }

    /**********************************/
    /*             RAYONS             */
    /**********************************/
    .body-stand .SVDv3_colonnes_colonne_gauche,
    .body-stand #tabs
    {
        display: none;
    }

    /**********************************/
    /*            DOMAINES            */
    /**********************************/

    #SVDv3_universCategories
    {
        margin-bottom: 30px;
        color: #444;
    }

    .body-stands-level-1 h1.SVDv3_mobile_navbar
    {
        margin-bottom: 0;
    }

    /**********************************/
    /*     LISTING PRODUIT LIGNE      */
    /**********************************/
    .SVDv3_rayon_listingProduits_liste_ligne
    {
        display: block;
        border-bottom: 1px solid #d0d0d0;
        padding: 20px 0;
    }

    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_ref,
    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_photo,
    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_description,
    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_descriptionCourte,
    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_prix,
    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_dispo,
    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_premium_nonlog
    {
        border-right: none;
    }

    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_description p,
    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_descriptionCourte p,
    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_prix p.SVDv3_rayon_prixPremium
    {
        padding: 0;
    }

    #layout_v3 .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_description,
    #layout_v3 .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_descriptionCourte,
    #layout_v3 .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_prix,
    #layout_v3 .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_dispo,
    #layout_v3 .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_action,
    #layout_v3 .SVDv3_rayon_listingProduits_grille_ligne .SVDv3_rayon_listingProduits_action,
    #layout_v3 .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_premium_nonlog,
    #layout_v3 .SVDv3_rayon_listingProduits_description,
    #layout_v3 .SVDv3_rayon_listingProduits_prix,
    #layout_v3 .SVDv3_rayon_listingProduits_dispo,
    #layout_v3 .SVDv3_rayon_listingProduits_action,
    #layout_v3 .SVDv3_rayon_listingProduits_premium_nonlog,
    #layout_v3 .SVDv3_reduction,
    .SVDv3_pourcentage_reduction
    {
        display: block;
        height: auto;
        width: 60%;
        margin-left: 40%;
        text-align: left;
        padding: 0 0 0 15px;
        font-size: 14px;
        line-height: 1.6;
    }

    #layout_v3 .SVDv3_rayon_listingProduits_prix .SVDv3_reduction
    {
        margin-left: 0;
        padding-left: 0;
    }

    .SVDv3_rayon_listingProduits_description
    {
        margin-bottom: 10px;
    }

    .SVDv3_rayon_listingProduits_grille_cellule .SVDv3_rayon_listingProduits_prix
    {
        margin-bottom: 0;
    }

    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_action a
    {
        width: auto;
    }
    /*****************************************/
    /*               APPELS B                */
    /*****************************************/

    .SVDv3_rayon_listingProduits_action br {
        display: none;
    }

    .SVDv3_appelB,
    .SVDv3_rayon_listingProduits_grille_cellule {
        width: 100% !important;
        padding: 20px 0 !important;
        border: 0;
        border-bottom: 1px solid #d0d0d0;
        position: relative !important;
        font-size: 14px !important;
    }

    .SVDv3_rayon_produit_content {
        display: block;
    }

    .SVDv3_appelB .SVDv3_appelB_content,
    .SVDv3_produit_categorie {
        width: 60%;
        margin: 0 0 0 40%;
        position: static;
        text-align: left;
        padding: 0 0 0 15px;
    }

    .SVDv3_appelB .SVDv3_produit_image,
    .SVDv3_content_content .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_ref,
    .SVDv3_content_content .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_photo,
    .SVDv3_content_content .SVDv3_rayon_listingProduits_grille .SVDv3_rayon_listingProduits_grille_cellule div.SVDv3_rayon_listingProduits_photo,
    .SVDv3_content_content .SVDv3_rayon_listingProduits_grille .SVDv3_rayon_listingProduits_grille_cellule .SVDv3_produitPremium div.SVDv3_rayon_listingProduits_photo {
        position: absolute !important;
        right: 60%;
        top: 20px;
        width: 95px !important;
        height: 95px !important;
    }

    .SVDv3_appelB .SVDv3_produit_image img,
    .SVDv3_content_content .SVDv3_rayon_listingProduits_photo img {
        max-width: 95px;
        max-height: 95px;
        width: auto;
    }

    .SVDv3_appelsB .SVDv3_produit_image a {
        position: static;
    }

    .SVDv3_appelB_pied {
        display: none;
    }

    .SVDv3_produit_categorie a {
        background: none;
        text-transform: none;
        font-size: 14px;
        line-height: 22px;
        padding: 0;
    }

    .SVDv3_produit_categorie a span {
        height: auto;
        padding: 0;
        overflow: visible;
        white-space: normal;
        text-overflow: initial;
    }

    /* marge avant les appels B */
    .SVDv3_appelsB,
    .SVDv3_appelB {
        margin: 0;
    }

    /**********************************/
    /*     LISTING PRODUIT GRILLE     */
    /**********************************/
    .SVDv3_colonnes_colonne_droite .SVDv3_rayon_listingProduits_grille .SVDv3_rayon_listingProduits_grille_cellule,
    .SVDv3_rayon_listingProduits_grille .SVDv3_rayon_listingProduits_grille_ligne,
    .SVDv3_rayon_listingProduits_grille .SVDv3_rayon_listingProduits_grille_cellule
    {
        display: block;
        width: 100%;
        float: none;
        margin: 0;
        padding: 0;
        text-align: left;
    }

    .SVDv3_rayon_listingProduits_grille_cellule .SVDv3_rayon_listingProduits_photo p,
    .SVDv3_rayon_listingProduits_grille_ligne .SVDv3_rayon_listingProduits_action
    {
        position: static;
    }

    #layout_v3 .SVDv3_rayon_listingProduits_grille_cellule,
    #layout_v3 .SVDv3_rayon_listingProduits_grille_cellule .SVDv3_rayon_listingProduits_photo,
    #layout_v3 .SVDv3_rayon_listingProduits_grille_cellule .SVDv3_rayon_listingProduits_photo p,
    .SVDv3_colonnes_colonne_droite .SVDv3_rayon_listingProduits_grille .SVDv3_rayon_listingProduits_grille_cellule div.SVDv3_rayon_listingProduits_photo,
    .SVDv3_colonnes_colonne_droite .SVDv3_rayon_listingProduits_grille .SVDv3_rayon_listingProduits_grille_cellule div.SVDv3_rayon_listingProduits_photo p
    {
        width: 100%;
    }

    .SVDv3_rayon_listingProduits_description
    {
        margin-bottom: 10px !important;
    }

    .SVDv3_rayon_listingProduits_grille_ligne p.SVDv3_dispo
    {
        text-align: left;
    }

    .SVDv3_rayon_listingProduits_grille .SVDv3_rayon_listingProduits_grille_cellule p.SVDv3_rayon_listingProduits_description
    {
        padding: 0 0 0 15px;
    }

    /**********************************/
    /*            A VOIR             */
    /**********************************/
    .SVDv3_colonnes_1colonne .mceContentBody table p a
    {
        display: block;
    }

    /**********************************/
    /*            BOUTONS             */
    /**********************************/

    .SVDv3_bouton + .SVDv3_bouton_ajoutPanier
    {
        margin-top: 0;
    }

    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_action .SVDv3_bouton,
    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_rayon_listingProduits_action .SVDv3_bouton_ajoutPanier {
        width: auto;
        max-width: 100%;
    }

    /* On masque le bouton "fiche produit" dans les rayons */
    .SVDv3_rayon_listingProduits_liste_ligne .SVDv3_bouton_gris
    {
        display: none;
    }

    /**********************************/
    /*             PAGER              */
    /**********************************/

    .SVDv3_pagination .SVDv3_pagination_nbResults,
    .SVDv3_pagination .SVDv3_pagination_nbPages
    {
        display: none;
    }

    .SVDv3_pagination div[class*="grid_container_"] .grid_row .SVDv3_pagination_listPages
    {
        margin: 0;
    }

    .SVDv3_pagination .text-left,
    .SVDv3_pagination .text-right
    {
        text-align: center;
    }

    .pagination > li > a,
    .pagination > li > span {
        float: none;
        display: inline-block;
    }

    .pagination > li i.icon {
        vertical-align: -10%;
    }

    .pagination-lg > li i.icon {
        vertical-align: -12%;
    }

    /**********************************/
    /*        ACCORDEON JQUERY        */
    /**********************************/
    .ui-dialog .ui-dialog-content
    {
        height: auto !important;
    }

    /**********************************/
    /*        POINTS RETRAITS         */
    /**********************************/
    #map_canvas
    {
        width: 70%;
    }

    .SVDv3_soCol_pointRetrait_boutonChoisir,
    #liste_points_retrait
    {
        width: 30%;
    }

    /**********************************/
    /*          BACK TO TOP           */
    /**********************************/
    #back-to-top
    {
        right: 8px;
        bottom: 80px;
    }

    #back-to-top:hover:before
    {
        background: #f0f0f0;
        color: #444;
    }

    /**********************************/
    /*            POP IN              */
    /**********************************/

    .SVDv3_popin_rappelImmediat,
    .SVDv3_popin_newsletter
    {
        background: none;
        min-height: 0;
        padding-right: 0;
    }

    .SVDv3_popin_contenu img[alt="UiV3_loader_medium"]
    {
        margin-bottom: 10px;
    }

    /**********************************/
    /*     POPIN RAPPEL IMMEDIAT      */
    /**********************************/
    #formRappelImmediat
    {
        width: auto;
    }

    /**********************************/
    /*       CALCULATEUR ECRAN        */
    /**********************************/
    #ecran_container,
    #cdistance,
    .SVDv3_calculEcran_e #cdistance,
    .SVDv3_calculEcran_formulaire,
    .SVDv3_calculEcran .SVDv3_form label,
    .SVDv3_body_act_videoprojectionCalculEcranE .col_8_col,
    .SVDv3_body_act_videoprojectionCalculEcran .col_8_col
    {
        width: 100%;
    }

    #chauteur,
    #clargeur,
    #cdistance,
    #clatitude
    {
        position: static;
    }

    #ecran
    {
        padding: 0;
        height: auto;
    }

    #chauteur p,
    #clargeur p,
    #cdistance p
    {
        text-align: left !important;
    }

    #chauteur
    {
        width: 170px;
    }

    #chauteur:before
    {
        content: "Hauteur de l'image";
    }

    .SVDv3_body_act_videoprojectionCalculEcran #clargeur:before
    {
        content: "Base de l'image";
    }

    .SVDv3_body_act_videoprojectionCalculEcran #cdistance:before
    {
        content: "Distance de projection";
    }

    .SVDv3_body_act_videoprojectionCalculEcranE .SVDv3_zonePrix_prix,
    .SVDv3_calculEcran_formulaire li:last-child
    {
        padding-bottom: 10px;
    }

    .SVDv3_calculEcran_produit_texte
    {
        width: 50%;
    }

    /*****************************************/
    /*         TITRES INDEX INITIALE         */
    /*****************************************/
    h2.SVDv3_titre_index_lettre:after,
    h2.SVDv3_titre_index_lettre:before
    {
        width: 40%;
    }

    /**********************************/
    /*             ERREUR             */
    /**********************************/
    .SVDv3_boite_erreur
    {
        height: auto;
        margin: 0;
        position: static;
        width: 100%;
    }

    /**********************************/
    /*       MAPS POINT RETRAITS      */
    /**********************************/
    #pointsRetrait_map
    {
        width: 100%;
    }

    .SVDv3_pointsRetraits_icone_plan
    {
        display: none;
    }

    /**********************************/
    /*          FORMULAIRES           */
    /**********************************/
    input,
    select,
    textarea
    {
        /* Font-size smaller than 16px causes a zoom on mobile  */
        font-size: 16px !important;
        height: auto;
    }

    .SVDv3_form label,
    .SVDv3_form_fields,
    .SVDv3_form .checkbox_wrapper
    {
        padding: 10px 0;
        width: 100%;
    }

    .SVDv3_form .SVDv3_texteGris
    {
        padding-bottom: 10px;
    }

    .SVDv3_form span.error
    {
        margin: 0;
    }

    .SVDv3_form input[type="text"],
    .SVDv3_form input[type="password"],
    .SVDv3_form textarea,
    .SVDv3_form select
    {
        width: 100%;
        float: none;
        margin-bottom: 8px;
    }

    .SVDv3_form input[type="text"],
    .SVDv3_form input[type="password"]
    {
        padding: 7px;
    }

    .SVDv3_form span.error
    {
        margin: 0;
    }

    .SVDv3_form_fields,
    .SVDv3_form .pull_right
    {
        margin-left: 0;
    }

    .SVDv3_form .highlight
    {
        margin: 30px 0 20px 0;
    }

    .question_radio_list,
    .SVDv3_form_checkboxes
    {
        width: 100%;
        float: none;
    }

    .SVDv3_form ul li,
    .mceContentBody .SVDv3_form ul li
    {
        overflow: visible;
    }

    .SVDv3_body_act_recherche .SVDv3_filtreRayon_listChkbox input[type="checkbox"]
    {
        margin-top: 4px;
    }

    /**************************************/
    /*               PANIER               */
    /**************************************/
    .SVDv3_panier_table
    {
        width: 111.1111% !important;
        margin-left: -5.55555% !important;
    }

    .SVDv3_panier_table thead,
    .SVDv3_panier_table .SVDv3_panier_total td:last-child
    {
        display: none;
    }

    .SVDv3_panier_table .SVDv3_panier_article_finArticle td
    {
        width: 100%;
    }

    .SVDv3_rayon_listingProduits thead th,
    .SVDv3_panier_table thead th
    {
        background: none;
        color: #000;
    }

    .SVDv3_panier_table td,
    .SVDv3_panier_table th
    {
        float: left;
        font-size: 14px;
        padding-left: 5px;
        padding-right: 5px;
    }

    .SVDv3_panier_table .SVDv3_panier_article td
    {
        padding: 10px 5px;
    }

    .SVDv3_panier_table .SVDv3_panier_article td.SVDv3_panier_article_col_qte
    {
        padding: 10px 0;
    }

    .SVDv3_panier_table
    {
        margin: 0 auto 20px auto;
        border-collapse: collapse;
    }

    .SVDv3_panier_article p
    {
        text-align: left;
    }

    /*********************************/
    /*         PANIER LIVRAISON      */
    /*********************************/
    /* labels adresse */
    .SVDv3_form_fieldset li label[for="fct_adresse2"],
    .SVDv3_form_fieldset li label[for="fct_adresse3"],
    .SVDv3_form_fieldset li label[for="fct_adresse4"],
    .SVDv3_form_fieldset li label[for="fct_adresse5"]
    {
        display: none;
    }

    /**********************************/
    /*        PANIER PAIEMENT         */
    /**********************************/

    #Paiement_alerte_svd
    {
        width: 100%;
    }

    /***********************************/
    /*          COMPTE CLIENT          */
    /***********************************/
    /* Accueil du compte client */
    .SVDv3_cptclient_accueil
    {
        background: none;
        border: 0;
        padding: 0;
    }

    #SVDv3_cpt_logoff
    {
        box-sizing: border-box;
        padding: 5px 10px;
        right: 5%;
        top: 0;
        bottom: initial;
        border: 1px solid #333;
        border-radius: 3px;
        text-transform: none;
        line-height: 18px;
    }

    #SVDv3_cpt_logoff .text-danger,
    #SVDv3_cpt_logoff a
    {
        color: #999;
    }

    /* Commande resume */
    .SVDv3_commande_resume .grid_container_10 .grid_row .col_5_col
    {
        margin-bottom: 0;
    }

    .SVDv3_body_mod_PctCompte.SVDv3_body_act_compteShowCommande .SVDv3_commande_adresse_facturation div.SVDv3_form_fieldset
    {
        border-bottom: 1px solid #d0d0d0 !important;
    }

    /* Separateur colonne adresse commande */
    .SVDv3_body_act_compteShowCommande .SVDv3_commande_resume .col_5_col:after
    {
        display: none;
    }

    /**********************************/
    /*         INSTALLATIONS          */
    /**********************************/

    #install_form_informations_block textarea#install_form_input_commentaire,
    #install_form_informations_block textarea#install_form_input_descriptif
    {
        width: 100%;
    }

    /* colonnes */
    .monInstallation_texte .grid_container_12 .grid_row .col_3_col,
    .monInstallation_texte .grid_container_12 .grid_row .col_4_col,
    .monInstallation_texte .grid_container_12 .grid_row .col_5_col
    {
        border-bottom: 1px solid #d0d0d0;
    }

    /* Photo principale */
    div.monInstallation_photo_main_wrapper
    {
        position: static;
        top: initial;
        left: initial;
        height: auto;
    }

    div.monInstallation_photo_main
    {
        margin: -30px 0 20px -5.55555% !important;
        position: static;
        height: auto;
        width: 111.1111% !important;
    }

    /**********************************/
    /*           INFOBULLE            */
    /**********************************/
    .infobulle_information
    {
        width: 90%;
        padding: 7px !important;
        margin-left: -7px !important;
    }

    /**********************************/
    /*            BIG SLIDER          */
    /**********************************/

    #SVDv3_domaine_slider
    {
        border-bottom: 1px solid #d0d0d0;
    }

    #SVDv3_domaine_slider
    {
        width: 111.1111% !important;
        margin-left: -5.55555% !important;
    }

    /*********************************/
    /*      CONFIGURATEUR LUMENE     */
    /*********************************/
    #SVDv3_lumene_configurateur
    {
        width: 100%;
    }

    /**********************************/
    /*            FANCYBOX            */
    /**********************************/
    #fancybox-content,
    #fancybox-wrap
    {
        width: 100% !important;
        height: auto !important;
        left: 0 !important;
    }

    /**********************************/
    /*             IMAGES             */
    /**********************************/
    /* TODO: deplacer en tete de fichier */
    img
    {
        max-width: 100%;
        /*height: auto !important;*/
    }

    /**************************************/
    /*           INSTALLATIONS            */
    /**************************************/

    .installationChoix .SVDv3_infiniteScroll_loader
    {
        width: 100%;
        height: 32px;
        float: none;
        margin: 0 0 20px 0;
    }

    /**************************************/
    /*             QUICK MENU             */
    /**************************************/
    .SVDv3_quickMenu_wrapper
    {
        display: block;
        table-layout: auto;
    }

    ul.SVDv3_quickMenu
    {
        display: block;
    }

    ul.SVDv3_quickMenu li
    {
        display: block;
        border: 0;
        border-bottom: 1px solid #777;
        width: 100%;
    }

    ul.SVDv3_quickMenu li:last-child
    {
        border-bottom: 0 !important;
    }

    ul.SVDv3_quickMenu li a
    {
        font-size: 13px;
        line-height: 19px;
        padding: 10px 5%;
    }

    /* Quick menu Super 14 */
    .SVDv3_super14_menu ul.SVDv3_quickMenu li
    {
        border: 0;
        border-bottom: 1px solid #d51820;
    }

    /*****************************************/
    /*                PRESSES                */
    /*****************************************/
    .SVDv3_CmsFiche_press
    {
        padding-bottom: 0;
    }
}

/*****************************************/
/*             RESIZE IMG                */
/*****************************************/

@media (min-width: 992px)
{
    .SVDv3_appelsB.SVDv3_appelsB_4x1 .SVDv3_produit_image img,
    .SVDv3_appelsB.SVDv3_appelsB_3x1 .SVDv3_produit_image img
    {
        max-width: 150px;
        max-height: 150px;
    }

    .SVDv3_content_content .SVDv3_rayon_listingProduits_photo img
    {
        max-width: 140px;
        max-height: 140px;
    }

    #videoresult img
    {
        max-width: 100px;
        max-height: 100px;
    }

    .SVDv3_sommaireCategorie_cellule_image img
    {
        max-width: 180px;
        max-height: 180px;
    }

    #search-filters-block
    {
        display: block!important;
    }
}

@media (max-width: 649px)
{

    /**********************************/
    /*       PAGINATION GUIDES        */
    /**********************************/
    .chapters-index ul li
    {
        display: block;
        margin-right: 0;
    }

    .chapters-index ul li:before {
        display: inline;
    }

}

/**********************************/
/*          A COMMENTER           */
/**********************************/

@media (max-width: 520px) {
    #SVDv3_cpt_logoff {
        display: block;
        margin: 5px auto 0;
        padding: 0;
    }

    .SVDv3_panier_resume_total span,
    .SVDv3_panier_resume span
    {
        width: 60%;
    }

    span.SVDv3_panier_resume_prix
    {
        width: 40%;
    }
}

@media (max-width: 460px)
{
    /* POINTS RETRAITS */
    #map_canvas
    {
        width: 100%;
    }
}

@media (max-width: 420px)
{
    /**************************************/
    /*          PANIER PAIEMENT           */
    /**************************************/
    .SVDv3_zonePrix_longueur .SVDv3_bouton_ajoutPanier,
    .SVDv3_zonePrix_couleur .SVDv3_bouton_ajoutPanier
    {
        position: static;
        width: 100%;
    }

    /*********************************/
    /*              RIB              */
    /*********************************/

    .rib-svd
    {
        font-size: 11px;
    }

    .rib-svd table td
    {
        padding: 0;
    }

    .rib-svd-number
    {
        padding: 0;
    }
}

/**********************************/
/*   MAX DEVICE WIDTH : 660PX     */
/**********************************/

@media (max-width: 767px)
{
    /**********************************/
    /*          PROMOS PANIER         */
    /**********************************/
    .SVDv3_panier_promotions .SVDv3_promo
    {
        width: 100%;
        margin-right: 0;
    }
}

/**********************************/
/*     NAV MOBILE 2 COLONNES      */
/**********************************/

@media (max-width: 991px)
{
    #SVDv3_universCategories #LiensTabs li a {
        text-transform: uppercase;
        font-weight: bold;
    }

    #SVDv3_universCategories #LiensTabs li a:before {
        line-height: 46px;
    }
}

@media (min-width: 320px) and (max-width: 991px)
{
    #SVDv3_universCategories
    {
        position: relative;
    }

    #SVDv3_universCategories:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        height: 10px;
        border-bottom: 1px solid #d0d0d0;
        width: 100%;
    }

    #SVDv3_universCategories:after {
        width: 111.1111%;
        margin: 0 0 0 -5.55555%!important;
    }

    #SVDv3_universCategories #LiensTabs li
    {
        width: 50% !important;
        float: left;
        display: table;
    }

    #SVDv3_universCategories #LiensTabs li a
    {
        width: 100% !important;
        float: none;
        border-right: 1px solid #d0d0d0;
        display: table-cell;
        vertical-align: middle;
        padding: 0 10%;
    }

    #SVDv3_universCategories #LiensTabs li a
    {
        height: 46px !important;
        position: relative;
        font-size: 13px;
        line-height: 17px;
    }

    #SVDv3_universCategories #LiensTabs li a:before
    {
        line-height: 46px;
    }

    #SVDv3_universCategories #LiensTabs li:nth-child(2n+2) a
    {
        border-right: none;
    }

    /* correction position fleche */
    #SVDv3_universCategories #LiensTabs li a:before
    {
        right: 10%;
    }
}

@media (max-width: 420px)
{
    #SVDv3_universCategories #LiensTabs li a
    {
        font-size: 11px;
        line-height: 16px;
    }
}

/****************************************/
/* BOOTSTRAP 3.3.4 RESPONSIVE UTILITIES */
/****************************************/

.visible-xs,
.visible-sm,
.visible-md,
.visible-lg
{
    display: none !important;
}

.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block
{
    display: none !important;
}

@media (max-width: 767px)
{
    .visible-xs
    {
        display: block !important;
    }

    table.visible-xs
    {
        display: table;
    }

    tr.visible-xs
    {
        display: table-row !important;
    }

    th.visible-xs,
    td.visible-xs
    {
        display: table-cell !important;
    }
}

@media (max-width: 767px)
{
    .visible-xs-block
    {
        display: block !important;
    }
}

@media (max-width: 767px)
{
    .visible-xs-inline
    {
        display: inline !important;
    }
}

@media (max-width: 767px)
{
    .visible-xs-inline-block
    {
        display: inline-block !important;
    }
}

@media (min-width: 768px) and (max-width: 991px)
{
    .visible-sm
    {
        display: block !important;
    }

    table.visible-sm
    {
        display: table;
    }

    tr.visible-sm
    {
        display: table-row !important;
    }

    th.visible-sm,
    td.visible-sm
    {
        display: table-cell !important;
    }
}

@media (min-width: 768px) and (max-width: 991px)
{
    .visible-sm-block
    {
        display: block !important;
    }
}

@media (min-width: 768px) and (max-width: 991px)
{
    .visible-sm-inline
    {
        display: inline !important;
    }
}

@media (min-width: 768px) and (max-width: 991px)
{
    .visible-sm-inline-block
    {
        display: inline-block !important;
    }
}

@media (min-width: 992px) and (max-width: 1199px)
{
    .visible-md
    {
        display: block !important;
    }

    table.visible-md
    {
        display: table;
    }

    tr.visible-md
    {
        display: table-row !important;
    }

    th.visible-md,
    td.visible-md
    {
        display: table-cell !important;
    }
}

@media (min-width: 992px) and (max-width: 1199px)
{
    .visible-md-block
    {
        display: block !important;
    }
}

@media (min-width: 992px) and (max-width: 1199px)
{
    .visible-md-inline
    {
        display: inline !important;
    }
}

@media (min-width: 992px) and (max-width: 1199px)
{
    .visible-md-inline-block
    {
        display: inline-block !important;
    }
}

@media (min-width: 1200px)
{
    .visible-lg
    {
        display: block !important;
    }

    table.visible-lg
    {
        display: table;
    }

    tr.visible-lg
    {
        display: table-row !important;
    }

    th.visible-lg,
    td.visible-lg
    {
        display: table-cell !important;
    }
}

@media (min-width: 1200px)
{
    .visible-lg-block
    {
        display: block !important;
    }
}

@media (min-width: 1200px)
{
    .visible-lg-inline
    {
        display: inline !important;
    }
}

@media (min-width: 1200px)
{
    .visible-lg-inline-block
    {
        display: inline-block !important;
    }
}

@media (max-width: 767px)
{
    .hidden-xs
    {
        display: none !important;
    }
}

@media (min-width: 768px) and (max-width: 991px)
{
    .hidden-sm
    {
        display: none !important;
    }
}

@media (min-width: 992px) and (max-width: 1199px)
{
    .hidden-md
    {
        display: none !important;
    }
}

@media (min-width: 1200px)
{
    .hidden-lg
    {
        display: none !important;
    }
}

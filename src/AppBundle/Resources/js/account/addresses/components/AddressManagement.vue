<script setup>
import { computed } from 'vue'
import { useAddressesStore } from '@/account/addresses/stores'
import EditAddress from '@/account/addresses/components/EditAddress.vue'
import CreateAddress from '@/account/addresses/components/CreateAddress.vue'

const store = useAddressesStore()

</script>

<template>
    <section id="app_account_addresses">
        <edit-address v-for="(address, index) in store.addresses" :index="index" :address="address" :key="index"></edit-address>
        <create-address></create-address>
    </section>
</template>

<style scoped lang="scss">

</style>
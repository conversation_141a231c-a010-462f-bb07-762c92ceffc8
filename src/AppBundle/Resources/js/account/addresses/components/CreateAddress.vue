<template>
    <div>
        <div class="SVDv3_article_element mceContentBody" v-if="!show_creation_form">
            <svd-button light outline @click="create" :label="add_an_address_text" />
        </div>

        <div class="account_address" v-if="show_creation_form">
            <div class="title-panel">Création d'une nouvelle adresse</div>

            <div class="grid_container_12">
                <common-address-form
                    action="create"
                    :civility_titles="civility_titles"
                    :addresses="addresses"
                    :user="user"
                    @save="save"
                    @cancel="cancel"
                />
            </div>
        </div>
    </div>
</template>

<script>
import { trans } from '@/shared/filters'
import CommonAddressForm from './CommonAddressForm.vue'
import SvdButton from '@/v5/components/button/SvdButton.vue'
import { useAddressesStore } from '@/account/addresses/stores'
import { useCountries } from '@/v5/composable/useCountries'
import { computed } from 'vue'


export default {
    name: 'CreateAddress',
    components: { SvdButton, CommonAddressForm },

    setup() {
        const { countries } = useCountries()
        const store = useAddressesStore()

        const user = computed(() => {
            return store.user
        })
        const show_creation_form = computed(() => {
            return store.show_creation_form
        })
        const civility_titles = computed(() => {
            return store.civility_titles
        })
        const addresses = computed(() => {
            return store.addresses
        })
        const add_an_address_text = computed(() => {
            return trans('Ajouter une adresse', {}, 'account')
        })

        const save = (data) => {
            store.save(...data)
        }
        const create = () => {
            store.create()
        }
        const cancel = (action) => {
            store.cancel(action)
        }

        return {
            countries,
            store,
            user,
            show_creation_form,
            civility_titles,
            addresses,
            add_an_address_text,
            save,
            create,
            cancel
        }
    }
}
</script>

<style scoped lang="scss">
@import '../../../../../Resources/scss/style';
.account_address {
    display: flex;
    flex-direction: column;
    gap: $space_16;
}
</style>

import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/shared/api/handler'
import { useSharedStore } from '@/shared/api/stores'

export const useAddressesStore = defineStore('addresses', () => {
    const civility_titles = ref([])
    const countries = ref([])
    const addresses = ref([])
    const urls = ref([])
    const message_content = ref(null)
    const message_owner = ref(null)
    const create_url = ref(null)
    const edited = ref(null)
    const user = ref(null)
    const show_creation_form = ref(false)
    const message_status = ref(true)

    const sharedStore = useSharedStore()

    const initializeData = (data) => {
        const addresses = data.addresses
        const urls = data.urls

        initialize(data)
        updateAddresses(addresses, urls)
    }

    const initialize = (information) => {
        create_url.value = information.create_url
        countries.value = information.countries
        civility_titles.value = information.civility_titles
    }

    const edit = (index) => {
        message_owner.value = null
        edited.value = index
    }

    const cancel = (action) => {
        message_owner.value = null
        if (action === 'create') {
            show_creation_form.value = false
        } else {
            edited.value = null
        }
    }

    const updateAddresses = (addressList, urlList) => {
        addresses.value = addressList
        urls.value = urlList
        if (addresses.value.length === 0) {
            show_creation_form.value = true
        }
    }

    const updateAddress = (index, key, value) => {
        addresses.value[index][key] = value
    }

    const showMessage = (index, message, status) => {
        message_content.value = message
        message_owner.value = index
        message_status.value = status
    }

    const hide_message = () => {
        message_content.value = null
        message_owner.value = null
    }

    const create = () => {
        message_owner.value = null
        show_creation_form.value = true
    }

    const updateUser = (data) => {
        user.value = data
    }

    const setAsDefault = (index, address) => {
        api.put(urls.value[index].main, {address})
            .then((response) => {
                edit(0)
                updateAddresses(response.payload.addresses, response.payload.urls)
                showMessage(0, response.message, response.success)
                return response
            })
    }

    const save = (action, index, address, callback) => {
        hide_message()
        const url = action === 'create' ? create_url.value : urls.value[index].update
        api.post(url, { address })
            .then((response) => {
                if (response.success) {
                    cancel(action)
                    updateAddresses(response.payload.addresses, response.payload.urls)
                }
                showMessage(index, response.message, response.success)

                return response
            })
            .then(callback)
    }

    const deleteAddress = (index, address, callback) => {
        api.delete(urls.value[index].delete)
            .then(callback)
    }

    const retrieveUser = () => {
        api.get(sharedStore.urls.api_customer_info)
            .then((response) => {
                updateUser(response.user)
            })
    }

    return {
        addresses,
        edited,
        message_content,
        message_owner,
        message_status,
        countries,
        civility_titles,
        show_creation_form,
        user,
        initializeData,
        edit,
        cancel,
        updateAddresses,
        updateAddress,
        showMessage,
        create,
        hide_message,
        setAsDefault,
        save,
        deleteAddress,
        retrieveUser
    }
})
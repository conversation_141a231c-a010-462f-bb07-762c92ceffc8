<template>
    <div class="account-creation">
        <svd-input-text
            v-model='email'
            @input='validateEmail'
            label="Email"
            required
            name="account_creation_form[email]"
            data-context='email'
            autocomplete='email'
            :helper="email_errors_type"/>
        <div>
            <svd-input-password
                v-model='password'
                @input='validatePassword'
                label="Mot de passe"
                required
                name="account_creation_form[password]"
                data-context='password-1'
                :helper="password_errors_type"
                autocomplete='new-password'
            />
            <div class="password-complexity" v-if='password.length > 0'>
                <account-creation-password-rule :valid="rulesValid['min-length']">Minimum de 8 caractères</account-creation-password-rule>
                <account-creation-password-rule :valid="rulesValid['min-maj']">Une minuscule et une majuscule</account-creation-password-rule>
                <account-creation-password-rule :valid="rulesValid['num']">Un chiffre</account-creation-password-rule>
                <account-creation-password-rule :valid="rulesValid['special']">Un caractère spécial (!?&#$*)</account-creation-password-rule>
            </div>
        </div>
        <svd-input-checkbox
            v-model='news'
            :label="checkboxQuestion"
            name='account_creation_form[want_newsletter]'
        />
        <svd-button submit label="Je crée mon compte" id='btnInscription' primary />
        <div class="subtext">
            <div>Vous avez déjà un compte ?</div>
            <div>
                <svd-link :href="login_path" label="Se connecter"></svd-link>
            </div>
        </div>
    </div>
</template>

<script>
import SvdInputText from '@/v5/components/input/SvdInputText.vue'
import SvdInputPassword from '@/v5/components/input/SvdInputPassword.vue'
import SvdInputCheckbox from '@/v5/components/input/SvdInputCheckbox.vue'
import SvdButton from '@/v5/components/button/SvdButton.vue'
import SvdLink from '@/v5/components/link/SvdLink.vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'
import { isValidEmail } from '@/shared/functions'
import AccountCreationPasswordRule from '@/account/creation/components/AccountCreationPasswordRule.vue'

export default {
    name: 'AccountCreation',
    components: {
        AccountCreationPasswordRule,
        SvdIcon,
        SvdLink,
        SvdButton,
        SvdInputCheckbox,
        SvdInputPassword,
        SvdInputText,
        Text,
    },
    props: {
        password_errors: {
            required: true,
            type: String,
        },
        email_errors: {
            required: true,
            type: String,
        },
        email_value: {
            required: true,
            type: String,
        },
        login_path: {
            required: true,
            type: String,
        },
    },
    mounted() {
        if (this.email_value !== null && this.email_value !== '') {
            this.email = this.email_value
        }
        if (this.password_errors) {
            this.password = ''
        }
    },
    data() {
        return {
            rulesValid: [],
            password: '',
            email: '',
            news: false,
            email_errors_type: {type: 'error', label: this.email_errors},
            password_errors_type: {type: 'error', label: this.password_errors.replace(/(<([^>]+)>)/gi, '')},
            checkboxQuestion:
                'J\'accepte de recevoir les bons plans en avant-première et le meilleur de l\'actualité "Image et Son", par email ou sms.',
        }
    },
    methods: {
        validateEmail() {
            this.email_errors_type = {type: 'error', label: ''}
            if (!isValidEmail(this.email)) {
                this.email_errors_type = {type: 'error', label: `Veuillez saisir une adresse email valide`}
            }
        },
        validatePassword() {
            this.password_errors_type = {}

            const hasMaj = /[A-Z]/g.test(this.password)
            const hasMin = /[a-z]/g.test(this.password)
            const hasNum = /[0-9]/g.test(this.password)
            const hasSpecial = /[^a-zA-Z\d\s:]/g.test(this.password)

            this.rulesValid['min-length'] = this.password.length >= 8;
            this.rulesValid['min-maj'] = hasMaj && hasMin
            this.rulesValid['num'] = hasNum
            this.rulesValid['special'] = hasSpecial
        }
    }
}
</script>

<style lang="scss" scoped>
@import '../../../../../Resources/scss/style';

.account-creation {
    display: flex;
    flex-direction: column;
    gap: $space_32;
    .password-complexity {
        font-size: $font_size_13;
        line-height: $height_20;
        .rule {
            display: flex;
            flex-direction: row;
            gap: $space_4;
            &.valid {
                color: $color_success;
            }
            &.invalid {
                color: $color_error;
            }
        }
    }
    .subtext {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        align-content: center;
        text-align: center;
        line-height: $height_24;
        font-size: $font_size_15;
        color: $color_grey_typo;
        @include media_min($media_xs) {
            flex-direction: row;
            gap: $space_8;
        }
    }
}
</style>

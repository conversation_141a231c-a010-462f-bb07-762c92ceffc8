<template>
    <div v-if="loading">
        <img :src="'/images/static/Basket/ajax-loader_p.gif' | asset('static_images')" alt="Chargement..." />
    </div>
    <div v-else-if="error_message">
        <svd-message type="error">
            {{ error_message }}
        </svd-message>
    </div>
    <div v-else class="my-order">
        <div class="customer-order" data-context="customer-order">
            <div>
                <div class="command-number" data-context="command-number">
                    <div class="order-label" data-context="order-label">
                        Commande n°{{ customerOrderId }}
                        <div v-if="is_awaiting_payment" class="status warning">En attente de paiement</div>
                    </div>
                </div>
                <div class="order-date"><PERSON><PERSON><PERSON> le {{ formatDate(customer_order.created_at) }}</div>
            </div>

            <!-- ACTIONS-->
            <div
                v-if="
                    customer_order.invoices.length > 0 ||
                    customer_order.return_notes.length > 0
                "
                class="command-actions"
            >
                <div v-if="customer_order.status === 'AWAITING_PAYMENT'">
                </div>
                <div v-if="customer_order.invoices.length === 1" data-context="invoice-link">
                    <svd-button
                        :href="`/mon-compte/ma-facture/telechargement/${customer_order.invoices[0]}.pdf`"
                        label="Télécharger ma facture"
                        icon="download"
                        primary
                    />
                </div>
                <div v-else-if="customer_order.invoices.length > 1" data-context="invoice-link">
                    <svd-button label="Télécharger mes factures" :choices="invoices" icon="download" primary />
                </div>
                <div v-if="customer_order.return_notes.length === 1" data-context="invoice-link">
                    <svd-button
                        :href="`/mon-compte/retour-commande/telecharger-pdf/${customer_order.return_notes[0]}`"
                        label="Télécharger mon retour"
                        icon="download"
                        primary
                    />
                </div>
                <div v-else-if="customer_order.return_notes.length > 1" data-context="invoice-link">
                    <svd-button label="Télécharger mes retours" :choices="returns" icon="download" primary />
                </div>
            </div>

            <!--  Awaiting payment messages-->
            <div v-if="is_awaiting_payment" class="command-messages">
                <svd-message v-if="is_awaiting_vir" type="info">
                    <div class="title">En attente de réception du virement</div>
                    <div class="big">Nous vous remercions d'effectuer le virement sur le compte suivant :</div>
                    <div>
                        <div><span class="big">Banque : </span>Crédit Industriel et Commercial</div>
                        <div><span class="big">Domiciliation : </span>CIC Paris Saint Augustin Entreprises (10646)</div>
                        <div><span class="big">Code banque : </span>30066</div>
                        <div><span class="big">Guichet : </span>10646</div>
                        <div><span class="big">N° de compte : </span>00010958304</div>
                        <div><span class="big">Clé RIB : </span>74</div>
                        <div class="line">
                            <span> <span class="big">IBAN : </span>FR76 3006 6106 4600 0109 5830 474 </span>
                            <span class="focus copy" @click="copyToClipboard">
                                <svd-icon icon="copy" color="36c" size="20" />
                                Copier
                            </span>
                        </div>
                        <div><span class="big">BIC (virement SWIFT) : </span>CMCIFRPP</div>
                        <div><span class="big">Montant : </span>{{ formatPriceValue(customer_order.total_price) }}</div>
                    </div>
                    <div>
                        <div class="big">
                            N’oubliez pas de préciser le numéro de votre commande (<span class="focus">{{
                                customerOrderId
                            }}</span
                            >) dans l’intitulé du virement.
                        </div>
                        <div>
                            Pour tout paiement par virement depuis l’international, veuillez impérativement ajouter la
                            mention suivante lors de votre virement : « règlements sans frais pour le bénéficiaire ».
                        </div>
                    </div>
                    <div class="big">
                        {{ phrase_registered_but_not_reserved }}
                    </div>
                    <div class="actions">
                        <div>
                            <svd-button
                                label="Télécharger le RIB en PDF"
                                :href="'/images/pdf/faq/rib-son-video-com.pdf' | asset('static_images')"
                                target="_blank"
                                primary
                            />
                        </div>
                    </div>
                </svd-message>
                <svd-message v-if="is_awaiting_svdcc" type="info">
                    <div class="title">En attente de réception des chèques cadeaux</div>
                    <div>
                        <div class="big">
                            Merci de nous envoyer votre ou vos chèques cadeaux par courrier (en reportant au dos le
                            numéro de commande : <span class="focus">{{ customerOrderId }}</span
                            >).
                        </div>
                        <div class="big">
                            Nous vous expédierons la marchandise dès la validation des informations fournies.
                        </div>
                    </div>
                    <div>Merci de nous l’envoyer par courrier suivi à l’adresse suivante :</div>
                    <div>
                        <div>Son-Vidéo.com</div>
                        <div>Service commande</div>
                        <div>309 avenue du Général de Gaulle</div>
                        <div>94500 Champigny-sur-Marne</div>
                        <div>France</div>
                    </div>
                    <div>
                        Vous serez tenu informé par email de la réception de vos chèques cadeaux et de l’état
                        d’avancement de votre commande.
                    </div>
                </svd-message>
                <svd-message v-if="is_awaiting_tel" type="info">
                    <div class="title">En attente de paiement par téléphone</div>
                    <div class="big">
                        Un conseiller va vous appeler au plus vite afin de valider votre paiement par téléphone.
                    </div>
                    <div>
                        Notre service client est disponible du lundi au samedi (hors jours fériés) de 9h à 19h. L’un de
                        nos conseiller va vous contacter au
                        <span class="big">{{ customer_order.billing_address.cellphone }}</span> pour finaliser avec vous
                        votre commande.
                    </div>
                    <div class="big">
                        {{ phrase_registered_but_not_reserved }}
                    </div>
                </svd-message>
                <svd-message v-if="has_rest_to_pay" type="info">
                    <div class="title">En attente d'un complément de paiement</div>
                    <div class="big">
                        Votre commande n'a pas encore été traitée car elle présente un reste à payer de
                        <span class="focus">{{ formatPriceValue(balance) }}</span>
                    </div>
                    <div>
                        Si vous souhaitez de l’aide pour procéder au paiement, notre service client est disponible du
                        lundi au samedi (hors jours fériés) de 9h à 19h.
                    </div>
                    <div class="big">
                        {{ phrase_registered_but_not_reserved }}
                    </div>
                    <div class="actions">
                        <svd-button
                            :href="`/mon-compte/ma-commande/${customer_order.customer_order_id}/ajouter-paiement`"
                            label="Payer ma commande"
                            data-context="pay-my-order"
                            primary
                        />
                    </div>
                </svd-message>
            </div>

            <!-- General status bar  -->
            <div v-if="products_not_shipped_or_cancelled.length > 0">
                <div class="command-status">
                    <div class="status-label">{{ order_status_label }}</div>
                    <div class="payment-date">{{ status_phrase }}</div>
                </div>
                <div class="command-body">
                    <div
                        v-if="['AWAITING_PROCESSING', 'AWAITING_PAYMENT'].includes(customer_order.status)"
                        class="command-messages"
                    >
                        <svd-message small type="info">
                            {{ phrase_registered_but_not_reserved }}
                        </svd-message>
                    </div>
                    <div class="container-bloc" data-context="unshipped-products">
                        <div class="products-headline no-border">
                            <span v-if="is_awaiting_payment || is_cancelled_order">Contenu de ma commande :</span>
                            <span v-else>Produits restant à expédier :</span>
                        </div>
                        <div class="command-products" data-context="unshipped-product">
                            <customer-order-product
                                v-for="product in products_not_shipped_or_cancelled"
                                :product="product"
                                :key="product.article_id"
                                :show-estimated-delivery-time="!is_cancelled_order && !is_awaiting_payment"
                                @clickUnknownDeliveryDate="is_open_side_panel = true"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transfers -->
            <div
                v-if="!is_cancelled_order && customer_order.shipment_details?.transfers?.length > 0"
                v-for="transfer in customer_order.shipment_details.transfers"
                data-context="transfer"
            >
                <div class="command-status">
                    <div class="status-label" data-context="delivery-id">
                        Envoi n°{{ transfer.transfer_id }} vers votre magasin
                        <span class="status success">{{ transferStatusLabel(transfer.status) }}</span>
                    </div>
                    <div class="payment-date" data-context="shipped-status">
                        <template v-if="transfer.status === 'SHIPPED'">
                            Expédié le {{ formatDate(transfer.shipped_at) }}
                        </template>
                        <template v-if="transfer.status === 'BEING_PREPARED'">
                            Début de préparation le {{ formatDate(transfer.created_at) }}
                        </template>
                    </div>
                </div>
                <div class="command-body">
                    <div class="parcel-content">
                        <svd-message small type="info">
                            Votre commande est en {{ transfer.status === 'SHIPPED' ? 'chemin' : 'préparation' }}. Vous
                            serez averti par SMS et par email dès que l’intégralité de celle-ci sera disponible au
                            retrait.
                        </svd-message>

                        <div class="products-headline no-border">
                            <span>Contenu de ma livraison :</span>
                        </div>
                        <div class="command-products">
                            <customer-order-product
                                v-for="product in transfer.products.map(mapFullProduct)"
                                :product="product"
                                :key="product.article_id"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delivery notes -->
            <div
                v-if="!is_cancelled_order && customer_order.shipment_details?.delivery_notes?.length > 0"
                v-for="delivery_note in customer_order.shipment_details.delivery_notes"
                data-context="delivery-note"
            >
                <div class="command-status">
                    <div class="status-label" data-context="delivery-id">
                        Bon de livraison n°{{ delivery_note.delivery_note_id }}
                        <span class="status success">{{ deliveryNoteStatusLabel(delivery_note.status) }}</span>
                    </div>
                    <div class="payment-date" data-context="shipped-status">
                        {{
                            deliveryString(
                                delivery_note.status,
                                delivery_note.delivered_at ?? delivery_note.shipped_at ?? delivery_note.created_at
                            )
                        }}
                    </div>
                </div>
                <div class="command-body">
                    <div
                        v-if="!customer_order.is_pickup_store"
                        v-for="parcel in delivery_note.parcels"
                        data-context="parcel-number"
                        class="parcel"
                    >
                        <div class="title">Numéro de suivi :</div>
                        <div data-context="parcel-number">
                            <span v-if="parcel.tracking_url">
                                {{ delivery_note.carrier_name }} 
                                <svd-link :href="parcel.tracking_url" target="_blank" :label="parcel.tracking_number" />
                            </span>
                            <span v-else>{{ delivery_note.carrier_name }} {{ parcel.tracking_number }}</span>
                        </div>
                    </div>
                    <div class="parcel-content">
                        <svd-message
                            v-if="customer_order.is_pickup_store && delivery_note.status === 'AVAILABLE'"
                            small
                            type="info"
                        >
                            C’est le jour J ! Votre commande est disponible au retrait dans votre magasin. <br />
                            Une pièce d’identité vous sera demandée pour le retrait de votre commande.
                        </svd-message>
                        <div v-if="canTrackParcel(delivery_note.parcels)" class="actions">
                            <svd-button
                                label="Suivre mon colis"
                                @click="openModal(delivery_note.parcels)"
                                primary
                                data-context="track-parcel-btn"
                            />
                        </div>

                        <div class="products-headline no-border">
                            <span v-if="customer_order.is_pickup_store">Contenu de mon retrait :</span>
                            <span v-else> Contenu de ma livraison : </span>
                        </div>
                        <div class="command-products">
                            <customer-order-product
                                v-for="product in delivery_note.products.map(mapFullProduct)"
                                :product="product"
                                :key="product.article_id"
                            />
                        </div>
                        <div class="actions">
                            <svd-button
                                label="Renvoyer des produits"
                                primary
                                outline
                                :href="return_parcel"
                                data-context="return-link"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available in store -->
            <div
                v-if="!is_cancelled_order && customer_order.shipment_details?.available_in_store"
                data-context="available-in-store"
            >
                <div class="command-status">
                    <div class="status-label" data-context="delivery-id">
                        Commande disponible au retrait dans votre magasin
                        <span class="status success">{{ deliveryNoteStatusLabel('AVAILABLE') }}</span>
                    </div>
                    <div class="payment-date" data-context="shipped-status">
                        {{ deliveryString('AVAILABLE', customer_order.shipment_details.available_in_store.sent_at) }}
                    </div>
                </div>
                <div class="command-body">
                    <div class="parcel-content">
                        <svd-message small type="info">
                            C’est le jour J ! Votre commande est disponible au retrait dans votre magasin. <br />
                            Une pièce d’identité vous sera demandée pour le retrait de votre commande.
                        </svd-message>

                        <div class="products-headline no-border">
                            <span>Contenu de mon retrait :</span>
                        </div>
                        <div class="command-products">
                            <customer-order-product
                                v-for="product in customer_order.shipment_details.available_in_store.products.map(
                                    mapFullProduct
                                )"
                                :product="product"
                                :key="product.article_id"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Addresses -->
            <div class="command-adresses">
                <div class="command-adresse" data-context="delivery-address">
                    <div class="address-title">Adresse de livraison</div>
                    <div class="address-content">
                        <div>{{ customer_order.shipping_address.company_name }}</div>
                        <div>
                            {{ customer_order.shipping_address.first_name }}
                            {{ customer_order.shipping_address.last_name }}
                        </div>
                        <div>{{ customer_order.shipping_address.address }}</div>
                        <div>
                            {{ customer_order.shipping_address.postal_code }} {{ customer_order.shipping_address.city }}
                        </div>
                        <div>{{ customer_order.shipping_address.country_name }}</div>
                        <div>{{ customer_order.shipping_address.cellphone }}</div>
                    </div>
                </div>
                <div class="command-adresse" data-context="billing-address">
                    <div class="address-title">Adresse de facturation</div>
                    <div class="address-content">
                        <div>{{ customer_order.billing_address.company_name }}</div>
                        <div>
                            {{ customer_order.billing_address.first_name }}
                            {{ customer_order.billing_address.last_name }}
                        </div>
                        <div>{{ customer_order.billing_address.address }}</div>
                        <div>
                            {{ customer_order.billing_address.postal_code }} {{ customer_order.billing_address.city }}
                        </div>
                        <div>{{ customer_order.billing_address.country_name }}</div>
                    </div>
                </div>
            </div>
            <div class="command-total" data-context="command-total">
                <div data-context="sub-total" class="amount" v-if="customer_order.shipping_price !== 0">
                    <div>Sous-total</div>
                    <div>{{ formatPriceValue(customer_order.total_price - customer_order.shipping_price) }}</div>
                </div>
                <div data-context="total-delivery" class="amount">
                    <div>Livraison</div>
                    <div v-if="customer_order.shipping_price === 0">
                        <span class="free">OFFERT</span>
                    </div>
                    <div v-else>{{ formatPriceValue(customer_order.shipping_price) }}</div>
                </div>
                <div class="global-price">
                    <div class="amount" data-context="total-price">
                        <div>TOTAL</div>
                        <div>{{ formatPriceValue(customer_order.total_price) }}</div>
                    </div>
                    <div data-context="rest-price" v-if="!is_cancelled_order && balance > 0" class="amount">
                        <div>RESTE À PAYER</div>
                        <div>{{ formatPriceValue(balance) }}</div>
                    </div>
                </div>
            </div>
            <div class="command-client-messages">
                <div class="client-messages-title">Mes messages</div>
                <customer-messages v-if="parsed_messages.length > 0" :messages="parsed_messages" />
            </div>
        </div>
        <account-customer-order-parcel-tracking-modal v-if="show_modal" :parcels="modal_data" @close="closeModal()" />
        <side-panel
            v-if="is_open_side_panel"
            @close="is_open_side_panel = false"
            title="En attente d’approvisionnement"
            template="v5"
        >
            <div class="unknown-delivery-time-explanation" data-context="unknown-delivery-time-explanation">
                <p>
                    L’article que vous avez commandé est actuellement indisponible. Nous mettons tout en œuvre pour
                    obtenir une date d’approvisionnement auprès de notre fournisseur.
                </p>
                <p>
                    Dès qu’une date d’approvisionnement nous est confirmée, les détails de votre commande seront
                    actualisés.
                </p>
                <p>
                    Si vous souhaitez modifier votre commande, contactez-nous. Nous essaierons de trouver un article
                    similaire à celui ou ceux qui font actuellement défaut.
                </p>
            </div>
        </side-panel>
    </div>
</template>

<script>
import AccountCustomerOrderParcelTrackingModal from './AccountCustomerOrderParcelTrackingModal.vue'
import format from 'date-fns/format'
import fr from 'date-fns/locale/fr'
import en from 'date-fns/locale/en'
import api from '@/shared/api/handler'
import SvdButton from '@/v5/components/button/SvdButton.vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'
import SvdMessage from '@/v5/components/message/SvdMessage.vue'
import { formatPrice } from '@/shared/filters'
import CustomerOrderStepper from '@/account/orders/components/CustomerOrderStepper.vue'
import SvdLink from '@/v5/components/link/SvdLink.vue'
import SvdBadge from '@/v5/components/badge/SvdBadge.vue'
import CustomerMessages from '@/account/orders/components/CustomerMessages.vue'
import SidePanel from '@/shared/components/SidePanel.vue'
import { STATUS_TRANSLATIONS } from '@/account/orders/common'
import CustomerOrderProduct from '@/account/orders/components/CustomerOrderProduct.vue'

export default {
    name: 'Order',
    components: {
        CustomerOrderProduct,
        SidePanel,
        CustomerMessages,
        SvdBadge,
        SvdLink,
        CustomerOrderStepper,
        SvdMessage,
        SvdIcon,
        SvdButton,
        AccountCustomerOrderParcelTrackingModal,
    },
    props: {
        customerOrderId: {
            type: String,
            required: true,
        },
        messages: {
            type: String,
            required: false,
        },
    },
    data() {
        return {
            show_modal: false,
            modal_data: null,
            customer_order: null,
            loading: true,
            error_message: null,
            show_choices: false,
            is_open_side_panel: false,
        }
    },
    computed: {
        validated_payments() {
            return this.customer_order.payments.filter((p) => p.validation_date !== null && p.cancel_date === null)
        },
        is_cancelled_order() {
            return this.customer_order.status === 'CANCELLED'
        },
        is_awaiting_vir() {
            return this.isAwaitingPayment('VIR')
        },
        is_awaiting_svdcc() {
            return this.isAwaitingPayment('SVDCC')
        },
        is_awaiting_tel() {
            return this.isAwaitingPayment('TEL')
        },
        return_parcel() {
            return `${window.location.origin}/mon-compte/retour-commande/creation-etape-1/${this.customer_order.customer_order_id}`
        },
        total_payments() {
            if (!this.customer_order) {
                return 0
            }
            return this.validated_payments.reduce((total, payment) => total + payment.amount, 0)
        },
        balance() {
            return this.customer_order.total_price - this.total_payments
        },
        has_rest_to_pay() {
            return this.customer_order.status === 'AWAITING_PAYMENT' && this.balance > 0
        },
        is_awaiting_payment() {
            return this.is_cancelled_order
                ? false
                : this.is_awaiting_vir || this.is_awaiting_svdcc || this.is_awaiting_tel || this.has_rest_to_pay
        },
        status_phrase() {
            if (this.is_cancelled_order) {
                return ``
            }

            const payments = this.validated_payments.filter((p) => p?.type === 'PAYMENT')
            const payments_count = payments.length

            if (this.is_awaiting_payment || payments_count === 0) {
                return `Commande créée le ${this.formatDate(this.customer_order.created_at)}`
            }

            if (payments_count === 1) {
                return `Commande payée le ${this.formatDate(payments[0].created_at)}`
            }

            const paymentDates = payments.map((payment) => this.formatDate(payment.created_at))
            const lastPaymentDate = paymentDates.pop()

            return `Commande payée le ${paymentDates.join(', ')} et le ${lastPaymentDate}`
        },
        invoices() {
            return this.customer_order.invoices.map((number) => {
                const url = `/mon-compte/ma-facture/telechargement/${number}.pdf`
                const label = `Facture n°${number}`
                const icon = 'pdf'

                return { url, label, icon }
            })
        },
        returns() {
            return this.customer_order.return_notes.map((number) => {
                const url = `/mon-compte/retour-commande/telecharger-pdf/${number}`
                const label = `Retour n°${number}`
                const icon = 'pdf'

                return { url, label, icon }
            })
        },
        parsed_messages() {
            return JSON.parse(this.messages) ?? []
        },
        order_status_label() {
            return `Commande ${STATUS_TRANSLATIONS[this.customer_order.status].toLowerCase()}`
        },
        products_data_by_id() {
            return this.customer_order.products.reduce((products, p) => {
                products[p.article_id] = p

                return products
            }, {})
        },
        products_not_shipped_or_cancelled() {
            if (this.is_cancelled_order) {
                return this.customer_order.products
            }

            return this.customer_order.shipment_details.still_to_be_shipped.map(this.mapFullProduct)
        },
        phrase_registered_but_not_reserved() {
            return `Votre commande a bien été enregistrée mais ne sera traitée qu’à réception du paiement. Veuillez noter qu’en l’absence de paiement, les articles ne sont pour l’instant pas réservés.`
        },
    },
    methods: {
        mapFullProduct(product) {
            return Object.assign(structuredClone(this.products_data_by_id[product.product_id]), {
                quantity: product.quantity,
            })
        },
        deliveryString(status, date) {
            if (status === 'AVAILABLE') {
                return `Commande à retirer depuis le ${this.formatDate(date)}`
            }
            if (status === 'DELIVERED') {
                return `Commande livrée le ${this.formatDate(date)}`
            }
            if (status === 'BEING_PREPARED') {
                return `Commande en cours de préparation depuis le ${this.formatDate(date)}`
            }
            if (status === 'PICKED_UP' || this.customer_order.status === 'PICKED_UP') {
                return `Commande retirée le ${this.formatDate(date)}`
            }
            return `Commande expédiée le ${this.formatDate(date)}`
        },
        isAwaitingPayment(code) {
            return (
                this.customer_order.payments.filter(
                    (p) => p.code === code && p.validation_date === null && p.cancel_date === null
                ).length > 0
            )
        },
        copyToClipboard() {
            navigator.clipboard.writeText('FR7630066106460001095830474')
        },
        formatPriceValue(value) {
            return formatPrice(value)
        },
        formatDate(date) {
            if (date) {
                return format(new Date(date), 'DD MMMM YYYY', {
                    locale: this.getObjectLocale('fr'),
                })
            }

            return 'Date indisponible'
        },
        getObjectLocale(locale) {
            if (locale === 'fr') {
                return fr
            }

            return en
        },
        getCustomerOrder() {
            this.loading = true
            api.get(`/api/customer-order/tracking/${this.customerOrderId}`)
                .then((data) => {
                    if (data.status === 'fail') {
                        this.error_message = data.data.error
                        this.customer_order = null

                        return
                    }

                    this.customer_order = data.data.customer_order
                })
                .catch((error) => {
                    this.customer_order = null
                    this.error_message = error?.data?.data?.error ?? 'Désolés, une erreur est survenue.'
                })
                .finally(() => (this.loading = false))
        },
        openModal(parcels) {
            this.modal_data = parcels
            this.show_modal = true
        },
        closeModal() {
            this.show_modal = false
            this.modal_data = null
        },
        /**
         * Check if there is parcel but no parcel_tracking
         * To display the tracking number but not the tracking number button
         */
        canTrackParcel(parcels) {
            return (
                !this.customer_order.is_pickup_store &&
                parcels !== null &&
                parcels.length > 0 &&
                parcels.some((parcel) => {
                    return parcel.tracks.length > 0
                })
            )
        },
        deliveryNoteStatusLabel(status) {
            return STATUS_TRANSLATIONS[status]
        },
        transferStatusLabel(status) {
            const TRANSLATIONS = {
                SHIPPED: 'Expédié',
                BEING_PREPARED: 'En cours de préparation',
            }

            return TRANSLATIONS[status]
        },
    },
    created() {
        this.getCustomerOrder()
    },
}
</script>

<style scoped lang="scss">
@import '../../../../../Resources/scss/style';
.my-order {
    display: flex;
    flex-direction: column;
    gap: $space_16;
    @include media_min($media_md) {
        gap: $space_32;
    }

    .status {
        font-size: $font_size_12;
        line-height: $height_18;
        border-radius: $space_4;
        padding: $space_4 $space_12;
        text-transform: uppercase;
        font-weight: $font_bold;
        width: fit-content;
        color: $color_white;
        align-items: center;
        align-content: center;
        text-align: center;
        &.warning {
            background-color: $color_warning;
        }
        &.success {
            background-color: $color_success;
        }
    }
    .customer-order {
        display: flex;
        flex-direction: column;
        gap: $space_16;
        @include media_min($media_md) {
            gap: $space_32;
        }
        .command-number {
            display: flex;
            flex-direction: row;
            gap: $space_16;
            .order-label {
                display: flex;
                flex-direction: row;
                gap: $space_16;
                color: $color_dark_default;
                font-weight: $font_bold;
                font-size: $font_size_18;
                line-height: $height_26;
            }
        }
        .order-date {
            color: $color_grey_typo;
            font-size: $font_size_15;
            line-height: $height_24;
            font-weight: $font_normal;
        }
        .command-messages {
            display: flex;
            flex-direction: column;
            gap: $space_16;
            font-size: $font_size_15;
            line-height: $height_24;
            color: $color_dark_default;
            @include media_min($media_md) {
                gap: $space_32;
            }
            .title {
                font-size: $font_size_18;
                font-weight: $font_bold;
                line-height: $height_26;
                color: $color_blue_web_safe;
            }
            .big {
                font-weight: $font_bold;
            }
            .focus {
                font-weight: $font_bold;
                color: $color_blue_web_safe;
            }
            .line {
                display: flex;
                flex-direction: row;
                gap: $space_16;
                text-align: center;
                align-items: center;
            }
            .copy {
                display: flex;
                flex-direction: row;
                text-align: center;
                align-items: center;
                cursor: pointer;
            }
            .actions {
                display: flex;
                display: flex;
                flex-direction: row;
                justify-content: center;
                text-align: center;
                align-items: center;
                div {
                    width: fit-content;
                }
            }
        }
        .command-actions {
            display: flex;
            flex-direction: row;
            gap: $space_16;
            div {
                width: fit-content;
            }
        }
        .command-adresses {
            display: flex;
            flex-direction: column;
            gap: $space_16;
            width: 100%;
            @include media_min($media_md) {
                flex-direction: row;
                gap: $space_32;
            }
            .command-adresse {
                display: flex;
                flex-direction: column;
                border: solid 1px $color_grey_default;
                border-radius: $space_8;
                width: 100%;
                padding: $space_16;
                gap: $space_8;
                color: $color_grey_typo;
                font-size: $font_size_15;
                line-height: $height_24;
                font-weight: $font_normal;
                font-family: $font_open_sans;
                @include media_min($media_md) {
                    padding: $space_32;
                    gap: $space_16;
                }
                .address-title {
                    color: $color_dark_default;
                    font-size: $font_size_18;
                    line-height: $height_26;
                    font-weight: $font_semi_bold;
                }
            }
        }
        .command-total {
            display: flex;
            flex-direction: column;
            border: solid 1px $color_grey_default;
            border-radius: $space_8;
            width: 100%;
            padding: $space_16;
            gap: $space_8;
            color: $color_grey_typo;
            font-size: $font_size_15;
            line-height: $height_24;
            font-weight: $font_normal;
            font-family: $font_open_sans;
            @include media_min($media_md) {
                padding: $space_32;
                gap: $space_16;
            }
            .amount {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                text-align: center;
                vertical-align: center;
                div:last-child {
                    font-weight: $font_bold;
                }
                .free {
                    color: $color_success;
                }
            }
            .global-price {
                display: flex;
                flex-direction: column;
                border-top: solid 2px $color_grey_default;
                padding-top: $space_8;
                gap: $space_8;
                color: $color_grey_typo;
                font-size: $font_size_18;
                line-height: $height_24;
                font-weight: $font_bold;
                font-family: $font_open_sans;
                text-transform: uppercase;
                @include media_min($media_md) {
                    padding-top: $space_16;
                    gap: $space_16;
                }
            }
        }
        .command-faq {
            border: solid 1px $color_grey_default;
            border-radius: $space_8;
            padding: $space_16;
            gap: $space_8;
            display: flex;
            flex-direction: column;
            @include media_min($media_md) {
                padding: $space_32;
                gap: $space_16;
            }
            .faq-title {
                color: $color_dark_default;
                font-size: $font_size_18;
                line-height: $height_26;
                font-weight: $font_semi_bold;
            }
            iframe {
                border: solid 1px $color_blue_web_safe;
            }
        }
        .command-client-messages {
            gap: $space_8;
            display: flex;
            flex-direction: column;
            @include media_min($media_md) {
                gap: $space_16;
            }
            .client-messages-title {
                color: $color_dark_default;
                font-size: $font_size_26;
                line-height: $height_36;
                font-weight: $font_bold;
                padding-bottom: $space_8;
                @include media_min($media_md) {
                    padding-bottom: $space_16;
                }
            }
        }
        .command-status {
            border: solid 1px $color_grey_default;
            border-top-left-radius: $space_8;
            border-top-right-radius: $space_8;
            padding: $space_16;
            display: flex;
            flex-direction: column;
            @include media_min($media_md) {
                padding: $space_32;
            }
            .status-label {
                color: $color_dark_default;
                font-size: $font_size_15;
                font-weight: $font_bold;
                line-height: $height_26;
            }
            .payment-date {
                display: flex;
                flex-direction: row;
                color: $color_grey_typo;
                font-size: $font_size_15;
                font-weight: $font_normal;
                line-height: $height_26;
                gap: $space_4;
                @include media_min($media_md) {
                    gap: $space_8;
                }
            }
        }
        .command-body {
            border: solid 1px $color_grey_default;
            border-top: unset;
            border-bottom-left-radius: $space_8;
            border-bottom-right-radius: $space_8;
            padding: $space_16;
            display: flex;
            flex-direction: column;
            @include media_min($media_md) {
                padding: $space_32;
            }
            .command-messages {
                margin-bottom: $space_8;
                @include media_min($media_md) {
                    margin-bottom: $space_16;
                }
            }
            .container-bloc {
                display: flex;
                flex-direction: column;
                gap: $space_8;
                @include media_min($media_md) {
                    gap: $space_16;
                }
            }
            .products-headline {
                font-size: $font_size_15;
                line-height: $height_24;
                color: $color_dark_default;
                font-weight: $font_semi_bold;
                border-top: solid 2px $color_grey_default;
                padding-top: $space_16;
                &.no-border {
                    border-top: none;
                    padding-top: 0;
                }
            }
            .command-products {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                align-items: flex-start;
                gap: $space_16;
            }
            .parcel {
                font-size: $font_size_15;
                line-height: $height_24;
                color: $color_dark_default;
                font-weight: $font_normal;
                .title {
                    font-weight: $font_semi_bold;
                }
            }
            .parcel-content {
                width: 100%;
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;
                gap: $space_8;
                @include media_min($media_md) {
                    gap: $space_16;
                }
                .actions {
                    width: fit-content;
                }
            }
        }
    }
}

.unknown-delivery-time-explanation {
    p {
        display: flex;
        flex-direction: column;
        gap: $space_16;
        font-size: $font_size_15;
        font-weight: $font_normal;
        line-height: $height_22;
        color: $color_grey_typo;
        padding-bottom: $space_16;
    }
}
</style>

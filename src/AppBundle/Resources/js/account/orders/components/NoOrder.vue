<script setup>
import { asset } from '@/shared/filters.js'
</script>

<template>
    <div class="no-order" data-context="no-order">
        <img
            :src="asset('/images/ui/uiV3/graphics/SVDILL_201803-Commande.jpg', 'static_images')"
            alt="Aucune commande"
        />
        Préparez-vous à redécouvrir vos films et musiques préférés.<br />
        Passez votre première commande et entrez dans un nouveau monde.
    </div>
</template>

<style scoped lang="scss">
@import '../../../../../Resources/scss/style';

.no-order {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: $space_16;
    font-size: $font_size_16;
    line-height: $height_20;
    font-weight: $font_semi_bold;
    @include media_min($media_md) {
        gap: $space_32;
    }
}
</style>

import Vue from 'vue'
import store from './store/index.js'
import ProductAnswerItem from './components/ProductAnswerItem'
import { trans, transChoice } from '../../shared/filters.js'

Vue.filter('trans', trans)
Vue.filter('transChoice', transChoice)

new Vue({
    el: '#product-answer-item-app',
    name: 'ProductAnswerItemApp',
    store,
    components: {
        ProductAnswerItem,
    },
    created() {
        const SonVideo = window.SonVideo
        this.$store.dispatch('initializeState', {
            api: SonVideo.api,
        })
        this.$store.dispatch('retrieveCommonContentAnswersAssociatedProducts')
    },
})

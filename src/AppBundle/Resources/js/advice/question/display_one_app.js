import Vue from 'vue'
import store from './store/index.js'
import ProductQuestionItem from './components/ProductQuestionItem'
import { trans, transChoice } from '../../shared/filters.js'

Vue.filter('trans', trans)
Vue.filter('transChoice', transChoice)

new Vue({
    el: '#product-questions-item-app',
    name: 'ProductQuestionsItemApp',
    store,
    components: {
        ProductQuestionItem,
    },
    created() {
        const SonVideo = window.SonVideo
        this.$store.dispatch('initializeState', {
            api: SonVideo.api,
        })
    },
})

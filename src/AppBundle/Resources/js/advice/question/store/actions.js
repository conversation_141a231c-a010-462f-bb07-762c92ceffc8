import * as types from './mutation-types'
import api from '../../../shared/api/handler'

export const actions = {
    initializeState({ commit }, { api }) {
        commit(types.SET_URLS, api)
    },
    retrieveAllQuestionsData({ state, commit }) {
        commit(types.SET_INITIALIZATION_STATUS, 'loading')
        api.get(state.urls.get_all_questions_data_url)
            .then((data) => {
                commit(types.UPDATE_QUESTIONS, data.questions)
                commit(types.UPDATE_ANSWERS, data.associated_products)
                commit(types.SET_INITIALIZATION_STATUS, 'loaded')
            })
            .catch(() => {
                commit(types.SET_INITIALIZATION_STATUS, 'not_loaded')
            })
    },
    pageSelected({ commit }, page) {
        return new Promise((resolve) => {
            commit(types.UPDATE_PAGE, page)
            resolve()
        })
    },
    changeSelectedSort({ commit }, selected_index) {
        commit(types.UPDATE_SELECTED_SORTING_OPTION, Number.parseInt(selected_index))
    },
    updateSearchQuery({ commit }, search_query) {
        commit(types.UPDATE_SEARCH_QUERY, search_query)
    },
    focusOnQuestion({ state, getters, dispatch }, question_id) {
        // reset the search
        if (state.search_query) {
            dispatch('updateSearchQuery', '')
        }

        // search and set the page
        // then scroll to the question
        const index = getters.sorted_questions.findIndex(
            (question) => question.common_content_question_id === question_id
        )
        const page_number = Math.floor(index / state.questions_per_page) + 1
        dispatch('pageSelected', page_number).then(() => {
            window.SonVideo.smoothScroll.scrollTo(`#question-${question_id}`, 0)

            // click on the toggler to show answers
            // (ugly, but the preferred way to toggle through the store would be longer to implement and refactor)
            if ($(`#question-${question_id} .product-answers-item:hidden`).length > 0) {
                $(`#question-${question_id} .toggle-answers>a`)[0].click()
            }
        })
    },
    retrieveCommonContentAnswersAssociatedProducts({ state, commit }) {
        api.get(state.urls.get_common_content_answers_associated_products).then((data) => {
            commit(types.UPDATE_ANSWERS, data.associated_products)
        })
    },
}

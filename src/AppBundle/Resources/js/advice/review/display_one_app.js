import Vue from 'vue'
import store from './store/index.js'
import ProductReviewItem from './components/ProductReviewItem'
import { trans, transChoice } from '../../shared/filters.js'

Vue.filter('trans', trans)
Vue.filter('transChoice', transChoice)

new Vue({
    el: '#product-reviews-item-app',
    name: 'ProductReviewItemApp',
    store,
    components: {
        ProductReviewItem,
    },
    created() {
        const SonVideo = window.SonVideo
        this.$store.dispatch('initializeState', {
            api: SonVideo.api,
        })
    },
})

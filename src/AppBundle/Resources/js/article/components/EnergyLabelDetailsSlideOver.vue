<template>
    <side-panel title="" content-class="energy-label-details" v-if="ready_and_show" @close="show = false">
        <div class="d-flex justify-content-center w-full" data-context="energy-label-details">
            <img :src="image_src" class="mt-3" width="300px" alt="Fiche signalétique" />
        </div>
    </side-panel>
</template>

<script>
import SidePanel from '../../shared/components/SidePanel'
export default {
    name: 'EnergyLabelDetailsSlideOver',
    components: { SidePanel },
    data() {
        return {
            show: false,
            image_src: null,
        }
    },
    computed: {
        ready_and_show() {
            return this.show && !!this.image_src
        },
    },
    methods: {
        openWith(image = null) {
            if (image) {
                this.image_src = image
            }
            this.show = true
        },
    },
}
</script>

<template>
    <span v-if="highlight_media !== ''">
        <a
            v-if="highlight_url"
            :href="highlight_url"
            class="product-highlight label label-md"
            :class="'label-' + highlight_class"
            aria-label="Article mis en avant"
            >{{ ('article.highlight.' + highlight_class) | trans({}, 'search') }}</a
        >
        <span v-else class="product-highlight label label-md" :class="'label-' + highlight_class">{{
            ('article.highlight.' + highlight_class) | trans({}, 'search')
        }}</span>
    </span>
</template>

<script>
import { getHighlightClassForType, getHighlightForType, getHighlightMediaForType } from '../../shared/article/functions'
import { asset, trans } from '../../shared/filters'
import Vue from 'vue'

Vue.filter('asset', asset)
Vue.filter('trans', trans)

export default {
    name: 'ProductHighlight',
    props: {
        article: {
            type: Object,
            required: true,
        },
    },
    computed: {
        highlight_media() {
            return getHighlightMediaForType(this.article, 'normal')
        },
        highlight_class() {
            return getHighlightClassForType(this.article, 'normal')
        },
        highlight_url() {
            let highlight = getHighlightForType(this.article, 'normal')
            if (highlight === null || typeof highlight.tag_path === 'undefined') {
                return ''
            }
            let tag = highlight.tag_path.substr(18)

            return typeof window.highlight_links[tag] !== 'undefined' ? window.highlight_links[tag] : ''
        },
    },
    methods: {
        asset,
        trans,
    },
}
</script>

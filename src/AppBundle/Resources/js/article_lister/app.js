import Vue from 'vue'
import store from './store/index'
import api, { makeUrlQueryString, updateUrlQueryString } from '../shared/api/handler'
import ArticleList from './components/ArticleList'
import ArticleFilters from './components/ArticleFilters'
import { mapActions, mapGetters, mapState } from 'vuex'
import { asset, capitalize, comaDelimiter, formatPriceSvd, trans, transChoice } from '../shared/filters'
import { extractUrlParams } from '../shared/functions'
import { LINES } from './referential/DISPLAY_OPTIONS'

Vue.filter('trans', trans)
Vue.filter('capitalize', capitalize)
Vue.filter('formatPriceSvd', formatPriceSvd)
Vue.filter('asset', asset)
Vue.filter('transChoice', transChoice)
Vue.filter('commaDelimiter', comaDelimiter)

/* eslint-disable no-new */
new Vue({
    el: '#article_lister',
    name: 'ArticleListerApp',
    store,
    components: { ArticleFilters, ArticleList },
    computed: {
        ...mapState(['sort_by', 'page', 'display', 'is_first_load_complete', 'default_selected_sorting_option']),
        ...mapGetters(['article_ids_to_retrieve', 'selected_filters']),
        selected_filter_keys() {
            let filter_keys = []
            this.selected_filters.forEach((selected_filter) => {
                for (const [key, value] of Object.entries(selected_filter.filter.values)) {
                    if (value.selected === true) {
                        filter_keys.push(key)
                    }
                }
            })

            return filter_keys
        },
        // parameters that should be in the url based on current state
        url_params() {
            return (
                [
                    {
                        name: 'tri',
                        value: this.sort_by,
                        default: this.default_selected_sorting_option,
                    },
                    {
                        name: 'page',
                        value: this.page,
                        default: 1,
                    },
                    {
                        name: 'filtres',
                        value: this.selected_filter_keys,
                    },
                    {
                        name: 'affichage',
                        value: this.display,
                        default: LINES,
                    },
                ]
                    // keep only relevant values
                    .filter((param) =>
                        Array.isArray(param.value) ? param.value.length > 0 : param.value !== param.default
                    )
                    // merge in a single object
                    .reduce((acc, current) => {
                        let value = current.value
                        if (typeof current.value === 'object' || Array.isArray(current.value)) {
                            value = JSON.stringify(current.value)
                        }
                        acc[current.name] = value

                        return acc
                    }, {})
            )
        },
    },
    methods: {
        ...mapActions(['restoreParamsFromUrl', 'updatePaginationOption', 'retrieveArticles']),
    },
    created() {
        this.$store.dispatch('initializeData', {
            default_selected_sorting_option: window.default_selected_sorting_option,
        })
    },
    mounted() {
        // retrieve parameters from the url
        let { tri, page, filtres, affichage } = extractUrlParams()

        // avoid error due to previous way to handle filters in url
        if (!Array.isArray(filtres)) {
            filtres = []
        }

        this.$store.dispatch('initApp', { tri, page, affichage })

        api.get(`/rayon/${this.$el.getAttribute('data-slug')}/_filters`).then((response) => {
            this.$store.dispatch('loadFilters', { filters: response.data.filters, selected_keys: filtres })
        })

        // listen to moves in the browser history
        window.addEventListener('popstate', this.restoreParamsFromUrl)
    },
    beforeDestroy() {
        window.removeEventListener('popstate', this.restoreParamsFromUrl)
    },
    watch: {
        async article_ids_to_retrieve() {
            if (this.is_first_load_complete) {
                await this.updatePaginationOption(1)
            } else {
                await this.retrieveArticles()
            }
        },
        // update parameters in the url to reflect filtering alterations
        url_params(params) {
            // get and clean actual url parameters
            let url_search = window.location.search
            url_search = url_search
                .replace(/[\?&]tri=[^&]*/g, '')
                .replace(/[\?&]page=[^&]*/g, '')
                .replace(/[\?&]filtres=[^&]*/g, '')
                .replace(/[\?&]affichage=[^&]*/g, '')
            // convert dynamic params to url usage
            const url_qs = makeUrlQueryString(params)

            // concat
            const search = url_search + (url_search.length > 0 && url_qs.length > 0 ? `&` : '') + url_qs
            updateUrlQueryString(search)
        },
    },
})

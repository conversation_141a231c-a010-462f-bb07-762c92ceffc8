<template>
    <div class="search-filters-block" data-type="default" :class="{ 'is-open': is_open }">
        <div
            class="d-flex justify-content-between search-filters-heading align-items-baseline cursor-pointer"
            @click="display"
        >
            <span class="search-filters-title">{{ filter.meta.label }}</span>
            <span>
                <i class="icon icon-chevron-bottom" data-context="filter-collapse-chevron"></i>
            </span>
        </div>

        <template v-if="is_open">
            <div class="search-filters-body">
                <div v-if="uses_internal_filter" class="internal-filter" data-context="internal-filter">
                    <label for="search">
                        <span class="icon-search">
                            <span class="sr-only">
                                {{ 'search' | trans({}, 'question') }}
                            </span>
                        </span>
                    </label>
                    <input type="text" id="search" v-model="internal_search" />
                </div>

                <ul class="search-filters-checkboxes filters_expanded">
                    <article-filter-type-default-checkbox
                        :facet="filter_value"
                        :selected_articles_ids_pool="selected_articles_ids_pool"
                        :key="filter_value.key"
                        v-for="filter_value in displayed_filter_values"
                        :display_as_stars="filter.name === 'score'"
                        :translate_value="has_option_translate_value"
                        :prefix="filter.meta.prefix"
                        :suffix="filter.meta.suffix"
                        @notify="onNotify"
                    />
                </ul>
                <a
                    aria-label="Retirer filtre de recherche"
                    href=""
                    class="search-filters-remove"
                    @click.prevent="onReset"
                    >Réinitialiser</a
                >
            </div>
        </template>
    </div>
</template>

<script>
import ArticleFilterTypeDefaultCheckbox from './ArticleFilterTypeDefaultCheckbox'
import { mapActions, mapState, mapGetters } from 'vuex'
import * as options from '../referential/FILTER_OPTIONS'
import { isNumeric } from '../../shared/functions'

export default {
    name: 'ArticleFilterTypeDefault',
    components: { ArticleFilterTypeDefaultCheckbox },
    props: {
        filter: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            is_open: this.filter.meta.hasOwnProperty('is_open') && !!this.filter.meta.is_open,
            internal_search: '',
        }
    },
    computed: {
        ...mapState(['pool_article_ids']),
        ...mapGetters(['selected_filters']),
        uses_internal_filter() {
            return this.filter.meta.hasOwnProperty('internal_filter') && this.filter.meta.internal_filter
        },
        has_option_sort_by_value_desc() {
            return (
                this.filter.meta.hasOwnProperty('display_options') &&
                this.filter.meta.display_options.includes(options.SORT_BY_VALUE_DESC)
            )
        },
        has_option_translate_value() {
            return (
                this.filter.meta.hasOwnProperty('display_options') &&
                this.filter.meta.display_options.includes(options.TRANSLATE_VALUE)
            )
        },
        // values extracted from the filter and ordered
        sorted_filter_values() {
            return Object.entries(this.filter.values)
                .map(([key, filter_value]) => filter_value)
                .sort((a, b) => {
                    if (isNumeric(a.value) && isNumeric(a.value)) {
                        return this.has_option_sort_by_value_desc ? b.value - a.value : a.value - b.value
                    } else {
                        return a.display_order === b.display_order
                            ? a.value.localeCompare(b.value)
                            : a.display_order - b.display_order
                    }
                })
        },
        // sorted values to display
        displayed_filter_values() {
            return this.sorted_filter_values.filter((filter_value) => {
                return this.uses_internal_filter
                    ? filter_value.value.toLowerCase().includes(this.internal_search.toLowerCase())
                    : true
            })
        },
        // intersection of the articles ids selected by the other filters
        selected_articles_ids_pool() {
            return this.selected_filters
                .filter((selected_filter) => selected_filter.filter.name !== this.filter.name)
                .map((selected_filter) => selected_filter.selected_article_ids)
                .reduce(
                    (selected_article_ids, article_ids) =>
                        selected_article_ids.filter((aid) => article_ids.includes(aid)),
                    this.pool_article_ids
                )
        },
    },
    methods: {
        ...mapActions(['removeFacets']),
        onReset() {
            this.removeFacets(Object.keys(this.filter.values))
            this.internal_search = ''
            window.dataLayer.push({
                event: 'eventTracking',
                category: 'Filtres',
                action: 'Retrait',
                label: `Retrait>${this.filter.meta.label}>Tous`,
            })

            window.dataLayer.push({
                event: 'search_filter',
                label: `Retrait>${this.filter.meta.label}>Tous`,
            })
        },
        display() {
            if (this.is_open === false) {
                window.dataLayer.push({
                    event: 'eventTracking',
                    category: 'Filtres',
                    action: 'Ouverture',
                    label: `Ouverture>${this.filter.meta.label}`,
                })
                window.dataLayer.push({
                    event: 'search_filter',
                    label: `Ouverture>${this.filter.meta.label}`,
                })
            } else {
                window.dataLayer.push({
                    event: 'eventTracking',
                    category: 'Filtres',
                    action: 'Fermeture',
                    label: `Fermeture>${this.filter.meta.label}`,
                })
                window.dataLayer.push({
                    event: 'search_filter',
                    label: `Fermeture>${this.filter.meta.label}`,
                })
            }

            this.is_open = !this.is_open
        },
        onNotify({ is_selected, value }) {
            if (is_selected) {
                window.dataLayer.push({
                    event: 'eventTracking',
                    category: 'Filtres',
                    action: 'Retrait',
                    label: `Retrait>${this.filter.meta.label}>${value}`,
                })
                window.dataLayer.push({
                    event: 'search_filter',
                    label: `Retrait>${this.filter.meta.label}>${value}`,
                })
            } else {
                window.dataLayer.push({
                    event: 'eventTracking',
                    category: 'Filtres',
                    action: 'Application',
                    label: `Application>${this.filter.meta.label}>${value}`,
                })
                window.dataLayer.push({
                    event: 'search_filter',
                    label: `Application>${this.filter.meta.label}>${value}`,
                })
            }
        },
    },
}
</script>

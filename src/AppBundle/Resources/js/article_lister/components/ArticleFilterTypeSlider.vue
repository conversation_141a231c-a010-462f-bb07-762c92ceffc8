<template>
    <div class="search-filters-block" data-type="slider" :class="{ 'is-open': is_open }">
        <div
            class="d-flex justify-content-between search-filters-heading align-items-baseline cursor-pointer"
            @click="display"
        >
            <span class="search-filters-title">{{ filter.meta.label }}</span>
            <span>
                <i class="icon icon-chevron-bottom"></i>
            </span>
        </div>

        <template v-if="is_open">
            <div class="search-filters-body">
                <div class="d-flex justify-content-center">
                    <form action="" v-if="has_valid_constraints" @submit.prevent="submitValues">
                        <label for="min_input">
                            {{ 'from' | trans({}, 'search') }}
                        </label>
                        <!-- the step attribute allows float in number field, otherwise browser validation would fail -->
                        <input
                            id="min_input"
                            ref="min"
                            name="min_input"
                            type="number"
                            step="0.01"
                            :value="value.min"
                            @blur="updateRangeFromInput('min', $event)"
                        />
                        <label for="max_input">
                            {{ 'to' | trans({}, 'search') }}
                        </label>
                        <input
                            id="max_input"
                            ref="max"
                            name="max_input"
                            type="number"
                            step="0.01"
                            :value="value.max"
                            @blur="updateRangeFromInput('max', $event)"
                        />
                        <template v-if="suffix">{{ suffix }}</template>
                        <button aria-label="Valider" type="submit" class="d-none"></button>
                    </form>
                </div>

                <range-slider
                    :constraint="constraint"
                    v-model="value"
                    ref="range_input"
                    v-if="has_valid_constraints"
                    @notify="onNotify"
                    :scale="scale"
                    :steps="values"
                    :use-decimal="useDecimal"
                />

                <a
                    aria-label="Retirer filtre de recherche"
                    href=""
                    class="search-filters-remove"
                    @click.prevent="onReset()"
                    >Réinitialiser</a
                >
            </div>
        </template>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import RangeSlider from '../../shared/components/form/RangeSlider'
import debounce from 'lodash.debounce'

export default {
    name: 'ArticleFilterTypeSlider',
    components: { RangeSlider },
    props: {
        filter: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            is_open: !!this.filter?.meta?.is_open,
            value: { min: 0, max: 0 },
            previous_selected_values: [],
            freeze_next_propagation: false,
        }
    },
    computed: {
        ...mapGetters(['selected_filters']),
        // all the numeric values selectable
        values() {
            return Object.keys(this.filter.values)
                .map((o) => {
                    return parseFloat(this.filter.values[o].value)
                })
                .sort((a, b) => a - b)
        },
        // min possible value
        min_boundary() {
            return Math.floor(Math.min(...this.values))
        },
        // max possible value
        max_boundary() {
            return Math.ceil(Math.max(...this.values))
        },
        constraint() {
            return { min: this.min_boundary, max: this.max_boundary }
        },
        has_valid_constraints() {
            return this.constraint.min >= 0 && this.constraint.max >= 0
        },
        slider_is_inactive() {
            return (
                this.has_valid_constraints &&
                this.min_boundary === this.value.min &&
                this.max_boundary === this.value.max
            )
        },
        suffix() {
            return this.filter.hasOwnProperty('meta') && this.filter.meta.hasOwnProperty('suffix')
                ? this.filter.meta.suffix
                : null
        },
        // based on the slider selection, compute the filter's values (keys) that should be selected
        should_be_selected_values() {
            if (this.slider_is_inactive) {
                return []
            }

            return Object.entries(this.filter.values)
                .filter(
                    ([key, filter_value]) =>
                        filter_value.value >= this.value.min && filter_value.value <= this.value.max
                )
                .map(([key]) => key)
        },
        // compare the keys previously selected and the one that should be selected to define what to do (add/remove)
        keys_to_update() {
            return {
                add: this.should_be_selected_values.filter(
                    (selected) => !this.previous_selected_values.includes(selected)
                ),
                remove: this.previous_selected_values.filter(
                    (selected) => !this.should_be_selected_values.includes(selected)
                ),
            }
        },
        // scale used by the range slider
        scale() {
            return this.filter?.meta?.scale
        },
        useDecimal() {
            return this.filter?.meta?.useDecimal ?? false
        },
    },
    methods: {
        ...mapActions(['updateFacets', 'forceFilter', 'unforceFilter']),
        submitValues() {
            this.$refs.min.blur()
            this.$refs.max.blur()
        },
        updateRangeFromInput(input_type, $event) {
            this.$refs.range_input.onExternalValueChange(input_type, $event.target.valueAsNumber)
        },
        onReset() {
            window.dataLayer.push({
                event: 'eventTracking',
                category: 'Filtres',
                action: 'Retrait',
                label: `Retrait>${this.filter.meta.label}>${this.value.min}-${this.value.max}`,
            })

            window.dataLayer.push({
                event: 'search_filter',
                label: `Retrait>${this.filter.meta.label}>${this.value.min}-${this.value.max}`,
            })
            this.value = Object.assign({}, this.constraint)
        },
        onKeysUpdated() {
            // update selection
            const to_add = this.keys_to_update.add.map((key) => {
                return { key, status: true }
            })
            const to_remove = this.keys_to_update.remove.map((key) => {
                return { key, status: false }
            })
            this.updateFacets(to_add.concat(to_remove))

            // update forced_select
            if (this.slider_is_inactive) {
                this.unforceFilter(this.filter.name)
            } else {
                this.forceFilter(this.filter.name)
            }

            // memorize for next time
            this.previous_selected_values = this.should_be_selected_values
        },
        display() {
            if (this.is_open === false) {
                window.dataLayer.push({
                    event: 'eventTracking',
                    category: 'Filtres',
                    action: 'Ouverture',
                    label: `Ouverture>${this.filter.meta.label}`,
                })
                window.dataLayer.push({
                    event: 'search_filter',
                    label: `Ouverture>${this.filter.meta.label}`,
                })
            } else {
                window.dataLayer.push({
                    event: 'eventTracking',
                    category: 'Filtres',
                    action: 'Fermeture',
                    label: `Fermeture>${this.filter.meta.label}`,
                })
                window.dataLayer.push({
                    event: 'search_filter',
                    label: `Fermeture>${this.filter.meta.label}`,
                })
            }
            this.is_open = !this.is_open
        },
        onNotify({ min, max }) {
            window.dataLayer.push({
                event: 'eventTracking',
                category: 'Filtres',
                action: 'Application',
                label: `Application>${this.filter.meta.label}>${min}-${max}`,
            })
            window.dataLayer.push({
                event: 'search_filter',
                label: `Application>${this.filter.meta.label}>${min}-${max}`,
            })
        },
    },
    created() {
        // debounced version of the onKeysUpdated method
        this.debouncedOnKeysUpdated = debounce(this.onKeysUpdated, 300)
    },
    mounted() {
        // retrieve values selected at store init time to set the slider value
        // the slider must not update the store at this spot
        const selected_filter = this.selected_filters.find((sf) => sf.filter.name === this.filter.name)
        if (selected_filter) {
            this.freeze_next_propagation = true
            let boundaries = Object.values(selected_filter.filter.values).reduce(
                (boundaries, v) => {
                    if (v.selected && parseFloat(v.value) < parseFloat(boundaries.min.toString())) {
                        boundaries.min = v.value
                    }
                    if (v.selected && parseFloat(v.value) > parseFloat(boundaries.max.toString())) {
                        boundaries.max = v.value
                    }

                    return boundaries
                },
                { min: this.constraint.max, max: this.constraint.min }
            )
            this.value = Object.assign({}, boundaries)

            this.previous_selected_values = this.should_be_selected_values
        } else {
            this.value = Object.assign({}, this.constraint)
        }
    },
    watch: {
        keys_to_update() {
            if (!this.freeze_next_propagation) {
                this.debouncedOnKeysUpdated()
            } else {
                this.freeze_next_propagation = false
            }
        },
        // force sliders to reset if key removed from the store
        selected_filters(sf) {
            if (!this.slider_is_inactive && sf.filter((f) => f.filter.name === this.filter.name).length === 0) {
                this.onReset()
            }
        },
    },
}
</script>

<style lang="scss" scoped>
// Remove arrows from number field
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type='number'] {
    -moz-appearance: textfield;
}
</style>

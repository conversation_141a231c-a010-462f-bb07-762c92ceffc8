<template>
    <div data-context="search-articles">
        <!-- Sorting options -->
        <div v-if="displaySortingOptions != 'false'" id="SVDv3_CmsRayon_sortingOptions">
            <div class="grid_container_12">
                <div class="grid_row">
                    <div id="SVDv3_CmsRayon_orderBy" class="col_6_col">
                        {{ 'order_by' | trans({}, 'search') }}
                        <select
                            name="items_sorting_options"
                            data-context="sorting-options"
                            v-model="selected_sorting_option"
                        >
                            <option v-for="(option, index) in sorting_options" :value="option.value">
                                {{ option.label | trans({}, 'search') }}
                            </option>
                        </select>
                    </div>
                    <div id="SVDv3_CmsRayon_displayType" class="col_6_col col_last text-right hidden-sm hidden-xs">
                        <a
                            class="btn btn-sm"
                            href=""
                            @click.prevent="updateDisplayOption(display_options.LINES)"
                            :class="[display === display_options.LINES ? 'btn-primary' : 'btn-default']"
                            data-context="toggle-line"
                        >
                            <i class="icon icon-bars">
                                <span class="sr-only">{{ 'display_line' | trans({}, 'common') }}</span>
                            </i>
                        </a>
                        <a
                            class="btn btn-sm"
                            href=""
                            @click.prevent="updateDisplayOption(display_options.GRID)"
                            :class="[display === display_options.GRID ? 'btn-primary' : 'btn-default']"
                            data-context="toggle-grid"
                        >
                            <i class="icon icon-th">
                                <span class="sr-only">{{ 'display_grid' | trans({}, 'common') }}</span>
                            </i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <template v-if="(is_loading_filters || is_loading_articles) && !init">
            <div class="article-list-skeleton" v-for="article_skeleton in article_skeletons"></div>
        </template>
        <template v-else>
            <!-- List or grid display -->
            <component :is="listComponent" :articles="formatted_articles"> </component>

            <!-- No article display -->
            <div class="nothing" v-show="articles.length === 0 && !is_loading_articles">
                {{ 'no_article_match_your_request' | trans({}, 'search') }}
            </div>

            <article-list-paginator />
        </template>
    </div>
</template>

<script>
import { mapActions, mapState } from 'vuex'
import capitalize from 'lodash.capitalize'
import ArticleListLines from './ArticleListLines'
import ArticleListGrid from './ArticleListGrid'
import ArticleListPaginator from './ArticleListPaginator'
import * as display_options from '../referential/DISPLAY_OPTIONS'
import * as display_modes from '../referential/DISPLAY_MODES'
import * as SORTING_OPTIONS from '../referential/SORT_OPTIONS'

export default {
    name: 'ArticleList',
    components: { ArticleListLines, ArticleListGrid, ArticleListPaginator },
    props: {
        displaySortingOptions: {
            type: String,
            default: 'true',
        },
        // By default there is no group by (empty), else you can choose a key element in the article object
        // to group articles by this key (for example "main_category_name" in some shop)
        groupBy: {
            type: String,
            default: '',
        },
        gridItemPerLine: {
            type: Number,
            default: 3,
        },
    },
    data() {
        return {
            display_options,
            article_skeletons: 20,
            init: false,
        }
    },
    computed: {
        ...mapState(['articles', 'display', 'is_loading_articles', 'is_loading_filters', 'sort_by', 'show_all_data']),
        selected_sorting_option: {
            get() {
                return this.sort_by
            },
            set(value) {
                this.updateSortingOption(value)
            },
        },
        listComponent() {
            const display_mapping = {
                GRID: 'ArticleListGrid',
                LINES: 'ArticleListLines',
            }

            return display_mapping[this.display]
        },
        formatted_articles() {
            return this.articles.map((a) => {
                a.formatted_name = this.getArticleName(a)
                a.has_score = this.hasScore(a)
                a.is_destock = this.isDestock(a)
                a.has_more_than_one = this.hasMoreThanOne(a)
                a.has_length = this.hasLength(a)
                a.formatted_length = this.formatLength(a)
                a.has_color = this.hasColor(a)
                a.formatted_color = this.formatColor(a)
                a.is_bundle = this.isBundle(a)
                a.origin_price = this.getOriginPrice(a)
                a.can_be_added_to_cart = this.canBeAddedToCart(a)
                a.promo_code = this.getPromoCode(a)
                a.second_life = this.getSecondLife(a)

                return a
            })
        },
        sorting_options() {
            return Object.values(SORTING_OPTIONS).sort((a, b) => a.order - b.order)
        },
    },
    watch: {
        is_loading_articles() {
            this.init = true
        },
    },
    methods: {
        capitalize,
        ...mapActions(['updateDisplayOption', 'updateSortingOption']),
        getOriginPrice(article) {
            return article?.origin_price?.selling_price ?? 0
        },
        getSecondLife(article) {
            return article.second_life_offer
        },
        hasScore(article) {
            return article.hasOwnProperty('score') && article.score > 0
        },
        hasLength(article) {
            return (
                article.hasOwnProperty('article_lengths') &&
                article.article_lengths !== null &&
                article.article_lengths.length > 0
            )
        },
        hasMoreThanOne(article) {
            return this.hasLength(article) && article.article_lengths.length > 1
        },
        /**
         * Check if article has property 'color_labels' filled with color(s).
         */
        hasColor(article) {
            return (
                article.hasOwnProperty('color_labels') &&
                article.color_labels !== null &&
                article.color_labels.length > 0
            )
        },
        isBundle(article) {
            // Is a bundle if it contains strictly more than 1 article (bundle with 1 article are fake bundle)
            return (
                typeof article.packaged_articles !== 'undefined' &&
                article.packaged_articles !== null &&
                article.packaged_articles.length > 1
            )
        },
        formatLength(article) {
            return this.hasLength(article)
                ? article.article_lengths.map((length) => length.replace(/\.00/g, '')).join(' / ')
                : null
        },
        formatColor(article) {
            return this.hasColor(article)
                ? article.color_labels
                      .slice()
                      .sort((a, b) => a.localeCompare(b))
                      .join(' / ')
                : null
        },
        getArticleName(article) {
            return article.editorial_content.short_description
        },
        isDestock(article) {
            return !!article.destock
        },
        canBeAddedToCart(article) {
            return (
                article.display_mode !== display_modes.CONFIDENTIAL_PRICE &&
                (article.display_mode !== display_modes.STORE_EXCLUSIVITY || this.isDestock(article)) &&
                (!article.hasOwnProperty('unbasketable_reason') || article.unbasketable_reason === null)
            )
        },
        hasPromoCode(article) {
            return (
                article.hasOwnProperty('promo_codes_description') &&
                article.promo_codes_description !== null &&
                article.promo_codes_description.length > 0
            )
        },
        getPromoCode(article) {
            return this.hasPromoCode(article) ? article.promo_codes_description[0] : null
        },
    },
}
</script>

<template>
    <div class="clearfix SVDv3_rayon_listingProduits_grille_cellule" data-context="grid-item">
        <div class="SVDv3_rayon_produit_content">
            <!-- ARTICLE IMAGE -->
            <div class="SVDv3_rayon_listingProduits_photo">
                <p>
                    <a
                        :aria-label="article.editorial_content.basket_description"
                        :href="article.article_url"
                        v-on:click="pushClickProductAnalytics"
                    >
                        <img
                            class="lozad"
                            :alt="article.formatted_name"
                            :src="emptyDataImage(150, 150)"
                            :data-src="
                                preset(asset(article.media_uri ?? ARTICLE_NO_IMAGE, 'article_images'), PRESET_140)
                            "
                            :data-srcset="
                                presets(asset(article.media_uri ?? ARTICLE_NO_IMAGE, 'article_images'), [
                                    [PRESET_95, '95w'],
                                    [PRESET_140, '140w'],
                                ])
                            "
                            :sizes="`${BREAKPOINT_TABLET_MIN_WIDTH} 140px, 95px`"
                        />
                    </a>
                </p>
            </div>

            <!-- ARTICLE DESCRIPTION -->
            <p class="SVDv3_rayon_listingProduits_description">
                <a
                    :aria-label="article_link_aria_label"
                    :href="article.article_url"
                    v-on:click="pushClickProductAnalytics"
                >
                    <strong data-context="article-name">{{ article.formatted_name }}</strong> </a
                ><br />
                <template v-if="article.has_score">
                    <span class="score-item">
                        <span class="score-item-row">
                            <span class="score-item-stars">
                                <span class="product-score"
                                    ><span class="score-stars" :style="{ width: 15 * article.score + 'px' }"></span
                                ></span>
                            </span>
                            <span class="score-item-text"
                                ><strong>{{ article.score | commaDelimiter }}</strong></span
                            >
                        </span>
                    </span>
                    <br />
                </template>
                <template v-if="article.promo_code">
                    <strong data-context="promo-code">{{ article.promo_code }}</strong>
                    <br />
                </template>
                <span v-if="article.cc_short_description" v-html="article.cc_short_description"></span>
                <span
                    v-else-if="article.editorial_content && article.editorial_content.basket_description"
                    v-html="article.editorial_content.basket_description"
                ></span>
                <br />
                <template
                    v-if="
                        article.is_destock && article.editorial_content && article.editorial_content.basket_description
                    "
                >
                    <br />
                    <b v-html="article.editorial_content.basket_description"></b>
                </template>
                <template v-if="article.has_length">
                    <br />
                    {{ 'longueur' | transChoice({ count: 1 }) }}&nbsp;:&nbsp;{{ article.article_length }}
                </template>
                <template v-if="article.has_color">
                    <br />
                    {{ 'couleur' | transChoice({ count: article.color_labels.length }) }} :
                    {{ article.formatted_color }}
                </template>
                <template v-if="show_ranking && article.ranking">
                    <br />
                    Pertinence : {{ article.ranking }}
                </template>
            </p>

            <!-- ARTICLE PRICE -->
            <div class="SVDv3_rayon_listingProduits_prix">
                <div
                    v-if="
                        article.display_mode !== display_modes.CONFIDENTIAL_PRICE &&
                        (article.hasOwnProperty('unbasketable_reason') === false ||
                            article.unbasketable_reason === null)
                    "
                >
                    <div v-if="article.reference_price" class="reference-price" data-context="reference-price">
                        <span class="percent" data-context="rounded-percentage">
                            <span class="back">-&nbsp;{{ Math.round(article.price_discount_percent) }}&nbsp;%</span>
                        </span>
                    </div>
                    <div
                        v-if="article.reference_price"
                        class="reference-price old"
                        data-context="reference-price-crossed"
                        @click="displayReferencePriceSlider"
                    >
                        <div class="crossed">
                            <span class="line-through">{{ article.reference_price | formatPriceSvd }}</span>
                            <span class="confirm">?</span>
                        </div>
                    </div>
                    <p class="SVDv3_zonePrix_prix">{{ article.price.selling_price | formatPriceSvd }}</p>
                    <p class="SVDv3_zonePrix_second_life" v-if="article.second_life">
                        <span v-if="article.second_life.count > 1">
                            +&nbsp;{{ article.second_life.count }}&nbsp;offres&nbsp;seconde<br />vie&nbsp;dès&nbsp;{{
                                article.second_life.minimum_price | formatPriceSvd
                            }}
                        </span>
                        <span v-else>
                            +&nbsp;{{ article.second_life.count }}&nbsp;offre&nbsp;seconde<br />vie&nbsp;à&nbsp;{{
                                article.second_life.minimum_price | formatPriceSvd
                            }}
                        </span>
                    </p>
                    <p
                        class="SVDv3_zonePrix_ald"
                        v-else-if="
                            article.display_mode !== display_modes.WITHOUT_INSTEAD_OF &&
                            !article.is_destock &&
                            article.price.instead_of_price &&
                            priceIsRelevant(article.price.selling_price, article.price.instead_of_price) &&
                            article.is_bundle &&
                            article.reference_price === null
                        "
                    >
                        {{ article.instead_of_price }}
                    </p>
                    <p class="SVDv3_zonePrix_ald" v-if="article.is_destock && article.origin_price !== 0">
                        {{ 'original_price' | trans }}
                        {{ article.origin_price | formatPriceSvd }}
                    </p>
                    <p
                        v-if="
                            article.highlight_tag &&
                            !['promotion', 'on_offer'].includes(
                                getClassesFromArticleHighlightTagPath(article.highlight_tag)
                            )
                        "
                        data-context="highlight"
                    >
                        <span
                            class="product-highlight label"
                            :class="getClassesFromArticleHighlightTagPath(article.highlight_tag, 'label')"
                            >{{ article.highlight_tag | trans({}, 'search') }}</span
                        >
                    </p>
                </div>
                <p
                    class="SVDv3_rayon_prixConfidentiel"
                    v-else-if="article.display_mode === display_modes.CONFIDENTIAL_PRICE"
                >
                    <span>{{ 'Prix confidentiel' | trans({}) }}</span>
                </p>
            </div>

            <!-- ARTICLE AVAILABILITY -->
            <div class="SVDv3_rayon_listingProduits_dispo" v-html="render_availability(article)"></div>

            <!-- ARTICLE ACTIONS -->
            <div class="SVDv3_rayon_listingProduits_action">
                <a
                    class="SVDv3_bouton SVDv3_bouton_gris SVDv3_bouton_largeurFixe SVDv3_bouton_largeurFixe_105px"
                    :href="article.article_url"
                    v-if="!article.can_be_added_to_cart"
                >
                    <span>{{ 'product_sheet' | trans({}, 'common') }}</span>
                </a>
                <a
                    href=""
                    class="SVDv3_bouton_ajoutPanier SVDv3_bouton_largeurFixe"
                    :data-value="article.product_id"
                    :data-label="article.editorial_content.short_description"
                    :data-complementary-url="article.complementary_articles_url"
                    v-on:click="pushClickAddToCartAnalytics"
                    v-else
                >
                    <span>{{ 'add_to_basket' | trans({}, 'common') }}</span>
                </a>
            </div>
        </div>
    </div>
</template>

<script>
import lazyLoadImagesMixin from '@/shared/mixins/lazyLoadImagesMixin'
import { getLocaleStorage } from '@/shared/functions'
import { asset, formatPriceSvd, preset, presets, render_availability, render_availability_v5 } from '@/shared/filters'
import { emptyDataImage, getClassesFromArticleHighlightTagPath } from '@/shared/article/functions'
import * as display_modes from '../referential/DISPLAY_MODES'
import { ARTICLE_NO_IMAGE, PRESET_140, PRESET_95 } from '@/shared/referential/images'
import { BREAKPOINT_TABLET_MIN_WIDTH } from '@/shared/referential/Breakpoints'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'

export default {
    name: 'ArticleListGridItem',
    mixins: [lazyLoadImagesMixin],
    components: { SvdIcon },
    props: {
        article: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            show_ranking: getLocaleStorage('god_mode', false),
            display_modes,
        }
    },
    computed: {
        ARTICLE_NO_IMAGE: () => ARTICLE_NO_IMAGE,
        PRESET_95: () => PRESET_95,
        PRESET_140: () => PRESET_140,
        BREAKPOINT_TABLET_MIN_WIDTH: () => BREAKPOINT_TABLET_MIN_WIDTH,
        article_link_aria_label: (state) => {
            if (state.article.price) {
                return `${state.article.formatted_name} au prix de ${formatPriceSvd(
                    state.article.price.selling_price
                )}, ${render_availability_v5(state.article)}`
            }
            return `${state.article.formatted_name}, Prix confidentiel, ${render_availability_v5(state.article)}`
        },
    },
    methods: {
        presets,
        preset,
        formatPriceSvd,
        asset,
        getClassesFromArticleHighlightTagPath,
        emptyDataImage,
        render_availability,
        priceIsRelevant(selling_price, price) {
            return price - selling_price > (selling_price * 5) / 100
        },
        pushClickProductAnalytics() {
            SonVideo.pushAnalytics('clickProduct', {
                actionField: { list: document.querySelector('#search_app').dataset.location },
                products: [
                    {
                        name: `${this.article.brand_name} ${this.article.name}`,
                        title: `${this.article.name}`,
                        id: this.article.product_id,
                        price: this.article?.price?.selling_price ?? 'Prix confidentiel',
                        brand: this.article.brand_name,
                        category: this.article.category_name,
                    },
                ],
            })
        },
        pushClickAddToCartAnalytics() {
            SonVideo.pushAnalytics('addToCart', {
                products: [
                    {
                        name: `${this.article.brand_name} ${this.article.name}`,
                        title: `${this.article.name}`,
                        id: this.article.product_id,
                        price: this.article.price.selling_price,
                        brand: this.article.brand_name,
                        category: this.article.category_name,
                        quantity: 1,
                    },
                ],
            })
        },
        displayReferencePriceSlider(event) {
            return window.SonVideo.reference_price.open(event)
        },
    },
}
</script>

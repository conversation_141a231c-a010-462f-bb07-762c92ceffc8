<template>
    <div id="search-results-products" class="SVDv3_rayon_listingProduits SVDv3_article_element" data-context="lines">
        <article-list-line-item
            v-for="article of articles"
            :article="article"
            :key="`line-item-${article.product_id}`"
        />
    </div>
</template>

<script>
import ArticleListLineItem from './ArticleListLineItem'

export default {
    name: 'ArticleListLines',
    components: { ArticleListLineItem },
    props: {
        articles: {
            type: Array,
            required: true,
        },
    },
    computed: {
        push_articles() {
            const location = document.querySelector('#stand-title')?.dataset?.location

            return this.articles.map((article) => ({
                title: `${article.name}`,
                name: `${article.brand_name} ${article.name}`,
                id: article.product_id,
                price: article.price,
                brand: article.brand_name,
                category: article.category_name,
                list: location,
            }))
        },
    },
    created() {
        if (this.push_articles.length > 0) {
            SonVideo.pushAnalytics('productView', this.push_articles)
        }
    },
}
</script>

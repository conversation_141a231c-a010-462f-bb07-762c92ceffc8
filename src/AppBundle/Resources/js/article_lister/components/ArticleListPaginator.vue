<template>
    <nav class="article-pagination" v-if="last_page > 1">
        <ul>
            <li v-if="page > 1" class="pagination-item">
                <a
                    aria-label="Page précédente"
                    @click.prevent="goToPage(page - 1)"
                    :href="getHrefForPage(page - 1)"
                    rel="prev"
                >
                    <svd-icon class="inline" icon="arrow-left" :size="22" keepColor />
                </a>
            </li>

            <template v-for="p in last_page">
                <li class="ellipsis" v-if="p === last_page && page + OFFSET + 1 < last_page">&hellip;</li>
                <li v-if="p === page" aria-current="page" class="pagination-item active">{{ p }}</li>
                <li
                    v-else
                    class="pagination-item"
                    :class="{ hidden: p > 1 && p < last_page && (p < page - OFFSET || p > page + OFFSET) }"
                >
                    <a :aria-label="`Aller à la page ${p}`" :href="getHrefForPage(p)" @click.prevent="goToPage(p)">
                        {{ p }}
                    </a>
                </li>
                <li class="ellipsis" v-if="p === 1 && page - OFFSET - 1 > 1">&hellip;</li>
            </template>
            <li v-if="page < last_page" class="pagination-item">
                <a
                    aria-label="Page suivante"
                    @click.prevent="goToPage(page + 1)"
                    :href="getHrefForPage(page + 1)"
                    rel="next"
                >
                    <svd-icon class="inline" icon="arrow-right" :size="22" keepColor />
                </a>
            </li>
        </ul>
    </nav>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex'
import ChevronRightIcon from '@/shared/components/HeroIcons/Solid/ChevronRightIcon.vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'

const OFFSET = 1

export default {
    name: 'ArticleListPaginator',
    components: { SvdIcon, ChevronRightIcon },
    data() {
        return {
            OFFSET,
        }
    },
    computed: {
        ...mapState(['articles', 'page', 'item_per_page']),
        ...mapGetters(['article_ids_to_retrieve']),
        last_page() {
            return Math.ceil(this.article_ids_to_retrieve.length / this.item_per_page)
        },
    },
    methods: {
        ...mapActions(['updatePaginationOption']),
        goToPage(page) {
            this.updatePaginationOption(page)
            window.SonVideo.smoothScroll.scrollTo('#article_lister')
        },
        getHrefForPage(page) {
            const search_params = new URLSearchParams(window.location.search)
            search_params.set('page', page)
            return `?${search_params.toString()}`
        },
    },
}
</script>

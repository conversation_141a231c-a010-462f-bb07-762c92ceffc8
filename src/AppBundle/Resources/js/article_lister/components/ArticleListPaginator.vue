<template>
    <article-paginator
        :current-page="page"
        :total-items="article_ids_to_retrieve.length"
        :items-per-page="item_per_page"
        scroll-target="#article_lister"
        @page-changed="handlePageChange"
    />
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex'
import ArticlePaginator from './ArticlePaginator.vue'

export default {
    name: 'ArticleListPaginator',
    components: { ArticlePaginator },
    computed: {
        ...mapState(['page', 'item_per_page']),
        ...mapGetters(['article_ids_to_retrieve']),
    },
    methods: {
        ...mapActions(['updatePaginationOption']),
        handlePageChange(page) {
            this.updatePaginationOption(page)
        },
    },
}
</script>

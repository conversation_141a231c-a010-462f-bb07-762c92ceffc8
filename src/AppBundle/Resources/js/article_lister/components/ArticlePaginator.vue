<template>
    <nav class="article-pagination" v-if="last_page > 1">
        <ul>
            <li v-if="currentPage > 1" class="pagination-item">
                <a
                    aria-label="Page précédente"
                    @click.prevent="goToPage(currentPage - 1)"
                    :href="getHrefForPage(currentPage - 1)"
                    rel="prev"
                >
                    <svd-icon class="inline" icon="arrow-left" :size="22" keepColor />
                </a>
            </li>

            <template v-for="p in last_page">
                <li class="ellipsis" v-if="p === last_page && currentPage + OFFSET + 1 < last_page">&hellip;</li>
                <li v-if="p === currentPage" aria-current="page" class="pagination-item active">{{ p }}</li>
                <li
                    v-else
                    class="pagination-item"
                    :class="{ hidden: p > 1 && p < last_page && (p < currentPage - OFFSET || p > currentPage + OFFSET) }"
                >
                    <a :aria-label="`Aller à la page ${p}`" :href="getHrefForPage(p)" @click.prevent="goToPage(p)">
                        {{ p }}
                    </a>
                </li>
                <li class="ellipsis" v-if="p === 1 && currentPage - OFFSET - 1 > 1">&hellip;</li>
            </template>
            <li v-if="currentPage < last_page" class="pagination-item">
                <a
                    aria-label="Page suivante"
                    @click.prevent="goToPage(currentPage + 1)"
                    :href="getHrefForPage(currentPage + 1)"
                    rel="next"
                >
                    <svd-icon class="inline" icon="arrow-right" :size="22" keepColor />
                </a>
            </li>
        </ul>
    </nav>
</template>

<script>
import ChevronRightIcon from '@/shared/components/HeroIcons/Solid/ChevronRightIcon.vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'

const OFFSET = 1

export default {
    name: 'ArticlePaginator',
    components: { SvdIcon, ChevronRightIcon },
    props: {
        // Current page number
        currentPage: {
            type: Number,
            required: true,
        },
        // Total number of items
        totalItems: {
            type: Number,
            required: true,
        },
        // Items per page
        itemsPerPage: {
            type: Number,
            required: true,
        },
        // Scroll target selector when page changes
        scrollTarget: {
            type: String,
            default: '#article_lister',
        },
        // Whether to update URL parameters
        updateUrl: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            OFFSET,
        }
    },
    computed: {
        last_page() {
            return Math.ceil(this.totalItems / this.itemsPerPage)
        },
    },
    methods: {
        goToPage(page) {
            this.$emit('page-changed', page)
            if (this.scrollTarget && window.SonVideo?.smoothScroll) {
                window.SonVideo.smoothScroll.scrollTo(this.scrollTarget)
            }
        },
        getHrefForPage(page) {
            if (!this.updateUrl) {
                return '#'
            }
            const search_params = new URLSearchParams(window.location.search)
            search_params.set('page', page)
            return `?${search_params.toString()}`
        },
    },
}
</script>

import * as types from './mutation-types'
import api from '../../shared/api/handler'
import { extractUrlParams } from '../../shared/functions'
import * as SORTING_OPTIONS from '../referential/SORT_OPTIONS'
import { LINES } from '../referential/DISPLAY_OPTIONS'

export default {
    initializeData({ commit }, search_data) {
        commit(types.INITIALIZE, search_data)
    },
    /**
     * Initialize data in the store
     * @param commit
     * @param tri
     * @param page
     * @param affichage
     */
    initApp({ commit }, { tri, page, affichage }) {
        if (tri !== undefined) {
            // handle previous way to store this param in urls, as an index
            if (!isNaN(tri)) {
                tri = Object.values(SORTING_OPTIONS).sort((a, b) => a.order - b.order)[parseInt(tri)].value
            }

            commit(types.UPDATE_SORTING_OPTION, tri)
        }
        if (page) {
            commit(types.UPDATE_PAGINATION_OPTION, parseInt(page))
        }
        if (affichage) {
            commit(types.UPDATE_DISPLAY_OPTION, affichage)
        }
    },
    loadFilters({ commit }, { filters, selected_keys }) {
        commit(types.LOAD_FILTERS, { filters, selected_keys })
    },
    updateDisplayOption({ commit, dispatch }, mode) {
        commit(types.UPDATE_DISPLAY_OPTION, mode)
    },
    async updateSortingOption({ commit, dispatch }, data) {
        commit(types.UPDATE_SORTING_OPTION, data)
        commit(types.UPDATE_PAGINATION_OPTION, 1)
        await dispatch('retrieveArticles')
    },
    async updatePaginationOption({ commit, dispatch }, page) {
        commit(types.UPDATE_PAGINATION_OPTION, page)
        await dispatch('retrieveArticles')
    },
    async retrieveArticles({ state, getters, commit }) {
        if (getters.article_ids_to_retrieve.length === 0) {
            commit(types.UPDATE_ARTICLES, [])
        } else {
            commit(types.UPDATE_ARTICLES_LOADING, true)

            // get articles data for the current page
            return await api
                .post('/articles/_search', {
                    article_ids: getters.article_ids_to_retrieve,
                    page: state.page,
                    item_per_page: state.item_per_page,
                    sort_by: state.sort_by,
                })
                .then((response) => {
                    commit(types.UPDATE_ARTICLES, response.data.articles)
                })
                .finally(() => {
                    commit(types.UPDATE_ARTICLES_LOADING, false)
                })
        }
    },
    /**
     * Add a single facet
     * @param commit
     * @param {string} facet_key
     */
    addFacet({ commit }, facet_key) {
        commit(types.SET_FACETS_STATUS, { facet_keys: [facet_key], status: true })
    },
    /**
     * Add multiple facets at once
     * @param commit
     * @param {array<string>} facet_keys
     */
    addFacets({ commit }, facet_keys) {
        commit(types.SET_FACETS_STATUS, { facet_keys, status: true })
    },
    /**
     * Remove a single facet
     * @param commit
     * @param {string} facet_key
     */
    removeFacet({ commit }, facet_key) {
        commit(types.SET_FACETS_STATUS, { facet_keys: [facet_key], status: false })
    },
    /**
     * Remove multiple facets at once
     * @param commit
     * @param {array<string>} facet_keys
     */
    removeFacets({ commit }, facet_keys) {
        commit(types.SET_FACETS_STATUS, { facet_keys, status: false })
    },
    /**
     * Add/remove multiple facets in one step
     * @param commit
     * @param {array<{key: string, status:boolean}>} facets_alterations
     */
    updateFacets({ commit }, facets_alterations) {
        commit(types.ALTER_FACETS, facets_alterations)
    },
    /**
     * Force a filter to be selected even if has no article selected
     * @param commit
     * @param {string} filter_name
     */
    forceFilter({ commit }, filter_name) {
        commit(types.FORCE_FILTER, { filter_name, status: true })
    },
    /**
     * Unforce a filter to be selected even if has no article selected
     * @param commit
     * @param {string} filter_name
     */
    unforceFilter({ commit }, filter_name) {
        commit(types.FORCE_FILTER, { filter_name, status: false })
    },
    /**
     * Take the url params and update the state accordingly
     * @param commit
     * @param state
     * @param dispatch
     * @param getters
     */
    restoreParamsFromUrl({ commit, state, dispatch, getters }) {
        const { tri, page, affichage, filtres } = extractUrlParams()

        let needs_article_reload = true
        const sort_by_before = state.sort_by
        const page_before = state.page

        commit(types.UPDATE_SORTING_OPTION, tri || state.default_selected_sorting_option)
        commit(types.UPDATE_PAGINATION_OPTION, page ? parseInt(page) : 1)
        commit(types.UPDATE_DISPLAY_OPTION, affichage || LINES)

        // compute facets to update
        let keys_in_store = []
        getters.selected_filters.forEach((selected_filter) => {
            for (const [key, value] of Object.entries(selected_filter.filter.values)) {
                if (value.selected === true) {
                    keys_in_store.push(key)
                }
            }
        })
        const to_remove = keys_in_store.filter((k) => !(filtres || []).includes(k))
        const to_add = (filtres || []).filter((k) => !keys_in_store.includes(k))
        if (to_remove.length > 0 || to_add.length > 0) {
            const facets_alterations = to_remove
                .map((key) => {
                    return { key, status: false }
                })
                .concat(
                    to_add.map((key) => {
                        return { key, status: true }
                    })
                )
            getters.selected_filters.forEach((selected_filter) =>
                commit(types.FORCE_FILTER, { filter_name: selected_filter.filter.name, status: false })
            )
            commit(types.ALTER_FACETS, facets_alterations)

            needs_article_reload = false
        }

        // reload articles if needed
        // note: updating the facets triggers an article reload
        needs_article_reload = needs_article_reload && (sort_by_before !== state.sort_by || page_before !== state.page)
        if (needs_article_reload) {
            commit(types.RESET_FIRST_LOAD)
            dispatch('retrieveArticles')
        }
    },
}

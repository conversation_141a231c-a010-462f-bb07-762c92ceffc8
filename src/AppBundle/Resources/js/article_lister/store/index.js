import Vue from 'vue'
import Vuex from 'vuex'
import actions from './actions'
import * as types from '../store/mutation-types'
import { COMPOSITE, DEFAULT, NUMERIC, SLIDER } from '../referential/FILTER_TYPES'
import { AVAILABILITY } from '../referential/SORT_OPTIONS'
import * as SORTING_OPTIONS from '../referential/SORT_OPTIONS'
import { LINES } from '../referential/DISPLAY_OPTIONS'

Vue.use(Vuex)

const debug = process.env.NODE_ENV !== 'production'

/**
 * Recursively collect articles ids from "filters" and store them in "target".
 *
 * @param filters Object
 * @param target array
 * @returns {array}
 */
const collectAllArticleIdsFromFilters = (filters, target) => {
    for (const [key, filter] of Object.entries(filters)) {
        if (filter.type === 'composite') {
            target = collectAllArticleIdsFromFilters(filter.children, target)

            continue
        }

        for (const [facet, value] of Object.entries(filter.values)) {
            target = target.concat(value.article_ids)
        }
    }

    return target
}

/**
 * Recursively convert the "filters" object into an array of ordered filters.
 *
 * @param {Object} filters
 * @param {array} selected_keys
 * @returns {array}
 */
const convertFiltersToArray = (filters, selected_keys) => {
    return Object.entries(filters)
        .sort((a, b) => a[1].meta.display_order - b[1].meta.display_order)
        .flatMap(([key, filter]) => {
            if (filter.type === COMPOSITE) {
                return convertFiltersToArray(filter.children, selected_keys)
            }

            // mutate numeric filter
            convertNumericFilter(filter)

            // alter filter structure to add/remove useful/confusing data
            filter.name = key
            filter.forced_select = false
            delete filter.children
            delete filter.meta.display_order
            Object.entries(filter.values).forEach(([key, value]) => {
                // for highlights, the values need to be converted for translation
                if (filter.name === 'highlight') {
                    value.value = `article.highlight.${value.value}`
                }

                value.selected = selected_keys.includes(value.key)
            })

            return filter
        })
}

/**
 * Mutate numeric filter's definition to be rendered based on its values:
 * - checkboxes if has CHECKBOXES_LIMIT or less values
 * - linear slider if has more than CHECKBOXES_LIMIT and contains a decimal value
 * - step slider if has more than CHECKBOXES_LIMIT to STEP_SLIDER_LIMIT values
 * - linear slider if has more than STEP_SLIDER_LIMIT values
 */
const convertNumericFilter = (filter) => {
    if (filter.type !== NUMERIC) {
        return
    }

    const CHECKBOXES_LIMIT = 5
    const STEP_SLIDER_LIMIT = 15

    const values = Object.keys(filter.values)
        .map((o) => {
            return parseFloat(filter.values[o].value)
        })
        .sort((a, b) => a - b)
    const contains_decimal = values.some((value) => !Number.isInteger(value))

    // alter type
    filter.type = values.length <= CHECKBOXES_LIMIT ? DEFAULT : SLIDER

    // alter meta definition
    filter.meta._is_numeric = true
    if (filter.type === SLIDER) {
        filter.meta.scale = values.length <= STEP_SLIDER_LIMIT && !contains_decimal ? 'step' : 'linear'
        filter.meta.useDecimal = contains_decimal
    }
}

export default new Vuex.Store({
    state: {
        is_loading_filters: true,
        is_loading_articles: true,
        is_first_load_complete: false,
        filters: [],
        pool_article_ids: [],
        articles: [],
        page: 1,
        item_per_page: 36,
        default_selected_sorting_option: AVAILABILITY.value,
        sort_by: AVAILABILITY.value,
        show_all_data: false,
        display: LINES,
    },
    getters: {
        // filters which have at least one selected value or is forced (eg. sliders excluding all its possible values),
        // along with the union of its selected article ids
        selected_filters: (state) => {
            return state.filters
                .map((filter) => {
                    // union of selected article ids
                    let selected_article_ids = Object.entries(filter.values)
                        .filter(([key, value]) => value.selected)
                        .reduce((ids, [key, value]) => ids.concat(value.article_ids), [])
                    selected_article_ids = [...new Set(selected_article_ids)]

                    return {
                        filter,
                        selected_article_ids,
                    }
                })
                .filter(
                    (selected_filter) =>
                        selected_filter.filter.forced_select || selected_filter.selected_article_ids.length > 0
                )
        },
        // intersection of article ids selected for each filter
        article_ids_to_retrieve: (state, getters) => {
            return getters.selected_filters.reduce((selected_article_ids, selected_filter) => {
                return selected_article_ids.filter((aid) => selected_filter.selected_article_ids.includes(aid))
            }, state.pool_article_ids)
        },
    },
    mutations: {
        [types.INITIALIZE](state, data) {
            state.default_selected_sorting_option = Object.values(SORTING_OPTIONS).sort((a, b) => a.order - b.order)[
                data.default_selected_sorting_option || state.default_selected_sorting_option
            ].value
            state.sort_by = state.default_selected_sorting_option
        },
        [types.LOAD_FILTERS](state, { filters, selected_keys }) {
            state.filters = convertFiltersToArray(filters, selected_keys)
            state.is_loading_filters = false
            state.pool_article_ids = [...new Set(collectAllArticleIdsFromFilters(filters, []))]
        },
        [types.UPDATE_ARTICLES](state, articles) {
            state.articles = articles
            state.is_first_load_complete = true
        },
        [types.UPDATE_ARTICLES_LOADING](state, status) {
            state.is_loading_articles = status
        },
        [types.UPDATE_DISPLAY_OPTION](state, mode) {
            state.display = mode
        },
        [types.UPDATE_SORTING_OPTION](state, sorting_option) {
            state.sort_by = sorting_option
        },
        [types.UPDATE_PAGINATION_OPTION](state, page) {
            state.page = page
        },
        /**
         * Set the same status for each facet key
         * @param state
         * @param {array<string>} facet_keys
         * @param {boolean} status
         */
        [types.SET_FACETS_STATUS](state, { facet_keys, status }) {
            facet_keys.forEach((key) => {
                state.filters.forEach((filter) => {
                    if (filter.values.hasOwnProperty(key)) {
                        filter.values[key].selected = status
                    }
                })
            })
        },
        /**
         * Apply a defined status per facet
         * @param state
         * @param {array<{key: string, status:boolean}>} facets_alterations
         */
        [types.ALTER_FACETS](state, facets_alterations) {
            facets_alterations.forEach(({ key, status }) => {
                state.filters.forEach((filter) => {
                    if (filter.values.hasOwnProperty(key)) {
                        filter.values[key].selected = status
                    }
                })
            })
        },
        /**
         * Set the forced_select value of the specified filter
         * @param state
         * @param {string} filter_name
         * @param {boolean} status
         */
        [types.FORCE_FILTER](state, { filter_name, status }) {
            state.filters.forEach((filter) => {
                if (filter.name === filter_name) {
                    filter.forced_select = status
                }
            })
        },
        /**
         * Set state as if articles were not already loaded.
         * Internally, this will force to retrieve all pages up to the current one on next article load.
         *
         * @param state
         */
        [types.RESET_FIRST_LOAD](state) {
            state.is_first_load_complete = false
        },
    },
    actions,
    strict: debug,
})

<template>
    <div class="container-review-score">
        <product-reviews-stats :reviews_stats="stats" :page_article="false"></product-reviews-stats>

        <div class="row flex-column flex-md-row justify-content-md-between pb-3 m-0 no-border">
            <div class="align-self-center">
                <span
                    @contextmenu="clickObfuscation($event, review_submission_url_base_64_encoded, 'right')"
                    @click="clickObfuscation($event, review_submission_url_base_64_encoded, 'left')"
                    class="btn btn-primary btn-block mb-2 mb-md-0 atc"
                >
                    {{ 'write_a_review' | trans({}, 'review') }}
                </span>
            </div>
        </div>
    </div>
</template>

<script>
import ProductReviewItem from '../../advice/review/components/ProductReviewItem'
import ProductReviewsStats from '../../advice/review/components/ProductReviewsStats'
import { mapActions, mapGetters, mapState } from 'vuex'
export default {
    name: 'ProductReviewsPageApp',
    components: { ProductReviewItem, ProductReviewsStats },
    computed: {
        ...mapGetters(['stats', 'computed_reviews']),
        ...mapState({
            review_submission_url: (state) => state.urls.post_review_url,
            sorting_options: (state) => state.sorting_options,
        }),
        review_submission_url_base_64_encoded() {
            return window.btoa(unescape(encodeURIComponent(this.review_submission_url)))
        },
    },
    methods: {
        clickObfuscation(e, attribute, side) {
            if (e.ctrlKey) {
                let newWindow = window.open(decodeURIComponent(window.atob(attribute)), '_blank')
                newWindow.focus()
            } else {
                if (side === 'left') {
                    document.location.href = decodeURIComponent(window.atob(attribute))
                } else {
                    window.open(decodeURIComponent(window.atob(attribute)), '_blank')
                }
            }
        },
    },
}
</script>

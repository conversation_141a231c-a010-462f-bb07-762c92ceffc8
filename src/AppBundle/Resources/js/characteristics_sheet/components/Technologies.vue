<script setup>
import { computed } from 'vue'
import SvdTooltip from '@/v5/components/tooltip/SvdTooltip.vue'
import { asset } from '@/shared/filters'
import TechnologyImg from '@/characteristics_sheet/components/TechnologyImg.vue'

const props = defineProps({
    article: {
        type: Object,
        required: true,
    },
})

const technologies = computed(() => {
    return props.article.technology
})

function image_src(technology) {
    return asset(technology.logo_uri, 'static_images')
}
</script>

<template>
    <div class="logos-list logos-list-technologies container" v-show="technologies && technologies.length > 0">
        <div class="logos-list-body row row-small-gutter">
            <div class="col-3 col-sm-2" v-for="(technology, index) of technologies">
                <svd-tooltip>
                    <template #action>
                        <div class="logos-list-item logos-list-item-border cursor-pointer">
                            <technology-img
                                :alt="article.technology_i18n[index].logo_caption"
                                :src="image_src(technology)"
                            />
                        </div>
                    </template>
                    <template #reaction>
                        <div class="tooltip-title">{{ article.technology_i18n[index].expression }}</div>
                        <div class="tooltip-body">{{ article.technology_i18n[index].definition }}</div>
                    </template>
                </svd-tooltip>
            </div>
        </div>
    </div>
</template>

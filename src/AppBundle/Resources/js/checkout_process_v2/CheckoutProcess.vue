<script setup>
import CheckoutTimeline from '@/checkout_process_v2/components/CheckoutTimeline.vue'
import SalesFunnelSkeleton from '@/checkout_process_v2/components/SalesFunnelSkeleton.vue'
import CheckoutHeader from '@/v5/checkout_process/CheckoutHeader.vue'
import CheckoutTitle from '@/checkout_process_v2/components/CheckoutTitle.vue'
import CustomerOrderAddresses from '@/checkout_process_v2/CustomerOrderAddresses.vue'
import CheckoutFooter from '@/v5/checkout_process/CheckoutFooter.vue'
import BasicErrorModal from '@/shared/tailwind/BasicErrorModal.vue'
import BasketSummary from '@/checkout_process_v2/components/summary/BasketSummary.vue'
import SummaryHeader from '@/checkout_process_v2/components/summary/SummaryHeader.vue'
import SvdMessage from '@/v5/components/message/SvdMessage.vue'
import { useCheckoutProcessStore, STEPS, STATE_STEPS } from '@/checkout_process_v2/stores/checkout_process'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { ga, GA_CHECKOUT_STEPS } from '@/analytics/useGoogleAnalytics'
import { useAddressBookStore } from '@/checkout_process_v2/stores/address_book'
import { isMobile } from '@/shared/functions'
import { formatPrice } from '@/shared/filters'
import { storeToRefs } from 'pinia'
import StepAddress from '@/checkout_process_v2/StepAddress.vue'
import StepDelivery from '@/checkout_process_v2/StepDelivery.vue'
import StepPayment from '@/checkout_process_v2/StepPayment.vue'
import { BREAKPOINT_DESKTOP } from '@/shared/referential/Breakpoints'
import SvdLink from '@/v5/components/link/SvdLink.vue'

const emit = defineEmits(['contact-us:open'])
const store = useCheckoutProcessStore()
const { current_step, show_order_created_message, has_failure_message, finalize_process_url } = storeToRefs(store)

// === Summary handling ===
const is_summary_displayed = ref(false)
const articles_count = computed(() =>
    (store.basket_order?.articles ?? []).reduce((quantity, article) => quantity + article.quantity, 0)
)
function toggleSummary() {
    is_summary_displayed.value = !is_summary_displayed.value
}

// === Contact Us feature ===
function reactToContactUsOpening() {
    store.saveStateThenNavigate({ step: STATE_STEPS.before_contact_us_opening })
}
function contactUs() {
    reactToContactUsOpening()
    window.SonVideo.contact.open({
        label: 'tunnel > contactez-nous',
        action: '',
        phone_only: true,
        use_history: true,
    })
}

// === Dynamic classes ===
const summary_step_classes = computed(() => {
    let classes = []
    if (current_step.value === STEPS.addresses_missing || store.is_editing_address) {
        classes.push('step1')
    }
    if (store.is_locked_to_payment) {
        classes.push('quote')
    }

    return classes
})
const step_classes = computed(() => {
    let classes = []
    if (current_step.value === STEPS.addresses_missing || store.is_editing_address) {
        classes.push('step1')
    }
    if (store.is_locked_to_payment) {
        classes.push('quote')
    }

    return classes
})

// === Google Analytics ===
const has_offer = computed(() => {
    return store.basket_order?.quote !== null && store.basket_order?.quote?.type === 'offer'
})
const ga_version = computed(() => {
    // TODO: éclaircir les points suivants
    // - le localStorage est modifié après la génération Twig du tracking, comment la donnée peut être pertinente ?
    // - pourquoi la valeur stockée n’est pas la même, et `has_offer` ne stocke rien ?
    if (store.customer_order_id !== null) {
        localStorage.setItem('funnel_version', 'v2-offre')
        return 'v2-commande'
    }
    if (store.is_quote) {
        localStorage.setItem('funnel_version', 'v2-devis')
        return 'v2-devis'
    }
    if (has_offer.value) {
        return 'v2-offre'
    }

    localStorage.setItem('funnel_version', 'v2')
    return 'v2'
})
const computed_articles_for_analytics = computed(() => {
    if (!store.basket_order?.summary) {
        return null
    }

    return store.basket_order.summary
        .filter((a) => ['customer.quote', 'article.article'].includes(a.type))
        .map((i) => ({
            item_name: i.brand_name + ' ' + i.article_name,
            item_category: i.category_name,
            item_brand: i.brand_name,
            item_id: `${i.id}`,
            price: i.selling_price,
            quantity: i.quantity,
        }))
})
watch(current_step, async (new_value, old_value) => {
    if (new_value === STEPS.addresses_missing) {
        ga.checkout(GA_CHECKOUT_STEPS.ADDRESS, ga_version.value)
    }

    if (new_value === STEPS.shipment_missing) {
        if (old_value === STEPS.loading) {
            ga.checkout(GA_CHECKOUT_STEPS.ADDRESS, ga_version.value)
        }
        ga.checkout(GA_CHECKOUT_STEPS.DELIVERY, ga_version.value)
    }

    if (new_value === STEPS.payment_selection && STEPS.loading !== old_value) {
        ga.checkout(GA_CHECKOUT_STEPS.PAYMENT, ga_version.value)
    }

    if (new_value === STEPS.payment_selection && old_value === STEPS.shipment_missing) {
        ga.addShippingInfo(
            store.basket_order.articles_price,
            store.shipment_cost ?? 0.0,
            store.basket_order.shipment_method.title,
            computed_articles_for_analytics.value
        )
    }
})

// === Finalize Process ===
watch(finalize_process_url, (url) => {
    if (url !== null) {
        ga.addPaymentInfo(
            store.basket_order.articles_price,
            computed_articles_for_analytics.value,
            store.payments.find((p) => p.payment_method_id === store.selected_payment_id)?.label
        )
        ga.checkout(GA_CHECKOUT_STEPS.PAYMENT, ga_version.value)

        window.location.href = url
    }
})

// === Smooth scroll feature ===
watch(current_step, () => {
    document.getElementById('tunnel-v5')?.scrollIntoView({ behavior: 'smooth' })
})

// === component lifecycle ===
onMounted(async () => {
    backForwardManager()
    window.addEventListener('popstate', restoreStateFromHistory)
    await store.retrieveBasketOrder()

    if (store.is_locked_to_payment || !isMobile(BREAKPOINT_DESKTOP)) {
        is_summary_displayed.value = true
    }

    // TODO: check if we need different behavior for order creation and repayment
    // Google analytics
    // Trigger some events if the shipment method is already set in the basket order on load. This cover :
    // - tunnel refresh (F5)
    // - customer quote of type quotation which bypass the shopping cart
    if (current_step.value === STEPS.payment_selection) {
        ga.beginCheckout(store.basket_order.articles_price, computed_articles_for_analytics.value)
        ga.addShippingInfo(
            store.basket_order.articles_price,
            store.shipment_cost ?? 0.0,
            store.basket_order.shipment_method.title,
            computed_articles_for_analytics.value
        )
        ga.checkout(GA_CHECKOUT_STEPS.CART, ga_version.value)
        ga.checkout(GA_CHECKOUT_STEPS.ADDRESS, ga_version.value)
        ga.checkout(GA_CHECKOUT_STEPS.DELIVERY, ga_version.value)
        ga.checkout(GA_CHECKOUT_STEPS.PAYMENT, ga_version.value)
    }
})
onUnmounted(() => {
    window.removeEventListener('popstate', restoreStateFromHistory)
})
function backForwardManager() {
    if (document.addEventListener) {
        window.addEventListener(
            'pageshow',
            function (event) {
                if (event.persisted || performance.getEntriesByType('navigation')[0].type === 'back_forward') {
                    location.href = '/mon-compte/mes-commandes'
                }
            },
            false
        )
    }
}
function restoreStateFromHistory(event) {
    const state = event?.state

    if (!state) {
        return
    }

    const actions = {}
    actions[STATE_STEPS.before_payment] = () => {
        store.resetShipmentMethod()
    }
    actions[STATE_STEPS.before_address_selection] = () => {
        store.setIsEditingAddress(false)
    }
    actions[STATE_STEPS.before_address_form] = () => {
        useAddressBookStore().forceListMode()
    }
    actions[STATE_STEPS.before_delivery_edit] = () => {
        store.setShipmentMethod(Object.assign({}, state.payload.shipment_method))
    }
    actions[STATE_STEPS.before_contact_us_opening] = () => {
        window?.SonVideo?.contact?.close()
    }

    if (actions.hasOwnProperty(state.step)) {
        actions[state.step]()
    }
}
</script>

<template>
    <!-- Failure when retrieving the basket order -->
    <!-- Failure when retrieving the customer addresses or setting the first as shipping and billing address -->
    <div v-if="store.has_data_failure" id="tunnel-v5">
        <svd-message type="error" data-context="data-failure-error-message" class="m-6">
            Une erreur s’est produite en récupérant vos informations. Veuillez recharger la page.
        </svd-message>
    </div>
    <div v-else id="tunnel-v5" class="tunnel-v5" data-context="sales-funnel">
        <checkout-header @contact-us:open="reactToContactUsOpening" />

        <sales-funnel-skeleton v-if="store.is_loading || !store.basket_order" />
        <template v-else>
            <div v-if="current_step > STEPS.addresses_missing">
                <div v-if="has_failure_message" class="failure-message-container">
                    <svd-message class="message-failure" type="error" data-context="failure-message">
                        <div>
                            Votre transaction n’a pu aboutir. Nous vous invitons à
                            <svd-link
                                data-context="contact-us-error"
                                label="contacter notre service clientèle"
                                class="bold"
                                @click="contactUs"
                            />
                            (en indiquant le numéro de commande <span class="bold">{{ store.customer_order_id }}</span
                            >), ou à choisir un nouveau mode de paiement ci-dessous.
                        </div>
                    </svd-message>
                </div>
                <checkout-title v-if="store.is_locked_to_payment" />
                <checkout-timeline v-else />
                <div v-if="show_order_created_message" class="created-order-message-container">
                    <svd-message type="info" data-context="created-order-message">
                        <div>
                            Votre commande a bien été enregistrée et partiellement payée avec votre carte cadeau
                            Son-Vidéo.com.<br />
                            Elle sera traitée après règlement du
                            <span class="bold">reste à payer de {{ formatPrice(store.total_price) }}</span
                            >.
                        </div>
                    </svd-message>
                </div>
            </div>
            <div
                class="body"
                :class="{
                    quote: store.is_locked_to_payment,
                    step1: current_step === STEPS.addresses_missing,
                }"
            >
                <!-- Left column on desktop -->
                <div class="left-side" :class="{ quote: store.is_locked_to_payment, error: has_failure_message }">
                    <customer-order-addresses v-if="store.is_quote" class="order-addresses" inline hide-arrow />
                    <div class="steps" :class="step_classes">
                        <step-address
                            v-if="store.current_step === STEPS.addresses_missing || store.is_editing_address"
                        />
                        <step-delivery
                            @contact-us:open="reactToContactUsOpening"
                            v-else-if="store.current_step === STEPS.shipment_missing"
                        />
                        <step-payment
                            v-else-if="store.current_step > STEPS.shipment_missing"
                            @contact-us:open="reactToContactUsOpening"
                        />
                    </div>
                </div>

                <!-- Right column on desktop -->
                <div class="summaries" :class="{ quote: store.is_quote, error: has_failure_message }">
                    <!-- Right column on desktop -->
                    <div class="summary" :class="summary_step_classes">
                        <summary-header
                            title="Panier"
                            :title-detail="`(${articles_count})`"
                            :arrow-up="is_summary_displayed"
                            @click="toggleSummary"
                        />
                        <basket-summary :summary-displayed="is_summary_displayed" />
                    </div>
                    <customer-order-addresses
                        v-if="store.current_step > STEPS.shipment_missing && !store.is_quote"
                        class="order-addresses"
                    />
                </div>
            </div>
        </template>

        <checkout-footer />

        <!-- Modal to handle error message-->
        <basic-error-modal v-if="store.error_message" @close="store.updateErrorMessage(null)">
            <div class="mt-2">
                <p class="text-sm text-gray-900">
                    {{ store.error_message }}
                </p>
            </div>
        </basic-error-modal>
    </div>
</template>

<style scoped lang="scss">
@import '../../../Resources/scss/style';
.tunnel-v5 {
    display: flex;
    flex-direction: column;
    background-color: $color_white;
    min-height: 100vh;
    height: 100%;
    overflow-x: hidden;
    .failure-message-container {
        margin: 32px auto;
        padding-left: $space_16;
        padding-right: $space_16;
        @include media_min($media_md) {
            width: 705px;
            padding: unset;
        }
        @include media_min($media_desktop) {
            width: 1280px;
        }
        .message-failure {
            width: 100%;
            @include media_min($media_desktop) {
                width: 836px;
            }
        }
    }
    .created-order-message-container {
        margin: 32px auto;
        padding-left: $space_16;
        padding-right: $space_16;
        @include media_min($media_md) {
            width: 705px;
            padding: unset;
        }
        @include media_min($media_desktop) {
            width: 1280px;
        }
    }
    .body {
        display: flex;
        flex-direction: column;
        min-height: calc(100vh - 60px - 255px - 105px);
        @include media_min($media_desktop) {
            flex-direction: row;
        }
        margin-right: auto;
        margin-left: auto;
        gap: $space_64;
        padding-bottom: $space_64;
        @include media_min($media_desktop) {
            gap: $space_64;
            width: 1280px;
        }
        &.quote {
            @include media_min($media_md) {
                margin-bottom: unset;
            }
        }
        &.step1 {
            @include media_min($media_md) {
                margin-top: $space_32;
            }
        }
        .left-side {
            display: flex;
            flex-direction: column;
            gap: $space_64;
            order: 1;
            @include media_min($media_desktop) {
                order: unset;
            }
            &.quote {
                order: 2;
                @include media_min($media_desktop) {
                    order: unset;
                }
            }
            &.error {
                order: 1;
                @include media_min($media_desktop) {
                    order: unset;
                }
            }
            .steps {
                background-color: $color_white;
                padding-right: $space_16;
                padding-left: $space_16;
                width: 100vw;
                height: 100%;
                @include media_min($media_md) {
                    margin-right: auto;
                    margin-left: auto;
                    padding-right: $space_32;
                    padding-left: $space_32;
                    width: 705px;
                }
                @include media_min($media_desktop) {
                    padding-right: unset;
                    padding-left: unset;
                    width: 836px;
                }
                &.quote {
                    @include media_min($media_md) {
                        padding-right: unset;
                        padding-left: unset;
                    }
                }
            }
        }

        .basket {
            margin-right: $space_16;
            margin-left: $space_16;
            width: calc(100vw - 32px);
            background-color: $color_white;
            border: solid 1px $color_grey_default;
            border-radius: $space_16;
            height: 100%;
            @include media_min($media_md) {
                margin-right: $space_32;
                margin-left: $space_32;
                width: 705px;
            }
            @include media_min($media_desktop) {
                margin-left: unset;
                width: 380px;
                height: 100%;
            }
        }
        .summaries {
            display: flex;
            flex-direction: column;
            gap: $space_32;
            height: 100%;
            order: 2;
            @include media_min($media_md) {
                margin-left: $space_32;
                width: 641px;
            }
            @include media_min($media_desktop) {
                margin-left: unset;
                width: 380px;
                order: unset;
            }
            &.quote {
                order: 1;
                @include media_min($media_md) {
                    margin-left: unset;
                    width: 100%;
                }
                @include media_min($media_desktop) {
                    order: unset;
                }
            }
            &.error {
                order: 2;
                @include media_min($media_desktop) {
                    order: unset;
                    margin-top: -238px;
                }
            }
            .summary {
                margin-right: $space_16;
                margin-left: $space_16;
                width: calc(100vw - 32px);
                background-color: $color_white;
                border: solid 1px $color_grey_default;
                border-radius: $space_16;
                height: 100%;
                @include media_min($media_md) {
                    margin: 0;
                    width: 100%;
                }
                @include media_min($media_desktop) {
                    margin-left: unset;
                    margin-right: unset;
                    height: 100%;
                }
            }
        }
    }
}
</style>

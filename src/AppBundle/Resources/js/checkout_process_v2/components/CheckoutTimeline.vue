<template>
    <nav class="nav-timeline" aria-label="Progress" data-context="timeline">
        <ol role="list" class="timeline">
            <li
                class="relative"
                v-for="(step, idx) in steps"
                :class="{ 'not-last': idx + 1 !== steps.length }"
                :data-context="getDataContext(step.step)"
            >
                <!-- Separator line between steps-->
                <div class="separator" aria-hidden="true">
                    <div class="design" :class="[current_step > step.step ? 'active' : '']"></div>
                </div>
                <span
                    class="step"
                    :class="{
                        'past-current': current_step >= step.step,
                        futur: current_step < step.step,
                        'not-allowed': step.step === current_step,
                    }"
                    @click="reactToTimelineClick(step.step)"
                >
                    <!-- Completed step -->
                    <span v-if="step.step === 0">
                        <checkout-tooltip>
                            <template #action>
                                <svd-icon alt="checked" icon="check-circle" :size="40" color="3366cc" />
                            </template>
                        </checkout-tooltip>
                    </span>
                    <svd-icon
                        v-else-if="current_step > step.step"
                        alt="checked"
                        icon="check-circle"
                        :size="40"
                        color="3366cc"
                    />

                    <!-- Current step -->
                    <span class="current" aria-hidden="true" v-if="current_step === step.step"></span>

                    <span class="sr">Step {{ step.step }}</span>
                    <span class="label" :class="{ past: current_step >= step.step }">
                        {{ step.label }}
                    </span>
                </span>
            </li>
        </ol>
    </nav>
</template>

<script>
import CheckIcon from '@/shared/components/HeroIcons/Mini/CheckIcon'
import DateFormatter from '@/account/orders/components/DateFormatter.vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'
import CheckoutTooltip from '@/v5/checkout_process/CheckoutTooltip.vue'
import { useCheckoutProcessStore } from '@/checkout_process_v2/stores/checkout_process'
import { computed } from 'vue'
import { storeToRefs } from 'pinia'

const TIMELINE_STEPS = [
    {
        step: 0,
        label: 'Panier',
    },
    {
        step: 2,
        label: 'Livraison',
    },
    {
        step: 3,
        label: 'Paiement',
    },
]

export default {
    name: 'CheckoutTimeline',
    components: { CheckoutTooltip, SvdIcon, DateFormatter, CheckIcon },
    setup() {
        const store = useCheckoutProcessStore()
        const { current_step } = storeToRefs(store)
        const steps = computed(() => TIMELINE_STEPS)

        function reactToTimelineClick(step_number) {
            if (store.current_step >= step_number && step_number > 0) {
                if (step_number === 2) {
                    store.resetShipmentMethod(true)
                }

                store.setIsEditingAddress(false)
            }
        }

        function getDataContext(step) {
            const prefix = `step-${step}`

            if (store.current_step > step) {
                return [prefix, 'completed'].join('-')
            }

            return [prefix, store.current_step === step ? 'current' : 'upcoming'].join('-')
        }

        return { current_step, steps, reactToTimelineClick, getDataContext }
    },
}
</script>

<style scoped lang="scss">
@import '../../../../Resources/scss/style';
.nav-timeline {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    height: 105px;
    background-color: $color_white;
    .timeline {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        min-width: 320px;
        width: 100%;
        max-width: 480px;
        padding-left: $space_32;
        padding-right: $space_32;
        li {
            position: relative;
            &.not-last {
                padding-right: calc(100vw / 6);
                @include media_min($media_md) {
                    padding-right: $space_128;
                }
            }
            .separator {
                display: flex;
                align-items: center;
                position: absolute;
                inset: 0;
                margin-bottom: $space_20;
                .design {
                    height: 2px;
                    width: 100%;
                    background-color: $color_grey_placeholder;
                    &.active {
                        background-color: $color_blue_web_safe;
                    }
                }
            }
            .step {
                position: relative;
                display: flex;
                height: $height_24;
                width: $width_24;
                align-items: center;
                justify-content: center;
                border-radius: $width_24;
                margin-bottom: $space_20;
                background-color: $color_white;
                img {
                    height: $height_26;
                    max-width: unset;
                }
                &.past-current {
                    cursor: pointer;
                    border: solid 2px $color_blue_web_safe;
                }
                &.futur {
                    background-color: $color_white;
                    border: solid 2px $color_grey_placeholder;
                    cursor: not-allowed;
                }
                &.not-allowed {
                    cursor: not-allowed;
                }
                .icon {
                    height: $height_14;
                    width: $width_14;
                    color: $color_white;
                }
                .current {
                    height: 100%;
                    width: 100%;
                    border-radius: 8px;
                    background-color: $color_blue_web_safe;
                }
                .sr {
                    position: absolute;
                    width: 1px;
                    height: 1px;
                    padding: 0;
                    margin: -1px;
                    overflow: hidden;
                    clip: rect(0, 0, 0, 0);
                    white-space: nowrap;
                    border-width: 0;
                }
                .label {
                    position: absolute;
                    top: $space_28;
                    color: $color_grey_placeholder;
                    font-family: $font_open_sans;
                    font-size: $font_size_13;
                    font-weight: $font_bold;
                    line-height: $height_20;
                    &.past {
                        color: $color_blue_web_safe;
                    }
                }
            }
        }
    }
}
</style>

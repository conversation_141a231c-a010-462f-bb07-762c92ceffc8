<script setup>
import SvdInputRadio from '@/v5/components/input/SvdInputRadio.vue'

const props = defineProps({
    group: {
        type: Object,
        required: true,
    },
    value: {
        type: Object,
        required: true,
    },
})

const emit = defineEmits(['input', 'click'])

function onClick(event) {
    emit('input', props.group)
    if (props.group.name !== props.value?.name) {
        emit('click', event)
    }
}
</script>

<template>
    <div class="method-group-title" data-context="method-group-title" @click="onClick($event)">
        <svd-input-radio
            class="method-radio"
            :id="props.group.name"
            :name="props.group.name"
            :value="props.value.name"
            :ariaLabel="props.group.label"
        />
        <div class="content">
            <div class="title">
                <slot />
                <slot class="icons" name="icons" v-if="$slots.icons" />
            </div>
            <div data-context="method-group-right" class="right" v-if="$slots.right">
                <slot name="right" />
            </div>
            <div class="subtitle" v-if="$slots.subtitle">
                <slot name="subtitle" />
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
@import '../../../../Resources/scss/style';

.method-group-title {
    display: flex;
    flex-direction: row;
    gap: $space_16;
    cursor: pointer;
    margin-top: $space_32;
    .method-radio {
        margin-top: 2px;
    }
    .content {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        width: 100%;
        @include media_min($media_md) {
            flex-direction: row;
            flex-wrap: wrap;
        }
        .title {
            display: flex;
            flex-direction: row;
            gap: $space_16;
            color: $color_dark_default;
            font-size: $font_size_18;
            font-weight: $font_bold;
            line-height: $height_28;
            @include media_min($media_md) {
                width: 80%;
            }
            .icons {
                display: none;
                @include media_min($media_md) {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    align-content: center;
                }
            }
        }
        .right {
            display: flex;
            justify-content: flex-start;
            font-size: $font_size_18;
            font-weight: $font_bold;
            line-height: $height_28;
            @include media_min($media_md) {
                width: 20%;
                justify-content: flex-end;
            }
        }
        .subtitle {
            color: $color_grey_typo;
            font-size: $font_size_13;
            font-weight: $font_normal;
            line-height: $height_20;
            width: 100%;
        }
    }
}
</style>

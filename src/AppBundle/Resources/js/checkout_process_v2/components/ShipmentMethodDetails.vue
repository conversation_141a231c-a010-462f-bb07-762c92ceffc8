<script setup>
import SidePanel from '@/shared/components/SidePanel.vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'
import { useCheckoutProcessStore } from '@/checkout_process_v2/stores/checkout_process'

const store = useCheckoutProcessStore()

function onClose() {
    store.setShipmentMethodInfo()
}
</script>

<template>
    <side-panel v-if="store.shipment_method_info" @close="onClose" title="" template="v5" :is-loading="false">
        <div class="carrier-details" data-context="carrier-side-panel">
            <div>
                <svd-icon
                    class="cursor-pointer"
                    alt="Transporteur"
                    :icon="store.shipment_method_info.logo"
                    keep-color
                    size="100"
                />
            </div>
            <div class="title">
                {{ store.shipment_method_info.title }}
            </div>
            <div class="description">{{ store.shipment_method_info.description }}</div>
        </div>
    </side-panel>
</template>

<style scoped lang="scss">
@import '../../../../Resources/scss/style';
.carrier-details {
    display: flex;
    flex-direction: column;
    gap: $space_16;
    font-family: $font_open_sans;
    font-weight: $font_normal;
    font-size: $font_size_15;
    line-height: $height_24;
    color: $color_grey_typo;
    .title {
        font-weight: $font_bold;
        font-size: $font_size_18;
        line-height: $height_28;
        color: $color_dark_default;
    }
    .description {
        white-space: pre-wrap;
    }
}
</style>

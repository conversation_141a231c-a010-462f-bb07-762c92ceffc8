<script setup>
import { computed, onMounted, ref } from 'vue'
import SvdMessage from '@/v5/components/message/SvdMessage.vue'
import PaymentMethodBase from '@/checkout_process_v2/components/payment_method/PaymentMethodBase.vue'

const props = defineProps({
    paymentMethod: {
        type: Object,
        required: true,
    },
})

const emit = defineEmits(['confirm'])

const PAYMENT_VERSION = {
    V1: 'v1',
    V2: 'v2',
}

const element_ref = ref(undefined)
const tokenizer = ref(undefined)
const fatal_error = ref(false)
const loading = ref(false)
const use_v2_hosted_tokenization = computed(
    () => props.paymentMethod?.workflow === 'payment_v2' && props.paymentMethod.extra_data.hosted_tokenization_url
)

onMounted(() => {
    if (use_v2_hosted_tokenization.value) {
        tokenizer.value = new Tokenizer(props.paymentMethod.extra_data.hosted_tokenization_url, element_ref.value.id, {
            hideCardholderName: false,
            askConsumerConsent: false,
            locale: 'fr_FR',
        })

        tokenizer.value
            .initialize()
            .then(() => {
                loading.value = false
            })
            .catch((reason) => {
                fatal_error.value = true
            })
    }
})

// V2 only
function submit() {
    loading.value = true
    tokenizer.value
        .submitTokenization({ storePermanently: false })
        .then((result) => {
            if (result.success) {
                emit('confirm', {
                    browser_data: {
                        color_depth: screen.colorDepth,
                        javascript_enabled: true,
                        screen_height: screen.height,
                        screen_width: screen.width,
                        locale: navigator.language,
                        timezone_offset: new Date().getTimezoneOffset(),
                    },
                    tokenization_id: result.hostedTokenizationId,
                })
            } else {
                loading.value = false
            }
        })
        .catch((reason) => {
            fatal_error.value = true
            loading.value = false
        })
}
</script>

<template>
    <!-- PAYMENT V2 with hosted tokenization -->
    <payment-method-base v-if="use_v2_hosted_tokenization" :loading="loading" @confirm="submit">
        <div class="worldline">
            <svd-message v-if="fatal_error" type="error"> Une erreur s’est produite. Veuillez réessayer. </svd-message>
            <div v-else ref="element_ref" id="div-hosted-tokenization"></div>
        </div>
    </payment-method-base>

    <!-- PAYMENT V1 or V2 with hosted checkout -->
    <payment-method-base :loading="loading" v-else @confirm="emit('confirm')" />
</template>

<style lang="scss">
#div-hosted-tokenization {
    iframe[name='htpIframe0'] {
        width: 100%;
        max-width: 400px;
    }
}
</style>

<script setup>
import SvdInputRadio from '@/v5/components/input/SvdInputRadio.vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'
import { computed } from 'vue'
const emit = defineEmits(['select'])

const props = defineProps({
    id: {
        type: Number,
        required: true,
    },
    selectedPaymentMethod: {
        type: Number,
        required: true,
    },
    label: {
        type: String,
        required: true,
    },
    description: {
        type: String,
        required: false,
    },
    icon: {
        type: String,
        required: true,
    },
    radio: {
        type: Boolean,
        default: false,
    },
})

const floaIcons = ['3x', '4x']
const cetelemIcons = ['10x', '20x', '30x', '40x']

const computedColor = computed(() => {
    if (floaIcons.includes(props.icon)) return 'color_cetelem'
    if (cetelemIcons.includes(props.icon)) return 'color_cetelem'
    return undefined
})

const computedKeepColor = computed(() => {
    return !(floaIcons.includes(props.icon) || cetelemIcons.includes(props.icon))
})
</script>

<template>
    <div class="payment_name" data-context="payment-mean" @click="emit('select', id)">
        <div class="name">
            <svd-input-radio
                v-if="radio"
                :name="`payment-method-radio-${id}`"
                :id="id"
                :value="selectedPaymentMethod"
                :ariaLabel="label"
                :data-context="`payment-method-radio-${id}`"
                class="radio"
                small
            />
            <div class="title-description">
                <div class="title" data-context="label">
                    <span>{{ label }}</span>
                </div>
                <div class="description">
                    {{ description }}
                </div>
            </div>
        </div>
        <span class="icon" data-context="icon" v-if="props.id !== 58">
            <svd-icon :color="computedColor" :keep-color="computedKeepColor" :icon="icon" :alt="icon" :size="30" />
        </span>
        <span class="icon grouped" data-context="icon" v-else>
            <svd-icon keep-color icon="cb" alt="CB / VISA / Mastercard / E-Carte bleue" :size="30" />
            <svd-icon keep-color icon="amex" alt="American Express" :size="23" />
        </span>
    </div>
</template>

<style scoped lang="scss">
@import '../../../../../Resources/scss/style';
.payment_name {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: $space_16;
    .name {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        gap: $space_16;
        color: $color_dark_default;
        font-weight: $font_semi_bold;
        .radio {
            padding-top: $space_4;
            padding-bottom: $space_4;
        }
        input[type='radio'] {
            margin-top: $space_4;
            margin-bottom: $space_4;
        }
        .title-description {
            padding-right: $space_16;
            .title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                .recommend {
                    background-color: $color_blue_web_safe;
                    padding: $space_2 $space_10;
                    color: $color_white;
                    text-transform: uppercase;
                }
            }
            .description {
                font-size: $font_size_13;
                font-weight: $font_normal;
                color: $color_grey_typo;
            }
        }
        .logo {
            color: $color_dark_default;
            font-weight: $font_semi_bold;
        }
    }
}
.grouped {
    display: flex;
    flex-direction: row;
    gap: $space_8;
}
</style>

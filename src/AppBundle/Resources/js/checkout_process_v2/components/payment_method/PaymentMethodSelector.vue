<script setup>
import Skeleton from '@/checkout_process_v2/components/Skeleton.vue'
import { computed, ref, watch } from 'vue'
import { isMobile } from '@/shared/functions'
import { useCheckoutProcessStore } from '@/checkout_process_v2/stores/checkout_process'
import MethodGroupTitle from '@/checkout_process_v2/components/MethodGroupTitle.vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'
import PaymentMethodHeader from '@/checkout_process_v2/components/payment_method/PaymentMethodHeader.vue'
import PaymentMethodContentGeneric from '@/checkout_process_v2/components/payment_method/PaymentMethodContentGeneric.vue'
import PaymentMethodContentMultipleGeneric from '@/checkout_process_v2/components/payment_method/PaymentMethodContentMultipleGeneric.vue'
import PaymentMethodContentPresto from '@/checkout_process_v2/components/payment_method/PaymentMethodContentPresto.vue'
import PaymentMethodContentSvdcc from '@/checkout_process_v2/components/payment_method/PaymentMethodContentSvdcc.vue'
import PaymentMethodContentOther from '@/checkout_process_v2/components/payment_method/PaymentMethodContentOther.vue'
import SvdMessage from '@/v5/components/message/SvdMessage.vue'
import { BREAKPOINT_DESKTOP } from '@/shared/referential/Breakpoints'
import PaymentMethodContentCreditCard from '@/checkout_process_v2/components/payment_method/PaymentMethodContentCreditCard.vue'

const emit = defineEmits(['confirm'])

const store = useCheckoutProcessStore()
const is_loading = ref(false)
const selected_payment_method = ref(0)

const GROUP_NAME = {
    CREDIT_CARD: 'credit_card',
    PAYPAL: 'paypal',
    INSTALLMENT_PLAN: 'installment_plan',
    OTHERS: 'others',
}

// returns only the groups that payment methods
const payment_groups = computed(() =>
    [
        {
            name: GROUP_NAME.CREDIT_CARD,
            label: 'Carte bancaire',
            methods: store.payments.filter((sm) => sm.payment_group_id === 'credit-card'),
        },
        {
            name: GROUP_NAME.PAYPAL,
            label: 'Paypal',
            methods: store.payments.filter((sm) => sm.payment_group_id === 'paypal'),
        },
        {
            name: GROUP_NAME.INSTALLMENT_PLAN,
            label: 'Paiement en plusieurs fois',
            methods: store.payments.filter(
                (sm) => sm.payment_group_id === '3x-4x-without-charge' || sm.payment_group_id === 'presto'
            ),
        },
        {
            name: GROUP_NAME.OTHERS,
            label: 'Autres modes de paiement',
            methods: store.payments.filter(
                (sm) =>
                    sm.payment_group_id !== 'credit-card' &&
                    sm.payment_group_id !== 'paypal' &&
                    sm.payment_group_id !== '3x-4x-without-charge' &&
                    sm.payment_group_id !== 'presto'
            ),
        },
    ].filter((g) => (g.methods ?? []).length > 0)
)
const expanded_group = ref(undefined)

// Simulated "created" hook (run before onMounted in the component lifecycle)
expanded_group.value = payment_groups.value?.[0]

watch(
    expanded_group,
    (newValue) => {
        if ((newValue?.methods ?? []).length > 0) {
            selectPaymentMethod(newValue.methods[0].payment_method_id)
        }
    },
    { deep: true, immediate: true }
)

const paypal_name = computed(() => {
    if (store.total_price > 30 && store.total_price < 2000) {
        return 'PayPal au comptant ou en 4 fois sans frais'
    }
    return 'Paypal'
})

function descriptionOnlyForCb(cb) {
    return [32, 58].includes(getPaymentId(cb)) ? cb.description : ''
}

function confirmPayment(with_payment_method, skip_submission = false, with_extra_data = null) {
    const payload = { payment_method_id: with_payment_method.payment_method_id }

    const extra_data = {
        ...(with_payment_method.extra_data ?? {}),
        ...(with_extra_data ?? {}),
    }
    if (Object.keys(extra_data).length > 0) {
        payload.extra_data = extra_data
    }

    emit('confirm', { payload, skip_submission })
}

function selectPaymentMethod(id) {
    selected_payment_method.value = id
}

function paymentSchedule(value) {
    return value.extra_data?.duration ?? Number(value.code.trim().match(/\d+/))
}

function getPaymentId(item) {
    return item?.payment_method_id
}

function onGroupClick(event) {
    if (isMobile(BREAKPOINT_DESKTOP)) {
        setTimeout(() => {
            event.target.scrollIntoView()
        }, 100)
    }
}
</script>

<template>
    <skeleton v-if="is_loading" class="mt-3" />
    <svd-message type="error" v-else-if="store.payments.length === 0" class="mt-3" data-context="error-message">
        Aucun moyen de paiement éligible n’a été retourné par le système.
    </svd-message>
    <div v-else data-context="payment-methods">
        <div v-for="group of payment_groups">
            <method-group-title
                :group="group"
                :value="expanded_group"
                @input="expanded_group = $event"
                @click="onGroupClick($event)"
            >
                {{ group.label }}

                <!-- Group icons -->
                <div
                    class="icons"
                    v-if="group.name === GROUP_NAME.CREDIT_CARD && expanded_group.name !== GROUP_NAME.CREDIT_CARD"
                >
                    <svd-icon icon="visa" alt="Visa" :size="20" keep-color />
                    <svd-icon icon="mastercard" alt="Mastercard" :size="20" keep-color />
                    <svd-icon icon="cb" alt="Carte bancaire" :size="20" keep-color />
                    <svd-icon icon="amex" alt="Amex" :size="20" keep-color />
                </div>
                <div class="icons" v-if="group.name === GROUP_NAME.PAYPAL && expanded_group.name !== GROUP_NAME.PAYPAL">
                    <svd-icon icon="paypal" alt="Paypal" :size="20" keep-color />
                </div>
                <div
                    class="icons"
                    v-if="
                        group.name === GROUP_NAME.INSTALLMENT_PLAN &&
                        expanded_group.name !== GROUP_NAME.INSTALLMENT_PLAN
                    "
                >
                    <svd-icon icon="cetelem" alt="cetelem" :size="60" keep-color />
                </div>
                <div class="icons" v-if="group.name === GROUP_NAME.OTHERS && expanded_group.name !== GROUP_NAME.OTHERS">
                    <svd-icon icon="virement" alt="Virement" :size="20" keep-color />
                    <svd-icon icon="cbtel" alt="Carte bancaire par téléphone" :size="20" keep-color />
                    <svd-icon icon="cadeau" alt="Carte cadeau" :size="20" keep-color />
                </div>
            </method-group-title>

            <div class="select-payment-method-container" v-if="group.name === expanded_group.name">
                <!-- Credit card -->
                <template v-if="expanded_group.name === GROUP_NAME.CREDIT_CARD">
                    <div
                        v-for="(cb, index) of group.methods"
                        class="select-payment-method"
                        data-context="cb-payment-expanded"
                        :key="`cb_payment_expanded_${index}`"
                        :class="{
                            selected: getPaymentId(cb) === selected_payment_method,
                        }"
                    >
                        <payment-method-header
                            :description="descriptionOnlyForCb(cb)"
                            :icon="cb.logo"
                            :radio="group.methods.length > 1"
                            :id="getPaymentId(cb)"
                            :label="cb.label"
                            :selected-payment-method="selected_payment_method"
                            @select="selectPaymentMethod"
                        />
                        <payment-method-content-credit-card
                            :payment-method="cb"
                            v-if="getPaymentId(cb) === selected_payment_method"
                            @confirm="confirmPayment(cb, false, $event)"
                        />
                    </div>
                </template>

                <!-- Paypal -->
                <template v-if="expanded_group.name === GROUP_NAME.PAYPAL">
                    <div
                        v-for="(paypal, index) of group.methods"
                        class="select-payment-method"
                        data-context="paypal-payment-expanded"
                        :key="`paypal_payment_expanded_${index}`"
                        :class="{
                            selected: getPaymentId(paypal) === selected_payment_method,
                        }"
                    >
                        <payment-method-header
                            :icon="paypal.logo"
                            :radio="group.methods.length > 1"
                            :id="getPaymentId(paypal)"
                            :label="paypal_name"
                            :selected-payment-method="selected_payment_method"
                            @select="selectPaymentMethod"
                        />
                        <payment-method-content-generic
                            v-if="getPaymentId(paypal) === selected_payment_method"
                            @confirm="confirmPayment(paypal)"
                        />
                    </div>
                </template>

                <!-- Installment plan -->
                <template v-if="expanded_group.name === GROUP_NAME.INSTALLMENT_PLAN">
                    <div
                        v-for="(multiple, index) of group.methods"
                        class="select-payment-method"
                        data-context="multiple-payment-expanded"
                        :key="`multiple_payment_expanded_${index}`"
                        :class="{
                            selected: getPaymentId(multiple) === selected_payment_method,
                        }"
                    >
                        <payment-method-header
                            :icon="multiple.logo"
                            :radio="group.methods.length > 1"
                            :id="getPaymentId(multiple)"
                            :label="multiple.label"
                            :selected-payment-method="selected_payment_method"
                            @select="selectPaymentMethod"
                        />
                        <template v-if="getPaymentId(multiple) === selected_payment_method">
                            <payment-method-content-presto
                                :payment-method="multiple"
                                v-if="multiple.code.trim() === 'PRESTO'"
                                @confirm="confirmPayment(multiple)"
                            />
                            <payment-method-content-multiple-generic
                                v-else
                                :use-schedule="paymentSchedule(multiple)"
                                @confirm="confirmPayment(multiple)"
                            />
                        </template>
                    </div>
                </template>

                <!-- Others -->
                <template v-if="expanded_group.name === GROUP_NAME.OTHERS">
                    <div
                        v-for="(other, index) of group.methods"
                        class="select-payment-method"
                        data-context="other-payment-expanded"
                        :key="`other_payment_expanded_${index}`"
                        :class="{
                            selected: getPaymentId(other) === selected_payment_method,
                        }"
                    >
                        <payment-method-header
                            :icon="other.logo"
                            :radio="group.methods.length > 1"
                            :id="getPaymentId(other)"
                            :label="other.label"
                            :selected-payment-method="selected_payment_method"
                            @select="selectPaymentMethod"
                        />
                        <template v-if="getPaymentId(other) === selected_payment_method">
                            <payment-method-content-svdcc
                                v-if="other.code.trim() === 'SVDCC'"
                                :total-price="store.total_price"
                                @confirm="confirmPayment(other, true)"
                            />
                            <payment-method-content-other
                                v-else
                                :transfer="other.code.trim() === 'VIR'"
                                :phone="other.code.trim() === 'TEL'"
                                :gift="other.code.trim() === 'BKDO'"
                                @confirm="confirmPayment(other)"
                            />
                        </template>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
@import '../../../../../Resources/scss/style';
.icons {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    align-content: center;
    gap: $space_8;
}
.select-payment-method-container {
    padding-bottom: $space_20;
    .select-payment-method {
        display: flex;
        flex-direction: column;
        margin-top: $space_16;
        gap: $space_16;
        border: solid 1px $color_grey_default;
        border-radius: $radius_16;
        &.selected {
            border: solid 2px $color_blue_web_safe;
            border-radius: $radius_16;
            .payment_name {
                border-bottom: solid 1px $color_grey_default;
            }
        }
        @include media_min($media_md) {
            margin-left: $space_36;
        }
    }
}
</style>

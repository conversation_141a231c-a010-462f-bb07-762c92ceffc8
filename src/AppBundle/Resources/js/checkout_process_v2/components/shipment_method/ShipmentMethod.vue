<template>
    <div
        class="shipment-method"
        :class="{
            selected: is_selected,
        }"
    >
        <div class="delivery-name" data-context="title" @click="selectShipmentMethod">
            <div class="name">
                <svd-input-radio
                    v-if="actionDisplayed"
                    :name="name"
                    :id="shipmentMethod.shipment_method_id"
                    :value="selectedShipmentMethod.shipment_method_id"
                    :ariaLabel="formated_aria_label"
                    :data-context="`shipment-method-radio-${shipmentMethod.shipment_method_id}`"
                    class="radio"
                    small
                />
                <div class="title-description">
                    <div class="title">
                        <span class="text">
                            {{ title }}
                            <svd-icon
                                @click="store.setShipmentMethodInfo(shipmentMethod)"
                                icon="question-circle"
                                alt="Afficher texte explicatif"
                                data-context="carrier-explain"
                                :size="16"
                            />
                        </span>
                        <span data-context="tag-recommended" v-if="shipmentMethod.tags?.recommended" class="recommend"
                            >Recommandé</span
                        >
                    </div>
                    <div class="description" v-if="!shipmentMethod.is_shipment_quotation">
                        <template v-if="shipmentMethod.shipping_delay !== null">
                            Dès <date-formatter :date="shipmentMethod.shipping_date" format="dddd DD MMMM" />
                        </template>
                        <template v-else> {{ shipmentMethod.no_delay_fallback }} </template>
                    </div>
                </div>
            </div>
            <span class="price" data-context="price">
                <template v-if="shipmentMethod.is_shipment_quotation">
                    <span>sur offre</span>
                </template>
                <template v-else-if="shipmentMethod.cost > 0">{{ shipmentMethod.cost_formatted }}</template>
                <template v-else-if="shipmentMethod.cost === 0">
                    <span class="free">OFFERT</span>
                </template>
            </span>
        </div>
        <div class="body" v-if="is_selected">
            <slot name="before-address" />
            <svd-message v-if="shipmentMethod.shipment_method_id === novea_commissioning_id" type="info" small>
                <div class="novea-message">
                    <div>
                        Disponible sur rendez-vous, cette option comprend le déballage de votre appareil, son
                        branchement au secteur, à votre box, en Ethernet ou à votre connexion Wi-Fi.
                    </div>
                    <svd-link label="En savoir plus" @click="store.setShipmentMethodInfo(shipmentMethod)" small />
                </div>
            </svd-message>
            <div class="shipping-address" data-context="shipping-address" v-if="shippingAddress">
                <div class="title">
                    <div v-if="shippingAddress.company_name && shippingAddress.company_name !== ''">
                        {{ shippingAddress.company_name }}
                    </div>
                    <div>
                        {{ formatted_civility }}
                        {{ shippingAddress.firstname }}
                        {{ shippingAddress.lastname }}
                    </div>
                </div>
                <div>
                    <div>{{ formatted_address_lines }}</div>
                    <div>
                        {{ shippingAddress.postal_code }}
                        {{ capitalize(shippingAddress.city) }}
                    </div>
                    <div>
                        {{ capitalize(shippingAddress.country_name) }}
                    </div>
                    <div>
                        {{ shippingAddress.cellphone }}
                    </div>
                </div>
            </div>
            <slot />
            <svd-message type="error" v-if="has_error">
                Une erreur s’est produite lors de l'enregistrement. Veuillez réessayer.
            </svd-message>
            <div class="details" data-context="info" v-if="isHome">
                <svd-button
                    v-if="isSubmitDisplayed && !has_error"
                    data-context="confirm-btn"
                    :disabled="isDisabled || isSubmitDisabled"
                    label="Valider ma livraison"
                    primary
                    @click="confirmShipmentMethod"
                />
                <svd-button
                    outline
                    light
                    data-context="other-address"
                    label="Choisir une autre adresse"
                    @click="$emit('address:edit')"
                />
                <slot name="after-select" :isFullyOpened="is_fully_opened" />
            </div>
        </div>
    </div>
</template>

<script>
import { asset, capitalize } from '@/shared/filters'
import TwButton from '@/shared/components/form/TwButton'
import ClockIcon from '@/shared/components/HeroIcons/Outline/ClockIcon'
import TwRadioInput from '@/shared/components/form/TwRadioInput'
import SvdInputRadio from '@/v5/components/input/SvdInputRadio.vue'
import SvdButton from '@/v5/components/button/SvdButton.vue'
import { useCheckoutProcessStore } from '@/checkout_process_v2/stores/checkout_process'
import SvdMessage from '@/v5/components/message/SvdMessage.vue'
import SvdLink from '@/v5/components/link/SvdLink.vue'
import DateFormatter from '@/account/orders/components/DateFormatter.vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'

export default {
    name: 'ShipmentMethod',
    components: {
        SvdIcon,
        SvdLink,
        SvdMessage,
        SvdButton,
        SvdInputRadio,
        TwRadioInput,
        TwButton,
        ClockIcon,
        DateFormatter,
    },
    props: {
        shipmentMethod: {
            type: Object,
            required: true,
            validator: (v) =>
                [
                    'title',
                    'description',
                    'logo',
                    'cost',
                    'cost_formatted',
                    'is_express',
                    '_is_submitting',
                    'shipping_delay',
                ].every((k) => v.hasOwnProperty(k)),
        },
        isDisabled: {
            type: Boolean,
            default: () => false,
        },
        isSubmitDisabled: {
            type: Boolean,
            default: () => false,
        },
        isSubmitDisplayed: {
            type: Boolean,
            default: () => true,
        },
        isLoading: {
            type: Boolean,
            default: () => false,
        },
        title: {
            type: String,
            required: true,
        },
        shippingAddress: {
            type: Object,
            required: false,
        },
        ariaLabelStart: {
            type: String,
            required: false,
        },
        name: {
            type: String,
            required: false,
        },
        selectedShipmentMethod: {
            type: Object,
            required: false,
        },
        isRelay: {
            type: Boolean,
            default: false,
        },
        isHome: {
            type: Boolean,
            default: false,
        },
        actionDisplayed: {
            type: Boolean,
            required: false,
            default: true,
        },
        previewShipping: {
            type: Object,
            required: false,
        },
    },
    setup() {
        const store = useCheckoutProcessStore()
        return { store }
    },
    data() {
        return {
            is_fully_opened: this.isSelected,
            civilities: { Mr: 'M.', Ms: 'Mme.' },
            novea_commissioning_id: 76,
        }
    },
    computed: {
        has_error() {
            return this.store.has_error
        },
        is_recommended() {
            return !!this.shipmentMethod?.tags?.recommended
        },
        formatted_civility() {
            return this.civilities[this.shippingAddress.title] || this.shippingAddress.title
        },
        formatted_address_lines() {
            return this.shippingAddress.address.replace(/\n/g, ', ')
        },
        formated_aria_label() {
            return this.ariaLabelStart + ' ' + this.shipmentMethod.title
        },
        is_selected() {
            return this.selectedShipmentMethod.shipment_method_id === this.shipmentMethod.shipment_method_id
        },
    },
    mounted() {
        if (this.previewShipping?.shipment_method_id === this.shipmentMethod.shipment_method_id) {
            this.selectShipmentMethod()
        }
    },
    methods: {
        capitalize,
        asset,
        selectShipmentMethod() {
            this.$emit('select', this.shipmentMethod)
        },
        confirmShipmentMethod() {
            this.$emit('confirm')
        },
    },
}
</script>

<style scoped lang="scss">
@import '../../../../../Resources/scss/style';
.shipment-method {
    display: flex;
    flex-direction: column;
    line-height: $height_24;
    font-size: $font_size_15;
    color: $color_grey_typo;
    border: solid 1px $color_grey_default;
    border-radius: $radius_16;
    &.selected {
        border: solid 2px $color_blue_web_safe;
        border-radius: $radius_16;
        .delivery-name {
            padding: $space_15;
            border-bottom: solid 1px $color_grey_default;
        }
    }
    .delivery-name {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        cursor: pointer;
        padding: $space_16;
        .name {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            gap: $space_16;
            color: $color_dark_default;
            font-weight: $font_semi_bold;
            width: 100%;
            .radio {
                padding-top: $space_4;
                padding-bottom: $space_4;
            }
            .title-description {
                padding-right: $space_16;
                width: 100%;
                .title {
                    display: flex;
                    flex-direction: column-reverse;
                    justify-content: space-between;
                    width: 100%;
                    @include media_min($media_md) {
                        flex-direction: row;
                    }
                    .text {
                        display: flex;
                        flex-direction: row;
                        gap: $space_8;
                        align-items: center;
                    }
                    .recommend {
                        background-color: $color_blue_web_safe;
                        padding: $space_2 $space_10;
                        color: $color_white;
                        text-transform: uppercase;
                        max-height: $height_28;
                        width: fit-content;
                    }
                }
                .description {
                    line-height: $height_20;
                    font-size: $font_size_13;
                    font-weight: $font_normal;
                    color: $color_grey_typo;
                }
            }
            input[type='radio'] {
                margin-top: $space_4;
                margin-bottom: $space_4;
            }
        }
        .price {
            color: $color_dark_default;
            font-weight: $font_bold;
            white-space: nowrap;
            .free {
                color: $color_success;
            }
        }
    }
    .body {
        display: flex;
        flex-direction: column;
        gap: $space_16;
        padding: $space_16;
        .novea-message {
            color: $color_grey_typo;
            font-family: $font_open_sans;
            font-size: $font_size_13;
            font-weight: $font_normal;
            line-height: $height_20;
            display: flex;
            flex-direction: column;
            gap: $space_4;
        }
        .details {
            display: flex;
            flex-direction: column;
            gap: $space_8;
            button {
                padding-right: unset;
                padding-left: unset;
                @include media_min($media_md) {
                    width: 250px;
                }
            }

            @include media_min($media_md) {
                flex-direction: row;
            }
        }
        .shipping-address {
            line-height: $height_20;
            font-size: $font_size_13;
            font-weight: $font_normal;
            color: $color_grey_typo;
            font-family: $font_open_sans;
        }
    }
}
</style>

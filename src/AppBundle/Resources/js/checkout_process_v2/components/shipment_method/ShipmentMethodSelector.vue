<script setup>
import { computed, onMounted, ref, watch } from 'vue'
import api from '@/shared/api/handler'
import Skeleton from '../Skeleton'
import ShipmentMethodBasic from './ShipmentMethodBasic'
import ShipmentMethodQuotation from './ShipmentMethodQuotation.vue'
import ShipmentMethodChronopostPrecise from './ShipmentMethodChronopostPrecise.vue'
import {
    isChronopostPreciseShipmentMethod,
    isMondialRelayShipmentMethod,
    isChronopostPickupShipmentMethod,
} from '@/checkout_process_v2/shipment_methods'
import ShipmentMethodRelay from './ShipmentMethodRelay.vue'
import ShipmentMethodData from '@/checkout_process_v2/components/shipment_method/ShipmentMethodData.vue'
import { useCheckoutProcessStore } from '@/checkout_process_v2/stores/checkout_process'
import SvdMessage from '@/v5/components/message/SvdMessage.vue'
import SvdLink from '@/v5/components/link/SvdLink.vue'
import SvdInputRadio from '@/v5/components/input/SvdInputRadio.vue'
import SvdButton from '@/v5/components/button/SvdButton.vue'
import DateFormatter from '@/account/orders/components/DateFormatter.vue'
import ShipmentMethodGroupTitle from '@/checkout_process_v2/components/shipment_method/ShipmentMethodGroupTitle.vue'

const emit = defineEmits(['contact-us:open'])

const store = useCheckoutProcessStore()

onMounted(() => {
    fetchShippingMethods()
})
const is_loading = ref(true)
const shipment_methods = ref([])
const default_store = ref(null)
const selected_suggestion = ref(null)
async function fetchShippingMethods() {
    try {
        is_loading.value = true
        const response = await api.get('/ma-commande/liste-moyens-transport')
        shipment_methods.value = response.map((sm) => ({
            ...sm,
            _is_submitting: false,
            _has_error: false,
        }))

        default_store.value = store_shipment_methods.value[0]
        initializeSelectedShipmentMethod()
    } catch (e) {
        shipment_methods.value = []
    } finally {
        is_loading.value = false
    }
}

const is_submitting = computed(() => shipment_methods.value.some((sm) => sm._is_submitting))

const initial_selected_shipment_method = ref(store.initial_selected_shipment_method)
const preview_shipping = ref(store.initial_selected_shipment_method)
function getCheapestShipmentMethod(shipment_methods) {
    return shipment_methods.reduce(
        (cheapest, shipment_method) => (shipment_method.cost < cheapest.cost ? shipment_method : cheapest),
        shipment_methods[0]
    )
}
function getQuickestShipmentMethod(shipment_methods) {
    return shipment_methods.reduce(
        (quickest, shipment_method) =>
            shipment_method.shipping_delay !== null && shipment_method.shipping_delay < quickest.shipping_delay
                ? shipment_method
                : quickest,
        shipment_methods[0]
    )
}
// ===
// Store group handling
// ===
const STORE_SHIPMENT_GROUP = 'store'
const store_search = ref([])
const search_error = ref(null)
const store_shipment_methods = computed(() => {
    if (store_search.value.length > 0) {
        return store_search.value
    }

    return shipment_methods.value.filter((sm) => sm.is_store)
})
const cheapest_store_shipment_method = computed(() => getCheapestShipmentMethod(store_shipment_methods.value))
const quickest_store_shipment_method = computed(() => getQuickestShipmentMethod(store_shipment_methods.value))
function openStore() {
    toggleExpanded(STORE_SHIPMENT_GROUP)
    selectShipmentMethod(default_store.value)
}
function searchStore(value) {
    search_error.value = null
    api.get(`/api/liste-emport-depot?zipcode=${value.postal_code}`)
        .then((response) => {
            const ordered_response_stores = response.data.stores
            store_search.value = []
            ordered_response_stores.forEach((store) => {
                const store_object = shipment_methods.value
                    .filter((sm) => sm.is_store)
                    .find((s) => s.shipment_method_id === store.shipment_method_id)

                if (undefined === store_object) {
                    return
                }

                store_search.value.push(store_object)
            })
        })
        .catch((e) => {
            search_error.value = 'Une erreur est survenue.'
            if ('Wrong Zip code' === e.data?.data?.error) {
                search_error.value = 'Le code postal est invalide.'
            }
        })
}

// ===
// Relay group handling
// ===
const RELAY_SHIPMENT_GROUP = 'relay'
const relay_shipment_methods = computed(() => shipment_methods.value.filter((sm) => sm.is_relay))
const cheapest_relay_shipment_method = computed(() => getCheapestShipmentMethod(relay_shipment_methods.value))
const quickest_relay_shipment_method = computed(() => getQuickestShipmentMethod(relay_shipment_methods.value))
const selectedRelay = ref(null)
function selectRelayMethod(values) {
    if (values.shipmentMethod && values.relay) {
        selectedRelay.value = values.relay
        selectShipmentMethod(values.shipmentMethod)
    } else {
        selectShipmentMethod(values)
    }
}

// ===
// Home group handling
// ===
const HOME_SHIPMENT_GROUP = 'home'
const home_shipment_methods = computed(() => shipment_methods.value.filter((sm) => !sm.is_relay && !sm.is_store))
const cheapest_home_shipment_method = computed(() => getCheapestShipmentMethod(home_shipment_methods.value))
const quickest_home_shipment_method = computed(() => getQuickestShipmentMethod(home_shipment_methods.value))

// ===
// Selection handling
// ===
const selected_shipment_method = ref(null)
const expanded_shipment_group = computed(() => {
    if (selected_shipment_method.value === null) {
        return null
    }

    if (selected_shipment_method.value.is_store) {
        return STORE_SHIPMENT_GROUP
    }

    if (selected_shipment_method.value.is_relay) {
        return RELAY_SHIPMENT_GROUP
    }

    return HOME_SHIPMENT_GROUP
})
function initializeSelectedShipmentMethod() {
    let selected_shipment_method = null

    let initialSelectedShipmentMethodFound = null
    if (initial_selected_shipment_method.value) {
        initialSelectedShipmentMethodFound = shipment_methods.value.find(
            (sm) => sm.shipment_method_id === initial_selected_shipment_method.value.shipment_method_id
        )
    }

    if (initialSelectedShipmentMethodFound) {
        selected_shipment_method = initial_selected_shipment_method.value
        selected_shipment_method.selected_data = initial_selected_shipment_method.value.extra_data
    } else if (store_shipment_methods.value.length > 0) {
        selected_shipment_method = store_shipment_methods.value[0]
    } else if (home_shipment_methods.value.length > 0) {
        selected_shipment_method = home_shipment_methods.value[0]
    } else if (relay_shipment_methods.value.length > 0) {
        selected_shipment_method = relay_shipment_methods.value[0]
    }

    selectShipmentMethod(selected_shipment_method)
}
function selectShipmentMethod(shipment_method) {
    if (shipment_method?.is_store) {
        let selected_openings_list = store_shipment_methods.value.find(
            (e) => e.shipment_method_id === shipment_method.shipment_method_id
        ).tags.store.extra_data.openings_list
        default_store.value = shipment_method
        default_store.value.tags.store.extra_data.openings_list = selected_openings_list
    }
    selected_shipment_method.value = shipment_method
    store.setInitialSelectedShipmentMethod(shipment_method)
}
function toggleExpanded(element) {
    if (element === expanded_shipment_group.value) {
        return
    }

    if (element === STORE_SHIPMENT_GROUP) {
        selectShipmentMethod(store_shipment_methods.value[0])
    }
    if (element === HOME_SHIPMENT_GROUP) {
        selectShipmentMethod(home_shipment_methods.value[0])
    }
    if (element === RELAY_SHIPMENT_GROUP) {
        selectShipmentMethod(relay_shipment_methods.value[0])
    }
}
function confirmShipmentMethod(selected_data = null) {
    if (null !== selected_data) {
        selected_shipment_method.value = { ...selected_shipment_method.value, selected_data }
    }
    store.selectShipmentMethod(selected_shipment_method.value)
}

// ===
// Dynamic components
// ===
function getDeliveryComponentName(shipment_method) {
    if (isChronopostPreciseShipmentMethod(shipment_method)) {
        return ShipmentMethodChronopostPrecise
    }

    if (shipment_method.is_shipment_quotation) {
        return ShipmentMethodQuotation
    }

    return ShipmentMethodBasic
}
function getPickupName(shipment_method) {
    if (isMondialRelayShipmentMethod(shipment_method)) {
        return 'mondial-relay'
    }
    if (isChronopostPickupShipmentMethod(shipment_method)) {
        return 'chronopost'
    }
    return null
}
function getPickupComponentName(shipment_method) {
    if (getPickupName(shipment_method)) {
        return ShipmentMethodRelay
    }

    console.error('No pickup component found for the shipment method')

    return null
}
function selectSuggestion() {
    let newAddress = selected_suggestion.value.properties
    let currentAddress = store.basket_order.shipping_address

    const address = {
        city: newAddress.city.toUpperCase(),
        name: currentAddress.name,
        phone: currentAddress.phone,
        title: currentAddress.title,
        address: newAddress.name,
        lastname: currentAddress.lastname,
        cellphone: currentAddress.cellphone,
        firstname: currentAddress.firstname,
        created_at: currentAddress.created_at,
        postal_code: newAddress.postcode,
        company_name: currentAddress.company_name,
        country_code: currentAddress.country_code,
    }
    api.post(store.endpoints.update_address, {
        address,
    }).then(async () => {
        await api.put(store.endpoints.put_update_basket_order, {
            billing_address: address,
            shipping_address: address,
        })
        await store.retrieveBasketOrder()
        await fetchShippingMethods()
    })
}
</script>

<template>
    <skeleton v-if="is_loading" class="mt-3" />

    <svd-message type="error" v-else-if="shipment_methods.length === 0" class="mt-3">
        Aucun moyen de transport éligible n’a été retourné par le système.
    </svd-message>

    <div v-else data-context="shipment-methods">
        <template v-if="store_shipment_methods.length > 0">
            <shipment-method-group-title
                :id="STORE_SHIPMENT_GROUP"
                name="shipment-method-selector"
                :value="expanded_shipment_group"
                ariaLabel="Retrait en magasin"
                @toggle="openStore"
            >
                Retrait en magasin
                <template #right v-if="expanded_shipment_group !== STORE_SHIPMENT_GROUP">
                    <template v-if="cheapest_store_shipment_method.cost > 0">{{
                        cheapest_store_shipment_method.cost_formatted
                    }}</template>
                    <template v-else>
                        <span class="free">OFFERT</span>
                    </template>
                </template>
                <template #subtitle v-if="expanded_shipment_group !== STORE_SHIPMENT_GROUP">
                    <template v-if="quickest_store_shipment_method.shipping_delay !== null">
                        Dès
                        <date-formatter :date="quickest_store_shipment_method.shipping_date" format="dddd DD MMMM" />
                    </template>
                    <template v-else>{{ quickest_store_shipment_method.no_delay_fallback }}</template>
                </template>
            </shipment-method-group-title>
            <div
                v-show="expanded_shipment_group === STORE_SHIPMENT_GROUP"
                class="select-shipment-method selected"
                data-context="store-shipment-expanded"
            >
                <div class="store-name">
                    <div class="name-price">
                        <div class="name">
                            {{ default_store.title }}
                        </div>
                        <div class="price" data-context="price">
                            <template v-if="default_store.cost > 0">dès {{ default_store.cost_formatted }}</template>
                            <template v-else>
                                <span class="free">OFFERT</span>
                            </template>
                        </div>
                    </div>
                    <div class="description">
                        <template v-if="default_store.shipping_delay !== null">
                            Dès
                            <date-formatter :date="default_store.shipping_date" format="dddd DD MMMM" />
                        </template>
                        <template v-else>{{ default_store.no_delay_fallback }}</template>
                    </div>
                </div>
                <div class="data">
                    <shipment-method-data
                        :search-error="search_error"
                        :shipment-address="default_store"
                        :locations="store_shipment_methods"
                        :billing-address="store.basket_order.billing_address"
                        @confirm="confirmShipmentMethod"
                        @select="selectShipmentMethod"
                        @search="searchStore"
                    />
                </div>
            </div>
        </template>

        <template v-if="relay_shipment_methods.length > 0">
            <shipment-method-group-title
                :id="RELAY_SHIPMENT_GROUP"
                name="shipment-method-selector"
                :value="expanded_shipment_group"
                ariaLabel="Livraison en point relais"
                @toggle="toggleExpanded(RELAY_SHIPMENT_GROUP)"
            >
                Livraison en point relais
                <template #right v-if="expanded_shipment_group !== RELAY_SHIPMENT_GROUP">
                    <template v-if="cheapest_relay_shipment_method.cost > 0">
                        dès {{ cheapest_relay_shipment_method.cost_formatted }}
                    </template>
                    <template v-else>
                        <span class="free">OFFERT</span>
                    </template>
                </template>
                <template #subtitle v-if="expanded_shipment_group !== RELAY_SHIPMENT_GROUP">
                    <template v-if="quickest_relay_shipment_method.shipping_delay !== null">
                        Dès
                        <date-formatter :date="quickest_relay_shipment_method.shipping_date" format="dddd DD MMMM" />
                    </template>
                    <template v-else>
                        {{ cheapest_relay_shipment_method.no_delay_fallback }}
                    </template>
                </template>
            </shipment-method-group-title>
            <div
                v-show="expanded_shipment_group === RELAY_SHIPMENT_GROUP"
                class="select-shipment-method"
                data-context="relay-shipment-expanded"
            >
                <component
                    v-for="shipment_method in relay_shipment_methods"
                    :is="getPickupComponentName(shipment_method)"
                    :shipment-method="shipment_method"
                    :selected-shipment-method="selected_shipment_method"
                    :key="shipment_method.shipment_method_id"
                    :relay-brand="getPickupName(shipment_method)"
                    :is-selected="
                        selected_shipment_method &&
                        selected_shipment_method.shipment_method_id === shipment_method.shipment_method_id
                    "
                    :action-displayed="relay_shipment_methods.length > 1"
                    :is-disabled="is_submitting"
                    :selectedRelay="selectedRelay"
                    @select="selectRelayMethod"
                    @confirm="confirmShipmentMethod"
                    ariaLabelStart="Livraison en point relais"
                    data-context="relay-shipment-method"
                />
            </div>
        </template>

        <template v-if="home_shipment_methods.length > 0">
            <shipment-method-group-title
                :id="HOME_SHIPMENT_GROUP"
                name="shipment-method-selector"
                :value="expanded_shipment_group"
                ariaLabel="Livraison à domicile"
                @toggle="toggleExpanded(HOME_SHIPMENT_GROUP)"
            >
                Livraison à domicile
                <template #right v-if="expanded_shipment_group !== HOME_SHIPMENT_GROUP">
                    <template v-if="cheapest_home_shipment_method.is_shipment_quotation">
                        <span>sur offre</span>
                    </template>
                    <template v-else-if="cheapest_home_shipment_method.cost > 0">
                        dès {{ cheapest_home_shipment_method.cost_formatted }}
                    </template>
                    <template v-else>
                        <span class="free">OFFERT</span>
                    </template>
                </template>
                <template #subtitle v-if="expanded_shipment_group !== HOME_SHIPMENT_GROUP">
                    <template v-if="quickest_home_shipment_method.shipping_delay !== null">
                        Dès <date-formatter :date="quickest_home_shipment_method.shipping_date" format="dddd DD MMMM" />
                    </template>
                    <template v-else> {{ cheapest_home_shipment_method.no_delay_fallback }} </template>
                </template>
            </shipment-method-group-title>
            <div
                v-show="expanded_shipment_group === HOME_SHIPMENT_GROUP"
                class="select-shipment-method"
                data-context="home-shipment-expanded"
            >
                <div
                    v-if="
                        store.showAddressSuggestion &&
                        store.basket_order.shipping_address.country_code === 'FR' &&
                        !store.is_loading
                    "
                >
                    <svd-message type="warning" class="suggestion-address">
                        <div class="suggestion-message" v-if="store.addressSuggestionList.length === 0">
                            Attention, l'adresse saisie peut poser un problème lors de la livraison. Nous vous invitons
                            à
                            <svd-link
                                @click="store.editAddress({ name: 'shipping' })"
                                label="vérifier votre adresse de livraison"
                            />
                        </div>
                        <div class="suggestion-message" v-else>
                            Attention, l'adresse saisie peut poser un problème lors de la livraison. Nous avons indiqué
                            nos suggestions avec les différences soulignées en rouge. Vous pouvez en sélectionner une ou
                            <svd-link @click="store.editAddress({ name: 'shipping' })" label="modifier votre adresse" />
                        </div>
                        <div class="suggestion-message">
                            Adresse renseignée : {{ store.basket_order.shipping_address.address.split('\n')[0] }}
                            {{ store.basket_order.shipping_address.postal_code }}
                            {{ store.basket_order.shipping_address.city }}
                        </div>
                        <div class="suggestion-message" v-if="store.addressSuggestionList.length > 0">
                            <div v-for="address in store.addressSuggestionList">
                                <svd-input-radio
                                    name="suggest_radio"
                                    :id="address.properties.label"
                                    :value="selected_suggestion"
                                    :label="
                                        store.compareStrings(
                                            {
                                                address: store.basket_order.shipping_address.address.split('\n')[0],
                                                complement: store.basket_order.shipping_address.address.split('\n')[1],
                                                zip_code: store.basket_order.shipping_address.postal_code,
                                                city: store.basket_order.shipping_address.city,
                                            },
                                            address.properties
                                        )
                                    "
                                    data-context="suggest_address_radio"
                                    class="suggestion-message"
                                    @click="selected_suggestion = address"
                                />
                            </div>
                        </div>
                        <div class="action" v-if="store.addressSuggestionList.length > 0">
                            <svd-button
                                @click="selectSuggestion"
                                label="Sélectionner cette adresse"
                                data-context="select_suggest_address"
                                small
                                outline
                                light
                                :disabled="selected_suggestion === null"
                            />
                        </div>
                    </svd-message>
                </div>
                <component
                    v-for="shipment_method in home_shipment_methods"
                    :is="getDeliveryComponentName(shipment_method)"
                    :shipment-method="shipment_method"
                    :selected-shipment-method="selected_shipment_method"
                    :key="shipment_method.shipment_method_id"
                    :is-disabled="is_submitting"
                    :action-displayed="home_shipment_methods.length > 1"
                    :preview-shipping="preview_shipping"
                    ariaLabelStart="Livraison à domicile"
                    @select="selectShipmentMethod"
                    @confirm="confirmShipmentMethod"
                    data-context="home-shipment-method"
                    name="home-shipment-method"
                    @address:edit="store.editAddress({ name: 'shipping' })"
                    @contact-us:open="emit('contact-us:open')"
                />
            </div>
        </template>
    </div>
</template>

<style scoped lang="scss">
@import '../../../../../Resources/scss/style';
.select-shipment-method {
    display: flex;
    flex-direction: column;
    margin-top: $space_32;
    gap: $space_16;
    &.selected {
        border: solid 2px $color_blue_web_safe;
        border-radius: $radius_16;
    }
    @include media_min($media_md) {
        margin-left: $space_36;
    }
    .suggestion-address {
        .suggestion-message {
            color: $color_grey_typo;
            font-family: $font_open_sans;
            font-size: $font_size_15;
            font-weight: $font_normal;
            line-height: $height_24;
        }
        .action {
            width: 100%;
            @include media_min($media_md) {
                max-width: 250px;
            }
        }
    }
    .data {
        padding-right: $space_16;
        padding-left: $space_16;
        padding-bottom: $space_16;
    }
    .store-name {
        padding: $space_16;
        border-bottom: solid 1px $color_grey_default;
        .name-price {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            line-height: $height_24;
            font-size: $font_size_15;
            .name {
                color: $color_dark_default;
                font-weight: $font_semi_bold;
            }
            .price {
                font-weight: $font_bold;
            }
        }
        .description {
            line-height: $height_20;
            font-size: $font_size_13;
            font-weight: $font_normal;
            color: $color_grey_typo;
            width: 100%;
            @include media_min($media_md) {
                width: 90%;
            }
        }
    }
}
.free {
    color: $color_success;
}
</style>

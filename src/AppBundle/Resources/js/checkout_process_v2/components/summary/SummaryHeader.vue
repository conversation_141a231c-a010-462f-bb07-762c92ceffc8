<script setup>
import ChevronDownIcon from '@/shared/components/HeroIcons/Solid/ChevronDownIcon.vue'
import ChevronUpIcon from '@/shared/components/HeroIcons/Solid/ChevronUpIcon.vue'

const emit = defineEmits(['click'])

const props = defineProps({
    title: {
        type: String,
        required: true,
    },
    titleDetail: {
        type: String,
        required: false,
    },
    subtitle: {
        type: String,
        required: false,
    },
    arrowUp: {
        type: Boolean,
        default: false,
    },
    hideArrow: {
        type: Boolean,
        default: false,
    },
})

function emitClickIfHasArrow() {
    if (!props.hideArrow) {
        emit('click')
    }
}
</script>

<template>
    <div class="summary-header-container" :class="{ 'hide-arrow': hideArrow }" @click="emitClickIfHasArrow">
        <div class="summary-header" data-context="summary-header">
            <div class="title">
                <span class="main">{{ title }}</span>
                <span class="detail" v-if="titleDetail" data-context="title-detail">{{ titleDetail }}</span>
            </div>
            <div v-if="!hideArrow" class="arrow" data-context="toggle-icon">
                <chevron-up-icon v-if="arrowUp" />
                <chevron-down-icon v-else />
            </div>
        </div>
        <div class="subtitle" v-if="subtitle">{{ subtitle }}</div>
    </div>
</template>

<style scoped lang="scss">
@import '../../../../../Resources/scss/style';
.summary-header-container {
    padding: $space_32;
    cursor: pointer;
    &.hide-arrow {
        cursor: default;
    }

    .summary-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        color: $color_input_placeholder;
        .title {
            font-size: $font_size_18;
            line-height: $height_26;
            .main {
                color: $color_dark_default;
                font-weight: $font_bold;
            }

            .detail {
                color: $color_input_placeholder;
            }
        }

        .arrow {
            color: $color_grey_typo;
            width: $width_26;
            height: $height_26;
        }
    }
    .subtitle {
        font-size: $font_size_15;
        line-height: $height_24;
        color: $color_grey_typo;
    }
}
</style>

import './bloomreach-utils'
import { isProd } from '@/shared/utils/environment'

// Fonction utilitaire pour les logs Bloomreach
const bloomreachLog = (message, type = 'info') => {
    if (isProd()) return

    const colors = {
        info: '#4CAF50',
        warning: '#FF9800',
        error: '#F44336',
    }

    console.log(`%cBloomreach: ${message}`, `color: ${colors[type]}; font-weight: bold;`)
}

function visitsEnabled() {
    // const path = window.location.pathname

    // // Exclure les fiches article (ex: /article/...)
    // if (path.startsWith('/article/')) return false

    // // Exclure les rayons (ex: /rayon/...)
    // if (path.startsWith('/rayon/')) return false

    // Ajoute ici d'autres exclusions si besoin
    // if (path.startsWith('/mon-autre-page/')) return false

    return true
}

const initBloomreach = (document, exponea, script, webxpClient, window, config) => {
    // Helper functions
    function getExpirationDate(expires) {
        if (typeof expires !== 'number') return expires
        const now = new Date()
        return new Date(now.getTime() + expires * 1000)
    }

    function createMethodObject(methods) {
        return methods.reduce(
            (obj, method) => {
                obj[method] = function () {
                    obj._.push([method.toString(), arguments])
                }
                return obj
            },
            { _: [] }
        )
    }

    function createScriptElement(src, type, document) {
        const script = document.createElement(type)
        script.src = src
        const firstScript = document.getElementsByTagName(type)[0]
        firstScript.parentNode.insertBefore(script, firstScript)
        return script
    }

    function isDate(obj) {
        return Object.prototype.toString.call(obj) === '[object Date]'
    }

    // Default configuration
    const defaultConfig = {
        target: 'https://api.exponea.com',
        file_path: config.target + '/js/exponea.min.js',
        timeout: 4000,
        hideClass: 'xnpe_async_hide',
    }

    // Initialize exponea object with methods
    window[exponea] = createMethodObject([
        'anonymize',
        'initialize',
        'identify',
        'getSegments',
        'update',
        'track',
        'trackLink',
        'trackEnhancedEcommerce',
        'getHtml',
        'showHtml',
        'showBanner',
        'showWebLayer',
        'ping',
        'getAbTest',
        'loadDependency',
        'getRecommendation',
        'reloadWebLayers',
        '_preInitialize',
        '_initializeConfig',
    ])

    // Add notifications and segments methods
    window[exponea].notifications = createMethodObject(['isAvailable', 'isSubscribed', 'subscribe', 'unsubscribe'])
    window[exponea].segments = createMethodObject(['subscribe'])
    window[exponea].snippetVersion = 'v2.7.0'

    // Initialize performance tracking
    window[exponea]._performance = {
        nowFn: Date.now,
        snippetStartTime: Date.now(),
    }

    // Initialize SDK
    window[webxpClient] = {
        sdk: window[exponea],
        sdkObjectName: exponea,
        skipExperiments: !!config.new_experiments,
        sign: config.token + '/new',
        path: config.target,
    }

    // Load exponea script
    createScriptElement(config.file_path, script, document)

    // Handle experiments
    if (config.new_experiments) {
        const hideClass = config.new_experiments.hide_class || defaultConfig.hideClass
        const timeout = config.new_experiments.timeout || defaultConfig.timeout
        const url = encodeURIComponent(window.location.href.split('#')[0])

        let expirationDate
        if (config.cookies && config.cookies.expires) {
            if (typeof config.cookies.expires === 'number' || isDate(config.cookies.expires)) {
                expirationDate = getExpirationDate(config.cookies.expires)
            } else if (config.cookies.expires.tracking) {
                expirationDate = getExpirationDate(config.cookies.expires.tracking)
            }
        }

        const modificationsUrl =
            config.target +
            '/webxp/' +
            exponea +
            '/' +
            window[script].sign +
            '/modifications.min.js?http-referer=' +
            url +
            '&timeout=' +
            timeout +
            'ms' +
            (expirationDate ? '&cookie-expires=' + Math.floor(expirationDate.getTime() / 1000) : '')

        if (config.new_experiments.mode === 'sync' && window.localStorage.getItem('__exponea__sync_modifications__')) {
            // Sync mode
            document.write('<script src="' + modificationsUrl + '"></script>')
            document.write(
                '<script>!' +
                    webxpClient +
                    '.init && document.writeln(' +
                    webxpClient +
                    '.script.replace("/script/", "/script-async/").replace("><", " async><"))</script>'
            )
        } else {
            // Async mode
            document.documentElement.classList.add(hideClass)
            const scriptElement = createScriptElement(modificationsUrl, script, document)

            scriptElement.onload = scriptElement.onerror = function () {
                if (!window[webxpClient].init) {
                    createScriptElement(modificationsUrl.replace('/script/', '/script-async/'), script, document)
                }
            }

            window.setTimeout(() => {
                document.documentElement.classList.remove(hideClass)
            }, timeout)

            window[script]._revealPage = function () {
                document.documentElement.classList.remove(hideClass)
            }
        }
    }

    // Initialize configuration and start
    window[exponea]._initializeConfig(config)

    if (config.experimental?.non_personalized_weblayers) {
        window[exponea]._preInitialize(config)
    }

    window[exponea].start = function (overrides) {
        if (overrides) {
            Object.keys(overrides).forEach((key) => {
                config[key] = overrides[key]
            })
        }
        window[exponea].initialize(config)
    }
}

// Supprime le cookie pour tous les cas courants selon l'env
function deleteExponeaCookie(name) {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;`
    const domains = [
        '.son-video.com',
        'son-video.com',
        '.son-video.work',
        'son-video.work',
        '.validation.aws.son-video.work',
        'validation.aws.son-video.work',
        '.staging.aws.son-video.work',
        'staging.aws.son-video.work',
        'fo-cms.lxc',
        '.fo-cms.lxc',
    ]
    domains.forEach((domain) => {
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${domain};`
    })
}

window.SonVideo.Bloomreach.stop = function () {
    deleteExponeaCookie('__exponea_etc__')
    deleteExponeaCookie('__exponea_time2__')
    bloomreachLog('Bloomreach tracking stopped and cookies cleared', 'warning')
}

let is_bloomreach_initialized = false
window.SonVideo.Bloomreach.initAndStart = function () {
    if (!is_bloomreach_initialized) {
        initBloomreach(document, 'exponea', 'script', 'webxpClient', window, {
            target: 'https://analytics-api.son-video.com', //Custom CDN
            file_path: 'https://analytics-api.son-video.com/js/exponea.min.js',
            token: isProd()
                ? '6ae79afa-1462-11f0-b093-32bdeccede82' // Production
                : '33f60612-1462-11f0-8e3a-1a81c963d720', // Development
            experimental: {
                non_personalized_weblayers: true,
            },
            track: {
                visits: visitsEnabled(),
            },
        })
        is_bloomreach_initialized = true
    }

    window.exponea.start()
    bloomreachLog('✅ Bloomreach initialized and started', 'info')
}

if (window.SonVideo.cookies.hasConsent('bloomreach')) {
    const urlParams = new URLSearchParams(window.location.search)
    const isLogout = urlParams.get('logout') === 'true'
    if (!isLogout) {
        window.SonVideo.Bloomreach.initAndStart()
    } else {
        window.SonVideo.Bloomreach.stop()
    }
}

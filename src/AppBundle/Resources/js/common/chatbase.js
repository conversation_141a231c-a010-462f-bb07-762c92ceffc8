import { isProd } from '@/shared/utils/environment'

const CHATBOTS = {
    SAV: {
        chatbotId: 'F-CxOc5PzMHl5MD2zSolu',
        feature_flag: 'chatbot:sav',
    },
    SAV_STAGING: {
        chatbotId: '7d8KqponcmhGkRJINB46w',
        feature_flag: 'chatbot:staging',
    },
    SELLER: {
        chatbotId: 'SDixDIe11OD5rHk19lOA9',
        feature_flag: 'chatbot:seller',
    },
}

let botLoaded = false
function loadBot(chatbot) {
    if (botLoaded || !botIsAuthorized(chatbot)) {
        return
    }

    if (!isProd() && chatbot !== CHATBOTS.SELLER) {
        chatbot = CHATBOTS.SAV_STAGING
    }

    window.embeddedChatbotConfig = {
        chatbotId: chatbot.chatbotId,
        domain: 'www.chatbase.co',
    }
    let script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = 'https://www.chatbase.co/embed.min.js'
    script.setAttribute('chatbotId', chatbot.chatbotId)
    script.setAttribute('domain', 'www.chatbase.co')
    script.setAttribute('async', '')
    script.setAttribute('defer', '')
    document.head.appendChild(script)

    botLoaded = true
}

function botIsAuthorized(chatbot) {
    const features = JSON.parse(sessionStorage.getItem('features') ?? '[]')

    return features.includes(chatbot.feature_flag)
}

if (!window.location.pathname.match(/^\/(ma-commande).*/i)) {
    if (window.location.pathname.match(/^\/(mon-compte|mon-panier).*/i)) {
        loadBot(CHATBOTS.SAV_STAGING)
        loadBot(CHATBOTS.SAV)
    }
    if (!window.location.pathname.match(/^\/commander.*/i)) {
        loadBot(CHATBOTS.SELLER)
    }
}

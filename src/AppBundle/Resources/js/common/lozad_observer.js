import lozad from 'lozad'

const lozadObserver = lozad('.lozad', {
    loaded: function (el) {
        // Custom implementation on a loaded element
        el.classList.add('lozad-loaded')
    },
})
lozadObserver.observe()

const SonVideo = window.SonVideo || {}
SonVideo.lozadObserver = lozadObserver

window.SonVideo = SonVideo

const img = document.getElementById('lozad-img')

if (img && img.complete) {
    img.classList.remove('bg-svd-neutral-100')
} else if (img) {
    const onLoad = (event) => {
        img.classList.remove('bg-svd-neutral-100')
        img.removeEventListener('load', onLoad)
    }

    img.addEventListener('load', onLoad)
}

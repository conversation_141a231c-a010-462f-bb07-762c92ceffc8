import { BREAKPOINT_SM_MIN_WIDTH, BREAKPOINT_TABLET_MIN_WIDTH } from '../shared/referential/Breakpoints'
let srollPosition = 0
let direction = 'DOWN'

document.addEventListener('DOMContentLoaded', function (event) {
    // setup mobile add to basket
    window.addEventListener('scroll', add_to_basket_button)
    window.addEventListener('resize', add_to_basket_button)

    // setup tabs
    window.addEventListener('scroll', (event) => {
        // detects new state and compares it with the new one
        direction = document.body.getBoundingClientRect().top > srollPosition ? 'UP' : 'DOWN'
        srollPosition = document.body.getBoundingClientRect().top
        tabs()
    })

    window.addEventListener('resize', () => {
        if (window.matchMedia(BREAKPOINT_SM_MIN_WIDTH).matches) {
            remove_active_mobile_tabs()
        } else {
            tabs()
        }
    })
})

function remove_active_mobile_tabs() {
    const tabs_section_tabs_list = document.querySelectorAll('.tabs-section-tabs-list')[0].children
    for (let i = 0; i < tabs_section_tabs_list.length; i++) {
        if (tabs_section_tabs_list[i].classList.contains('active')) {
            tabs_section_tabs_list[i].classList.remove('active')
        }
    }

    tabs_section_tabs_list[0].classList.add('active')
}

function add_to_basket_button() {
    if (
        !window.matchMedia(BREAKPOINT_TABLET_MIN_WIDTH).matches &&
        document.getElementsByClassName('mobile-add-to-basket').length
    ) {
        const product_header_content = document.getElementsByClassName('product-header-content')[0]
        const mobile_add_to_basket = document.getElementsByClassName('mobile-add-to-basket')[0]
        const footer_payment = document.getElementsByClassName('payment-method')[0]
        const window_scrollY = window.scrollY
        const product_header_content_offsetHeight = product_header_content.offsetHeight
        const product_header_content_parentNode_offsetTop = product_header_content.parentNode.offsetTop

        if (window_scrollY > product_header_content_offsetHeight + product_header_content_parentNode_offsetTop - 44) {
            mobile_add_to_basket.classList.add('active')
            footer_payment.style.marginBottom = '63px'
        } else {
            mobile_add_to_basket.classList.remove('active')
            footer_payment.style.marginBottom = '0px'
        }
    }
}

function tabs() {
    const tabs_section_item_toarray = Array.prototype.slice.call(document.getElementsByClassName('tabs-section-item'))
    const tabs_section_item = document.getElementsByClassName('tabs-section-item')[0]
    const tabs_section_tabs_list_toarray = Array.prototype.slice.call(
        document.querySelectorAll('.tabs-section-tabs-list')[0].children
    )

    const tabs_section_item_offsetTop = tabs_section_item.offsetParent.offsetTop
    const tabs_section = document.getElementById('tabs-section').firstElementChild

    const tabs_anchor_offset_top_array = tabs_section_item_toarray.map((elem, index, array) => {
        return {
            id: elem.id,
            offset_top:
                elem && elem.id === 'ficheDescription'
                    ? elem.offsetParent.offsetTop
                    : elem.offsetTop + elem.offsetParent.offsetTop,
        }
    })
    const window_scrollY = window.scrollY
    let offset_top

    if (window.matchMedia(BREAKPOINT_TABLET_MIN_WIDTH).matches) {
        offset_top = 46
    } else if (window.matchMedia(BREAKPOINT_SM_MIN_WIDTH).matches) {
        offset_top = 90
    } else {
        offset_top = 124
    }

    if (
        window_scrollY + offset_top >= tabs_section_item_offsetTop &&
        (direction === 'DOWN' || window.matchMedia(BREAKPOINT_TABLET_MIN_WIDTH).matches)
    ) {
        tabs_section.classList.add('fixed')
    } else {
        tabs_section.classList.remove('fixed')
    }

    const filtred_tabs_anchor = tabs_anchor_offset_top_array.filter(
        (anchor) => anchor.offset_top <= window_scrollY + 78 + 45
    )

    tabs_section_tabs_list_toarray.forEach((list_elem) => {
        list_elem.classList.remove('active')
    })

    const filtred_anchor_active = tabs_section_tabs_list_toarray.filter((anchor) => {
        if (filtred_tabs_anchor[filtred_tabs_anchor.length - 1] === undefined) {
            return anchor.firstElementChild.hash === '#ficheDescription'
        } else {
            return anchor.firstElementChild.hash === '#' + filtred_tabs_anchor[filtred_tabs_anchor.length - 1].id
        }
    })

    filtred_anchor_active[0].classList.add('active')
    $(filtred_anchor_active[0]).parents('.tabs-section-tabs-list').scrollLeft(filtred_anchor_active[0].offsetLeft)
}

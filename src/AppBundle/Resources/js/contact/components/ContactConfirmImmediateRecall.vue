<template>
    <!-- v5 -->
    <div
        class="flex flex-col items-center text-center text-md leading-6 gap-4"
        data-context="contact-confirm-immediate-recall"
        v-if="'v5' === version"
    >
        <div class="rounded-full overflow-hidden w-32 h-32 my-2">
            <img
                :src="'/images/ui/peoples/conseiller-03.png' | asset('static_images')"
                alt="Illustration conseiller de vente"
            />
        </div>

        <p class="text-lg">Merci,</p>
        <p class="text-center">
            Votre demande a bien été prise en compte. Veuillez patienter, un conseiller va vous rappeler dans quelques
            secondes
        </p>

        <tw-button class="mt-4 w-full" @click="$emit('close')">Fermer cette fenêtre</tw-button>
    </div>
    <!-- v3 -->
    <div data-context="contact-confirm-immediate-recall" v-else>
        <div class="img-container-border">
            <img
                :src="'/images/ui/peoples/conseiller-03.png' | asset('static_images')"
                class="img-responsive"
                alt="content"
            />
        </div>
        <p class="text-center text-lg mb-1">Merci,</p>
        <p class="text-center">
            Votre demande a bien été prise en compte. Veuillez patienter, un conseiller va vous rappeler dans quelques
            secondes
        </p>
        <button @click.prevent="$emit('close')" class="btn btn-primary btn-block mb-2">Fermer cette fenêtre</button>
    </div>
</template>

<script>
import TwButton from '@/shared/components/TwButton.vue'
import { PROPS_DEFINITION } from '@/contact/components/shared'

export default {
    name: 'ContactConfirmImmediateRecall',
    components: { TwButton },
    emits: ['close'],
    inject: ['version'],
    props: {
        ...PROPS_DEFINITION,
    },
}
</script>

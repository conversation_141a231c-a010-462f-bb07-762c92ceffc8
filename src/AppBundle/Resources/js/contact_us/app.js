import Vue from 'vue'
import ContactUs from './components/ContactUs'
import { trans } from '@/shared/filters'

Vue.filter('trans', trans)

new Vue({
    name: 'ContactUsApp',
    el: '#app_contactus',
    // Give those options to all descendants who may inject them
    // Useful instead of using a store in more simple case
    // @see https://v3.vuejs.org/guide/component-provide-inject.html
    provide: {
        form_data: window.SonVideo.contact_us_form_data,
    },
    components: { ContactUs },
})

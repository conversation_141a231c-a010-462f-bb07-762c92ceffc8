<template>
    <div>
        <div class="grid_container_12 font-opensans mb-0 pb-2">
            <div class="grid_row form-item">
                <div class="col_12_col mb-0">
                    <label for="brand">{{ 'brand' | trans({}, 'event') }}</label>
                </div>
                <div class="col_12_col mb-3">
                    <p v-if="brand_list_loading" class="text-lightgrey">
                        <em>{{ 'message.loading' | trans }}</em>
                    </p>
                    <select v-else="" @change="onBrandSelected" name="brand" id="brand" :value="brand">
                        <option :value="null">{{ 'pick_a_brand' | trans({}, 'event') }}…</option>
                        <option v-for="brand in brand_list" :value="brand.brand_id">{{ brand.name }}</option>
                    </select>
                </div>
            </div>
            <div class="grid_row form-item" v-if="brand != null">
                <div class="col_12_col mb-0">
                    <label for="brand">{{ 'category' | trans({}, 'event') }}</label>
                </div>
                <div class="col_12_col mb-3">
                    <p v-if="category_list_loading" class="text-lightgrey">
                        <em>{{ 'message.loading' | trans }}</em>
                    </p>
                    <select
                        v-else-if="brand"
                        @change="onCategorySelected"
                        name="category"
                        id="category"
                        :value="category"
                    >
                        <option :value="null">{{ 'pick_a_category' | trans({}, 'event') }}…</option>
                        <option v-for="category in category_list" :value="category.category_id">
                            {{ category.name }}
                        </option>
                    </select>
                </div>
            </div>
            <div class="grid_row form-item" v-if="category != null">
                <div class="col_12_col mb-0">
                    <label for="brand">{{ 'model' | trans({}, 'event') }}</label>
                </div>
                <div class="col_12_col mb-3">
                    <p v-if="article_list_loading" class="text-lightgrey">
                        <em>{{ 'message.loading' | trans }}</em>
                    </p>
                    <select
                        v-else-if="category"
                        @change="onArticleSelected"
                        name="article"
                        id="article"
                        :value="article"
                    >
                        <option :value="null">{{ 'pick_a_model' | trans({}, 'event') }}…</option>
                        <option v-for="article in article_list" :value="article.article_id">{{ article.name }}</option>
                    </select>
                </div>
            </div>

            <div v-if="!is_free_choice && article">
                <p class="clearfix mt-0 mb-3">
                    <a
                        aria-label="Augmenter la quantité"
                        href=""
                        @click.prevent="selectQuantity()"
                        class="btn btn-purple btn-outline btn-round"
                        >{{ 'add' | trans }}</a
                    >
                </p>
            </div>

            <div class="hr mb-3"><hr /></div>

            <p class="mb-2">
                <a aria-label="Un équipement n'est pas dans la liste" href="" @click.prevent="setFreeChoice(true)">{{
                    'one_of_your_equipments_not_in_the_list' | trans({}, 'event')
                }}</a>
            </p>

            <div v-if="is_free_choice">
                <div class="grid_row form-item">
                    <div class="col_12_col mb-0">
                        <p v-html="free_input_label" class="mb-2"></p>
                    </div>
                    <div class="col_12_col mb-3">
                        <input type="text" v-model="free_input" />
                        <a
                            href=""
                            @click.prevent="selectQuantity()"
                            class="btn btn-purple btn-outline btn-round mt-3"
                            v-if="free_input"
                            >{{ 'add' | trans }}</a
                        >
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import api from '../../../shared/api/handler'
import { trans } from '../../../shared/filters'

export default {
    data() {
        return {
            brand_list: [],
            category_list: [],
            article_list: [],
            brand_list_loading: false,
            category_list_loading: false,
            article_list_loading: false,
            brand: null,
            category: null,
            article: null,
            is_free_choice: false,
            free_input: '',
            free_input_label: trans('free_input_label', {}, 'event'),
        }
    },
    created() {
        // init brand list
        this.brand_list_loading = true
        api.get('/api/marques')
            .then((response) => {
                this.brand_list = response.data.brands
                this.brand_list_loading = false
            })
            .catch(() => {
                this.brand_list_loading = false
            })
    },
    methods: {
        onBrandSelected(e) {
            this.setFreeChoice(false)
            this.brand = parseInt(e.target.value)
            this.category = null
            this.article = null

            // load category list
            if (e.target.value) {
                this.category_list_loading = true
                api.get('/api/categories', { brand_id: e.target.value })
                    .then((response) => {
                        this.category_list = response.data.categories
                        this.category_list_loading = false
                    })
                    .catch(() => {
                        this.category_list_loading = false
                    })
            }
        },
        onCategorySelected(e) {
            this.setFreeChoice(false)
            this.category = parseInt(e.target.value)
            this.article = null

            // load category list
            if (e.target.value) {
                this.article_list_loading = true
                api.get('/api/articles', { brand_id: this.brand, category_id: e.target.value })
                    .then((response) => {
                        this.article_list = response.data.articles
                        this.article_list_loading = false
                    })
                    .catch(() => {
                        this.article_list_loading = false
                    })
            }
        },
        onArticleSelected(e) {
            this.article = parseInt(e.target.value)
        },
        setFreeChoice(value) {
            this.is_free_choice = value

            if (value) {
                this.brand = null
                this.category = null
                this.article = null
            }
        },
        selectQuantity() {
            this.submitChoice(1)
        },
        submitChoice(quantity) {
            // compute equipment
            const selected_brand = this.brand_list.find((brand) => brand.brand_id === this.brand)
            const selected_category = this.category_list.find((category) => category.category_id === this.category)
            const selected_article = this.article_list.find((article) => article.article_id === this.article)
            const equipment = this.is_free_choice
                ? {
                      quantity,
                      article_id: null,
                      name: this.free_input,
                      category: null,
                      category_url: null,
                  }
                : {
                      quantity,
                      article_id: this.article,
                      name: `${selected_brand.name} ${selected_article.name}`,
                      category: selected_category.name,
                      category_url: `/categorie/${selected_category.slug}`,
                  }

            // emit it
            this.$emit('equipment-selected', equipment)

            // clear selections
            this.brand = null
            this.category = null
            this.article = null
            this.free_input = null
            this.is_free_choice = false
        },
    },
}
</script>

<template>
    <div>
        <div class="installationChoix mb-5">
            <div class="tag-list-wrapper tag-list-inline text-center">
                <ul class="tag-list tag-list-lg text-center">
                    <li v-for="choice in choices">
                        <span
                            class="btn btn-default btn-lg"
                            :class="{ selected: choice === selected }"
                            @click="select(choice)"
                        >
                            {{ `${choice}_setups` | trans({}, 'event') }}
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        choices: Array,
        selected: String,
    },
    methods: {
        select(choice) {
            this.$emit('choice-selected', choice)
        },
    },
}
</script>

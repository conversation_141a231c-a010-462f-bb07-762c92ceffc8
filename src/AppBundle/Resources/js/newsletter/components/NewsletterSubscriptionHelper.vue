<script setup>
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'

const props = defineProps({
    errorMessage: {
        type: String,
        required: false,
    },
    showSuccess: {
        type: Boolean,
        required: true,
    },
})
</script>

<template>
    <section>
        <div class="helpers" v-if="showSuccess || errorMessage">
            <div v-if="errorMessage" class="text text-danger">
                <svd-icon alt="Erreur" icon="xmark-circle" color="ff3838" :size="20" />
                {{ errorMessage }}
            </div>
            <div v-else class="text text-success">
                <svd-icon alt="Succès" icon="check-circle" color="419c09" :size="20" />
                Vous êtes maintenant abonné à la newsletter.
            </div>
        </div>
    </section>
</template>

<style scoped lang="scss">
@import '../../../../Resources/scss/style';
.helpers {
    font-size: $font_size_13;
    text-align: left;
    line-height: $height_20;
    .text {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        align-content: center;
        gap: $space_10;
        animation: $fade_in;
        &.text-success {
            color: $color_success_bg_black;
        }
        &.text-danger {
            color: $color_error_bg_black;
        }
    }
}
</style>

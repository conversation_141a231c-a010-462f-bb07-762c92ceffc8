<script setup>
import { useBasketStore } from '@/order/basket/store'
import { computed } from 'vue'
import CataMag from '@/order/basket/components/CataMag.vue'

const store = useBasketStore()

const checked = computed(() => {
    return store.catalog_in_basket_info.has_catalog
})

function toggleCatalog() {
    if (checked.value) {
        store.removeCatalog()
    } else {
        store.addCatalog()
    }
}
</script>

<template>
    <cata-mag
        data-context="basket-catalog"
        :name="store.available_catalog.name"
        :description="store.available_catalog.short_description"
        :thumbnail="store.available_catalog.thumbnail"
        :checked="checked"
        @toggle="toggleCatalog"
    />
</template>

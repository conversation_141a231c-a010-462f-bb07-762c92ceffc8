<script setup>
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'
import { useBasketStore } from '@/order/basket/store'
import { formatPrice } from '@/shared/filters'
const store = useBasketStore()
</script>

<template>
    <section>
        <div
            v-for="promotion in store.global_promotions"
            :key="promotion.code"
            class="basket-item option"
            data-context="global-promo-code"
        >
            <div class="data-prices-quantity">
                <div class="data">
                    <div class="picture small">
                        <svd-icon icon="promotion" :size="50" keepColor />
                    </div>
                    <div class="infos">
                        <div class="basket-item-title">Code promotionnel</div>
                        <div class="other" data-context="promo-label">
                            {{ store.promoLabel(promotion) }}
                        </div>
                    </div>
                </div>
                <div class="prices-quantity">
                    <div class="prices" data-context="promo-price">
                        <div v-if="promotion.price === 0" class="final free">Offert</div>
                        <div v-else class="final">{{ formatPrice(promotion.price) }}</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<style scoped lang="scss"></style>

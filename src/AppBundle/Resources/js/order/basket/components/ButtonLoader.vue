<template>
    <span class="container-button-text-loader">
        <span :class="{ 'hidden-opacity': is_loading }">
            {{ button_text }}
        </span>
        <span class="loader-button" v-if="is_loading">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
        </span>
    </span>
</template>

<script>
export default {
    name: 'ButtonLoader',
    props: {
        is_loading: {
            type: Boolean,
            required: true,
        },
        button_text: {
            type: String,
            required: true,
        },
    },
}
</script>

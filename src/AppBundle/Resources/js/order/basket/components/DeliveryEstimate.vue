<script setup>
import Boxes from '@/order/basket/components/Boxes.vue'
import SvdInputSelect from '@/v5/components/input/SvdInputSelect.vue'
import SvdInputText from '@/v5/components/input/SvdInputText.vue'
import SvdButton from '@/v5/components/button/SvdButton.vue'
import { computed, ref, watch } from 'vue'
import { formatPrice } from '@/shared/filters'
import { useBasketStore } from '@/order/basket/store'
import { useCountries } from '@/v5/composable/useCountries'
import api from '@/shared/api/handler'
import SvdLink from '@/v5/components/link/SvdLink.vue'

const { countries, first_country_entry, countries_best_sales, countries_dom, other_countries, isMetropolitanFrance } =
    useCountries()
const store = useBasketStore()

const zip_code = ref('')
const country_id = ref('FR')
const show_result = ref(false)
const deliveries = ref([])
const is_loading = ref(false)

const selected_country = computed(() => countries.value.find((c) => c.country_id === country_id.value))
const cant_estimate = computed(() => zip_code.value === '' || country_id.value === '')

async function estimate() {
    if (cant_estimate.value) {
        return
    }

    is_loading.value = true
    try {
        const response = await api.post('/mon-panier/estimer', {
            country_id: country_id.value,
            zip_code: zip_code.value,
        })
        const first_store_shipment = response.shipment_methods.find((sm) => sm.is_store)
        let shipment_methods = response.shipment_methods.filter((sm) => !sm.is_store)
        if (isMetropolitanFrance(country_id.value) && first_store_shipment) {
            shipment_methods.unshift({ ...first_store_shipment, title: `Retrait en magasin` })
        }
        deliveries.value = shipment_methods.sort((a, b) => a.cost - b.cost)
        show_result.value = true
    } catch (e) {
        // TODO: afficher une erreur?
    } finally {
        is_loading.value = false
    }
}

if (store.is_authenticated) {
    prefillFormWithValidAddress()
}
async function prefillFormWithValidAddress() {
    try {
        const response = await api.get('/mon-compte/mes-adresses')

        const first_valid = response.addresses.find((address) => address.is_valid)
        if (first_valid) {
            country_id.value = first_valid.country_code
            zip_code.value = first_valid.postal_code
        }
    } catch (e) {
        // Silently fail in case of error
    }
}

watch(
    () => store.loading,
    (new_value, old_value) => {
        if (old_value && !new_value && show_result.value) {
            estimate()
        }
    }
)
</script>

<template>
    <boxes title="Estimer ma livraison" data-context="delivery-estimate" collapsed>
        <div v-if="!show_result" class="delivery" data-context="form">
            <svd-input-select name="country" label="Pays" v-model="country_id" required>
                <option
                    v-for="country in first_country_entry"
                    :key="`first_${country.country_id}`"
                    :value="country.country_id"
                >
                    {{ country.name }}
                </option>
                <option
                    v-for="country in countries_best_sales"
                    :key="`best_${country.country_id}`"
                    :value="country.country_id"
                >
                    {{ country.name }}
                </option>
                <optgroup label="DOM-TOM">
                    <option
                        v-for="country in countries_dom"
                        :key="`dom_tom_${country.country_id}`"
                        :value="country.country_id"
                    >
                        {{ country.name }}
                    </option>
                </optgroup>
                <option
                    v-for="country in other_countries"
                    :key="`other_${country.country_id}`"
                    :value="country.country_id"
                >
                    {{ country.name }}
                </option>
            </svd-input-select>
            <svd-input-text name="zip_code" label="Code postal" required v-model="zip_code" @enter="estimate" />
            <svd-button
                label="Estimer ma livraison"
                outline
                light
                :disabled="cant_estimate"
                :loading="is_loading"
                @click="estimate"
                data-context="submit-btn"
            />
        </div>
        <div v-else class="delivery" data-context="estimation-result">
            <div class="user-info">
                <div>
                    Pays : <span class="bold">{{ selected_country.name }}</span>
                </div>
                <div>
                    Code postal : <span class="bold">{{ zip_code }}</span>
                </div>
                <svd-link
                    href="#"
                    @click="show_result = false"
                    data-context="modify-btn"
                    label="Modifier"
                    hyper-discreet
                />
            </div>
            <div class="divider" />
            <div class="result">
                <template v-if="is_loading">
                    <div class="price" v-for="i in Math.max(deliveries.length, 3)">
                        <div class="loader-line"></div>
                        <div class="loader-line"></div>
                    </div>
                </template>
                <div v-else class="price" v-for="delivery in deliveries">
                    <div>{{ delivery.title }}</div>
                    <div v-if="delivery.is_shipment_quotation" class="value">sur offre</div>
                    <div v-else-if="delivery.cost === 0" class="free">OFFERT</div>
                    <div v-else class="value">{{ formatPrice(delivery.cost) }}</div>
                </div>
            </div>
        </div>
    </boxes>
</template>

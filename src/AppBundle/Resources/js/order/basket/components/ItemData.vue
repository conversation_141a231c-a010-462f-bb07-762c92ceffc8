<script setup>
import { asset, formatPrice, preset, presets } from '@/shared/filters'
import SvdInputSelect from '@/v5/components/input/SvdInputSelect.vue'
import SvdInputText from '@/v5/components/input/SvdInputText.vue'
import { useBasketStore } from '@/order/basket/store'
import { computed, ref } from 'vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'
import { ARTICLE_NO_IMAGE, PRESET_100, PRESET_55 } from '@/shared/referential/images'
import { BREAKPOINT_MD_MIN_WIDTH } from '@/shared/referential/Breakpoints'
import ArticleAvailability from '@/v5/components/article/ArticleAvailability.vue'
import SvdLink from '@/v5/components/link/SvdLink.vue'

const store = useBasketStore()

const props = defineProps({
    basketLine: {
        type: Object,
        required: true,
    },
    isQuotation: {
        type: <PERSON>olean,
        default: false,
    },
})
const emits = defineEmits(['quantity:update', 'delete'])

const quantity = ref(props.basketLine.quantity)
const max_quantity = computed(() => props.basketLine.quantity_limit)

const quantities = [
    { value: 1, label: '1' },
    { value: 2, label: '2' },
    { value: 3, label: '3' },
    { value: 4, label: '4' },
    { value: 5, label: '5' },
    { value: 6, label: '6' },
    { value: 7, label: '7' },
    { value: 8, label: '8' },
    { value: 9, label: '9' },
    { value: 10, label: '10+' },
]
function removeItem() {
    emits('delete')
}
function forceQuantity(value) {
    if (value > 0) {
        value = Number.parseInt(value)
        if (value > max_quantity.value) {
            value = max_quantity.value
        }
        const must_emit_update = quantity.value !== value
        quantity.value = value

        if (must_emit_update) {
            emits('quantity:update', quantity.value)
        }
        return
    }
    emits('delete')
}

function percentage(partial, total) {
    return Math.round(100 - (100 * partial) / total)
}

function openWarrantyDetails() {
    store.openWarrantyDetails(props.basketLine)
}

function openReferenceSlider(event) {
    window.SonVideo?.reference_price?.open(event)
}

const warranty_label = computed(() => {
    let years = null
    if (props.basketLine.svd_warranty_yr === 1 || props.basketLine.manufacturer_warranty_yr === 1) {
        years = `2 ans`
    } else if (props.basketLine.svd_warranty_yr) {
        years = `${props.basketLine.svd_warranty_yr} ans`
    } else if (props.basketLine.manufacturer_warranty_yr >= 99) {
        years = 'à vie'
    } else if (props.basketLine.manufacturer_warranty_yr) {
        years = `${props.basketLine.manufacturer_warranty_yr} ans`
    }

    return years ? `Garantie initiale : ${years}` : null
})

const is_basketable = computed(() => !props.basketLine?.unbasketable_reason)
const total_unit_price = computed(() => props.basketLine.unit_selling_price - props.basketLine.unit_discount_amount)
const title = computed(() => props.basketLine.title)
</script>

<template>
    <div class="item-data">
        <div class="data-prices-quantity">
            <div class="data">
                <a class="picture" :href="basketLine.article_url ?? '#'">
                    <img
                        :src="preset(asset(basketLine.image_url ?? ARTICLE_NO_IMAGE, 'article_images'), PRESET_100)"
                        :srcset="
                            presets(asset(basketLine.image_url ?? ARTICLE_NO_IMAGE, 'article_images'), [
                                [PRESET_55, '55w'],
                                [PRESET_100, '100w'],
                            ])
                        "
                        :sizes="`${BREAKPOINT_MD_MIN_WIDTH} 100px, 55px`"
                        :alt="title"
                    />
                </a>
                <div class="infos">
                    <a data-context="item-title" class="basket-item-title" :href="basketLine.article_url ?? '#'">
                        {{ title }}
                    </a>
                    <article-availability :article="basketLine" />

                    <div v-if="warranty_label" class="other" data-context="item-warranty">{{ warranty_label }}</div>
                    <div v-if="basketLine.promo_offers" class="promo-offers other">
                        <div v-for="offer in basketLine.promo_offers" data-context="promo-offer" class="promo-item">
                            {{ offer }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="prices-quantity">
                <div v-if="is_basketable" class="prices">
                    <div class="promo" v-if="basketLine.unit_discount_amount && !basketLine.reference_price && !basketLine.packaged_articles">
                        <div class="percent" data-context="item-percent">
                            - {{ percentage(total_unit_price, basketLine.unit_selling_price) }} %
                        </div>
                        <div class="old" data-context="item-old">
                            {{ formatPrice(basketLine.unit_selling_price) }}
                        </div>
                    </div>
                    <div
                        data-context="reference-price"
                        class="promo"
                        v-if="basketLine.reference_price"
                        @click.prevent="openReferenceSlider"
                    >
                        <div class="percent" data-context="item-percent">
                            - {{ Math.round(basketLine.price_discount_percent) }} %
                        </div>
                        <div class="old" data-context="item-reference-price">
                            {{ formatPrice(basketLine.reference_price) }}
                            <svd-icon :size="13" icon="question-circle" color="999999" />
                        </div>
                    </div>
                    <div class="final" data-context="item-total">
                        {{ formatPrice(total_unit_price) }}
                    </div>
                </div>
                <div class="quantity">
                    <div v-if="is_basketable" class="select">
                        <svd-input-select
                            v-if="quantity < 10"
                            name=""
                            label=""
                            :value="quantity"
                            :options="quantities"
                            :disabled="isQuotation || 1 === max_quantity"
                            data-context="quantity-select"
                            @input="forceQuantity"
                        />
                        <svd-input-text
                            v-else
                            name=""
                            type="number"
                            :max="max_quantity"
                            :value="quantity"
                            :disabled="isQuotation || 1 === max_quantity"
                            data-context="quantity-text"
                            @change="forceQuantity"
                            @blur="forceQuantity"
                            @enter="forceQuantity"
                        />
                    </div>
                    <svd-link
                        v-if="!isQuotation"
                        href="#"
                        @click="removeItem"
                        data-context="delete-btn"
                        label="Supprimer"
                        hyper-discreet
                    />
                </div>
            </div>
        </div>
        <div
            class="package"
            v-if="basketLine.packaged_articles && basketLine.packaged_articles.length > 1"
            data-context="package"
        >
            <div class="separator" />
            <span class="title">Contenu de ce pack</span>
            <div class="items">
                <div
                    class="item"
                    v-for="packaged_article in basketLine.packaged_articles"
                    data-context="packaged-article"
                >
                    <div class="picture">
                        <img
                            :src="
                                preset(
                                    asset(packaged_article.image_url ?? ARTICLE_NO_IMAGE, 'article_images'),
                                    PRESET_100
                                )
                            "
                            :srcset="
                                presets(asset(packaged_article.image_url ?? ARTICLE_NO_IMAGE, 'article_images'), [
                                    [PRESET_55, '55w'],
                                    [PRESET_100, '100w'],
                                ])
                            "
                            :sizes="`${BREAKPOINT_MD_MIN_WIDTH} 100px, 55px`"
                            :alt="packaged_article.short_description"
                        />
                    </div>
                    <div class="infos">
                        <div class="basket-item-title">
                            {{ packaged_article.short_description }} ×{{ packaged_article.quantity }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="isQuotation && basketLine.selected_warranties.length > 0" class="item-extension">
            <div
                v-for="warranty in basketLine.selected_warranties"
                class="extension-content"
                data-context="quotation-extension"
            >
                <div class="explain">
                    <div class="picture">
                        <svd-icon icon="premium-check" :size="50" keep-color />
                    </div>
                    <div class="infos">
                        <div class="premium-title">
                            {{ warranty.label }}
                        </div>
                        <div class="other">
                            Remboursé à 100% en cas de panne. <span @click="openWarrantyDetails">En savoir plus</span>
                        </div>
                    </div>
                </div>
                <div class="actions">
                    <div class="price">
                        <div>
                            {{ formatPrice(warranty.unit_selling_price_tax_included) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

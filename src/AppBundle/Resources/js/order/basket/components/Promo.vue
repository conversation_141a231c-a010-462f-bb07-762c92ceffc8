<script setup>
import Boxes from '@/order/basket/components/Boxes.vue'
import SvdInputText from '@/v5/components/input/SvdInputText.vue'
import SvdButton from '@/v5/components/button/SvdButton.vue'
import { useBasketStore } from '@/order/basket/store'
import { ref } from 'vue'

const store = useBasketStore()
const promoCode = ref(store.promo_code)

function setPromoCode() {
    store.submitPromotionalCode(promoCode.value)
}
function removePromoCode() {
    store.removePromotionalCode(promoCode.value)
}
</script>

<template>
    <boxes title="Un code promo ?" data-context="promo" :collapsed="!store.promo_code">
        <div class="promotion">
            <div v-if="store.promo_code" class="code" data-context="promo-applied">
                <div>{{ promoCode.toUpperCase() }}</div>
                <svd-button label="Retirer" outline light data-context="remove-promo-code" @click="removePromoCode" />
            </div>
            <template v-else>
                <svd-input-text name="promo_code" placeholder="Code promo" v-model="promoCode" @enter="setPromoCode" />
                <svd-button
                    class="btn-section"
                    data-context="set-promo-code"
                    label="Valider"
                    outline
                    light
                    :disabled="promoCode === ''"
                    @click="setPromoCode"
                />
            </template>
        </div>
        <div v-if="store.promotion_message?.error" class="error" data-context="promo-error">
            {{ store.promotion_message.error }}
        </div>
    </boxes>
</template>

<script setup>
import ItemData from '@/order/basket/components/ItemData.vue'
import { useBasketStore } from '@/order/basket/store'
import SvdLink from '@/v5/components/link/SvdLink.vue'

const store = useBasketStore()

const props = defineProps({
    quote: {
        type: Object,
        required: true,
    },
})

function formatItemFromQuoteLineData(data) {
    const short_description = data.product.editorial_content
        ? data.product.editorial_content.short_description
        : data.product.short_description

    return {
        image_url: data.product.image,
        editorial_content: {
            short_description,
        },
        estimated_delivery_time: data.product.estimated_delivery_time,
        quantity: data.quantity,
        quantity_limit: data.quantity_limit,
        article_url: data.product.article_url,
        unit_selling_price: data.selling_price_tax_included,
        unit_discount_amount: data.unit_discount_amount_abs_tax_included,
        selected_warranties: data.selected_warranties,
        svd_warranty_yr: data.product.svd_warranty_yr,
        manufacturer_warranty_yr: data.product.manufacturer_warranty_yr,
        packaged_articles: data.product.packaged_articles,
        unbasketable_reason: data.product.unbasketable_reason,
        title: data.product.short_description,
    }
}

function removeQuote() {
    store.removeQuote(props.quote)
}
</script>

<template>
    <div class="basket-item" data-context="quote-line">
        <div class="quotation-number">
            <div data-context="quotation-title">Offre n°{{ quote.content.id }}</div>
            <svd-link href="#" @click="removeQuote" data-context="quotation-delete" label="Supprimer" hyper-discreet />
        </div>
        <div class="quotation-items" data-context="quotation-products">
            <item-data
                v-for="quote_line in quote.quote_lines"
                v-if="quote_line.type === 'product'"
                :key="`quote_${quote_line.type}_${quote_line.display_order}`"
                :basket-line="formatItemFromQuoteLineData(quote_line.data)"
                is-quotation
                class="quotation-item"
            />
        </div>
    </div>
</template>


import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import api from '@/shared/api/handler'
import { formatPriceSvd } from '@/shared/filters'

export const useBasketStore = defineStore('basket', () => {
    const is_authenticated = ref(false)
    const previous_cart_item_ids = ref([])
    const accessories = ref([])
    const quotations = ref([])
    const articles = ref([])
    const quotes = ref([])
    const magazine_in_basket_info = ref(null)
    const catalog_in_basket_info = ref(null)
    const available_magazine = ref(null)
    const available_catalog = ref(null)
    const promotion_message = ref(null)
    const open_warranty = ref(null)
    const promotions = ref(null)
    const nb_success_to_clean = ref(0)
    const summary = ref({ empty: true })
    const loading = ref(true)

    const initializeData = (data) => {
        updateData(data)
    }

    const setLoading = (value) => {
        loading.value = value
    }

    const submitPromotionalCode = (code) => {
        code = code.trim()
        if (code === '') {
            alterPromotionMessage({ error: 'Aucun code promotionnel saisi' })
            return
        }
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_add_promo, { code })
            .then((data) => {
                updateDataAndCartSummary(data)

                alterPromotionMessage({ success: 'Promotion ajoutée avec succès' })
                incrementNbSuccessToClean()
                setTimeout(() => cleanPromoSuccessMessage(), 5000)
            })
            .catch((error) => {
                setLoading(false)
                alterPromotionMessage({ error: error.data.message })
            })
    }

    const removePromotionalCode = (promotion) => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_remove_promo, {
            provider_type: promotion.provider_type,
        })
            .then((response) => {
                updateDataAndCartSummary(response)
            })
            .catch((error) => {
                setLoading(false)
                alterPromotionMessage({ error: error.data.message })
            })
    }

    const removeArticle = (article) => {
        SonVideo.pushAnalytics('removeFromCart', {
            products: [
                {
                    name: `${article.brand_name} ${article.article_name}`,
                    title: `${article.article_name}`,
                    id: article.article_id,
                    price: article.selling_price,
                    brand: article.brand_name,
                    category: article.category_name,
                    quantity: article.quantity,
                },
            ],
        })
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_remove_article, { article_id: article.article_id }).then(
            (response) => {
                updateDataAndCartSummary(response)
            }
        )
    }

    const updateArticleQuantity = ({ article, quantity }) => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_update_article_quantity, {
            article_id: article.article_id,
            quantity,
        }).then((response) => {
            updateDataAndCartSummary(response)
        })
    }

    const saveCartToSelection = (name) => {
        return api
            .post(window.SonVideo.basket_data.api.save_cart_selection, { selection_name: name })
            .catch((response) => Promise.reject(response.data.message))
    }

    const removeQuotation = (quotation) => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_remove_quotation, { quotation_id: quotation.quote_id }).then(
            (response) => {
                updateDataAndCartSummary(response)
            }
        )
    }

    const updateQuotationQuantity = ({ quotation, quantity }) => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_update_quotation_quantity, {
            quotation_id: quotation.quotation_id,
            quantity,
        }).then((response) => {
            updateDataAndCartSummary(response)
        })
    }

    const removeQuote = (quote) => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_remove_quote, { quote_id: quote.quote_id }).then((response) => {
            updateDataAndCartSummary(response)
        })
    }

    const addWarranty = ({ article, warranty }) => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_add_warranty, {
            article_id: article.article_id,
            warranty: warranty.tag_path,
        }).then((response) => {
            updateDataAndCartSummary(response)
        })
    }

    const removeWarranty = ({ article, warranty }) => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_remove_warranty, {
            article_id: article.article_id,
            warranty: warranty.tag_path,
        }).then((response) => {
            updateDataAndCartSummary(response)
        })
    }

    const openWarrantyDetails = (article) => {
        open_warranty.value = article
    }
    const closeWarrantyDetails = () => {
        open_warranty.value = null
    }

    const updateAccessoryQuantity = ({ accessory, quantity }) => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_update_accessory_quantity, {
            accessory_id: accessory.accessory_id,
            quantity,
        }).then((response) => {
            updateDataAndCartSummary(response)
        })
    }

    const removeAccessory = (accessory) => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_remove_accessory, { accessory_id: accessory.accessory_id }).then(
            (response) => {
                updateDataAndCartSummary(response)
            }
        )
    }

    const addCatalog = () => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_add_catalog).then((response) => {
            updateDataAndCartSummary(response)
        })
    }
    const addMagazine = () => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_add_magazine).then((response) => {
            updateDataAndCartSummary(response)
        })
    }
    const removeCatalog = () => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_remove_catalog).then((response) => {
            updateDataAndCartSummary(response)
        })
    }
    const removeMagazine = () => {
        setLoading(true)
        api.post(window.SonVideo.basket_data.api.cart_remove_magazine).then((response) => {
            updateDataAndCartSummary(response)
        })
    }

    const cleanPromoSuccessMessage = () => {
        if (nb_success_to_clean.value === 1 && promotion_message.value.success) {
            alterPromotionMessage()
        }
        decrementNbSuccessToClean()
    }
    const updateDataAndCartSummary = (data = {}) => {
        updateData(data)

        if (data.summary) {
            const SonVideo = window.SonVideo || {}
            if (SonVideo.header) {
                SonVideo.header.updateSummary(data.summary)
            }
        }
    }

    const updateData = (data) => {
        if (data) {
            if (data.hasOwnProperty('is_authenticated')) {
                is_authenticated.value = data.is_authenticated
            }
            if (data.hasOwnProperty('summary')) {
                summary.value = data.summary
            }
            if (data.hasOwnProperty('promotions')) {
                promotions.value = data.promotions
            }
            if (data.hasOwnProperty('articles')) {
                // display "fake" discount on packaged articles
                articles.value = data.articles.map((article) => {
                    let discount = 0
                    if (
                        article.instead_of_price &&
                        article.instead_of_price - article.selling_price > article.instead_of_price * 0.05
                    ) {
                        discount = article.instead_of_price - article.selling_price
                    }

                    return {
                        ...article,
                        unit_selling_price: article.selling_price + discount,
                        unit_discount_amount: discount,
                    }
                })
            }
            if (data.hasOwnProperty('quotations')) {
                quotations.value = data.quotations
            }
            if (data.hasOwnProperty('quotes')) {
                quotes.value = data.quotes
            }
            if (data.hasOwnProperty('accessories')) {
                accessories.value = data.accessories
            }
            if (data.hasOwnProperty('available_catalog')) {
                available_catalog.value = data.available_catalog
            }
            if (data.hasOwnProperty('catalog_in_basket_info')) {
                catalog_in_basket_info.value = data.catalog_in_basket_info
            }
            if (data.hasOwnProperty('available_magazine')) {
                available_magazine.value = data.available_magazine
            }
            if (data.hasOwnProperty('magazine_in_basket_info')) {
                magazine_in_basket_info.value = data.magazine_in_basket_info
            }
            if (data.hasOwnProperty('previous_cart_item_ids')) {
                previous_cart_item_ids.value = data.previous_cart_item_ids
            }
        }
        setLoading(false)
    }

    const alterPromotionMessage = (message = {}) => {
        promotion_message.value = message
    }

    const incrementNbSuccessToClean = () => {
        nb_success_to_clean.value++
    }

    const decrementNbSuccessToClean = () => {
        nb_success_to_clean.value = Math.max(nb_success_to_clean.value - 1, 0)
    }

    const promo_code_discount = computed(() => {
        if (promotions.value) {
            return promotions.value.reduce((sum, promo) => sum + promo.price, 0)
        }

        return 0
    })
    const quotes_discount = computed(() => {
        if (quotes.value) {
            return quotes.value.reduce((sum, quote) => sum - 1 * quote.quote_prices.total_discount_tax_included, 0)
        }

        return 0
    })
    const gift_article = computed(() => {
        const gift_promotion = promotions.value.find((promotion) => 'gift' === promotion.type)

        if (undefined === gift_promotion || !gift_promotion.article_name) {
            return null
        }

        return {
            quantity: gift_promotion.article_quantity,
            name: gift_promotion.article_name,
            selling_price: gift_promotion.selling_price,
        }
    })
    const totalDiscount = computed(() => {
        return (
            promo_code_discount.value +
            quotes_discount.value +
            -1 * (gift_article.value?.selling_price ?? 0)
        )
    })
    const subtotal = computed(() => {
        return (summary.value?.prices?.raw_amount_selling_price ?? 0) + -1 * totalDiscount.value
    })

    const pushDataLayer = () => {
        let items = articles.value.map((item) => {
            return {
                item_name: item.brand_name + ' ' + item.article_name,
                item_category: item.category_name,
                item_brand: item.brand_name,
                item_id: item.article_id.toString(),
                price: parseFloat(item.selling_price),
                quantity: parseInt(item.quantity),
            }
        })

        if (quotations.value) {
            quotations.value.forEach((quotation) => {
                items = items.concat(
                    quotation.quotation_lines.map((item) => {
                        return {
                            item_name: item.brand_name + ' ' + item.article_name,
                            item_category: item.category_name,
                            item_brand: item.brand_name,
                            item_id: item.article_id.toString(),
                            price: parseFloat(item.selling_price),
                            quantity: parseInt(item.quantity),
                        }
                    })
                )
            })
        }

        if (quotes.value) {
            quotes.value.forEach((quote) => {
                items = items.concat(
                    quote.quote_lines
                        .filter((quote_line) => quote_line.data.product !== undefined)
                        .map((quote_line) => {
                            return {
                                item_name: quote_line.data.product.brand_name + ' ' + quote_line.data.product.name,
                                item_category: quote_line.data.product.category_name,
                                item_brand: quote_line.data.product.brand_name,
                                item_id: quote_line.data.product.product_id.toString(),
                                price: parseFloat(quote_line.data.total_price),
                                quantity: quote_line.data.quantity,
                            }
                        })
                )
            })
        }

        dataLayer.push({ ecommerce: null })
        dataLayer.push({
            event: 'begin_checkout',
            ecommerce: {
                currency: 'EUR',
                value:
                    summary.value.prices?.raw_amount_selling_price ??
                    window.SonVideo?.basket_data.summary?.prices?.raw_amount_selling_price ??
                    0,
                items: items,
            },
        })
        window.location.href = '/ma-commande/creation'
    }

    //
    // Promotions handling
    //
    const promo_code = computed(() => promotions.value.reduce((promo_code, p) => p.code, null))
    const article_promotions = computed(() => promotions.value.filter((promo) => promo.type === 'variable_discount'))
    const global_promotions = computed(() =>
        promotions.value.filter((promo) => ['gift', 'fixed_discount'].includes(promo.type))
    )

    const promoLabel = (promotion) => {
        if (promotion.type === 'fixed_discount') {
            return `${formatPriceSvd(Math.abs(promotion.price))} de remise avec le code ${promotion.code.toUpperCase()}`
        }
        if (promotion.type === 'variable_discount') {
            return `${promotion.name} de remise avec le code ${promotion.code.toUpperCase()}`
        }
        if (promotion.type === 'gift') {
            let priceLabel = `pour ${formatPriceSvd(promotion.price)}`
            if (promotion.price === 0) {
                priceLabel = 'offert'
            }

            return `${promotion.article_name} ${priceLabel} au lieu de ${formatPriceSvd(
                promotion.selling_price
            )} avec le code ${promotion.code.toUpperCase()}`
        }
    }

    return {
        is_authenticated,
        previous_cart_item_ids,
        accessories,
        quotations,
        articles,
        quotes,
        magazine_in_basket_info,
        catalog_in_basket_info,
        available_magazine,
        available_catalog,
        promotion_message,
        promo_code,
        article_promotions,
        global_promotions,
        nb_success_to_clean,
        summary,
        open_warranty,
        loading,
        subtotal,
        totalDiscount,
        initializeData,
        submitPromotionalCode,
        removePromotionalCode,
        removeArticle,
        updateArticleQuantity,
        cleanPromoSuccessMessage,
        saveCartToSelection,
        removeQuotation,
        updateQuotationQuantity,
        removeQuote,
        addWarranty,
        removeWarranty,
        updateAccessoryQuantity,
        removeAccessory,
        addCatalog,
        addMagazine,
        removeCatalog,
        removeMagazine,
        openWarrantyDetails,
        closeWarrantyDetails,
        pushDataLayer,
        promoLabel,
    }
})

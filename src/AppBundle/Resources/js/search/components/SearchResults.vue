<template>
    <div class="w-100">
        <div v-if="displaySortingOptions != 'false'" id="SVDv3_CmsRayon_sortingOptions">
            <div class="grid_container_12">
                <div class="grid_row">
                    <div id="SVDv3_CmsRayon_orderBy" class="col_6_col">
                        {{ 'order_by' | trans({}, 'search') }}
                        <select
                            id="items_sorting_options"
                            name="items_sorting_options"
                            :value="selected_sorting_option"
                            @change="changeSelectedSort($event.target.value)"
                        >
                            <option v-for="(option, index) in sorting_options" :value="index">
                                {{ option.label | trans({}, 'search') }}
                            </option>
                        </select>
                    </div>
                    <div id="SVDv3_CmsRayon_displayType" class="col_6_col col_last text-right hidden-sm hidden-xs">
                        <a
                            class="btn btn-sm"
                            href=""
                            @click.prevent="changeDisplay('lines')"
                            :class="[display === 'lines' ? 'btn-primary' : 'btn-default']"
                        >
                            <i class="icon icon-bars">
                                <span class="sr-only">{{ 'display_line' | trans({}, 'common') }}</span>
                            </i>
                        </a>
                        <a
                            class="btn btn-sm"
                            href=""
                            @click.prevent="changeDisplay('grid')"
                            :class="[display === 'grid' ? 'btn-primary' : 'btn-default']"
                        >
                            <i class="icon icon-th">
                                <span class="sr-only">{{ 'display_grid' | trans({}, 'common') }}</span>
                            </i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- End sorting options -->
        <div class="nothing" v-show="fully_computed_articles.length === 0">
            {{ 'no_article_match_your_request' | trans({}, 'search') }}
        </div>

        <!-- Lines template -->
        <div
            id="search-results-products"
            class="SVDv3_rayon_listingProduits SVDv3_article_element"
            v-if="display === 'lines'"
        >
            <div
                v-for="article in articles_lines"
                class="clearfix SVDv3_rayon_listingProduits_liste_ligne animated"
                :key="`list-${article.article_id}`"
                data-context="search-article"
                :data-brand="article.brand_name"
                :data-category="article.category_name"
                :data-id="article.article_id"
                :data-price="getArticleSellingPrice(article)"
                :data-name="article.common_content_name"
            >
                <div class="SVDv3_rayon_listingProduits_photo">
                    <p>
                        <a
                            :aria-label="article.editorial_content.basket_description"
                            :href="article.article_url"
                            v-on:click="pushClickProductAnalytics(article)"
                        >
                            <img
                                class="lozad"
                                :alt="getArticleName(article)"
                                :src="emptyDataImage(95, 95)"
                                :data-src="
                                    preset(asset(article.media_uri ?? ARTICLE_NO_IMAGE, 'article_images'), PRESET_140)
                                "
                                :data-srcset="
                                    presets(asset(article.media_uri ?? ARTICLE_NO_IMAGE, 'article_images'), [
                                        [PRESET_95, '95w'],
                                        [PRESET_140, '140w'],
                                    ])
                                "
                                :sizes="`${BREAKPOINT_DESKTOP_MIN_WIDTH} 140px, 95px`"
                            />
                        </a>
                    </p>
                </div>
                <div class="SVDv3_rayon_listingProduits_description">
                    <p>
                        <a
                            :aria-label="articleLinkAriaLabel(article)"
                            :href="article.article_url"
                            v-on:click="pushClickProductAnalytics(article)"
                        >
                            <strong>{{ getArticleName(article) }}</strong>
                        </a>
                        <br />
                        <template v-if="hasScore(article)">
                            <span class="score-item">
                                <span class="score-item-row">
                                    <span class="score-item-stars">
                                        <span class="product-score"
                                            ><span
                                                class="score-stars"
                                                :style="{ width: 15 * article.score + 'px' }"
                                            ></span
                                        ></span>
                                    </span>
                                    <span class="score-item-text"
                                        ><strong>{{ article.score | comaDelimiter }}</strong></span
                                    >
                                </span>
                            </span>
                            <br />
                        </template>
                        <template v-if="hasPromoCode(article)">
                            <strong data-context="promo-code">{{ getPromoCode(article) }}</strong>
                            <br />
                        </template>
                        <span v-if="article.cc_short_description" v-html="article.cc_short_description"></span>
                        <span
                            v-else-if="article.editorial_content && article.editorial_content.basket_description"
                            v-html="article.editorial_content.basket_description"
                        ></span>
                        <template v-if="hasLength(article)">
                            <br />
                            {{ 'longueur' | transChoice({ count: article.article_lengths.length }) }}&nbsp;:&nbsp;{{
                                formatLength(article)
                            }}
                        </template>
                        <template v-if="hasColor(article)">
                            <br />
                            {{ 'couleur' | transChoice({ count: article.color_labels.length }) }} :
                            {{ formatColor(article) }}
                        </template>
                        <template v-if="show_ranking && article.ranking">
                            <br />
                            Pertinence : {{ article.ranking }}
                        </template>
                    </p>
                </div>
                <div class="SVDv3_rayon_listingProduits_prix">
                    <div
                        v-if="
                            article.display_mode != display_mode.confidential_price &&
                            (article.hasOwnProperty('unbasketable_reason') === false ||
                                article.unbasketable_reason === null)
                        "
                    >
                        
                        <div v-if="article.reference_price" class="reference-price" data-context="reference-price">
                            <span class="percent" data-context="rounded-percentage">
                                <span class="back">-&nbsp;{{ Math.round(article.price_discount_percent) }}&nbsp;%</span>
                            </span>
                        </div>
                        <div
                            v-if="article.reference_price"
                            class="reference-price old"
                            data-context="reference-price-crossed"
                            @click="displayReferencePriceSlider"
                        >
                            <div class="crossed">
                                <span class="line-through">{{ article.reference_price | formatPriceSvd }}</span>
                                <span class="confirm">?</span>
                            </div>
                        </div>
                        <p class="SVDv3_zonePrix_prix">{{ getArticleSellingPrice(article, true) }}</p>
                        <p
                            class="SVDv3_zonePrix_second_life"
                            v-if="
                                article.second_life && !(article.parent_destock_price && get_price_new(article) !== 0)
                            "
                        >
                            <span v-if="article.second_life.count > 1">
                                +&nbsp;{{
                                    article.second_life.count
                                }}&nbsp;offres&nbsp;seconde<br />vie&nbsp;dès&nbsp;{{
                                    article.second_life.minimum_price | formatPriceSvd
                                }}
                            </span>
                            <span v-else>
                                +&nbsp;{{ article.second_life.count }}&nbsp;offre&nbsp;seconde<br />vie&nbsp;à&nbsp;{{
                                    article.second_life.minimum_price | formatPriceSvd
                                }}
                            </span>
                        </p>
                        <p
                            class="SVDv3_zonePrix_ald"
                            v-if="article.parent_destock_price && get_price_new(article) !== 0"
                        >
                            <template v-if="article.destock">
                                <span data-context="destock-grade">{{
                                    translateDestockState(article.destock.state)
                                }}</span
                                ><br />
                            </template>
                            <span data-context="destock-old-price">
                                Prix neuf :
                                {{ get_price_new(article) | formatPriceSvd }}
                            </span>
                        </p>
                        <p
                            v-if="
                                getHighlightMediaForType(article, 'small') !== '' &&
                                getHighlightClassForType(article, 'small') !== 'promotion' &&
                                getHighlightClassForType(article, 'small') !== 'on_offer'
                            "
                            data-context="highlight"
                        >
                            <span
                                class="product-highlight label"
                                :class="'label-' + getHighlightClassForType(article, 'small')"
                                >{{
                                    ('article.highlight.' + getHighlightClassForType(article, 'small'))
                                        | trans({}, 'search')
                                }}</span
                            >
                        </p>
                    </div>
                    <p
                        class="SVDv3_rayon_prixConfidentiel"
                        v-else-if="article.display_mode == display_mode.confidential_price"
                    >
                        <span>{{ 'Prix confidentiel' | trans({}) }}</span>
                    </p>
                </div>
                <div class="SVDv3_rayon_listingProduits_dispo" v-html="render_availability(article)"></div>
                <div class="SVDv3_rayon_listingProduits_action">
                    <a
                        class="SVDv3_bouton SVDv3_bouton_gris SVDv3_bouton_largeurFixe SVDv3_bouton_largeurFixe_105px"
                        :href="article.article_url"
                        v-on:click="pushClickProductAnalytics(article)"
                    >
                        <span>{{ 'product_sheet' | trans({}, 'common') }}</span>
                    </a>
                    <a
                        href=""
                        class="SVDv3_bouton_ajoutPanier SVDv3_bouton_largeurFixe"
                        v-if="canAddToCart(article)"
                        :data-value="article.article_id"
                        :data-label="article.editorial_content.short_description"
                        :data-complementary-url="article.complementary_articles_url"
                        v-on:click="pushClickAddToCartAnalytics(article)"
                    >
                        <span>{{ 'add_to_basket' | trans({}, 'common') }}</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Grid template -->
        <div class="SVDv3_article_element" v-if="display === 'grid'" v-for="(grid, num_grid) of articles_grid">
            <div v-if="groupBy !== '' && group_by_items.length > 1" :id="'DOM' + num_grid">
                <div class="tabs-section">
                    <ul class="tabs-section-tabs-list clearfix">
                        <li
                            v-for="(item, num_item) of group_by_items"
                            :class="{ 'ui-tabs-selected': item == grid[0][0][groupBy] }"
                            class="tabs-section-tabs-item"
                        >
                            <h2 class="SVDv3_titres_noStyle">
                                <a :aria-label="`Lien vers ${item}`" :href="'#DOM' + num_item">{{ item }}</a>
                            </h2>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="SVDv3_rayon_listingProduits_grille">
                <div class="SVDv3_rayon_listingProduits_grille_ligne">
                    <div
                        class="clearfix SVDv3_rayon_listingProduits_grille_cellule"
                        v-for="article of articles_lines"
                        :key="`grid-${article.article_id}`"
                        data-context="search-article"
                        :data-brand="article.brand_name"
                        :data-category="article.category_name"
                        :data-id="article.article_id"
                        :data-price="getArticleSellingPrice(article)"
                        :data-name="article.common_content_name"
                    >
                        <div class="SVDv3_rayon_produit_content">
                            <div class="SVDv3_rayon_listingProduits_photo">
                                <p>
                                    <a
                                        :aria-label="article.editorial_content.basket_description"
                                        :href="article.article_url"
                                        v-on:click="pushClickProductAnalytics(article)"
                                    >
                                        <img
                                            class="lozad"
                                            :alt="getArticleName(article)"
                                            :src="emptyDataImage(150, 150)"
                                            :data-src="
                                                preset(
                                                    asset(article.media_uri ?? ARTICLE_NO_IMAGE, 'article_images'),
                                                    PRESET_140
                                                )
                                            "
                                            :data-srcset="
                                                presets(
                                                    asset(article.media_uri ?? ARTICLE_NO_IMAGE, 'article_images'),
                                                    [
                                                        [PRESET_95, '95w'],
                                                        [PRESET_140, '140w'],
                                                    ]
                                                )
                                            "
                                            :sizes="`${BREAKPOINT_TABLET_MIN_WIDTH} 140px, 95px`"
                                        />
                                    </a>
                                </p>
                            </div>
                            <p class="SVDv3_rayon_listingProduits_description">
                                <a
                                    :aria-label="`vendu ${getArticleSellingPrice(article, true)},
                                        ${render_availability_v5(article)}`"
                                    :href="article.article_url"
                                    v-on:click="pushClickProductAnalytics(article)"
                                >
                                    <strong>{{ getArticleName(article) }}</strong>
                                </a>
                                <br />
                                <template v-if="hasScore(article)">
                                    <span class="score-item">
                                        <span class="score-item-row">
                                            <span class="score-item-stars">
                                                <span class="product-score"
                                                    ><span
                                                        class="score-stars"
                                                        :style="{ width: 15 * article.score + 'px' }"
                                                    ></span
                                                ></span>
                                            </span>
                                            <span class="score-item-text"
                                                ><strong>{{ article.score | comaDelimiter }}</strong></span
                                            >
                                        </span>
                                    </span>
                                    <br />
                                </template>
                                <template v-if="hasPromoCode(article)">
                                    <strong data-context="promo-code">{{ getPromoCode(article) }}</strong>
                                    <br />
                                </template>
                                <span v-if="article.cc_short_description" v-html="article.cc_short_description"></span>
                                <span
                                    v-else-if="
                                        article.editorial_content && article.editorial_content.basket_description
                                    "
                                    v-html="article.editorial_content.basket_description"
                                ></span>
                                <template v-if="hasLength(article)">
                                    <br />
                                    {{
                                        'longueur' | transChoice({ count: article.article_lengths.length })
                                    }}&nbsp;:&nbsp;{{ formatLength(article) }}
                                </template>
                                <template v-if="hasColor(article)">
                                    <br />
                                    {{ 'couleur' | transChoice({ count: article.color_labels.length }) }} :
                                    {{ formatColor(article) }}
                                </template>
                                <template v-if="show_ranking && article.ranking">
                                    <br />
                                    Pertinence : {{ article.ranking }}
                                </template>
                            </p>
    
                            <div class="SVDv3_rayon_listingProduits_prix">
                                <div
                                    v-if="
                                        article.display_mode != display_mode.confidential_price &&
                                        (article.hasOwnProperty('unbasketable_reason') === false ||
                                            article.unbasketable_reason === null)
                                    "
                                >
                                    <div
                                        v-if="article.reference_price"
                                        class="reference-price"
                                        data-context="reference-price"
                                    >
                                        <span class="percent" data-context="rounded-percentage">
                                            <span class="back"
                                                >-&nbsp;{{ Math.round(article.price_discount_percent) }}&nbsp;%</span
                                            >
                                        </span>
                                    </div>
                                    <div
                                        v-if="article.reference_price"
                                        class="reference-price old"
                                        data-context="reference-price-crossed"
                                        @click="displayReferencePriceSlider"
                                    >
                                        <div class="crossed">
                                            <span class="line-through">{{
                                                article.reference_price | formatPriceSvd
                                            }}</span>
                                            <span class="confirm">?</span>
                                        </div>
                                    </div>
                                    <p class="SVDv3_zonePrix_prix">
                                        {{ getArticleSellingPrice(article, true) }}
                                    </p>
                                    <p
                                        class="SVDv3_zonePrix_second_life"
                                        v-if="
                                            article.second_life &&
                                            !(article.parent_destock_price && get_price_new(article) !== 0)
                                        "
                                    >
                                        <span v-if="article.second_life.count > 1">
                                            +&nbsp;{{
                                                article.second_life.count
                                            }}&nbsp;offres&nbsp;seconde<br />vie&nbsp;dès&nbsp;{{
                                                article.second_life.minimum_price | formatPriceSvd
                                            }}
                                        </span>
                                        <span v-else>
                                            +&nbsp;{{
                                                article.second_life.count
                                            }}&nbsp;offre&nbsp;seconde<br />vie&nbsp;à&nbsp;{{
                                                article.second_life.minimum_price | formatPriceSvd
                                            }}
                                        </span>
                                    </p>
                                    <p
                                        class="SVDv3_zonePrix_ald"
                                        v-if="article.parent_destock_price && get_price_new(article) !== 0"
                                    >
                                        <template v-if="article.destock">
                                            <span data-context="destock-grade">{{
                                                translateDestockState(article.destock.state)
                                            }}</span
                                            ><br />
                                        </template>
                                        <span data-context="destock-old-price">
                                            Prix neuf :
                                            {{ get_price_new(article) | formatPriceSvd }}
                                        </span>
                                    </p>
                                    <p
                                        v-if="
                                            getHighlightMediaForType(article, 'small') !== '' &&
                                            getHighlightClassForType(article, 'small') !== 'promotion' &&
                                            getHighlightClassForType(article, 'small') !== 'on_offer'
                                        "
                                        data-context="highlight"
                                    >
                                        <span
                                            class="product-highlight label"
                                            :class="'label-' + getHighlightClassForType(article, 'small')"
                                            >{{
                                                ('article.highlight.' + getHighlightClassForType(article, 'small'))
                                                    | trans({}, 'search')
                                            }}</span
                                        >
                                    </p>
                                </div>
                                <p
                                    class="SVDv3_rayon_prixConfidentiel"
                                    v-else-if="article.display_mode == display_mode.confidential_price"
                                >
                                    <span>{{ 'Prix confidentiel' | trans({}) }}</span>
                                </p>
                            </div>

                            <div class="SVDv3_rayon_listingProduits_dispo">
                                <div
                                    v-if="
                                        article.hasOwnProperty('unbasketable_reason') === false ||
                                        article.unbasketable_reason === null
                                    "
                                    v-html="render_availability(article)"
                                ></div>
                                <div
                                    class="mceContentBody"
                                    v-else-if="
                                        article.hasOwnProperty('unbasketable_reason') &&
                                        article.unbasketable_reason !== null
                                    "
                                >
                                    <p class="margin-md">
                                        <span class="label label-danger label-md">{{ 'Discontinued' | trans }}</span>
                                    </p>
                                </div>
                            </div>
                            <div class="SVDv3_rayon_listingProduits_action">
                                <a
                                    class="SVDv3_bouton SVDv3_bouton_gris SVDv3_bouton_largeurFixe SVDv3_bouton_largeurFixe_105px"
                                    v-if="!canAddToCart(article)"
                                    :href="article.article_url"
                                    v-on:click="pushClickProductAnalytics(article)"
                                >
                                    <span>{{ 'product_sheet' | trans({}, 'common') }}</span>
                                </a>
                                <a
                                    href=""
                                    class="SVDv3_bouton_ajoutPanier SVDv3_bouton_largeurFixe"
                                    v-else
                                    :data-value="article.article_id"
                                    :data-label="article.editorial_content.short_description"
                                    :data-complementary-url="article.complementary_articles_url"
                                    v-on:click="pushClickAddToCartAnalytics(article)"
                                >
                                    <span>{{ 'add_to_basket' | trans({}, 'common') }}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End article list -->

        <article-paginator
            v-if="shouldShowPagination"
            :current-page="paginationData.currentPage"
            :total-items="paginationData.totalItems"
            :items-per-page="paginationData.itemsPerPage"
            scroll-target="#search-results-products"
            @page-changed="handlePageChange"
        />
    </div>
</template>

<script>
import Vue from 'vue'
import { mapGetters, mapState, mapActions } from 'vuex'
import {
    trans,
    transChoice,
    formatPriceSvd,
    render_availability,
    asset,
    comaDelimiter,
    render_availability_v5,
    preset,
    presets,
} from '@/shared/filters'
import { getHighlightMediaForType, getHighlightClassForType, emptyDataImage } from '@/shared/article/functions'
import lazyLoadImagesMixin from '../../shared/mixins/lazyLoadImagesMixin'
import { getLocaleStorage } from '@/shared/functions'
import { ARTICLE_NO_IMAGE, PRESET_140, PRESET_95 } from '@/shared/referential/images'
import { BREAKPOINT_DESKTOP_MIN_WIDTH, BREAKPOINT_TABLET_MIN_WIDTH } from '@/shared/referential/Breakpoints'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'
import ArticlePaginator from '../../article_lister/components/ArticlePaginator.vue'

Vue.filter('transChoice', transChoice)
Vue.filter('comaDelimiter', comaDelimiter)

const LOCATION = ['destock', 'resultats de recherche', 'promos', 'soldes', 'rayon niveau 2']

export default {
    mixins: [lazyLoadImagesMixin],
    components: { SvdIcon, ArticlePaginator },
    props: {
        displaySortingOptions: {
            type: String,
            default: 'true',
        },
        // Possible values are lines and grid
        defaultDisplay: {
            type: String,
            default: 'lines',
        },
        // By default there is no group by (empty), else you can choose a key element in the article object
        // to group articles by this key (for example "main_category_name" in some shop)
        groupBy: {
            type: String,
            default: '',
        },
        gridItemPerLine: {
            type: Number,
            default: 3,
        },
    },
    data() {
        return {
            limit: 60,
            show_ranking: getLocaleStorage('god_mode', false),
        }
    },
    computed: {
        ARTICLE_NO_IMAGE: () => ARTICLE_NO_IMAGE,
        PRESET_95: () => PRESET_95,
        PRESET_140: () => PRESET_140,
        BREAKPOINT_TABLET_MIN_WIDTH: () => BREAKPOINT_TABLET_MIN_WIDTH,
        BREAKPOINT_DESKTOP_MIN_WIDTH: () => BREAKPOINT_DESKTOP_MIN_WIDTH,
        ...mapState([
            'offset',
            'display',
            'display_mode',
            'show_all_data',
            'sorting_options',
            'selected_sorting_option',
            'search_terms',
        ]),
        ...mapGetters(['fully_computed_articles']),
        /**
         * Result set used when displaying items as lines of products
         *
         * @returns {Array}
         */
        articles_lines() {
            // Convert to pagination: show only articles for current page
            const startIndex = (this.offset - 1) * this.limit
            const endIndex = this.offset * this.limit
            return this.fully_computed_articles.slice(startIndex, endIndex)
        },
        group_by_items() {
            if (this.groupBy === '') {
                return []
            }

            let articles = this.fully_computed_articles.slice()
            let indexes = []

            for (let key in articles) {
                if (indexes.indexOf(articles[key][this.groupBy]) === -1) {
                    indexes.push(articles[key][this.groupBy])
                }
            }

            return indexes
        },
        /**
         * Result set used when displaying items as a grid of 3 products per lines
         *
         * @returns {Array}
         */
        articles_grid() {
            let grids = []

            let articles_group_by = this.getArticlesGroupBy(this.fully_computed_articles)

            for (let index in articles_group_by) {
                let articles = articles_group_by[index]
                let lines = []
                let line = []
                for (let key in articles) {
                    line.push(articles[key])

                    if (line.length % parseInt(this.gridItemPerLine) === 0) {
                        lines.push(line)
                        line = []
                    }
                }

                if (line.length > 0) {
                    lines.push(line)
                }

                grids.push(lines)
            }

            return grids
        },
        /**
         * Pagination data for ArticlePaginator
         *
         * @returns {Object}
         */
        paginationData() {
            return {
                currentPage: this.offset,
                totalItems: this.fully_computed_articles.length,
                itemsPerPage: this.limit
            }
        },
        /**
         * Whether to show pagination (only when not grouping and has multiple pages)
         *
         * @returns {boolean}
         */
        shouldShowPagination() {
            return this.groupBy === '' && Math.ceil(this.fully_computed_articles.length / this.limit) > 1
        },
    },
    methods: {
        presets,
        preset,
        render_availability_v5,
        formatPriceSvd,
        ...mapActions(['showMore', 'updatePage', 'changeSelectedSort', 'changeDisplay', 'initializeDefaultDisplay']),
        trans,
        asset,
        getHighlightMediaForType,
        getHighlightClassForType,
        emptyDataImage,
        render_availability,
        getArticleSellingPrice(article, format = false) {
            if (article.selling_price) {
                if (format) {
                    return formatPriceSvd(article.selling_price)
                }
                return article.selling_price
            }
            return 'Prix confidentiel'
        },
        articleLinkAriaLabel(article) {
            return `${article.formatted_name}, ${this.getArticleSellingPrice(article)}, ${render_availability_v5(
                article
            )}`
        },
        getArticlesGroupBy(articles) {
            let articles_group_by = {}

            if (this.groupBy !== '') {
                for (let key in articles) {
                    let index_key = articles[key][this.groupBy]
                    if (typeof articles_group_by[index_key] === 'undefined') {
                        articles_group_by[index_key] = []
                    }

                    articles_group_by[index_key].push(articles[key])
                }
            } else {
                // With no index, we use limit and offset
                articles_group_by['no_index'] = articles.slice(0, this.limit * this.offset)
            }

            return articles_group_by
        },
        get_price_new(article) {
            if (
                typeof article.parent_destock_price !== 'undefined' &&
                article.parent_destock_price !== null &&
                typeof article.parent_destock_price.selling_price !== 'undefined' &&
                article.parent_destock_price.selling_price !== null
            ) {
                return article.parent_destock_price.selling_price
            }

            return 0
        },
        hasScore(article) {
            return article.hasOwnProperty('score') && article.score > 0
        },
        hasLength(article) {
            return (
                article.hasOwnProperty('article_lengths') &&
                article.article_lengths !== null &&
                article.article_lengths.length > 0
            )
        },
        hasMoreThanOne(article) {
            return this.hasLength(article) && article.article_lengths !== null && article.article_lengths.length > 1
        },
        /**
         * Check if article has property 'color_labels' filled with color(s).
         */
        hasColor(article) {
            return (
                article.hasOwnProperty('color_labels') &&
                article.color_labels !== null &&
                article.color_labels.length > 0
            )
        },
        priceIsRelevant(selling_price, price) {
            return price - selling_price > (selling_price * 5) / 100
        },
        isBundle(article) {
            // Is a bundle if it contains strictly more than 1 article (bundle with 1 article are fake bundle)
            return (
                typeof article.packaged_articles !== 'undefined' &&
                article.packaged_articles !== null &&
                article.packaged_articles.length > 1
            )
        },
        formatLength(article) {
            return article.article_lengths.map((length) => length.replace(/\.00/g, '')).join(' / ')
        },
        /**
         * Format text to display available colors on article line from property 'color_labels'.
         */
        formatColor(article) {
            return article.color_labels
                .slice()
                .sort((a, b) => a.localeCompare(b))
                .join(' / ')
        },
        /**
         * Displayed name differ based on show_all_data parameter and the fact that the article is a pack.
         *
         * @param article
         * @returns {*}
         */
        getArticleName(article) {
            return article.editorial_content.short_description
        },
        isDestock(article) {
            return article.parent_destock_price
        },
        canAddToCart(article) {
            return (
                article.display_mode !== this.display_mode.confidential_price &&
                (article.display_mode !== this.display_mode.store_exclusivity || this.isDestock(article)) &&
                (!article.hasOwnProperty('unbasketable_reason') || article.unbasketable_reason === null)
            )
        },
        hasPromoCode(article) {
            return (
                article.hasOwnProperty('promo_codes_description') &&
                article.promo_codes_description !== null &&
                article.promo_codes_description.length > 0
            )
        },
        getPromoCode(article) {
            return article.promo_codes_description[0]
        },
        handlePageChange(page) {
            this.updatePage(page)
        },
        pushClickProductAnalytics(article) {
            if (article.document_id && this.search_terms) {
                window.SonVideo.tracker.push({
                    name: 'search-result-click',
                    payload: { document_id: article.document_id, search_terms: this.search_terms },
                })
            }
            SonVideo.pushAnalytics('clickProduct', {
                actionField: { list: document.querySelector('#search_app').dataset.location },
                products: [
                    {
                        title: `${article.name}`,
                        name: `${article.brand_name} ${article.name}`,
                        id: article.article_id,
                        price: article.selling_price,
                        brand: article.brand_name,
                        category: article.category_name,
                    },
                ],
            })
        },
        pushClickAddToCartAnalytics(article) {
            SonVideo.pushAnalytics('addToCart', {
                products: [
                    {
                        title: `${article.name}`,
                        name: `${article.brand_name} ${article.name}`,
                        id: article.article_id,
                        price: article.selling_price,
                        brand: article.brand_name,
                        category: article.category_name,
                        quantity: 1,
                    },
                ],
            })
        },
        displayReferencePriceSlider(event) {
            return window.SonVideo.reference_price.open(event)
        },
        translateDestockState(state) {
            const DESTOCK_STATE_TRANSLATIONS = {
                GREAT: 'Très bon état',
                GOOD: 'Bon état',
                ACCEPTABLE: 'État acceptable',
            }

            return DESTOCK_STATE_TRANSLATIONS[state]
        },
    },
    created() {
        const data_location = document.querySelector('#search_app').dataset.location
        const matches = LOCATION.filter((elem) => elem.includes(data_location))
        this.initializeDefaultDisplay(this.defaultDisplay)
        // Set the display if not already set through the store initialization
        if (!this.display) {
            this.changeDisplay(this.defaultDisplay)
        }
        let push_articles = []
        this.fully_computed_articles.forEach(function (article) {
            push_articles.push({
                title: `${article.name}`,
                name: `${article.brand_name} ${article.name}`,
                id: article.article_id,
                price: article.selling_price,
                brand: article.brand_name,
                category: article.category_name,
                list: data_location,
            })
        })

        if (push_articles.length > 0 && !matches) {
            SonVideo.pushAnalytics('productView', push_articles)
        }
    },
}
</script>

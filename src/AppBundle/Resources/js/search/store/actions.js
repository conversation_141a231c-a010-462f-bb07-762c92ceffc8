import * as types from './mutation-types'

export default {
    initializeData({ commit }, search_data) {
        commit(types.INITIALIZE, search_data)
    },
    initializeDefaultDisplay({ commit }, default_display) {
        commit(types.INITIALIZE_DEFAULT_DISPLAY, default_display)
    },
    updatePriceRange({ commit }, boundaries) {
        commit(types.UPDATE_PRICE_RANGE, {
            min: boundaries.min,
            max: boundaries.max,
            from: boundaries.from,
            to: boundaries.to,
        })
    },
    showMore({ commit, state }) {
        commit(types.UPDATE_OFFSET, state.offset + 1)
    },
    updatePage({ commit }, page) {
        commit(types.UPDATE_OFFSET, page)
    },
    registerFilter({ commit }, [filter_name, label, search_key]) {
        commit(types.REGISTER_FILTER, { filter_name, label, search_key })
    },
    toggleFilter({ commit }, [filter_name, value]) {
        commit(types.TOGGLE_FILTER, { filter_name, value })
    },
    resetOneFilter({ commit }, filter_name) {
        commit(types.RESET_ONE_FILTER, filter_name)
    },
    resetAllFilters({ commit }) {
        commit(types.RESET_ALL_FILTERS)
    },
    changeSelectedSort({ commit }, selected_index) {
        commit(types.UPDATE_SELECTED_SORTING_OPTION, Number.parseInt(selected_index))
    },
    changeDisplay({ commit }, display_type) {
        commit(types.UPDATE_DISPLAY, display_type)
    },
    restoreParamsFromUrl({ commit }) {
        commit(types.UPDATE_FROM_URL_PARAMS)
    },
}

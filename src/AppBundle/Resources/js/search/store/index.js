import Vue from 'vue'
import Vuex from 'vuex'
import * as types from './mutation-types'
import actions from './actions'
import { applyFilter, extractPrices, numericRangeBoundaries } from '../helpers'
import { extractUrlParams, generateSortingFromDefinition } from '../../shared/functions'
import {
    calculateHighlightsOnArticle,
    formatLengthOnArticle,
    formatColorOnArticle,
} from '../../shared/article/functions'

Vue.use(Vuex)

const debug = process.env.NODE_ENV !== 'production'
const DEFAULT_OFFSET = 1
const DEFAULT_SELECTED_SORTING_OPTION = 0

export default new Vuex.Store({
    state: {
        articles: [],
        price_range: {
            min: 0,
            max: 0,
            from: 0,
            to: 0,
        },
        filters: {},
        attributes_filters: {},
        registered_filters: {},
        init_filters: {},
        default_offset: DEFAULT_OFFSET,
        offset: DEFAULT_OFFSET,
        display: null,
        default_display: '',
        display_mode: {},
        sorting_options: [
            {
                label: 'relevance',
                sort_direction: 'desc',
                sort_key: 'ranking',
                key_type: 'float',
            },
            {
                label: 'price_low_to_high',
                sort_direction: 'asc',
                sort_key: 'selling_price',
                key_type: 'float',
            },
            {
                label: 'price_high_to_low',
                sort_direction: 'desc',
                sort_key: 'selling_price',
                key_type: 'float',
            },
            {
                label: 'note_high_to_low',
                sort_direction: 'desc',
                sort_key: 'score',
                key_type: 'float',
            },
            {
                label: 'brand_a_z',
                sort_direction: 'asc',
                sort_key: 'brand_name',
                key_type: 'string',
            },
            {
                label: 'brand_z_a',
                sort_direction: 'desc',
                sort_key: 'brand_name',
                key_type: 'string',
            },
            {
                label: 'availability',
                sort_direction: 'desc',
                sort_key: 'estimated_delivery_time',
                key_type: 'string',
            },
        ],
        default_selected_sorting_option: DEFAULT_SELECTED_SORTING_OPTION,
        selected_sorting_option: DEFAULT_SELECTED_SORTING_OPTION,
        show_all_data: false,
        search_terms: null,
    },
    getters: {
        filtered_articles: (state) => {
            // Filter on price
            let filtered = state.articles
            if (state.price_range.min !== state.price_range.from || state.price_range.max !== state.price_range.to) {
                filtered = filtered.filter(
                    (article) =>
                        typeof article.price !== 'undefined' &&
                        article.price !== null &&
                        typeof article.price.selling_price !== 'undefined' &&
                        article.price.selling_price !== null
                )
            }

            return filtered.filter((article) => {
                if (
                    typeof article.price === 'undefined' ||
                    article.price === null ||
                    typeof article.price.selling_price === 'undefined' ||
                    article.price.selling_price === null
                ) {
                    return true
                }

                const price = parseFloat(article.price.selling_price)

                return !(price < state.price_range.from || price > state.price_range.to)
            })
        },
        /**
         * Whether we should display only default articles (true) or all declinations (false).
         *
         * @param state
         * @returns {boolean}
         */
        main_filter: (state) => {
            if (
                state.show_all_data ||
                (state.filters['colors'] && state.filters['colors'].length > 0) ||
                (state.filters['sizes'] && state.filters['sizes'].length > 0) ||
                (state.filters['highlights'] && state.filters['highlights'].length > 0)
            ) {
                return false
            } else {
                return true
            }
        },
        fully_filtered_articles: (state, getters) => {
            return applyFilter(
                getters.filtered_articles,
                state.filters,
                state.registered_filters,
                Object.keys(state.filters)
            )
        },
        articles_displayed: (state, getters) => {
            let computed_articles = getters.fully_filtered_articles.slice()

            // Depending on page, display only default the article or all the available declinations.
            return computed_articles.filter((article) => (getters.main_filter ? article.is_default_article : article))
        },
        articles_available: (state, getters) =>
            getters.articles_displayed.filter(
                (a) =>
                    a.estimated_delivery_time !== null &&
                    a.estimated_delivery_time !== 'AVAILABILITY_16_30_DAYS' &&
                    a.estimated_delivery_time !== 'AVAILABILITY_11_15_DAYS'
            ),
        articles_available_11_30: (state, getters) =>
            getters.articles_displayed.filter(
                (a) =>
                    a.estimated_delivery_time === 'AVAILABILITY_16_30_DAYS' ||
                    a.estimated_delivery_time === 'AVAILABILITY_11_15_DAYS'
            ),
        articles_not_available: (state, getters) =>
            getters.articles_displayed.filter((a) => a.estimated_delivery_time === null),
        fully_computed_articles: (state, getters) => {
            const sort_by = state.sorting_options[state.selected_sorting_option]
            if (sort_by.sort_key === 'estimated_delivery_time') {
                const selling_price = {
                    label: 'price_high_to_low',
                    sort_direction: 'asc',
                    sort_key: 'selling_price',
                    key_type: 'float',
                }
                return [
                    ...getters.articles_available.sort(generateSortingFromDefinition(selling_price)),
                    ...getters.articles_available_11_30.sort(generateSortingFromDefinition(selling_price)),
                    ...getters.articles_not_available.sort(generateSortingFromDefinition(selling_price)),
                ]
            }
            // sort by selected dropdown option
            return getters.articles_displayed.sort(generateSortingFromDefinition(sort_by))
        },
    },
    mutations: {
        [types.INITIALIZE](state, data) {
            state.search_terms = data?.search_terms ?? null
            // Highlight calculation
            calculateHighlightsOnArticle(state, data)
            // Format article attributes to avoid issues on filter when data not similar (ex: 5.00 m and 5 m)
            formatLengthOnArticle(state, data)
            formatColorOnArticle(state, data)
            state.default_selected_sorting_option =
                data.default_selected_sorting_option || state.default_selected_sorting_option
            state.display_mode = data.display_mode

            // Init some values from url params
            const { filtres, prix, tri, page, affichage } = extractUrlParams()
            state.init_filters = filtres || state.init_filters
            const init_price_range = prix || {}
            state.selected_sorting_option = Number.parseInt(tri || state.default_selected_sorting_option)
            state.offset = Number.parseInt(page || state.default_offset)
            state.display = affichage ?? data.display

            // Set whether to display the relevance filter (eg only on search page).
            state.sorting_options = data.on_search_page ? state.sorting_options : state.sorting_options.slice(1)
            // Set whether only default articles should be displayed.
            state.show_all_data = data.show_all_data

            // Set price range boundaries based on the article list
            let boundaries = numericRangeBoundaries(extractPrices(state.articles))
            boundaries.from =
                typeof init_price_range.from !== 'undefined'
                    ? Math.max(init_price_range.from, boundaries.min)
                    : boundaries.min
            boundaries.to =
                typeof init_price_range.to !== 'undefined'
                    ? Math.min(init_price_range.to, boundaries.max)
                    : boundaries.max
            state.price_range.min = boundaries.min
            state.price_range.max = boundaries.max
            state.price_range.from = boundaries.from
            state.price_range.to = boundaries.to
        },
        [types.UPDATE_PRICE_RANGE](state, { min, max, from, to }) {
            state.price_range.min = min
            state.price_range.max = max
            state.price_range.from = from
            state.price_range.to = to

            state.offset = 1
        },
        [types.UPDATE_OFFSET](state, value) {
            state.offset = value
        },
        [types.REGISTER_FILTER](state, { filter_name, label, search_key }) {
            state.registered_filters[filter_name] = {
                key: search_key,
                label: label,
            }

            // Make key Observable
            Vue.set(state.filters, filter_name, state.init_filters[filter_name] || [])
        },
        [types.TOGGLE_FILTER](state, { filter_name, value }) {
            if (typeof state.filters[filter_name] !== 'undefined') {
                if (state.filters[filter_name].indexOf(value) === -1) {
                    state.filters[filter_name].push(value)
                } else {
                    state.filters[filter_name].splice(state.filters[filter_name].indexOf(value), 1)
                    // if no more filter selected
                    if (
                        Object.values(state.filters)
                            .map((filter) => filter.length)
                            .reduce((acc, filter) => acc + filter) === 0
                    ) {
                    }
                }
            }

            state.offset = 1
        },
        [types.RESET_ONE_FILTER](state, filter_name) {
            state.filters[filter_name] = []
        },
        [types.RESET_ALL_FILTERS](state) {
            state.price_range.from = state.price_range.min
            state.price_range.to = state.price_range.max

            Object.keys(state.filters).forEach(function (key) {
                Vue.set(state.filters, key, [])
            })

            state.offset = 1
        },
        [types.UPDATE_SELECTED_SORTING_OPTION](state, value) {
            state.selected_sorting_option = value
        },
        [types.UPDATE_DISPLAY](state, value) {
            state.display = value
        },
        [types.INITIALIZE_DEFAULT_DISPLAY](state, value) {
            state.default_display = value
        },
        [types.UPDATE_FROM_URL_PARAMS](state) {
            let { filtres, prix, tri, page, affichage } = extractUrlParams()

            // Set default values
            const price_from = prix && prix.from ? prix.from : state.price_range.min
            const price_to = prix && prix.to ? prix.to : state.price_range.max
            if (!tri) {
                tri = state.default_selected_sorting_option
            }
            if (!page) {
                page = state.default_offset
            }
            if (!affichage) {
                affichage = state.default_display
            }

            // Set state
            for (let key in state.filters) {
                if (state.filters.hasOwnProperty(key)) {
                    state.filters[key] = []
                }
            }
            state.filters = Object.assign(state.filters, filtres)
            state.price_range.from = price_from
            state.price_range.to = price_to
            state.selected_sorting_option = Number.parseInt(tri)
            state.offset = Number.parseInt(page)
            state.display = affichage
        },
    },
    actions,
    strict: debug,
})

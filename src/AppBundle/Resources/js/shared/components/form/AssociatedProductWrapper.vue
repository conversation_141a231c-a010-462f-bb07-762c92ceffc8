<template>
    <div class="d-flex flex-column align-items-center text-center">
        <div
            class="section-bordered squared-placeholder d-flex flex-column justify-content-between align-items-center clickable"
            :class="{ 'has-errors': errors.length > 0 }"
            @click.prevent.stop="browse"
        >
            <template v-if="!product">
                <span class="d-flex flex-column justify-content-center align-items-center flex-grow"
                    ><i class="icon icon-zoom icon-4x"></i
                ></span>
                <span class="text-on-bottom">{{ trans('find', {}, 'component') }}</span>
            </template>
            <div v-else class="associated-product-preview d-flex flex-column justify-content-center flex-grow">
                <img v-if="image" :src="image" alt="" />
                <span v-else>{{ trans('no_picture', {}, 'component') }}</span>
            </div>
            <a
                :aria-label="`Supprimer ${delete_label}`"
                v-if="product"
                href=""
                class="text-on-bottom"
                @click.prevent.stop="deleteMe"
                >{{ delete_label }}</a
            >
        </div>
        <a
            :aria-label="`Lien vers l'article ${product.title}`"
            v-if="product"
            :href="product.item_slug"
            target="_blank"
            class="text-primary associated-product-title"
            >{{ product.title }}</a
        >

        <component style="display: none" :is="rendered_prototype" :value="product_id" />
    </div>
</template>

<script>
import Vue from 'vue'
import ProductPicker from '../ProductPicker'
import WrapperMixin from './WrapperMixin'
import { trans, extract_image_uri_by_reference, asset } from '../../filters'

export default {
    mixins: [WrapperMixin],
    components: { ProductPicker },
    data() {
        return {
            product: null,
            product_picker: null,
        }
    },
    computed: {
        // Internal configuration based on default values and config prop
        conf() {
            return Object.assign(
                {
                    auto_open: true, // whether or not the product picker is open on creation
                },
                this.config
            )
        },
        // Extract product_id to pass it to the hidden SF form
        product_id() {
            return this.product ? this.product.common_content_id : null
        },
        // Extract image from product
        image() {
            return this.product && this.product.media_variation
                ? asset(extract_image_uri_by_reference(this.product.media_variation, '90'), 'article_images')
                : null
        },
        // Detected errors on the input file, based on conf
        errors() {
            let errors = []

            // check required product
            if (this.conf.required && !this.product) {
                errors.push(trans('file_required', {}, 'component'))
            }

            return errors
        },
    },
    methods: {
        trans,
        /**
         * Open the product picker in a popin
         */
        browse() {
            window
                .swal({
                    title: trans('associate_a_product', {}, 'component'),
                    html: `<div id="product-selector-popin" class="text-left mceContentBody" style="min-height: 400px;">
                            <product-picker @selected="updateProduct"></product-picker>
                        </div>`,
                    showCloseButton: true,
                    showConfirmButton: false,
                    showCancelButton: true,
                    cancelButtonText: trans('cancel'),
                    cancelButtonClass: 'btn btn-default btn-round btn-lg',
                    buttonsStyling: false,
                    onOpen: () => {
                        const me = this
                        this.product_picker = new Vue({
                            name: 'ProductSelectorPopin',
                            el: '#product-selector-popin',
                            components: {
                                ProductPicker,
                            },
                            methods: {
                                updateProduct(product) {
                                    me.product = product
                                    window.swal.close()
                                },
                            },
                        })
                    },
                    onClose: () => {
                        this.product_picker.$destroy()
                    },
                })
                .catch(window.swal.noop)
        },
    },
    mounted() {
        // auto-open product picker based on configuration
        if (this.conf.auto_open === true) {
            this.browse()
        }
    },
}
</script>

/**
 * Shared is not the same as common
 *
 * Common are component that can/should be loaded on each pages regardless of the context
 * Shared are component that can/should be loaded on per requirement basis and compiled within another component
 *
 * This file for example is a shared trans filter that should be used only from a page who have a VueJS application
 * It makes sense only with this scenario, hence it is "shared" not "common"
 */
const currencyEUR = new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
})

export function trans(value, parameters, domain) {
    parameters = parameters || {}
    domain = domain || 'messages'

    return window.Translator.trans(value, parameters, domain)
}

export function transChoice(value, parameters, domain) {
    parameters = parameters || {}
    domain = domain || 'messages'

    if (!parameters.hasOwnProperty('count')) {
        console.warn('You need to provide a parameter "count" in order to use transChoice filter')

        return value
    }

    const { count } = parameters

    return window.Translator.transChoice(value, count, parameters, domain)
}

export function capitalize(text) {
    return text ? text[0].toUpperCase() + text.slice(1).toLowerCase() : ''
}

/**
 * Format price display in eur
 *
 * @param value
 * @return {*}
 */
export function formatPrice(value) {
    return currencyEUR.format(value).replace(' ', ' ').replace(' ', ' ')
}
/**
 * Format price display based on locale
 *
 * @param value
 * @return {*}
 */
export function comaDelimiter(value) {
    if (typeof value !== 'number') {
        return value
    }
    const formatter = new Intl.NumberFormat('fr', {
        decimal: ',',
    })
    return formatter.format(value)
}

/**
 * Format price based on formatPrice() result, without decimal if zero.
 *
 * @param value
 */
export function formatPriceSvd(value) {
    return formatPrice(value).replace(',00', '')
}

/**
 * Get the url of the asset "value" from the package "domain".
 *
 * @param {String} value
 * @param {String} domain
 * @return {String}
 */
export function asset(value, domain) {
    if (!value || !/^\//.test(value)) {
        console.warn('The asset value must begin with a slash to properly work')

        return value
    }

    const SonVideo = window.SonVideo || {}
    if (SonVideo.assets && SonVideo.assets.hasOwnProperty(domain)) {
        return SonVideo.assets[domain].replace('/{{asset}}', value)
    }

    console.warn(`Try to use an unknown domain asset "${domain}"`)

    return value
}

/**
 * Append the given preset to an url
 *
 * @param url
 * @param preset
 */
export function preset(url, preset) {
    if (url.includes('?p=')) {
        console.warn(`Try to use a preset on url already using one`)
    } else {
        url = `${url}?p=${preset}`
    }

    return url
}

/**
 * Apply multiple presets to an url.
 * Useful to generate a srcset.
 *
 * @param url
 * @param {array<[string, ?string]>} presets
 */
export function presets(url, presets) {
    return presets.map((p) => preset(url, p[0]) + (p[1] ? ` ${p[1]}` : '')).join(', ')
}

/**
 * Extract the uri from the media_variation structure matching the reference
 * or use the closest one in larger first, then in smaller
 *
 * @param media_variation json
 * @param reference       string
 * @return {string}
 */
export function extract_image_uri_by_reference(media_variation, reference) {
    if (!media_variation.hasOwnProperty('image')) {
        console.error('media_variation is not an image')

        return ''
    }

    // Return direct match
    if (media_variation.image.referential.hasOwnProperty(reference)) {
        return media_variation.image.referential[reference]
    }

    // … else find the closest larger variation, excluding special cases (xxx_squared…)
    let keys_ordered = Object.keys(media_variation.image.referential)
        .filter((variation) => variation.indexOf('_') === -1)
        .sort((a, b) => (parseInt(a) > parseInt(b) ? 1 : -1))
    for (let key of keys_ordered) {
        if (parseInt(key) > parseInt(reference)) {
            return media_variation.image.referential[key]
        }
    }

    // … else find the closest smaller variation
    keys_ordered.sort((a, b) => (parseInt(a) > parseInt(b) ? -1 : 1))

    for (let key of keys_ordered) {
        if (parseInt(key) < parseInt(reference)) {
            return media_variation.image.referential[key]
        }
    }

    // should not append
    return ''
}

const availability = (availability, has_an_ongoing_supplier_order) => {
    // previous value for for widget immediate recall reactivation :
    // let formatted_availability =
    //     trans('availability') + ` : <span class="widget-rappel-immediat cursor-pointer text-primary">${trans('availability_contact')}</span>`

    let formatted_availability = trans(
        has_an_ongoing_supplier_order ? 'availability_being_resupplied_clipped' : 'availability_on_demand'
    )

    switch (availability) {
        case 'AVAILABILITY_1_2_DAYS':
            formatted_availability = `<span class="SVDv3_dispo_stock" title="${trans('availability_title')}">${trans(
                availability
            )}</span>`
            break
        case 'AVAILABILITY_3_5_DAYS':
            formatted_availability = `<span class="SVDv3_dispo_bientot">${trans('availability')} ${trans(
                availability
            )}</span>`
            break
        case 'AVAILABILITY_6_10_DAYS':
        case 'AVAILABILITY_11_15_DAYS':
        case 'AVAILABILITY_16_30_DAYS':
        case 'AVAILABILITY_1_2_MONTHS':
        case 'AVAILABILITY_2_3_MONTHS':
        case 'AVAILABILITY_3_6_MONTHS':
        case 'AVAILABILITY_6_9_MONTHS':
            formatted_availability = trans('availability') + ' ' + trans(availability)
            break
    }

    return formatted_availability
}

export function render_availability_v5(thing, longText = false) {
    const availability = thing.estimated_delivery_time
    const has_an_ongoing_supplier_order = thing?.has_an_ongoing_supplier_order ?? false

    if (thing.sku === 'SVLEMAG' || thing.sku === 'SVCATAPREMHIVER') {
        return ''
    }

    if (thing.unbasketable_reason) {
        return `Indisponible à la vente`
    }

    let formatted_availability = has_an_ongoing_supplier_order ? "En cours d'approvisionnement" : 'Sur commande'

    switch (availability) {
        case 'AVAILABILITY_1_2_DAYS':
            formatted_availability = longText ? `En stock : expédié sous 24h` : `En stock`
            break
        case 'AVAILABILITY_3_5_DAYS':
            formatted_availability = longText ? `En stock sous 3 à 5 jours` : `3 à 5 jours`
            break
        case 'AVAILABILITY_6_10_DAYS':
            formatted_availability = longText ? `En stock sous 6 à 10 jours` : `6 à 10 jours`
            break
        case 'AVAILABILITY_11_15_DAYS':
            formatted_availability = longText ? `En stock sous 11 à 15 jours` : `11 à 15 jours`
            break
        case 'AVAILABILITY_16_30_DAYS':
            formatted_availability = longText ? `En stock sous 16 à 30 jours` : `16 à 30 jours`
            break
        case 'AVAILABILITY_1_2_MONTHS':
            formatted_availability = longText ? `Délai 1 à 2 mois` : `1 à 2 mois`
            break
        case 'AVAILABILITY_2_3_MONTHS':
            formatted_availability = longText ? `Délai 2 à 3 moi` : `2 à 3 mois`
            break
        case 'AVAILABILITY_3_6_MONTHS':
            formatted_availability = longText ? `Délai 3 à 6 mois` : `3 à 6 mois`
            break
        case 'AVAILABILITY_6_9_MONTHS':
            formatted_availability = longText ? `Délai 6 à 9 mois` : `6 à 9 mois`
            break
    }
    return formatted_availability
}
/**
 * Return html to display the (un)availability of a thing (article, product, accessory…).
 *
 * @param {Object} thing
 * @param {Object} options
 * @return string
 */
export function render_availability(thing, options) {
    // extract options values
    const { in_cart } = Object.assign(
        {
            in_cart: false,
        },
        options
    )

    // unavailability display
    if (thing.unbasketable_reason) {
        const label = in_cart
            ? `<span class="text-warning text-md">${trans('product_not_orderable_for_now')}</span>`
            : `<span class="label label-danger label-md">${trans('Discontinued')}</span>`

        return `
            <div class="mceContentBody">
                <p class="${in_cart ? 'margin-no' : 'margin-md'}">${label}</p>
            </div>
        `
    }

    // availability display
    return `<p class="SVDv3_dispo ${in_cart ? 'margin-no' : ''} ${
        isAvailableWithDelay(thing.estimated_delivery_time) ? 'text-svd-warning font-bold' : ''
    }">${availability(thing.estimated_delivery_time, thing?.has_an_ongoing_supplier_order || false)}</p>`
}

export function isAvailableWithDelay(estimated_delivery_time) {
    return [
        'AVAILABILITY_3_5_DAYS',
        'AVAILABILITY_6_10_DAYS',
        'AVAILABILITY_11_15_DAYS',
        'AVAILABILITY_16_30_DAYS',
        'AVAILABILITY_1_2_MONTHS',
        'AVAILABILITY_2_3_MONTHS',
        'AVAILABILITY_3_6_MONTHS',
        'AVAILABILITY_6_9_MONTHS',
    ].includes(estimated_delivery_time)
}

/**
 * @param quantity
 * @param article
 * @returns {boolean}
 */
export function isStockUnavailable(quantity, article) {
    if ('AVAILABILITY_1_2_DAYS' !== article.estimated_delivery_time) {
        return false
    }
    if (article.packaged_articles === null) {
        return parseInt(quantity) > parseInt(article.quantity_available_reservation) && !article.unbasketable_reason
    }
    return parseInt(quantity) > parseInt(article.informational_stock)
}

/**
 * @param v
 * @returns {*}
 */
export const convertBrToLineField = (v) => (v ? v.replaceAll('<br/>', '\n') : v)

/**
 *
 * @param object
 * @param chains
 * @returns {string}
 */
export function optionalChaining(object, chains) {
    let obj = object
    let finalString = ''
    chains.forEach((e, index) => {
        if (obj && obj[e]) {
            if (index + 1 === chains.length) {
                finalString = obj[e]
            } else {
                obj = obj[e]
            }
        }
    })
    return finalString
}

/**
 * Handle the insertion of the google maps api script in the page
 * and return a Promise resolved when its loaded.
 *
 * @return {Promise}
 */
export function loadGoogleMapsApi() {
    return new Promise((resolve, reject) => {
        // if already loaded in the past, resolve
        if (window.__googleMapApiLoaded) {
            return resolve(true)
        }

        // Google Maps may have been loaded through another way (vue2-google-maps lib), we can resolve
        if (window.google?.maps) {
            window.__googleMapApiPromiseQueue = window.__googleMapApiPromiseQueue || []
            googleMapsCallback()

            return resolve(true)
        }

        // else add resolver to the queue
        window.__googleMapApiPromiseQueue = window.__googleMapApiPromiseQueue || []
        window.__googleMapApiPromiseQueue.push(() => resolve(true))

        // load google script if not already included in the page
        if (!document.querySelector('script[src*="maps.googleapis.com/maps/api/js"]')) {
            // global function handling the resolving of the queue
            window['__googleMapsApiOnLoadCallback'] = googleMapsCallback

            const gapiurl =
                'https://maps.googleapis.com/maps/api/js?key=' +
                window.SonVideo.google_maps.api_key +
                '&callback=__googleMapsApiOnLoadCallback'

            let script = document.createElement('script')
            script.type = 'text/javascript'
            script.src = gapiurl
            script.setAttribute('async', '')

            const s = document.getElementsByTagName('script')[0]
            s.parentNode.insertBefore(script, s)
        }
    })
}
function googleMapsCallback() {
    window.__googleMapApiLoaded = true
    window.__googleMapApiPromiseQueue.forEach((fct) => fct())
    window.__googleMapApiPromiseQueue = []
}

/**
 * Check if a code is a valid postal code for a specific country
 *
 * @param code
 * @param country
 * @return {boolean}
 */
export function isPostalCodeValid(code, country) {
    const regexes = {
        FR: /^[0-9]{5}$/i,
        BE: /^[0-9]{4}$/i,
    }

    if (!regexes.hasOwnProperty(country)) {
        return false
    }

    return regexes[country].test(code)
}

/**
 * Return an object with key => value extracted from the URL's search part.
 * Value is json and uri decoded.
 * If a key is present more than once in the URL, the resulting value will be an array.
 *
 * @return {{}}
 */
export function extractUrlParams() {
    let params = {}
    if (!window.location.search) {
        return params
    }

    // tiny repetitive method used below to convert an url value
    const convertValue = (val) => {
        val = decodeURIComponent(val)
        if (!/\"/.test(val)) {
            return val
        }

        return JSON.parse(val)
    }

    const vars = window.location.search.substring(1).split('&')
    for (let i = 0; i < vars.length; i++) {
        let pair = vars[i].split('=')

        if (typeof params[pair[0]] === 'undefined') {
            params[pair[0]] = convertValue(pair[1])
        } else if (Array.isArray(params[pair[0]])) {
            params[pair[0]].push(convertValue(pair[1]))
        } else {
            params[pair[0]] = [params[pair[0]], convertValue(pair[1])]
        }
    }

    return params
}

/**
 * Return a function to use as parameter of "Array.sort()", based on a provided definition.
 * A sort_key must be provided in the definition, which tell which property is compared in the data.
 * Use sort_direction = 'desc' in the definition to invert sorting.
 * Use key_type = 'float' in the definition if the values to compare must be parsed as floats (same for integers).
 *
 * @param definition
 * @return {function(*, *)}
 */
export function generateSortingFromDefinition(definition) {
    if (!definition.hasOwnProperty('sort_key')) {
        console.error('Definition needs to have a sort_key defined to properly work')
    }

    const key = definition.sort_key

    return (previous, next) => {
        if (previous[key] === null && next[key] === null) {
            return 1
        } else if (previous[key] === null) {
            return 1
        } else if (next[key] === null) {
            return -1
        }

        let previous_value = definition.key_type === 'float' ? parseFloat(previous[key]) : previous[key]
        let next_value = definition.key_type === 'float' ? parseFloat(next[key]) : next[key]
        let compared = definition.sort_direction === 'desc' ? previous_value < next_value : previous_value > next_value

        return compared ? 1 : -1
    }
}

/**
 * Get a value from the localStorage with proper conversion.
 * A default value can be defined if not found.
 *
 * @param key
 * @param default_value
 * @returns {any}
 */
export function getLocaleStorage(key, default_value = null) {
    return localStorage.getItem(key) !== null ? JSON.parse(localStorage.getItem(key)) : default_value
}

/**
 * For a given value `v`, returns a comparison function between `a` and `b` to know which one is the closest to v.
 * Useful to find the closest value in an array of value.
 *
 * @param {Number} v
 * @return {function(Number, Number): Number}
 */
export const closestReducer = (v) => (a, b) => Math.abs(v - a) < Math.abs(v - b) ? a : b

/**
 * Test if a given value is numeric.
 * Beware that this does not work with some advanced representations (exponential...)
 *
 * @param v
 * @return {boolean}
 */
export const isNumeric = (v) => /^-?[0-9]+(\.[0-9]+)?$/.test(v)

export function prependPrepositionDeTo(value) {
    const preposition = ['a', 'e', 'i', 'o', 'u', 'y', 'h'].includes(value.substring(0, 1).toLowerCase()) ? 'd’' : 'de '

    return `${preposition}${value}`
}

export const computeFrenchCivility = (key) => ('Mr' === key ? 'M.' : 'Mme.')

export const isValidEmail = (value) => {
    const validRegex = new RegExp(
        '(?:[a-z0-9!#$%&\'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&\'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\\[(?:(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9]))\\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\\])'
    )
    return value.match(validRegex)
}

export const isMobile = (screen_size = 769) => {
    if (window.matchMedia(`only screen and (max-width: ${screen_size}px)`).matches) {
        return true
    }
    return false
}

import { icon } from 'leaflet/src/layer'
import { asset } from '@/shared/filters'

const DEFAULT_ICON = {
    url: asset('/images/ui/uiV5/maps/map-marker-v5-primary.png', 'static_images'),
    width: 32,
    height: 44,
}
const SELECTED_ICON = {
    url: asset('/images/ui/uiV5/maps/map-marker-v5-secondary.png', 'static_images'),
    width: 32,
    height: 44,
}
export const MAP_DEFAULT = {
    layer_url: 'https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}',
    marker_icon: iconToMarkerIcon(DEFAULT_ICON),
    zoom: 15,
    all_stores_zoom: 5,
    center: [48.8109, 2.54517], // Champigny warehouse
    france_center: [46.227638, 2.213749],
}

export const SELECTED_MARKER_ICON = iconToMarkerIcon(SELECTED_ICON)

function iconToMarkerIcon(my_icon) {
    return icon({
        iconUrl: my_icon.url,
        iconSize: [my_icon.width, my_icon.height],
        iconAnchor: [my_icon.width / 2, my_icon.height],
        popupAnchor: [0, -my_icon.height],
    })
}

// Indicates if recaptcha should be silently bypassed client-side
const disabled = !window.SonVideo.recaptcha.enabled

/**
 * loadReCaptchaScript
 *
 * Handle the insertion of the reCaptcha api script in the page
 * and return a Promise resolved when its loaded.
 *
 * @return {Promise}
 */
const loadScript = () => {
    return new Promise((resolve, reject) => {
        // if disabled, resolve
        if (disabled) {
            return resolve(true)
        }

        // if already loaded in the past, resolve
        if (window.__reCaptchaApiLoaded) {
            return resolve(true)
        }

        // else add resolver to the queue
        window.__reCaptchaApiPromiseQueue = window.__reCaptchaApiPromiseQueue || []
        window.__reCaptchaApiPromiseQueue.push(() => resolve(true))

        // load google script if not already included in the page
        if (!document.querySelector('script[src*="google.com/recaptcha/api.js"]')) {
            // global function handling the resolving of the queue
            window['__reCaptchaApiOnLoadCallback'] = () => {
                window.__reCaptchaApiLoaded = true
                window.__reCaptchaApiPromiseQueue.forEach((fct) => fct())
            }

            const recaptcha_url = `https://www.google.com/recaptcha/api.js?onload=__reCaptchaApiOnLoadCallback&render=explicit&hl=${
                window?.Translator?.locale ?? 'fr'
            }`

            let script = document.createElement('script')
            script.type = 'text/javascript'
            script.src = recaptcha_url
            script.setAttribute('async', '')
            script.setAttribute('defer', '')

            const s = document.getElementsByTagName('script')[0]
            s.parentNode.insertBefore(script, s)
        }
    })
}

/**
 * render
 *
 * Wrap the functionality of original recaptcha render to handle case where its disabled.
 *
 * @param id
 * @param params
 *
 * @return {Object} render() result of recapcha or simply an handler of the callback if disabled
 */
const render = (id, params) => {
    // set default options
    params = Object.assign(
        {
            sitekey: window.SonVideo.recaptcha.public_key,
            size: 'invisible',
            callback: () => {},
        },
        params
    )

    // if disabled, return the callback param so it can be called on next execute()
    if (disabled) {
        return {
            callback: params.callback,
        }
    }

    return window.grecaptcha.render(id, params)
}

/**
 * execute
 *
 * Wrap the functionality of original recaptcha execute to handle case where its disabled.
 *
 * @param captcha
 */
const execute = (captcha) => {
    // if disabled, call the callback stored on the captcha, if any
    if (disabled) {
        if (captcha.hasOwnProperty('callback')) {
            captcha.callback()
        }

        return
    }

    window.grecaptcha.execute(captcha)
}

/**
 * reset
 *
 * @param captcha
 */
const reset = (captcha) => {
    if (disabled) {
        return
    }

    window.grecaptcha.reset(captcha)
}

/**
 * Simple helper to add the expected recaptcha token in request parameters
 *
 * @param token
 * @param url
 *
 * @return {string} Url with the token added
 */
const addTokenToUrl = (token, url) => {
    return url.indexOf('?') > -1 ? `${url}&g-recaptcha-response=${token}` : `${url}?g-recaptcha-response=${token}`
}

export default {
    loadScript,
    render,
    execute,
    addTokenToUrl,
    reset,
}

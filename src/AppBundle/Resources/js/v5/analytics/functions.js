const product_sliders = [
    { data_ga: 'products_our_unmissable', list: 'Homepage : sélection' },
    { data_ga: 'products_best_sellers', list: 'Homepage : meilleures ventes' },
    { data_ga: 'products_new_articles', list: 'Homepage : nouveautés' },
]
export function registerViewAnalyticsEvent() {
    /**
     * Push to GA events about banner click on page load
     */
    window.addEventListener('load', function () {
        const promotions = []
        const articles = []

        // Promo banner on the homepage
        ;[...document.querySelectorAll('[data-ga="main-slider"] .swiper-slide')].forEach(function (slide) {
            const carousel_banner_elem = document.querySelector(`[data-index="${slide.dataset['index']}"]`)
            if (carousel_banner_elem !== null && !promotions.some((p) => p.id === carousel_banner_elem.dataset.id)) {
                promotions.push({
                    name: carousel_banner_elem.dataset.title,
                    id: carousel_banner_elem.dataset.id,
                    position: carousel_banner_elem.dataset.position,
                    creative: carousel_banner_elem.dataset.location + '_carousel',
                })
            }
        })

        /**
         * Article slider on the homepage
         */
        product_sliders.forEach((slider) => {
            ;[...document.querySelectorAll(`[data-ga="${slider.data_ga}"] .swiper-slide`)].forEach(function (article) {
                articles.push(Object.assign({}, article.dataset, { list: slider.list }))
            })
        })
        SonVideo.pushAnalytics('view', { articles, promotions, products: [] })
    })
}

export function registerClickAnalyticsEvent() {
    ;[...document.querySelectorAll('[data-ga="main-slider"] .swiper-slide')].forEach(function (slide) {
        const carousel_banner_elem = document.querySelector(`[data-index="${slide.dataset['index']}"]`)

        carousel_banner_elem.addEventListener('click', () => {
            SonVideo.pushAnalytics('promoClick', [
                {
                    name: carousel_banner_elem.dataset.title,
                    id: carousel_banner_elem.dataset.id,
                    creative: 'home',
                    position: carousel_banner_elem.dataset.position,
                },
            ])
        })
    })

    product_sliders.forEach((slider) => {
        ;[...document.querySelectorAll(`[data-ga="${slider.data_ga}"] .swiper-slide`)].forEach(function (article) {
            article.addEventListener('click', () => {
                SonVideo.pushAnalytics('clickProduct', {
                    actionField: { list: slider.list },
                    products: [article.dataset],
                })
            })
        })
    })
}

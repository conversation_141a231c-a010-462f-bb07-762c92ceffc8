<script setup>
import { isAvailableWithDelay, render_availability_v5 } from '@/shared/filters'
import { computed } from 'vue'

const props = defineProps({
    article: {
        type: Object,
        required: true,
        validator: (v) => ['estimated_delivery_time'].every((k) => k in v),
    },
})

const available = computed(() => props.article.estimated_delivery_time === 'AVAILABILITY_1_2_DAYS')
const available_with_delay = computed(() => isAvailableWithDelay(props.article.estimated_delivery_time))
const is_unbasketable = computed(() => props.article?.unbasketable_reason ?? false)
</script>

<template>
    <div
        data-context="article-availability"
        class="stock"
        :class="{ available, 'available-with-delay': available_with_delay, unbasketable: is_unbasketable }"
    >
        {{ render_availability_v5(article) }}
    </div>
</template>

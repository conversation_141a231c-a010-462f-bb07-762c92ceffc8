import SvdBadge from './SvdBadge.vue'
export default {
    component: SvdBadge,
    title: 'Badge',
    args: { label: 'Label' },
}

export const Sandbox = {
    render: (args, { argTypes }) => ({
        components: { SvdBadge },
        props: Object.keys(argTypes),
        template: '<svd-badge v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'primary',
    },
}

export const Primary = {
    render: (args, { argTypes }) => ({
        components: { SvdBadge },
        props: Object.keys(argTypes),
        template: '<svd-badge v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'primary',
    },
}

export const PrimaryOutline = {
    render: (args, { argTypes }) => ({
        components: { SvdBadge },
        props: Object.keys(argTypes),
        template: '<svd-badge v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'primary',
        type: 'outline',
    },
}

export const Secondary = {
    render: (args, { argTypes }) => ({
        components: { SvdBadge },
        props: Object.keys(argTypes),
        template: '<svd-badge v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'secondary',
    },
}

export const SecondaryOutline = {
    render: (args, { argTypes }) => ({
        components: { SvdBadge },
        props: Object.keys(argTypes),
        template: '<svd-badge v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'secondary',
        type: 'outline',
    },
}

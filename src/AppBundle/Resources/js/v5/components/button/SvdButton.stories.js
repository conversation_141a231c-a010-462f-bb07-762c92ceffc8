import SvdButton from './SvdButton.vue'
export default {
    component: SvdButton,
    title: 'Button',
    args: { label: 'Label' },
}

export const Sandbox = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'primary',
    },
}

export const Primary = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'primary',
    },
}

export const PrimaryDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'primary',
        disabled: true,
    },
}

export const PrimaryOutline = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'primary',
        type: 'outline',
    },
}

export const PrimaryOutlineDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'primary',
        type: 'outline',
        disabled: true,
    },
}

export const PrimaryLink = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'primary',
        type: 'link',
    },
}

export const PrimaryLinkDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'primary',
        type: 'link',
        disabled: true,
    },
}

export const Secondary = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'secondary',
    },
}

export const SecondaryDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'secondary',
        disabled: true,
    },
}

export const SecondaryOutline = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'secondary',
        type: 'outline',
    },
}

export const SecondaryOutlineDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'secondary',
        type: 'outline',
        disabled: true,
    },
}

export const SecondaryLink = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'secondary',
        type: 'link',
    },
}

export const SecondaryLinkDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'secondary',
        type: 'link',
        disabled: true,
    },
}

export const Light = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'light',
    },
}

export const LightDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'light',
        disabled: true,
    },
}

export const LightOutline = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'light',
        type: 'outline',
    },
}

export const LightOutlineDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'light',
        type: 'outline',
        disabled: true,
    },
}

export const LightLink = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'light',
        type: 'link',
    },
}

export const LightLinkDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'light',
        type: 'link',
        disabled: true,
    },
}

export const White = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'white',
    },
}

export const WhiteDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'white',
        disabled: true,
    },
}

export const WhiteOutline = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'white',
        type: 'outline',
    },
}

export const WhiteOutlineDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'white',
        type: 'outline',
        disabled: true,
    },
}

export const WhiteLink = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'white',
        type: 'link',
    },
}

export const WhiteLinkDisabled = {
    render: (args, { argTypes }) => ({
        components: { SvdButton },
        props: Object.keys(argTypes),
        template: '<svd-button v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'white',
        type: 'link',
        disabled: true,
    },
}

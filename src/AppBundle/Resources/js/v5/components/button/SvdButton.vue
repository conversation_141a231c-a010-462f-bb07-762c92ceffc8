<script setup>
import { computed, ref, onBeforeUnmount, onMounted } from 'vue'
import SvdIcon from '@/v5/components/icon/SvdIcon.vue'
const emit = defineEmits(['click', 'contextmenu'])
const showChoices = ref(false)
const isHover = ref(false)

const props = defineProps({
    /**
     * Identifiant du bouton
     */
    id: {
        type: String,
        required: false,
    },
    /**
     * Texte affiché
     */
    label: {
        type: String | Number,
        required: true,
    },
    /**
     * Stylise la couleur de police et de fond.
     */
    color: {
        type: String,
        required: false,
        validator: (val) => ['primary', 'secondary', 'light', 'white', 'focus'].includes(val),
    },
    /**
     * Équivalent à color='primary', sous forme de booléen.
     */
    primary: {
        type: Boolean,
        default: false,
    },
    /**
     * Équivalent à color='secondary', sous forme de booléen.
     */
    secondary: {
        type: Boolean,
        default: false,
    },
    /**
     * Équivalent à color='light', sous forme de booléen.
     */
    light: {
        type: Boolean,
        default: false,
    },
    /**
     * Équivalent à color='white', sous forme de booléen.
     */
    white: {
        type: Boolean,
        default: false,
    },
    /**
     * Équivalent à color='focus', sous forme de booléen.
     */
    focus: {
        type: Boolean,
        default: false,
    },
    /**
     * Stylise la bordure et le fond
     */
    type: {
        type: String,
        required: false,
        default: 'plain',
        validator: (val) => {
            if (!val) return true
            return ['plain', 'outline', 'link'].includes(val)
        },
    },
    /**
     * Équivalent à type='plain', sous forme de booléen.
     */
    plain: {
        type: Boolean,
        default: false,
    },
    /**
     * Équivalent à type='outline', sous forme de booléen.
     */
    outline: {
        type: Boolean,
        default: false,
    },
    /**
     * Équivalent à type='link', sous forme de booléen.
     */
    link: {
        type: Boolean,
        default: false,
    },
    /**
     * Pour désactiver le bouton, visuellement et fonctionnellement.
     */
    disabled: {
        type: Boolean,
        default: false,
    },
    /**
     * Permet de valider un formulaire
     */
    submit: {
        type: Boolean,
        default: false,
    },
    /**
     * Affiche une icone de chargement
     */
    loading: {
        type: Boolean,
        default: false,
    },
    /**
     * Affichage plus petit
     */
    small: {
        type: Boolean,
        default: false,
    },
    /**
     * Affichage rond
     */
    rounded: {
        type: Boolean,
        default: false,
    },
    /**
     * Utiliser le bouton comme un lien
     */
    href: {
        type: String,
        default: '',
    },
    /**
     * Affichage le moins large possible
     */
    fit: {
        type: Boolean,
        default: false,
    },
    /**
     * Afficher une liste de boutons
     */
    choices: {
        type: Array,
    },
    /**
     * Icone qui sera affichée à gauche du texte
     */
    icon: {
        type: String,
        required: false,
    },
    /**
     * Icone qui sera affichée à droite du texte
     */
    iconRight: {
        type: String,
        required: false,
    },
    /**
     * Donne l'effet open au bouton. Utile pour les choices
     */
    open: {
        type: Boolean,
        default: false,
    },
    /**
     * Méthode d'ouverture du lien
     */
    target: {
        type: String,
        default: '_self',
    },
})

const btnClass = computed(() => {
    const classList = []
    if (props.primary || props.color === 'primary') classList.push('primary')
    if (props.secondary || props.color === 'secondary') classList.push('secondary')
    if (props.light || props.color === 'light') classList.push('light')
    if (props.white || props.color === 'white') classList.push('white')
    if (props.focus || props.color === 'focus') classList.push('focus')
    if (props.plain || props.type === 'plain') classList.push('plain')
    if (props.outline || props.type === 'outline') classList.push('outline')
    if (props.link || props.type === 'link') classList.push('btn-link')
    if (props.disabled || props.loading) classList.push('disabled')
    if (props.small) classList.push('small')
    if (props.rounded) classList.push('rounded')
    if (props.fit) classList.push('fit')
    if (props.open) classList.push('open')

    return classList.toString().replaceAll(',', ' ')
})

const iconColor = computed(() => {
    if (props.primary || props.color === 'primary') {
        if (isHover.value) {
            return 'color_white'
        }
        if (props.outline || props.type === 'outline') return 'color_blue_web_safe'
        if (props.link || props.type === 'link') return 'color_blue_web_safe'
        return 'color_white'
    }
    if (props.secondary || props.color === 'secondary') {
        if (isHover.value) {
            return 'color_white'
        }
        if (props.outline || props.type === 'outline') return 'color_grey_typo'
        if (props.link || props.type === 'link') return 'color_grey_typo'
        return 'color_white'
    }
    if (props.light || props.color === 'light' || props.white || props.color === 'white') {
        if (isHover.value) {
            return 'color_grey_typo'
        }
        if (props.outline || props.type === 'outline') return 'color_grey_typo'
        if (props.link || props.type === 'link') return 'color_grey_typo'
        return 'color_grey_typo'
    }
    if (props.focus || props.color === 'focus') {
        if (isHover.value) {
            return 'color_white'
        }
        if (props.outline || props.type === 'outline') return 'color_highlight_focus'
        if (props.link || props.type === 'link') return 'color_highlight_focus'
        return 'color_white'
    }
    return 'color_dark_default'
})

function onClick() {
    if (!props.submit) {
        if (props.href) {
            window.location.href = props.href
        }
        emit('click')
    }
}

onMounted(() => {
    if (props.choices && props.choices.length > 0) {
        document.addEventListener('click', outsideClickListener)
    }
})

onBeforeUnmount(() => {
    if (props.choices && props.choices.length > 0) {
        document.removeEventListener('click', outsideClickListener)
    }
})

const outsideClickListener = (event) => {
    if (!event.target.closest('.choices-container')) {
        showChoices.value = false
    }
}
</script>

<template>
    <section v-if="!choices || choices.length === 0">
        <button
            data-context="svd-button"
            :id="id"
            :type="submit ? 'submit' : 'button'"
            class="btn"
            :class="btnClass"
            :disabled="disabled || loading"
            :aria-label="label"
            :aria-disabled="disabled"
            @click="onClick"
            @mouseover="isHover = true"
            @mouseleave="isHover = false"
            @contextmenu="emit('contextmenu')"
        >
            <span v-if="loading">
                <div class="loading-animation">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </span>
            <span class="btn-label" v-else>
                <svd-icon v-if="icon" :color="iconColor" :icon="icon" size="24" />
                {{ label }}
                <svd-icon v-if="iconRight" :color="iconColor" :icon="iconRight" class="right" size="24" />
            </span>
        </button>
    </section>
    <section v-else class="choices-container">
        <svd-button
            @click="showChoices = !showChoices"
            v-bind="{ ...$props, choices: null, iconRight: showChoices ? 'arrow-up' : 'arrow-down', open: showChoices }"
        />
        <div v-show="showChoices" class="choices" :class="btnClass">
            <a v-for="(choice, index) in choices" :key="index" :href="choice.url" @click="showChoices = false">
                <svd-icon
                    v-if="choice.icon"
                    :icon="choice.icon"
                    :keep-color="!choice.color"
                    :color="choice.color ? choice.color : null"
                    size="24"
                />
                {{ choice.label }}
            </a>
        </div>
    </section>
</template>

<style scoped lang="scss">
@import '../../../../../Resources/scss/style';
@import '../../../../../Resources/views/V5/Component/button/button';
</style>

import SvdIcon from './SvdIcon.vue'
import { ICON_MAPPING } from '@/shared/Icons'
const all_icons = []
for (const [key, value] of Object.entries(ICON_MAPPING)) {
    all_icons.push(key)
}
export default {
    component: SvdIcon,
    title: 'Icon',
    args: { icon: 'eye', alt: 'Texte alternatif' },
    argTypes: {
        icon: {
            options: all_icons,
            control: { type: 'select' },
        },
        color: {
            control: { type: 'color' },
        },
    },
}

export const Sandbox = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
}

export const Default = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
}

export const Color = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        color: 'ff0000',
    },
}

export const Size = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        size: 100,
    },
}

export const KeepColor = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'svcc',
        keepColor: true,
    },
}

/**
 * List of the icons available
 */

export const TroisX = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: '3x',
        keepColor: true,
    },
}
export const QuatreX = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: '4x',
        keepColor: true,
    },
}
export const DixX = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: '10x',
        keepColor: true,
    },
}
export const VingtX = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: '20x',
        keepColor: true,
    },
}
export const TrenteX = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: '30x',
        keepColor: true,
    },
}
export const QuaranteX = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: '40x',
        keepColor: true,
    },
}
export const Amex2 = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'amex2',
        keepColor: true,
    },
}
export const Amex = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'amex',
    },
}
export const ArrowDown = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'arrow-down',
    },
}
export const ArrowLeft = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'arrow-left',
    },
}
export const ArrowRight = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'arrow-right',
    },
}
export const ArrowUp = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'arrow-up',
    },
}
export const Bancontact = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'bancontact',
        keepColor: true,
    },
}
export const Bell = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'bell',
    },
}
export const Bestgroupe = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'bestgroupe',
    },
}
export const Bimpli = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'bimpli',
    },
}

export const BookOpen = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'book-open',
    },
}

export const Box = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'box',
    },
}

export const Cadeau = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'cadeau',
    },
}
export const Cadhoc = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'cadhoc',
        keepColor: true,
    },
}

export const Calculator = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'calculator',
    },
}

export const Capital2024 = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'capital-2024',
    },
}

export const Card = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'card',
    },
}

export const Cartecadeau = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'cartecadeau',
    },
}

export const Cb2 = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'cb2',
    },
}

export const Cb = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'cb',
        keepColor: true,
    },
}

export const Cbtel = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'cbtel',
    },
}

export const Cetelem = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'cetelem',
        keepColor: true,
    },
}

export const CheckCircle = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'check-circle',
    },
}

export const Check = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'check',
    },
}
export const Chronopost = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'chronopost',
        keepColor: true,
    },
}
export const ChronopostPickup = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'chronopost-pickup',
        keepColor: true,
    },
}

export const CircleUser = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'circle-user',
    },
}

export const Clock = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'clock',
    },
}

export const ColisPrive = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'colis-prive',
        keepColor: true,
    },
}
export const Colissimo = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'colissimo',
        keepColor: true,
    },
}

export const Comments = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'comments',
    },
}

export const Cotation = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'cotation',
        keepColor: true,
    },
}

export const Copy = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'copy',
    },
}

export const Dhl = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'dhl',
        keepColor: true,
    },
}

export const Download = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'download',
    },
}

export const Envelope = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'envelope',
    },
}

export const Eye = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'eye',
    },
}

export const EyeSlash = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'eye-slash',
    },
}

export const Facebook = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'facebook',
    },
}

export const Fedex = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'fedex',
        keepColor: true,
    },
}

export const Floa = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'floa',
        keepColor: true,
    },
}

export const File = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'file',
    },
}

export const FranceExpress = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'france-express',
        keepColor: true,
    },
}

export const France = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'france',
    },
}

export const Gear = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'gear',
    },
}

export const Globe = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'globe',
    },
}

export const Gls = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'gls',
        keepColor: true,
    },
}

export const Headset = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'headset',
    },
}

export const IdCard = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'id-card',
    },
}

export const InfoCircleFill = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'info-circle-fill',
    },
}

export const Instagram = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'instagram',
    },
}

export const Kadeos = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'kadeos',
    },
}

export const Leaf = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'leaf',
    },
}

export const LightbulbOn = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'lightbulb-on',
    },
}

export const Lightbulb = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'lightbulb',
    },
}

export const Lock = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'lock',
    },
}

export const Mastercard2 = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'mastercard2',
    },
}

export const Mastercard = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'mastercard',
        keepColor: true,
    },
}

export const MobileComment = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'mobile-comment',
    },
}

export const Mobile = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'mobile',
    },
}

export const MondialRelay = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'mondial-relay',
        keepColor: true,
    },
}

export const NxCard = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'nx-card',
    },
}

export const Novea = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'novea',
        keepColor: true,
    },
}

export const Pen = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'pen',
    },
}

export const Paypal2 = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'paypal2',
    },
}

export const Paypal = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'paypal',
        keepColor: true,
    },
}

export const Pdf = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'pdf',
        keepColor: true,
    },
}

export const Pinterest = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'pinterest',
    },
}

export const PlaylistMusic = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'playslists-music',
    },
}

export const PlaylistVideo = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'playslists-video',
    },
}

export const PremiumCheckOutline = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'premium-check-outline',
        keepColor: true,
    },
}

export const PremiumCheck = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'premium-check',
        keepColor: true,
    },
}

export const PremiumOutline = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'premium-outline',
        keepColor: true,
    },
}

export const Premium = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'premium',
        keepColor: true,
    },
}

export const Promotion = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'promotion',
    },
}

export const Promotion2 = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'promotion2',
    },
}

export const QuestionCircle = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'question-circle',
    },
}

export const ScrewdriverWrench = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'screwdriver-wrench',
    },
}

export const Shield = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'shield',
    },
}

export const ShoppingBag = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'shopping-bag',
    },
}

export const Speakers = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'speakers',
    },
}

export const Tnt = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'tnt',
        keepColor: true,
    },
}

export const Signature = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'signature',
    },
}

export const Svcc = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'svcc',
        keepColor: true,
    },
}

export const Tirgroupe = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'tirgroupe',
        keepColor: true,
    },
}

export const Tiktok = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'tiktok',
    },
}

export const Truck = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'truck',
    },
}

export const Twitter = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'twitter',
    },
}

export const Videos = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'videos',
    },
}

export const Views = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'views',
    },
}

export const Virement = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'virement',
    },
}

export const Visa2 = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'visa2',
    },
}

export const Visa = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'visa',
        keepColor: true,
    },
}

export const X = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'x',
    },
}

export const Xmark = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'xmark',
    },
}

export const XmarkCircle = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'xmark-circle',
    },
}

export const Youtube = {
    render: (args, { argTypes }) => ({
        components: { SvdIcon },
        props: Object.keys(argTypes),
        template: '<svd-icon v-bind="$props" v-on="$props" />',
    }),
    args: {
        icon: 'youtube',
    },
}

<script>
import { ICON_MAPPING } from '@/shared/Icons'
const all_icons = ['']
for (const [key, value] of Object.entries(ICON_MAPPING)) {
    all_icons.push(key)
}
</script>

<script setup>
import { asset } from '@/shared/filters'
import { getIconUrl } from '@/shared/Icons'
import { getColor } from '@/shared/colors'
import { getFilterToApply } from '@/common/transform_img_color'
import { computed } from 'vue'

const props = defineProps({
    /**
     * Nom de l'icone à afficher
     */
    icon: {
        type: String,
        required: true,
        validator: (v) => all_icons.includes(v),
    },
    /**
     * Stylise la couleur de l'icone, accepte les variables scss définies dans /variables/_colors.scss
     */
    color: {
        type: String,
        required: false,
        default: '#000',
    },
    /**
     * Texte alternatif pour l'accessibilité
     */
    alt: {
        type: String,
        required: false,
    },
    /**
     * Taille de l'image
     */
    size: {
        type: Number | String,
        required: false,
        default: 40,
    },
    /**
     * G<PERSON>er la couleur de l'image
     */
    keepColor: {
        type: Boolean,
        required: false,
    },
})

const defineFilter = computed(() => {
    if (props.keepColor === true) {
        return 'unset'
    }
    if (props.color.startsWith('color_')) {
        return getFilterToApply(getColor(props.color))
    }
    return getFilterToApply(props.color)
})
</script>

<template>
    <img
        class="icon-v5"
        v-if="icon !== ''"
        :src="asset(getIconUrl(icon), 'static_images')"
        :alt="alt ? alt : icon"
        :style="{ filter: defineFilter, maxHeight: `${size}px`, maxWidth: `${size}px` }"
        :width="size"
        :height="size"
        :title="alt"
        v-on="$listeners"
    />
</template>

<style scoped lang="scss">
@import '../../../../../Resources/views/V5/Component/icon/icon';
</style>

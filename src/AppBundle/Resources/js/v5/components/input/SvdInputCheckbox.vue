<script setup>
import { ref } from 'vue'
const emit = defineEmits(['click', 'input'])

const props = defineProps({
    /**
     * Valeur de la checkbox
     */
    value: {
        type: Boolean,
        default: false,
    },
    /**
     * Texte affiché
     */
    label: {
        type: String,
        required: true,
    },
    /**
     * Champ obligatoire
     */
    required: {
        type: Boolean,
        default: false,
    },
    /**
     * Nom de l'input
     */
    name: {
        type: String,
        required: true,
    },
    /**
     * Désactiver la checkbox
     */
    disabled: {
        type: Boolean,
        required: false,
        default: false,
    },
})
const id = ref(Math.random())
</script>

<template>
    <section>
        <div class="input-type checkbox">
            <input
                :disabled="disabled"
                :id="id"
                :name="name"
                type="checkbox"
                :checked="value"
                :aria-label="label"
                :aria-disabled="disabled"
                @click="emit('click')"
                @change="emit('input', $event.target.checked)"
            />
            <div :class="disabled ? 'disabled' : ''" class="input-label checkbox">
                <label :for="id">{{ label }}</label>
                <span class="is-required" v-if="required">&nbsp;*</span>
            </div>
        </div>
    </section>
</template>

<style scoped lang="scss">
@import '../../../../../Resources/views/V5/Component/input/input';
</style>

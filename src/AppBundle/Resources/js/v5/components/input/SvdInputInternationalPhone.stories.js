import SvdInputInternationalPhone from './SvdInputInternationalPhone.vue'

export default {
    components: { SvdInputInternationalPhone },
    component: SvdInputInternationalPhone,
    title: 'Input/Phone',
    args: { label: 'Label', name: 'name' },
    argTypes: {
        helper: {
            options: ['error', 'success', 'info'],
            mapping: {
                error: {
                    type: 'error',
                    label: "Message d'erreur",
                },
                success: {
                    type: 'success',
                    label: 'Message de succès',
                },
                info: {
                    type: 'info',
                    label: 'Message informatif',
                },
            },
            control: 'select',
        },
    },
}

export const Sandbox = {
    render: (args, { argTypes }) => ({
        components: { SvdInputInternationalPhone },
        props: Object.keys(argTypes),
        template: '<svd-input-international-phone v-bind="$props" v-on="$props" />',
    }),
    args: {},
}

export const Default = {
    render: (args, { argTypes }) => ({
        components: { SvdInputInternationalPhone },
        props: Object.keys(argTypes),
        template: '<svd-input-international-phone v-bind="$props" v-on="$props" />',
    }),
    args: {},
}

export const Requis = {
    render: (args, { argTypes }) => ({
        components: { SvdInputInternationalPhone },
        props: Object.keys(argTypes),
        template: '<svd-input-international-phone v-bind="$props" v-on="$props" />',
    }),
    args: {
        required: true,
    },
}

export const Disabled = {
    render: (args, { argTypes }) => ({
        components: { SvdInputInternationalPhone },
        props: Object.keys(argTypes),
        template: '<svd-input-international-phone v-bind="$props" v-on="$props" />',
    }),
    args: {
        disabled: true,
        value: '0600000000',
    },
}

export const Error = {
    render: (args, { argTypes }) => ({
        components: { SvdInputInternationalPhone },
        props: Object.keys(argTypes),
        template: '<svd-input-international-phone v-bind="$props" v-on="$props" />',
    }),
    args: {
        helper: {
            label: "Un message d'erreur",
            type: 'error',
        },
    },
}

export const Info = {
    render: (args, { argTypes }) => ({
        components: { SvdInputInternationalPhone },
        props: Object.keys(argTypes),
        template: '<svd-input-international-phone v-bind="$props" v-on="$props" />',
    }),
    args: {
        helper: {
            label: 'Un message informatif',
            type: 'info',
        },
    },
}

export const Success = {
    render: (args, { argTypes }) => ({
        components: { SvdInputInternationalPhone },
        props: Object.keys(argTypes),
        template: '<svd-input-international-phone v-bind="$props" v-on="$props" />',
    }),
    args: {
        helper: {
            label: 'Un success',
            type: 'success',
        },
    },
}
export const Placeholder = {
    render: (args, { argTypes }) => ({
        components: { SvdInputInternationalPhone },
        props: Object.keys(argTypes),
        template: '<svd-input-international-phone v-bind="$props" v-on="$props" />',
    }),
    args: {
        placeholder: 'Message si le champ est vide',
    },
}

export const DefaultValue = {
    render: (args, { argTypes }) => ({
        components: { SvdInputInternationalPhone },
        props: Object.keys(argTypes),
        template: '<svd-input-international-phone v-bind="$props" v-on="$props" />',
    }),
    args: {
        value: '0600000000',
    },
}

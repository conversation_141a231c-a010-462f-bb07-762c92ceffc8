<script setup>
import { computed } from 'vue'
const emit = defineEmits(['click'])

const props = defineProps({
    id: {
        type: String | Number,
        required: false,
        default: `id-${Math.random()}`,
    },
    name: {
        type: String,
        required: false,
    },
    value: {
        type: String | Number,
        required: false,
    },
    /**
     * Désactiver la checkbox
     */
    disabled: {
        type: Boolean,
        required: false,
        default: false,
    },
    /**
     * Champ obligatoire
     */
    required: {
        type: Boolean,
        default: false,
    },
    /**
     * Texte affiché
     */
    label: {
        type: String,
        required: false,
    },
    /**
     * Texte lu par les outils d'accessibilité
     */
    ariaLabel: {
        type: String,
        required: false,
    },
    small: {
        type: Boolean,
        default: false,
    },
})

const getAriaLabel = computed(() => {
    if (props.ariaLabel) {
        return props.ariaLabel
    }
    return props.label
})

const getClass = computed(() => {
    let classes = []
    if (props.disabled) {
        classes.push('disabled')
    }
    if (props.small) {
        classes.push('small')
    }
    return classes.join(' ')
})
</script>

<template>
    <section>
        <div class="input-type radio">
            <input
                :id="id"
                :name="name"
                :value="id"
                :class="{ small: small }"
                v-model="value"
                type="radio"
                :aria-label="getAriaLabel"
                @click="emit('click')"
            />

            <div v-if="label" :class="getClass" class="input-label radio">
                <label :for="id" v-html="label" />
                <span class="is-required" v-if="required">&nbsp;*</span>
            </div>
        </div>
    </section>
</template>

<style scoped lang="scss">
@import '../../../../../Resources/views/V5/Component/input/input';
</style>

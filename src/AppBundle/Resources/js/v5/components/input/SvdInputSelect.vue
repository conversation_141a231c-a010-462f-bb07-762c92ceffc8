<script setup>
const emit = defineEmits(['input'])

const props = defineProps({
    /**
     * Texte affiché
     */
    label: {
        type: String,
        required: true,
    },
    /**
     * Champ obligatoire
     */
    required: {
        type: Boolean,
        default: false,
    },
    /**
     * Texte à afficher sous l'input. Peut-être un message informatif, d'erreur ou de succès.
     */
    helper: {
        type: Object,
        required: false,
        validator: (value) =>
            Object.keys(value).length === 0 ||
            (['label', 'type'].every((key) => value.hasOwnProperty(key)) &&
                ['error', 'info', 'success'].includes(value.type)),
    },
    /**
     * Valeur assigné à l'input
     */
    value: {
        type: String | Number,
        required: false,
    },
    /**
     * Nom de l'input
     */
    name: {
        type: String,
        required: true,
    },
    /**
     * Liste des options disponibles dans le select
     */
    options: {
        type: Array,
        required: false,
        default: () => [
            {
                value: null,
                label: '',
            },
        ],
    },
    /**
     * Nom du champ pour afficher le label d'une option
     */
    labelName: {
        type: String,
        required: false,
        default: 'label',
    },
    /**
     * Nom du champ pour afficher la valeur d'une option
     */
    valueName: {
        type: String,
        required: false,
        default: 'value',
    },
    /**
     * Propriété required uniquement pour le visuel, gestion du formulaire à la main
     */
    fakeRequired: {
        type: Boolean,
        required: false,
        default: false,
    },
    /**
     * Propriété désactivée
     */
    disabled: {
        type: Boolean,
        required: false,
        default: false,
    },
})
</script>

<template>
    <div class="input-type">
        <div :class="disabled ? 'disabled' : ''" class="input-label">
            {{ label }}
            <span class="is-required" v-if="required">&nbsp;*</span>
        </div>
        <select
            :name="name"
            type="text"
            :value="value"
            @input="emit('input', $event.target.value)"
            :required="fakeRequired ? false : required"
            :disabled="disabled"
            :aria-label="label"
            :aria-disabled="disabled"
        >
            <template v-if="!$slots.default">
                <option v-for="option in options" :value="option[valueName]">{{ option[labelName] }}</option>
            </template>
            <slot />
        </select>
        <slot name="after_select" />
        <div class="helper">
            <span v-if="helper" :class="helper.type">{{ helper.label }}</span>
        </div>
    </div>
</template>

<style scoped lang="scss">
@import '../../../../../Resources/views/V5/Component/input/input';
</style>

import SvdTooltip from './SvdTooltip.vue'

export default {
    components: { SvdTooltip },
    component: SvdTooltip,
    title: 'Tooltip',
}

export const Default = {
    render: (args, { argTypes }) => ({
        props: Object.keys(argTypes),
        template:
            '<svd-tooltip v-bind="$props" v-on="$props">' +
            '<template #action>Cliquez ici</template>' +
            '<template #reaction>Texte magique</template>' +
            '</svd-tooltip>',
    }),
}

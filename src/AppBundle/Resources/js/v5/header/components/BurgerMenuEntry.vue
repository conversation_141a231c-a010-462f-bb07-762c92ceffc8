<template>
    <li
        v-if="'v5' === version"
        class="list-none rounded-[40px] text-[15px] cursor-pointer w-[260px] md:duration-300 group"
        :style="style"
        @click="openSubLevel"
        @mouseover="is_hover = true"
        @mouseleave="is_hover = false"
    >
        <!-- It's fine to have an "a" without an href when entry has no url (has children). It is considered in html5 as a "placeholder hyperlink" -->
        <a
            :href="entry.url"
            class="flex items-center justify-between leading-[24px] h-full py-1 w-full px-4 min-h-[40px]"
        >
            <div class="flex items-center leading-[24px] h-full w-full">
                <img
                    v-if="entry.icon"
                    class="w-6 h-6 mr-3.5"
                    :src="asset(entry.icon, 'static_images')"
                    :alt="`icon-${entry.label}`"
                    :style="{ filter: getFilterToApply(style.color) }"
                />
                <span class="font-semibold" :class="available_width[label_width]">
                    {{ entry.label }}
                    <span
                        data-context="burger-menu-badge"
                        v-if="hasBadge(entry) && getBadge(entry) !== ''"
                        :class="badge_class"
                    >
                        {{ getBadge(entry) }}
                    </span>
                </span>
            </div>
            <div v-if="entry.sections.length > 0" class="absolute w-8 h-8 right-[16px]">
                <arrow-right class="icon-50" fill="currentColor" />
            </div>
        </a>
    </li>

    <!-- template v3 -->
    <li
        v-else
        class="entry"
        @click="openSubLevel"
        :style="style"
        @mouseover="is_hover = true"
        @mouseleave="is_hover = false"
    >
        <a :href="entry.url">
            <div class="entry-name">
                <img
                    v-if="entry.icon"
                    :style="{ filter: getFilterToApply(style.color) }"
                    :src="asset(entry.icon, 'static_images')"
                    :alt="`icon-${entry.label}`"
                />
                <span
                    :style="{ color: style.color, maxWidth: `${label_width}px` }"
                    :class="[!entry.icon ? 'no-icon' : '']"
                >
                    {{ entry.label }}
                    <span
                        data-context="burger-menu-badge"
                        v-if="hasBadge(entry) && getBadge(entry) !== ''"
                        :class="badge_class"
                    >
                        {{ getBadge(entry) }}
                    </span>
                </span>
            </div>
            <div class="arrow-container">
                <div
                    v-if="entry.sections.length > 0"
                    :style="{ filter: getFilterToApply(style.color) }"
                    class="arrow"
                ></div>
            </div>
        </a>
    </li>
</template>

<script>
import arrowRight from '@/../icons/svd/light/arrow-right.svg'
import { mapActions } from 'pinia'
import { useBurgerMenuStore } from '@/v5/header/stores'
import { useCustomerInfoStore } from '@/shared/stores/customer_info'
import { asset } from '@/shared/filters'
import { getFilterToApply } from '@/common/transform_img_color'

const eligible_badge_urls = ['/mon-compte/mes-devis-et-offres']
export default {
    name: 'BurgerMenuEntry',
    components: { arrowRight },
    inject: ['version'],
    props: {
        entry: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            is_hover: false,
            available_width: {
                228: 'w-[228px]',
                203: 'w-[203px]',
                190: 'w-[190px]',
                165: 'w-[165px]',
            },
        }
    },
    computed: {
        text_color() {
            return this.is_hover
                ? this.entry.color
                    ? '#fff'
                    : '#494949'
                : this.entry.color
                ? `#${this.entry.color}`
                : '#494949'
        },
        background_color() {
            return this.is_hover ? (this.entry.color ? `#${this.entry.color}` : '#eeeeee') : 'transparent'
        },
        style() {
            return {
                color: this.text_color,
                backgroundColor: this.background_color,
            }
        },
        label_width() {
            let width = 228
            if (this.entry.icon) {
                width = width - 38
            }
            if (this.entry.sections.length > 0) {
                width = width - 25
            }
            return width
        },
        badge_class() {
            return 'v5' === this.version ? 'px-1 text-white bg-[#466edc] rounded-2xl text-xs font-bold ml-2.5' : 'badge'
        },
    },
    methods: {
        getFilterToApply,
        ...mapActions(useBurgerMenuStore, ['openEntry']),
        asset,
        openSubLevel() {
            if (this.entry.sections.length > 0) {
                this.openEntry(this.entry)
            }
        },
        hasBadge(entry) {
            return eligible_badge_urls.includes(entry.url)
        },
        getBadge(entry) {
            if (entry.url === '/mon-compte/mes-devis-et-offres') {
                const customer_infos = useCustomerInfoStore()
                return this.badgeCalculator(customer_infos?.quotes_info?.count)
            }
            return ''
        },
        badgeCalculator(value) {
            const parsed_value = parseInt(value)
            if (isNaN(parsed_value) || (parsed_value ?? 0) === 0) {
                return ''
            }

            if (parsed_value > 99) {
                return '99+'
            }

            return `${parsed_value}`
        },
    },
}
</script>

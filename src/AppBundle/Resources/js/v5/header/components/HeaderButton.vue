<template>
    <a
        :aria-label="ariaLabel"
        v-if="$attrs.hasOwnProperty('href')"
        v-on="$listeners"
        :style="{ height: icon_size + 'px', width: icon_size + 'px' }"
        :class="'v5' === version ? 'relative rounded-full hover:bg-[#36393F] duration-200' : 'action'"
    >
        <svg-icon :icon="icon" :size="icon_size" />
        <span data-context="badge" v-if="badge.length > 0" :class="badge_class">{{ badge }}</span>
    </a>
    <span
        v-else
        :style="{ height: icon_size + 'px', width: icon_size + 'px' }"
        v-on="$listeners"
        :class="'v5' === version ? '' : 'action cursor-pointer'"
    >
        <svg-icon
            :alt="ariaLabel"
            :icon="icon"
            :size="icon_size"
            :class="'v5' === version ? 'rounded-full hover:bg-[#36393F] duration-200 cursor-pointer' : ''"
        />
    </span>
</template>

<script>
import { computed, inject } from 'vue'
import SvgIcon from '@/shared/components/SvgIcon.vue'

export default {
    name: 'HeaderButton',
    components: {
        SvgIcon,
    },
    props: {
        icon: {
            type: String,
            require: true,
        },
        large: {
            type: Boolean,
            default: () => false,
        },
        badge: {
            type: String,
            default: () => '',
        },
        ariaLabel: {
            type: String,
            required: true,
        },
    },
    setup(props) {
        const version = inject('version')

        const icon_size = computed(() => (props.large ? 50 : 38))
        const badge_class = computed(() =>
            'v5' === version
                ? props.large
                    ? 'absolute px-1 bg-[#466edc] rounded-2xl text-xs font-bold left-9 top-0.5'
                    : 'absolute px-1 bg-[#466edc] rounded-2xl text-xs font-bold -right-0.5 top-0'
                : 'counter'
        )

        return {
            version,
            icon_size,
            badge_class,
        }
    },
}
</script>

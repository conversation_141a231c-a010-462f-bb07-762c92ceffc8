<template>
    <!-- v5 -->
    <section
        v-if="'v5' === version"
        class="hidden items-center h-[112px] bg-[#101214] pl-[15px] pr-[30px] gap-2.5 md:flex"
        data-context="top"
    >
        <a
            href="/"
            class="block h-[112px] w-auto min-w-[277px] site-logo"
            title="Son-Vidéo.com la référence hi-fi et home-cinéma"
            data-context="logo"
        ></a>

        <search-desktop-input />
        <header-toolbar large />
    </section>
    <!-- v3 -->
    <section v-else class="top align-items-center" data-context="top" id="header-desktop-app">
        <a
            aria-label="Retour vers la page d'accueil"
            href="/"
            class="site-logo"
            title="Son-Vidéo.com la référence hi-fi et home-cinéma"
            data-context="logo"
        ></a>
        <search-desktop-input />
        <header-toolbar large />
    </section>
</template>

<script>
import { inject } from 'vue'
import SearchDesktopInput from '@/v5/header/components/SearchDesktopInput.vue'
import HeaderToolbar from '@/v5/header/components/HeaderToolbar.vue'

export default {
    name: 'HeaderDesktop',
    components: {
        SearchDesktopInput,
        HeaderToolbar,
    },
    setup() {
        const version = inject('version')

        return {
            version,
        }
    },
}
</script>

<template>
    <!-- v5 -->

    <div
        class="flex items-center w-full h-[60px] bg-[#101214] pr-1.5 min-[377px]:px-1.5 md:hidden"
        data-context="mobile"
        v-if="'v5' === version"
    >
        <section class="flex items-center" data-context="left">
            <div class="block" @click="activateBurgerMenu">
                <svg-icon :icon="Large.Bars" class="cursor-pointer -mx-1 min-[377px]:mx-0" data-context="burger" />
            </div>

            <a
                href="/"
                class="block h-[43.41px] w-[158px] -ml-[5px] site-logo-mobile mt-px sm:mt-0"
                title="Son-Vidéo.com la référence hi-fi et home-cinéma"
                data-context="mobile-logo"
            ></a>
        </section>

        <header-toolbar />
    </div>

    <!-- v3 -->

    <div class="mobile" data-context="mobile" v-else>
        <section class="left" data-context="left">
            <svg-icon
                :icon="Large.Bars"
                class="menu-trigger-mobile icon-50 cursor-pointer"
                @click="activateBurgerMenu"
                data-context="burger"
            />

            <a
                href="/"
                class="mobile-logo"
                title="Son-Vidéo.com la référence hi-fi et home-cinéma"
                data-context="mobile-logo"
            ></a>
        </section>

        <header-toolbar />
    </div>
</template>

<script>
import { inject } from 'vue'
import { useBurgerMenuStore } from '@/v5/header/stores'
import { Large, Medium } from '@/shared/referential/SvdIconsReferential'

export default {
    name: 'HeaderMobile',
    components: {
        SvgIcon: () => import('@/shared/components/SvgIcon.vue'),
        HeaderToolbar: () => import('./HeaderToolbar.vue'),
    },
    setup() {
        const version = inject('version')
        const { activate: activateBurgerMenu } = useBurgerMenuStore()

        return {
            version,
            Large,
            Medium,
            activateBurgerMenu,
        }
    },
}
</script>

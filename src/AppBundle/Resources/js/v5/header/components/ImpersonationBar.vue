<template>
    <!-- v5 -->
    <div
        v-if="'v5' === version"
        class="fixed bottom-0 inset-x-0 text-black z-10 text-sm w-full md:relative md:flex"
        data-context="impersonation"
    >
        <div
            class="bg-red-500 text-white p-2 font-bold uppercase flex items-center whitespace-nowrap md:px-6"
            data-context="badge"
        >
            Mode conseiller
        </div>
        <div class="p-2 flex flex-col gap-2 text-center bg-red-100 md:grow md:flex-row md:flex-row md:pl-6">
            <div class="flex flex-col md:flex-row md:grow md:flex-wrap md:items-center md:gap-x-1">
                <span>Vous êtes connecté en tant que</span>
                <span>{{ customer_info.impersonation_username }}</span>
            </div>
            <tw-button-link href="/mon-compte/deconnexion" class="whitespace-nowrap" data-context="logout"
                >Me déconnecter</tw-button-link
            >
        </div>
    </div>
    <!-- v3 -->
    <div v-else id="impersonation" data-context="impersonation">
        <div class="impersonation-badge" data-context="badge">Mode conseiller</div>
        <div class="impersonation-content">
            <div class="message">
                <span>Vous êtes connecté en tant que</span>
                <span>{{ customer_info.impersonation_username }}</span>
            </div>
            <a aria-label="Se déconnecter" href="/mon-compte/deconnexion" type="button" data-context="logout"
                >Me déconnecter</a
            >
        </div>
    </div>
</template>

<script>
import { storeToRefs } from 'pinia'
import { useCustomerInfoStore } from '@/shared/stores/customer_info'
import TwButtonLink from '@/shared/components/TwButtonLink.vue'

export default {
    name: 'ImpersonationBar',
    components: { TwButtonLink },
    inject: ['version'],
    setup() {
        const store = useCustomerInfoStore()
        const { customer_info } = storeToRefs(store)

        return { customer_info }
    },
}
</script>

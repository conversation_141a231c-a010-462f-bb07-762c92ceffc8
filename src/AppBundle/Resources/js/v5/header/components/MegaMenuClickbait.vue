<template>
    <!-- "clickbait" is a forbidden word. It should never appear in rendered html on the site. Use "cb" instead. -->
    <li v-if="'v5' === version" data-context="mega-menu-cb">
        <a :aria-label="`Lien vers ${item.name}`" :href="item.url" class="flex flex-col">
            <img
                class="h-[228px] object-cover w-full mb-4"
                :src="item.media | asset('static_images')"
                :title="item.name"
                :alt="item.name"
            />
            <span class="text-[17px] font-semibold mb-2"
                ><span
                    class="bg-gradient-to-r from-black to-black bg-left-bottom bg-no-repeat transition-all duration-200 hover:bg-[bottom:1px_left:0] bg-[length:100%_0px] hover:bg-[length:100%_2px]"
                    >{{ item.name }}</span
                ></span
            >
            <span class="text-[15px] leading-[22px]">{{ item.description }}</span>
        </a>
    </li>

    <li v-else data-context="mega-menu-cb" class="mega-menu-cb">
        <a :aria-label="`Lien vers ${item.name}`" :href="item.url">
            <img :src="item.media | asset('static_images')" :title="item.name" :alt="item.name" />
            <span class="mega-menu-section"
                ><span>{{ item.name }}</span></span
            >
            <span class="mega-menu-item"
                ><span>{{ item.description }}</span></span
            >
        </a>
    </li>
</template>

<script>
export default {
    name: 'MegaMenuClickbait',
    inject: ['version'],
    props: {
        item: {
            type: Object,
            required: true,
            validator: (v) => ['name', 'media', 'description', 'url'].every((k) => v.hasOwnProperty(k)),
        },
    },
}
</script>

import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/shared/api/handler'

export const useCartSummaryStore = defineStore('cart_summary', () => {
    const summary = ref(null)

    const retrieveSummary = async () => {
        try {
            const response = await api.get('/mon-panier/resume')
            updateSummary(response)
        } catch (Error) {
            updateSummary(null)
        }
    }

    const updateSummary = (new_value) => {
        summary.value = new_value
    }

    // execute immediately (equivalent to the "created" hook in the options API)
    void retrieveSummary()

    return { summary, retrieveSummary, updateSummary }
})

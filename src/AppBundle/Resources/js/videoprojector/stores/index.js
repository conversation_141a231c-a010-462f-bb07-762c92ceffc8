import { defineStore } from 'pinia'
import { ref } from 'vue';

export const useVideoProjectorStore = defineStore('video_projector', () =>{
    const video_projectors = ref([])
    const video_projector = ref(null)
    const video_projector_distance = ref(0)
    const screen_data = ref(null)

    const initializeData = (data) => {
        video_projectors.value = data.video_projectors;
        video_projector.value = data.video_projector;
        video_projector_distance.value = data.video_projector_distance;
        screen_data.value = data.screen_data;
    }
    const updateProjector = (index) => {
        video_projector.value = video_projectors.value[index];
    }

    return {
        video_projectors,
        video_projector,
        video_projector_distance,
        screen_data,
        initializeData,
        updateProjector
    }
})
$animation_duration: 0.2s;
$animation_duration_long: 2s;

$transition_all_02: all $animation_duration ease-in-out, border-radius 0s;
$fade_in: fadeIn $animation_duration;
$fade_out: fadeOut $animation_duration;
$transition_opacity: opacity $animation_duration ease-in-out;
$transition_height: max-height $animation_duration ease-in-out;
$pulse: pulseanim $animation_duration_long ease-in-out infinite;

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@keyframes pulseanim {
    0% {
        transform: scale(0);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

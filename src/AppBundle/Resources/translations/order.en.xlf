<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
    <file source-language="fr" target-language="en" datatype="plaintext" original="file.ext">
        <body>
            <trans-unit id="order_free">
                <source>order_free</source>
                <target>Free</target>
            </trans-unit>
            <trans-unit id="order_checkout_order">
                <source>order_checkout_order</source>
                <target>Place order</target>
            </trans-unit>
            <trans-unit id="order_summary_title">
                <source>order_summary_title</source>
                <target>Your order</target>
            </trans-unit>
            <trans-unit id="order_summary_no_article">
                <source>order_summary_no_article</source>
                <target>No article in shopping cart</target>
            </trans-unit>
            <trans-unit id="order_amount_selling_price">
                <source>order_amount_selling_price</source>
                <target>Sub-total:</target>
            </trans-unit>
            <trans-unit id="order_amount_ecotax">
                <source>order_amount_ecotax</source>
                <target>with ecotax:</target>
            </trans-unit>
            <trans-unit id="ecotax_included">
                <source>ecotax_included</source>
                <target>Ecotax (included):</target>
            </trans-unit>
            <trans-unit id="order_summary_delivery_fee">
                <source>order_summary_delivery_fee</source>
                <target>Delivery fee:</target>
            </trans-unit>
            <trans-unit id="order_summary_store_fee">
                <source>order_summary_store_fee</source>
                <target>Extra store %name% fee:</target>
            </trans-unit>
            <trans-unit id="order_summary_payment_fee">
                <source>order_summary_payment_fee</source>
                <target>Extra payment method fee:</target>
            </trans-unit>
            <trans-unit id="order_summary_total">
                <source>order_summary_total</source>
                <target>Total (tax incl.):</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_details">
                <source>order_checkout_contact_details</source>
                <target>Contact details</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_details_billing_address">
                <source>order_checkout_contact_details_billing_address</source>
                <target>Billing address</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_details_shipping_address">
                <source>order_checkout_contact_details_shipping_address</source>
                <target>Shipping address</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_details_select_another_address">
                <source>order_checkout_contact_details_select_another_address</source>
                <target>Select another address</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_details_use_a_different_delivery_address">
                <source>order_checkout_contact_details_use_a_different_delivery_address</source>
                <target>Use a different delivery address</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_details_use_a_different_billing_address">
                <source>order_checkout_contact_details_use_a_different_billing_address</source>
                <target>Use a different billing address</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_details_validate">
                <source>order_checkout_contact_details_validate</source>
                <target>Validate these adresses</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_reset">
                <source>order_checkout_contact_reset</source>
                <target>Change addresses</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_details_select_billing_address">
                <source>order_checkout_contact_details_select_billing_address</source>
                <target>Select a billing address</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_details_delivery_address">
                <source>order_checkout_contact_details_delivery_address</source>
                <target>Delivery address</target>
            </trans-unit>
            <trans-unit id="order_checkout_contact_details_select_delivery_address">
                <source>order_checkout_contact_details_select_delivery_address</source>
                <target>Select a delivery address</target>
            </trans-unit>
            <trans-unit id="order_checkout_delivery_mode">
                <source>order_checkout_delivery_mode</source>
                <target>Delivery</target>
            </trans-unit>
            <trans-unit id="order_checkout_payment">
                <source>order_checkout_payment</source>
                <target>Payment</target>
            </trans-unit>
            <trans-unit id="order_checkout_payment_not_found">
                <source>order_checkout_payment_not_found</source>
                <target>No eligible payment method found by the system.</target>
            </trans-unit>
            <trans-unit id="order_checkout_shipment_validation_btn">
                <source>order_checkout_shipment_validation_btn</source>
                <target>Validate this delivery method</target>
            </trans-unit>
            <trans-unit id="order_checkout_shipment_shipment_delivery">
                <source>order_checkout_shipment_shipment_delivery</source>
                <target>Shipment delivery</target>
            </trans-unit>
            <trans-unit id="order_checkout_shipment_relay_package">
                <source>order_checkout_shipment_relay_package</source>
                <target>Shipping relay point</target>
            </trans-unit>
            <trans-unit id="order_checkout_shipment_pickup_in_store">
                <source>order_checkout_shipment_pickup_in_store</source>
                <target>Shipping in a store Son-Vidéo.com</target>
            </trans-unit>
            <trans-unit id="order_checkout_shipment_pickup_in_store_description">
                <source>order_checkout_shipment_pickup_in_store_description</source>
                <target>
                    You receive an SMS and a confirmation email as soon as your order is available in store.
                </target>
            </trans-unit>
            <trans-unit id="order_checkout_shipment_show_all_stores">
                <source>order_checkout_shipment_show_all_stores</source>
                <target>Show all stores Son-Vidéo.com</target>
            </trans-unit>
            <trans-unit id="order_checkout_shipment_hide_stores">
                <source>order_checkout_shipment_hide_stores</source>
                <target>Hide stores</target>
            </trans-unit>
            <trans-unit id="order_checkout_shipment_go_to_store">
                <source>order_checkout_shipment_go_to_store</source>
                <target>Go to store</target>
            </trans-unit>
            <trans-unit id="order_checkout_shipment_reset_btn">
                <source>order_checkout_shipment_reset_btn</source>
                <target>Change shipment method</target>
            </trans-unit>
            <trans-unit id="create_basket_error">
                <source>create_basket_error</source>
                <target>Sorry, an item in your order is finally no longer available</target>
            </trans-unit>
            <trans-unit id="handle_order_api_error_message">
                <source>handle_order_api_error_message</source>
                <target>Sorry, an error occurred</target>
            </trans-unit>
            <trans-unit id="choose_payment_method">
                <source>choose_payment_method</source>
                <target>Choose your payment method</target>
            </trans-unit>
            <trans-unit id="choosen_payment_method">
                <source>choosen_payment_method</source>
                <target>Payment method chosen</target>
            </trans-unit>
            <trans-unit id="order_checkout_payment_validate_btn">
                <source>order_checkout_payment_validate_btn</source>
                <target>Confirm and pay</target>
            </trans-unit>
            <trans-unit id="comment_about_your_order">
                <source>comment_about_your_order</source>
                <target>Comment about your order</target>
            </trans-unit>
            <trans-unit id="order_cart_change_detected_title">
                <source>order_cart_change_detected_title</source>
                <target>Shopping cart updated</target>
            </trans-unit>
            <trans-unit id="order_cart_change_detected_message">
                <source>order_cart_change_detected_message</source>
                <target>
                    You have modified your shopping cart. Order summary and payment method have been updated accordingly.
                    Thanks to check it's correct before to proceed.
               </target>
            </trans-unit>
            <trans-unit id="order_cart_and_shipment_change_detected_message">
                <source>order_cart_and_shipment_change_detected_message</source>
                <target>
                    You have modified your shopping cart and shipment methods have changed.
                    Thanks to choose again a shipment method.
               </target>
            </trans-unit>
            <trans-unit id="order_cart_change_detected_btn">
                <source>order_cart_change_detected_btn</source>
                <target>Ok</target>
            </trans-unit>
            <trans-unit id="contact_details">
                <source>contact_details</source>
                <target>Contact details</target>
            </trans-unit>
            <trans-unit id="billing_contact_details">
                <source>billing_contact_details</source>
                <target>Billing contact details</target>
            </trans-unit>
            <trans-unit id="shipment_by_carrier">
                <source>shipment_by_carrier</source>
                <target>Shipment by carrier</target>
            </trans-unit>
            <trans-unit id="store_pick_up">
                <source>store_pick_up</source>
                <target>Store pick-up</target>
            </trans-unit>
            <trans-unit id="location_pick_up">
                <source>location_pick_up</source>
                <target>Location pick-up</target>
            </trans-unit>
            <trans-unit id="order_modify_btn">
                <source>order_modify_btn</source>
                <target>Modify</target>
            </trans-unit>
            <trans-unit id="promotional_code">
                <source>promotional_code</source>
                <target>Promotional code</target>
            </trans-unit>
            <trans-unit id="promotional_code_label">
                <source>promotional_code_label</source>
                <target>Promotional code %code%</target>
            </trans-unit>
            <trans-unit id="promotional_code_placeholder">
                <source>promotional_code_placeholder</source>
                <target>Enter your promotional code</target>
            </trans-unit>
            <trans-unit id="empty_promotional_code">
                <source>empty_promotional_code</source>
                <target>No promotional code entered</target>
            </trans-unit>
            <trans-unit id="promotion_addition_success">
                <source>promotion_addition_success</source>
                <target>Promotion successfully added</target>
            </trans-unit>
            <trans-unit id="order_total">
                <source>order_total</source>
                <target>Total</target>
            </trans-unit>
            <trans-unit id="order_total_with_separator">
                <source>order_total_with_separator</source>
                <target>Total:</target>
            </trans-unit>
            <trans-unit id="empty_shopping_cart">
                <source>empty_shopping_cart</source>
                <target>Your cart is empty</target>
            </trans-unit>
            <trans-unit id="carry_on_shopping">
                <source>carry_on_shopping</source>
                <target>Carry on shopping</target>
            </trans-unit>
            <trans-unit id="remove_article">
                <source>remove_article</source>
                <target>Remove article</target>
            </trans-unit>
            <trans-unit id="price_per_unit">
                <source>price_per_unit</source>
                <target>Price per unit:</target>
            </trans-unit>
            <trans-unit id="quantity">
                <source>quantity</source>
                <target>Quantity</target>
            </trans-unit>
            <trans-unit id="quantity_with_separator">
                <source>quantity_with_separator</source>
                <target>Quantity:</target>
            </trans-unit>
            <trans-unit id="save_cart">
                <source>save_cart</source>
                <target>Save my cart</target>
            </trans-unit>
            <trans-unit id="discount">
                <source>discount</source>
                <target>Discount</target>
            </trans-unit>
            <trans-unit id="gift">
                <source>gift</source>
                <target>Gift</target>
            </trans-unit>
            <trans-unit id="offer_no_num">
                <source>offer_no_num</source>
                <target>Offer No. %num%</target>
            </trans-unit>
            <trans-unit id="order_number_num">
                <source>order_number_num</source>
                <target>Order No. %no%</target>
            </trans-unit>
            <trans-unit id="billing_address">
                <source>billing_address</source>
                <target>Billing address</target>
            </trans-unit>
            <trans-unit id="shipping_address">
                <source>shipping_address</source>
                <target>Shipping address</target>
            </trans-unit>
            <trans-unit id="sending_address">
                <source>sending_address</source>
                <target>Sending address</target>
            </trans-unit>
            <trans-unit id="ON_GOING">
                <source>ON_GOING</source>
                <target>On going</target>
            </trans-unit>
            <trans-unit id="DELIVERED">
                <source>DELIVERED</source>
                <target>Delivered</target>
            </trans-unit>
            <trans-unit id="ORDER_DELIVERED">
                <source>ORDER_DELIVERED</source>
                <target>Delivered</target>
            </trans-unit>
            <trans-unit id="LOST">
                <source>LOST</source>
                <target>Lost</target>
            </trans-unit>
            <trans-unit id="BROKEN">
                <source>BROKEN</source>
                <target>Broken</target>
            </trans-unit>
            <trans-unit id="ISSUE">
                <source>ISSUE</source>
                <target>Issue</target>
            </trans-unit>
            <trans-unit id="LATE_DELIVERY">
                <source>LATE_DELIVERY</source>
                <target>Late delivery</target>
            </trans-unit>
            <trans-unit id="AWAITING_PAYMENT">
                <source>AWAITING_PAYMENT</source>
                <target>Awaiting payment</target>
            </trans-unit>
            <trans-unit id="BEING_PROCESSED">
                <source>BEING_PROCESSED</source>
                <target>Being processed</target>
            </trans-unit>
            <trans-unit id="BEING_PREPARED">
                <source>BEING_PREPARED</source>
                <target>Being prepared</target>
            </trans-unit>
            <trans-unit id="PARTIALLY_SHIPPED">
                <source>PARTIALLY_SHIPPED</source>
                <target>Partially shipped</target>
            </trans-unit>
            <trans-unit id="SHIPPED">
                <source>SHIPPED</source>
                <target>Shipped</target>
            </trans-unit>
            <trans-unit id="CANCELLED">
                <source>CANCELLED</source>
                <target>Canceled</target>
            </trans-unit>
            <trans-unit id="AVAILABLE">
                <source>AVAILABLE</source>
                <target>Available in store</target>
            </trans-unit>
            <trans-unit id="PICKED_UP">
                <source>PICKED_UP</source>
                <target>Picked up</target>
            </trans-unit>
            <trans-unit id="IN_TRANSIT">
                <source>IN_TRANSIT</source>
                <target>In transit</target>
            </trans-unit>
            <trans-unit id="order_AWAITING_PAYMENT">
                <source>order_AWAITING_PAYMENT</source>
                <target>Order awaiting payment.</target>
            </trans-unit>
            <trans-unit id="order_BEING_PROCESSED">
                <source>order_BEING_PROCESSED</source>
                <target>Order being processed.</target>
            </trans-unit>
            <trans-unit id="order_BEING_PREPARED">
                <source>order_BEING_PREPARED</source>
                <target>Order being prepared.</target>
            </trans-unit>
            <trans-unit id="order_PARTIALLY_SHIPPED">
                <source>order_PARTIALLY_SHIPPED</source>
                <target>Order partially shipped.</target>
            </trans-unit>
            <trans-unit id="order_SHIPPED">
                <source>order_SHIPPED</source>
                <target>Order shipped.</target>
            </trans-unit>
            <trans-unit id="order_CANCELLED">
                <source>order_CANCELLED</source>
                <target>Order canceled.</target>
            </trans-unit>
            <trans-unit id="order_ABANDONED">
                <source>order_ABANDONED</source>
                <target>Order abandoned.</target>
            </trans-unit>
            <trans-unit id="order_content">
                <source>order_content</source>
                <target>Order content</target>
            </trans-unit>
            <trans-unit id="num_order">
                <source>num_order</source>
                <target>No. of order</target>
            </trans-unit>
            <trans-unit id="order_placed_on">
                <source>order_placed_on</source>
                <target>Order placed on</target>
            </trans-unit>
            <trans-unit id="no_detail">
                <source>no_detail</source>
                <target>No detail</target>
            </trans-unit>
            <trans-unit id="see_detail">
                <source>see_detail</source>
                <target>See detail</target>
            </trans-unit>
            <trans-unit id="order_confirmation">
                <source>order_confirmation</source>
                <target>Order has been confirmed</target>
            </trans-unit>
            <trans-unit id="my_order">
                <source>my_order</source>
                <target>My order</target>
            </trans-unit>
            <trans-unit id="order_number">
                <source>order_number</source>
                <target>Order number:</target>
            </trans-unit>
            <trans-unit id="order_number_simple">
                <source>order_number_simple</source>
                <target>Order number</target>
            </trans-unit>
            <trans-unit id="order_abandonned_message">
                <source>order_abandonned_message</source>
                <target>
                    <![CDATA[
                        <p>It may be due to thresold exceeds, network issue, or you may have voluntarily stopped your transaction</p>
                        <p>Contact our customer service if you want to resume your payment</p>
                    ]]>
                </target>
            </trans-unit>
            <trans-unit id="order_not_exists">
                <source>order_not_exists</source>
                <target>The customer order "%customer_order_id%" doesn't exist.</target>
            </trans-unit>
            <trans-unit id="your_order_is_being_processed">
                <source>your_order_is_being_processed</source>
                <target>
                    <![CDATA[
                        <p>You order is being processed</p>
                        <p>You'll be redirected.</p>
                    ]]>
                </target>
            </trans-unit>
            <trans-unit id="gift_card_amount_does_not_cover_order_amount_choose_a_complementary_payment_means">
                <source>gift_card_amount_does_not_cover_order_amount_choose_a_complementary_payment_means</source>
                <target>The gift card does not cover your order balance. Thanks to choose a complementary payment means:</target>
            </trans-unit>
            <trans-unit id="i_accept_the_term_and_conditions_of_son_video">
                <source>i_accept_the_term_and_conditions_of_son_video</source>
                <target><![CDATA[I accept the <a target="_blank" href="%url%">term and conditions of Son-Vidéo.com</a>]]></target>
            </trans-unit>
            <trans-unit id="term_and_conditions_validation">
                <source>term_and_conditions_validation</source>
                <target>Terms and conditions validation</target>
            </trans-unit>
            <trans-unit id="your_gift_card_has_been_added_to_your_order">
                <source>your_gift_card_has_been_added_to_your_order</source>
                <target>Votre carte cadeau d'un montant de %amount% à été ajoutée à votre commande</target>
            </trans-unit>
            <trans-unit id="remove_gift_card_and_choose_another_payment_means">
                <source>remove_gift_card_and_choose_another_payment_means</source>
                <target>Remove gift card and choose another payment means</target>
            </trans-unit>
            <trans-unit id="gift_card_is_invalid">
                <source>gift_card_is_invalid</source>
                <target>
                    <![CDATA[
                        <strong>The gift card number you entered is invalid.</strong><br>
                        <small>Enter another number or select another payment means.</small>
                    ]]>
                </target>
            </trans-unit>
            <trans-unit id="gift_card_is_expired">
                <source>gift_card_is_expired</source>
                <target>
                    <![CDATA[
                        <strong>The gift card entered is invalid or has expired.</strong><br>
                        <small>Enter another number or select another payment means.</small>
                    ]]>
                </target>
            </trans-unit>
            <trans-unit id="select_a_payment_means">
                <source>select_a_payment_means</source>
                <target>Please choose a payment means</target>
            </trans-unit>
            <trans-unit id="select_a_complementary_payment_means">
                <source>select_a_complementary_payment_means</source>
                <target>PLease, select a complementary payment means</target>
            </trans-unit>
            <trans-unit id="accept_the_term_and_conditions">
                <source>accept_the_term_and_conditions</source>
                <target>Please, accept the term and conditions.</target>
            </trans-unit>
            <trans-unit id="free_shipping">
                <source>free_shipping</source>
                <target>Free shipping</target>
            </trans-unit>
            <trans-unit id="free_shipping_mondial_relay">
                <source>free_shipping_mondial_relay</source>
                <target>Withdrawal in relay point offered</target>
            </trans-unit>
            <trans-unit id="from_amount_purchase">
                <source>from_amount_purchase</source>
                <target>From %from_amount% of purchase</target>
            </trans-unit>
            <trans-unit id="estimated_delivery_cost">
                <source>estimated_delivery_cost</source>
                <target>Estimated shipping costs<![CDATA[&nbsp;]]>:</target>
            </trans-unit>
            <trans-unit id="estimated_delivery_cost_note">
                <source>estimated_delivery_cost_note</source>
                <target>Calculated on the basis of your delivery address</target>
            </trans-unit>
            <trans-unit id="my_invoices">
                <source>my_invoices</source>
                <target>My invoices</target>
            </trans-unit>
            <trans-unit id="no_invoice">
                <source>no_invoice</source>
                <target>No invoice</target>
            </trans-unit>
            <trans-unit id="invoice_number">
                <source>invoice_number</source>
                <target><![CDATA[Invoice No.<strong>%number%</strong>]]></target>
            </trans-unit>
            <trans-unit id="invoice_number_other">
                <source>invoice_number_other</source>
                <target>Invoice No.%number%</target>
            </trans-unit>
            <trans-unit id="parcel_shipped">
                <source>parcel_shipped</source>
                <target>Parcel shipped</target>
            </trans-unit>
            <trans-unit id="my_credit_notes">
                <source>my_credit_notes</source>
                <target>My credit notes</target>
            </trans-unit>
            <trans-unit id="credit_note_number">
                <source>credit_note_number</source>
                <target><![CDATA[Credit note No.<strong>%number%</strong>]]></target>
            </trans-unit>
            <trans-unit id="my_delivery_notes">
                <source>my_delivery_notes</source>
                <target>My delivery notes</target>
            </trans-unit>
            <trans-unit id="delivery_note_number">
                <source>delivery_note_number</source>
                <target>
                    <![CDATA[
                        Delivery note No.<strong>%number%</strong><br>
                        Shipped on %date%
                    ]]>
                </target>
            </trans-unit>
            <trans-unit id="delivery_note_number_other">
                <source>delivery_note_number_other</source>
                <target>Delivery note No.%number%</target>
            </trans-unit>
            <trans-unit id="shipped_on">
                <source>shipped_on</source>
                <target>Shipped on : %number%</target>
            </trans-unit>
            <trans-unit id="picked_up_on">
                <source>picked_up_on</source>
                <target>Picked up on : %number%</target>
            </trans-unit>
            <trans-unit id="my_return_notes">
                <source>my_return_notes</source>
                <target>My return notes</target>
            </trans-unit>
            <trans-unit id="return_note_number">
                <source>return_note_number</source>
                <target><![CDATA[Return note No.<strong>%number%</strong>]]></target>
            </trans-unit>
            <trans-unit id="return_number">
                <source>return_number</source>
                <target>Return number</target>
            </trans-unit>
            <trans-unit id="return_note_number_other">
                <source>return_note_number_other</source>
                <target>Return note No.%number%</target>
            </trans-unit>
            <trans-unit id="message_author">
                <source>message_author</source>
                <target>{0}<![CDATA[Posted by <strong>Son-Vidéo.com</strong> on <strong>%date%</strong>]]>|{1}<![CDATA[Posted by <strong>Vous-même</strong> on <strong>%date%</strong>]]></target>
            </trans-unit>
            <trans-unit id="messages">
                <source>messages</source>
                <target>Messages</target>
            </trans-unit>
            <trans-unit id="send_a_message">
                <source>send_a_message</source>
                <target>
                    <![CDATA[
                    <div class="title-bg-lightgrey">Send a message</div>
                    <p>Please use the form below to exchange on your order with our customer service:</p>
                ]]>
                </target>
            </trans-unit>
            <trans-unit id="add_new_customer_order_message">
                <source>add_new_customer_order_message</source>
                <target>Send</target>
            </trans-unit>
            <trans-unit id="your_messages">
                <source>your_messages</source>
                <target>Your messages</target>
            </trans-unit>
            <trans-unit id="your_message_has_been_sent_successfully">
                <source>your_message_has_been_sent_successfully</source>
                <target>Your message has been sent successfully</target>
            </trans-unit>
            <trans-unit id="an_error_occurred_while_attempting_to_save_your_message">
                <source>an_error_occurred_while_attempting_to_save_your_message</source>
                <target>An error occurred while saving your message. Try again or contact our customer service</target>
            </trans-unit>
            <trans-unit id="enter_your_message">
                <source>enter_your_message</source>
                <target>Enter your message</target>
            </trans-unit>
            <trans-unit id="select_a_pickup_location">
                <source>select_a_pickup_location</source>
                <target>Select a pickup location</target>
            </trans-unit>
            <trans-unit id="enter_a_location_near_your_wanted_pickup_location">
                <source>enter_a_location_near_your_wanted_pickup_location</source>
                <target>Enter below an address near your wanted pickup location:</target>
            </trans-unit>
            <trans-unit id="select_this_location">
                <source>select_this_location</source>
                <target>Choose this location</target>
            </trans-unit>
            <trans-unit id="select_your_pickup_location">
                <source>select_your_pickup_location</source>
                <target>Choose your pickup location</target>
            </trans-unit>
            <trans-unit id="no_eligible_pickup_location_found_near_this_address">
                <source>no_eligible_pickup_location_found_near_this_address</source>
                <target>No eligible pickup location found near this address</target>
            </trans-unit>
            <trans-unit id="enter_valid_zip_code">
                <source>enter_valid_zip_code</source>
                <target>Please enter a valid zip code</target>
            </trans-unit>
            <trans-unit id="city_name_is_mandatory">
                <source>city_name_is_mandatory</source>
                <target>You need to provide a city name</target>
            </trans-unit>
            <trans-unit id="with_disabled_access">
                <source>with_disabled_access</source>
                <target>With disabled access</target>
            </trans-unit>
            <trans-unit id="domicile">
                <source>domicile</source>
                <target>DOMICILE</target>
            </trans-unit>
            <trans-unit id="sorry_none_product_is_found_in_order_to_make_a_return">
                <source>sorry_none_product_is_found_in_order_to_make_a_return</source>
                <target>Sorry none product is found in order to make a return.</target>
            </trans-unit>
            <trans-unit id="return_some_products">
                <source>return_some_products</source>
                <target>Return some products</target>
            </trans-unit>
            <trans-unit id="delivery_note_number_short">
                <source>delivery_note_number_short</source>
                <target>DN Number</target>
            </trans-unit>
            <trans-unit id="shipping_date">
                <source>shipping_date</source>
                <target>Shipping date</target>
            </trans-unit>
            <trans-unit id="delivery_date">
                <source>delivery_date</source>
                <target>Delivery date</target>
            </trans-unit>
            <trans-unit id="number_to_return">
                <source>number_to_return</source>
                <target>Number to return</target>
            </trans-unit>
            <trans-unit id="you_dont_choose_any_article">
                <source>you_dont_choose_any_article</source>
                <target>You don't choose any article</target>
            </trans-unit>
            <trans-unit id="reason">
                <source>reason</source>
                <target>Reason</target>
            </trans-unit>
            <trans-unit id="withdrawal">
                <source>withdrawal</source>
                <target>Withdrawal</target>
            </trans-unit>
            <trans-unit id="breakdown_at_unpacking">
                <source>breakdown_at_unpacking</source>
                <target>Breakdown at unpacking</target>
            </trans-unit>
            <trans-unit id="shipment_error">
                <source>shipment_error</source>
                <target>Shipment error</target>
            </trans-unit>
            <trans-unit id="precise_the_return_reason_of_the_products">
                <source>precise_the_return_reason_of_the_products</source>
                <target>Precise the return reason of the products:</target>
            </trans-unit>
            <trans-unit id="serial_number">
                <source>serial_number</source>
                <target>Serial number</target>
            </trans-unit>
            <trans-unit id="comment">
                <source>comment</source>
                <target>Comment</target>
            </trans-unit>
            <trans-unit id="message_fill_serial_number">
                <source>message_fill_serial_number</source>
                <target>In order to improve the process, please fill the serial number of your products and a comment:</target>
            </trans-unit>
            <trans-unit id="i_ask_the_reshipment_of_the_ordered_product">
                <source>i_ask_the_reshipment_of_the_ordered_product</source>
                <target>I ask the reshipment of the ordered product</target>
            </trans-unit>
            <trans-unit id="a_return_note_has_been_assigned">
                <source>a_return_note_has_been_assigned</source>
                <target>A return note has been assigned.</target>
            </trans-unit>
            <trans-unit id="click_here_to_download_it">
                <source>click_here_to_download_it</source>
                <target>Click here to download it.</target>
            </trans-unit>
            <trans-unit id="print_it_cut_it_and_paste_the_label_on_related_product">
                <source>print_it_cut_it_and_paste_the_label_on_related_product</source>
                <target>Print it, cut it and paste the label on related product.</target>
            </trans-unit>
            <trans-unit id="return_status_PENDING">
                <source>return_status_PENDING</source>
                <target>Pending</target>
            </trans-unit>
            <trans-unit id="return_status_HANDLED">
                <source>return_status_HANDLED</source>
                <target>Handled</target>
            </trans-unit>
            <trans-unit id="your_order_does_not_need_additional_payment">
                <source>your_order_does_not_need_additional_payment</source>
                <target>Your order does not need additional payment</target>
            </trans-unit>
            <trans-unit id="paid">
                <source>paid</source>
                <target>Paid</target>
            </trans-unit>
            <trans-unit id="left_to_pay">
                <source>left_to_pay</source>
                <target>To pay</target>
            </trans-unit>
            <trans-unit id="no_awaiting_payment_order">
                <source>no_awaiting_payment_order</source>
                <target>You currently have no orders pending payment.</target>
            </trans-unit>
            <trans-unit id="go_to_the_store">
                <source>go_to_the_store</source>
                <target>Go to the store</target>
            </trans-unit>
            <trans-unit id="open_in_google_maps">
                <source>open_in_google_maps</source>
                <target>Open in Google Maps</target>
            </trans-unit>
            <trans-unit id="choose_another_payment_method">
                <source>choose_another_payment_method</source>
                <target>
                     <![CDATA[
                        Select another payment method by <a href="%url%" id="add_payment_link">clicking here</a></target>
                     ]]>
                </target>
            </trans-unit>
            <trans-unit id="no_address_is_defined_in_your_account">
                <source>no_address_is_defined_in_your_account</source>
                <target>No address is defined in your account</target>
            </trans-unit>
            <trans-unit id="shipment_cost_quotation">
                <source>shipment_cost_quotation</source>
                <target>On quotation</target>
            </trans-unit>
            <trans-unit id="down_payment_amount">
                <source>down_payment_amount</source>
                <target><![CDATA[(deposit of %amount%)]]></target>
            </trans-unit>
            <trans-unit id="down_payment_description">
                <source>down_payment_description</source>
                <target>This deposit of 10% will not be charged. The final payment will be made with you after quotation delivery of your order:</target>
            </trans-unit>
            <trans-unit id="cetelem_terms_and_conditions">
                <source>cetelem_terms_and_conditions</source>
                <target>A loan commits you and shall be repaid. Be sure of your reimbursement capacities before committing.</target>
            </trans-unit>
            <trans-unit id="fullcb_3x_detail">
                <source>fullcb_3x_detail</source>
                <target>
                    <![CDATA[
                    <p><strong>You need to live in France to select this payment method.</strong></p>
                    <p><strong>Financing estimation:</strong></p>
                    <ul>
                        <li><strong>%first_month_payment%</strong> today,</li>
                        <li><strong>%second_month_payment%</strong> 30 days after your order,</li>
                        <li><strong>%third_month_payment%</strong> 60 days after your order.</li>
                    </ul>
                    <div class="payment_method_example">
                    <p>3XCB is a 3 times payment method via credit card or Mastercard* which allows to spread the order payment in 3 deadlines:</p>
                        <ul>
                            <li>1st deadline the day of your order: 1/3 of the order amount,</li>
                            <li>2nd deadline 30 days after your order: 1/3 of the order amount,</li>
                            <li>3rd deadline 60 days after your order: 1/3 of the order amount.</li>
                        </ul>
                        <p>Example for a 300€ order: 3&nbsp;deadlines of 100&nbsp;€ each. Total amount of credit operation: 300€. Nominal tax: 0%. You benefit a 14 days retractation delay.</p>
                        <p>(*)&nbsp;To benefit from the 3XCB payment, your order amount must be between 90€ and 3000€, you must be adult and reside in France. The following cards are not accepted: Electron, Maestro, e-CB, pre-paid cards and American Express.</p>
                        <p>This service is assured by our partner BNP Paribas&nbsp;Personal&nbsp;Finance, SA au capital de 583&nbsp;834&nbsp;454&nbsp;€ -&nbsp;SIREN&nbsp;542&nbsp;097&nbsp;902&nbsp;RCS Paris –&nbsp;1, boulevard&nbsp;Haussmann 75&nbsp;318 Paris&nbsp;Cedex&nbsp;09 –&nbsp;N°&nbsp;ORIAS&nbsp;: 07&nbsp;023&nbsp;128 –&nbsp;Tél.&nbsp;:&nbsp;05&nbsp;56&nbsp;55&nbsp;56&nbsp;00.</p>
                        <p>Under 90&nbsp;days financing contract.</p>
                    </div>
                    ]]>
                </target>
            </trans-unit>
            <trans-unit id="fullcb_4x_detail">
                <source>fullcb_4x_detail</source>
                <target>
                    <![CDATA[
                    <p><strong>You need to live in France to select this payment method.</strong></p>
                    <p><strong>Financing estimation:</strong></p>
                     <ul>
                        <li><strong>%first_month_payment%</strong> today,</li>
                        <li><strong>%second_month_payment%</strong> 30 days after your order,</li>
                        <li><strong>%third_month_payment%</strong> 60 days after your order,</li>
                        <li><strong>%fourth_month_payment%</strong> 85 days after your order.</li>
                    </ul>
                    <div class="payment_method_example">
                    <p>4XCB is a 4 times payment method via credit card or Mastercard* which allows to spread the order payment in 4 deadlines:</p>
                         <ul>
                            <li>1st deadline the day of your order: 1/4 of the order amount,</li>
                            <li>2nd deadline 30 days after your order: 1/4 of the order amount,</li>
                            <li>3rd deadline 60 days after your order: 1/4 of the order amount.</li>
                            <li>4th deadline 85 days after your order: 1/4 of the order amount.</li>
                        </ul>
                        <p>Example for a 400€ order: 4&nbsp;deadlines of 100&nbsp;€ each. Total amount of credit operation: 400€. Nominal tax: 0%. You benefit a 14 days retractation delay.</p>
                        <p>(*)&nbsp;To benefit from the 3XCB payment, your order amount must be between 90€ and 3000€, you must be adult and reside in France. The following cards are not accepted: Electron, Maestro, e-CB, pre-paid cards and American Express.</p>
                        <p>This service is assured by our partner BNP Paribas&nbsp;Personal&nbsp;Finance, SA au capital de 583&nbsp;834&nbsp;454&nbsp;€ -&nbsp;SIREN&nbsp;542&nbsp;097&nbsp;902&nbsp;RCS Paris –&nbsp;1, boulevard&nbsp;Haussmann 75&nbsp;318 Paris&nbsp;Cedex&nbsp;09 –&nbsp;N°&nbsp;ORIAS&nbsp;: 07&nbsp;023&nbsp;128 –&nbsp;Tél.&nbsp;:&nbsp;05&nbsp;56&nbsp;55&nbsp;56&nbsp;00.</p>
                        <p>Under 90&nbsp;days financing contract.</p>
                    </div>
                    ]]>
                </target>
            </trans-unit>
            <trans-unit id="shopping_cart_amount_greater_than_0_for_payment">
                <source>shopping_cart_amount_greater_than_0_for_payment</source>
                <target>The amount of the shopping cart must be greatest than 0 euro to be able to use this payment method.</target>
            </trans-unit>
            <trans-unit id="pay_my_order">
                <source>pay_my_order</source>
                <target>Pay my order</target>
            </trans-unit>
            <trans-unit id="quantity_exceeds_available_stock">
                <source>quantity_exceeds_available_stock</source>
                <target>The amount you wish to checkout exceeds the amount of products currently available in stock.</target>
            </trans-unit>
            <trans-unit id="this_product_line_comes_from_your_basket_created_during_your_last_log_in">
                <source>this_product_line_comes_from_your_basket_created_during_your_last_log_in</source>
                <target>The line above comes from your basket created from your previous log in.</target>
            </trans-unit>
            <trans-unit id="your_actual_basket_is_merged_with_the_basket_created_during_your_last_log_in">
                <source>your_actual_basket_is_merged_with_the_basket_created_during_your_last_log_in</source>
                <target>This basket is merged with the one created from your last log in. Please check its content.</target>
            </trans-unit>
            <trans-unit id="return_note_error_message">
                <source>return_note_error_message</source>
                <target>
                    <![CDATA[
                    <p>An error occurred while we tried to create the return note. <br>Please try later or call a sale advisor.</p>
                    ]]>
                </target>
            </trans-unit>
            <trans-unit id="select_a_delivery_slot">
                <source>select_a_delivery_slot</source>
                <target>Select a delivery slot</target>
            </trans-unit>
            <trans-unit id="no_delivery_slots_found">
                <source>no_delivery_slots_found</source>
                <target>No delivery slot found</target>
            </trans-unit>
            <trans-unit id="content_of_the_order_to_be_shipped">
                <source>content_of_the_order_to_be_shipped</source>
                <target>Content of the order to be shipped</target>
            </trans-unit>
            <trans-unit id="return_items">
                <source>return_items</source>
                <target>Return items</target>
            </trans-unit>
            <trans-unit id="date_not_available">
                <source>date_not_available</source>
                <target>Date not available</target>
            </trans-unit>
            <trans-unit id="basket_birthday_giveaway">
                <source>basket_birthday_giveaway</source>
                <target>Order and try to win a LG home-theater system !</target>
            </trans-unit>
            <trans-unit id="guaranteed_delivery_before_christmas">
                <source>guaranteed_delivery_before_christmas</source>
                <target>Guaranteed delivery before Christmas</target>
            </trans-unit>
        </body>
    </file>
</xliff>

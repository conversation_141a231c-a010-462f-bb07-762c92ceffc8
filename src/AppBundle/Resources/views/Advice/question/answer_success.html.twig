{% extends 'AppBundle:Advice:question/answer.html.twig' %}
{% import 'AppBundle:Common:event_macro.html.twig' as event_macro %}

{% block answer_content %}
    <div class="SVDv3_colonnes_colonne_droite clearfix">
        {% if app.session.flashBag.has('submission_success') %}
            <div class="SVDv3_messageAlerte SVDv3_messageAlerte_success mceContentBody">
                {% for msg in app.session.flashBag.get('submission_success') %}
                    <p>{{ msg|raw }}</p>
                {% endfor %}
            </div>
        {% endif %}

        {% block recurring_events %}
            <h2 class="SVDv3_titre_page">{% trans from 'event' %}dont_miss{% endtrans %}</h2>
            {{  event_macro.recurring_events(false) }}
        {% endblock %}
    </div>
{% endblock %}

{% block advices_to_write %}{% endblock %}

{% block javascripts %}{% endblock %}

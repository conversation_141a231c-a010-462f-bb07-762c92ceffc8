{% extends 'AppBundle:Advice:review_submission_template.html.twig' %}
{% import 'AppBundle:Common:event_macro.html.twig' as event_macro %}

{% block content %}
    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% block crumbs %}
            <li>{{ 'review_submission_breadcrumb' | trans({}, 'comment') }}</li>
        {% endblock crumbs %}
    {% endembed %}
    <h1 class="SVDv3_mobile_navbar">{% trans from 'comment' %}write_a_review{% endtrans %}</h1>
    <div class="SVDv3_colonnes_colonne_droite clearfix">
        {% if app.session.flashBag.has('review_submission_success') %}
            <div class="SVDv3_messageAlerte SVDv3_messageAlerte_success mceContentBody">
                {% for msg in app.session.flashBag.get('review_submission_success') %}
                    <p>{{ msg|raw }}</p>
                {% endfor %}
            </div>
        {% endif %}

        {% block recurring_events %}
            <h2 class="SVDv3_titre_page">{% trans from 'event' %}dont_miss{% endtrans %}</h2>
            {{  event_macro.recurring_events(false) }}
        {% endblock %}
    </div>
{% endblock %}

{% block advices_to_write %}{% endblock %}

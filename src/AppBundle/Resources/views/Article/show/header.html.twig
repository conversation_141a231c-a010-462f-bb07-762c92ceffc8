{% import 'AppBundle:Common:article_macro.html.twig' as article_macro %}
{% import 'AppBundle:Common:promotion_macro.html.twig' as promotion_macro %}

{% set video = article.common_content.editorial_content.video  ?? ''  %}

{% set file = article.article_documents ?? []  %}

<div class="product-header" data-name='{{ article.brand.name~' '~article.name }}' data-title='{{ article.name }}' data-price='{{ article.price.selling_price }}' data-brand='{{ article.brand.name }}' data-category='{{ (article.main_stands | last).name }}' data-id='{{ article.id }}'>
    <div class="container">
        <div class="row">
            <div class="col-12 col-md-7 col-xl-8">
                <div class="product-header-header">
                    <h1 class="product-header-title">
                        <div class="product-header-title-model">
                            {% if article.packaged_articles | length > 1 %}
                                {{ article.editorial_content.short_description }}
                            {% else %}
                                {{ article_short_desc }}
                            {% endif %}
                        </div>
                        <div class="product-header-title-stand">
                            <a href="{{ path('show_stand', {'slug': article.main_stands|last.slug}) }}">
                                {{ article.main_stands|last.name }}
                            </a>
                        </div>
                    </h1>
                    <div class="product-header-reviews">
                        <summary-opinions :article="article"></summary-opinions>
                    </div>
                </div>
                <div class="product-header-media">
                    <article-media
                        :article="article"
                        video='{{ video }}'
                        file='{{ file|json_encode }}'
                        :article_images_path="article_images_path"
                    >
                        <div class="slider-skeleton">
                            <div class="slider-img"></div>
                            <ul class="slider-nav">
                                <li></li>
                                <li></li>
                                <li></li>
                                <li></li>
                            </ul>
                        </div>
                    </article-media>
                </div>
            </div>

            <div class="col-12 col-md-5 col-xl-4">
                <div class="product-header-before-christmas" id="before-christmas">
                    <before-christmas :article="article"></before-christmas>
                </div>

                <div class="product-header-content article" data-context="product-content">

                    <div class="product-header-section product-header-section-row">

                        {% if article.brand.logo_url is not empty %}
                            <div class="product-header-section product-header-brand-logo product-header-section-noborder">
                                <a href="{{ path('show_brand', {'slug': article.brand.slug}) }}"
                                   title="{% trans  with {'%brand.name%': article.brand.name|e} from "brand" %}brand_show_all_products{% endtrans %}">
                                    <img height='47' alt="{{ article.brand.name }}" class="alpha-hover" src="{{ asset(article.brand.logo_url, 'static_images') }}">
                                </a>
                            </div>
                        {% endif %}

                        <div class="product-header-section product-header-common product-header-section-noborder">
                            {% if article.unbasketable_reason is null %}

                                {% if article.highlights is not empty and article.highlights[0].tag_path and article.highlights[0].tag_path != 'article.highlight.on_offer' and article.highlights[0].tag_path != 'article.highlight.promotion' %}
                                    <div class="product-header-section product-header-highlight product-header-section-noborder" id="product-highlight">
                                        <div class="product-highlight-list">
                                            <product-highlight :article="article"></product-highlight>
                                            {% if article.display_mode == display_mode.store_exclusivity %}
                                                <span class="product-highlight label label-md label-default">{% trans %}store_exclusivity{% endtrans %}</span>
                                            {% endif %}
                                            {% if article['parent_destock_price'] is defined and article['parent_destock_price'] is not empty and article['parent_destock_price']['selling_price'] is defined %}
                                                <a href="{{ path('show_destock_stand') }}" class="product-highlight label label-md label-destock">{% trans %}destock{% endtrans %}</a>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% elseif article.highlights is not empty or article.display_mode == display_mode.store_exclusivity or ( article['parent_destock_price'] is defined and article['parent_destock_price'] is not empty and article['parent_destock_price']['selling_price'] is defined ) %}
                                    <product-highlight :article="article"></product-highlight>
                                    {% if article.display_mode == display_mode.store_exclusivity and article.highlights and article.highlights[0].tag_path != 'article.highlight.on_offer' and article.highlights[0].tag_path != 'article.highlight.promotion' %}
                                        <div class="product-header-section product-header-highlight product-header-section-noborder" id="product-highlight">
                                            <div class="product-highlight-list">
                                                <span class="product-highlight label label-md label-default">{% trans %}store_exclusivity{% endtrans %}</span>
                                            </div>
                                        </div>
                                    {% endif %}
                                    {% if article['parent_destock_price'] is defined and article['parent_destock_price'] is not empty and article['parent_destock_price']['selling_price'] is defined %}
                                        <div class="product-header-section product-header-highlight product-header-section-noborder" id="product-highlight">
                                            <div class="product-highlight-list">
                                                <a href="{{ path('show_destock_stand') }}" class="product-highlight label label-md label-destock">{% trans %}destock{% endtrans %}</a>
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endif %}
                                <div class="product-header-section product-header-pricing product-header-section-noborder">
                                    <div class="product-header-price">
                                        {{ article_macro.article_price(article) }}
                                    </div>
                                    <div class="product-header-availability">
                                        {{ article_macro.article_availability(article, { is_in_price_block: true }) }}
                                    </div>
                                </div>
                            {% else %}
                                <div class="product-header-section product-header-pricing product-header-section-noborder">
                                    <div class="product-header-availability">
                                        <p class="SVDv3_dispo"><span class="SVDv3_dispo_outofstock">{% trans %}Discontinued{% endtrans %}</span></p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>

                    </div>
                    {% set declination_without_destock = 0 %}
                    {% for declination in article.declinations if 'DESTOCK-' not in declination.sku %}
                        {% set declination_without_destock = declination_without_destock + 1 %}
                    {% endfor %}
                    {% if article.unbasketable_reason is not null and declination_without_destock < 1  %}
                        {% if  article['substitute'] is defined and article['substitute'] is not empty
                            and article['substitute']['article_url'] is defined and article['substitute']['article_url'] is not empty %}
                            <div class="product-header-section product-header-substitute product-header-section-noborder">
                                {% trans %}find_out_substitute{% endtrans %}
                                <a href="{{ article.substitute.article_url }}" class="article-substitute-link"><strong>{{ article['substitute']['full_name'] }}</strong></a>
                            </div>
                            <div class="product-header-section product-header-button product-header-button-substitute product-header-section-noborder">
                            {{ article_macro.see_substitute_button(article.substitute) }}
                            </div>

                        {% elseif article['suggestion'] is defined and article['suggestion'] is not empty %}
                            <div class="product-header-section product-header-substitute product-header-section-noborder">
                                {% trans %}find_out_suggested{% endtrans %}
                                <a href="{{ article.suggestion.article_url }}" class="article-substitute-link"><strong>{{ article['suggestion']['full_name'] }}</strong></a>
                            </div>
                            <div class="product-header-section product-header-button product-header-button-substitute product-header-section-noborder">
                                {{ article_macro.see_substitute_button(article.suggestion) }}
                            </div>

                        {% elseif article['stand_suggestion'] is defined and article['stand_suggestion'] is not empty %}
                            <div class="product-header-section product-header-substitute product-header-section-noborder">
                                {% trans %}find_out_stand_store{% endtrans %}
                                <a href="{{ path('show_stand', {'slug': article.stand_suggestion.stand_slug}) }}" class="article-substitute-link"><strong>{{ article['stand_suggestion']['stand_name'] }}</strong></a>
                            </div>
                            <div class="product-header-section product-header-button product-header-button-substitute product-header-section-noborder">
                                {{ article_macro.substitute_button(path('show_stand', {'slug': article.stand_suggestion.stand_slug}), 'see_substitute_stand'|trans) }}
                            </div>

                        {% elseif article['brand_suggestion'] is defined and article['brand_suggestion'] is not empty %}
                            <div class="product-header-section product-header-substitute product-header-section-noborder">
                                {% trans %}find_out_brand_store{% endtrans %}
                                <a href="{{ path('show_brand', {'slug': article.brand_suggestion.brand_slug}) }}" class="article-substitute-link">
                                    <strong>{{ article['brand_suggestion']['brand_name'] }}</strong>
                                </a>
                            </div>
                            <div class="product-header-section product-header-button product-header-button-substitute product-header-section-noborder">
                                {{ article_macro.substitute_button(path('show_brand', {'slug': article.brand_suggestion.brand_slug}), 'see_substitute_brand'|trans) }}
                            </div>
                        {% endif %}
                    {% endif %}

                    {% if article.parent_destock_url is defined and article.parent_destock_url is not empty %}
                        {{ article_macro.without_declination(article) }}
                    {% elseif article.declination_type is defined and article.declination_type == declination_type.color %}
                        {{ article_macro.with_color_declination(article) }}
                    {% elseif article.declination_type is defined and article.declination_type == declination_type.length %}
                        {{ article_macro.with_length_declination_current(article) }}
                    {% else %}
                        {% set nb_articles = 0 %}
                        {% for declination in article.declinations if declination.parent_destock_price is empty %}
                            {% set nb_articles = nb_articles + 1 %}
                        {% endfor %}
                        {% if (nb_articles > 0 and article.unbasketable_reason is not null) or nb_articles > 1 %}
                            {{ article_macro.with_default_declination(article) }}
                        {% else %}
                            {{ article_macro.without_declination(article) }}
                        {% endif %}
                    {% endif %}

                    {% set has_other_second_life_offer = second_life_list|length > 1 or (second_life_list|length == 1 and second_life_list[0].article_id != article.article_id) %}
                    {% if has_other_second_life_offer %}
                        {% embed "@App/Article/show/article_second_life.html.twig" %}{% endembed %}
                    {% endif %}

                    {% if article.unbasketable_reason is null %}
                        <div class="product-header-section product-header-warranties">
                            <div class="row row-small-gutter">
                                <div class="col-12">
                                    {{ article_macro.article_warranty_solo(article) }}
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <div class="container-installments">
                        <div class="installments-title">Réglez en plusieurs mensualités</div>
                        <div class="installments-options" data-context="content-simulator-list">
                            {% set price = article.price.selling_price %}

                            {% if price >= 90 and price <= 3000 %}
                                <div
                                    data-context="content-simulator-item"
                                    class="installment-option"
                                    onclick='window.SonVideo.installments.open({
                                        amount: {{ price|json_encode }},
                                        times: 3,
                                        skus: {{ [article.sku]|json_encode|raw }}
                                    })'
                                    role="button"
                                    tabindex="0"
                                >
                                    3x
                                </div>
                                <div
                                    data-context="content-simulator-item"
                                    class="installment-option"
                                    onclick='window.SonVideo.installments.open({
                                        amount: {{ price|json_encode }},
                                        times: 4,
                                        skus: {{ [article.sku]|json_encode|raw }}
                                    })'
                                    role="button"
                                    tabindex="0"
                                >
                                    4x
                                </div>
                            {% endif %}

                            {% if price >= 150 and price <= 16000 %}
                                <div
                                    data-context="content-simulator-item"
                                    class="installment-option"
                                    onclick='window.SonVideo.installments.open({
                                        amount: {{ price|json_encode }},
                                        times: 10,
                                        skus: {{ [article.sku]|json_encode|raw }}
                                    })'
                                    role="button"
                                    tabindex="0"
                                >
                                    10x
                                </div>
                            {% endif %}

                            {% if price >= 300 and price <= 16000 %}
                                <div
                                    data-context="content-simulator-item"
                                    class="installment-option"
                                    onclick='window.SonVideo.installments.open({
                                        amount: {{ price|json_encode }},
                                        times: 20,
                                        skus: {{ [article.sku]|json_encode|raw }}
                                    })'
                                    role="button"
                                    tabindex="0"
                                >
                                    20x
                                </div>
                            {% endif %}

                            {% if price >= 450 and price <= 16000 %}
                                <div
                                    data-context="content-simulator-item"
                                    class="installment-option"
                                    onclick='window.SonVideo.installments.open({
                                        amount: {{ price|json_encode }},
                                        times: 30,
                                        skus: {{ [article.sku]|json_encode|raw }}
                                    })'
                                    role="button"
                                    tabindex="0"
                                >
                                    30x
                                </div>
                            {% endif %}

                            {% if price >= 600 and price <= 16000 %}
                                <div
                                    data-context="content-simulator-item"
                                    class="installment-option"
                                    onclick='window.SonVideo.installments.open({
                                        amount: {{ price|json_encode }},
                                        times: 40,
                                        skus: {{ [article.sku]|json_encode|raw }}
                                    })'
                                    role="button"
                                    tabindex="0"
                                >
                                    40x
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    {# Start regulated attributes Section #}
                    {% set energy_label_media = (article.article_medias ?? []) | extractEnergyLabelDataFromMedia  %}
                    {% set energy_label_media_attribute = (energy_label_media is not empty) ? 'data-energy-label=' ~ asset(energy_label_media | article_media_extract, 'article_images') : '' %}

                    {% if article.regulated_attributes.repairability_index is not null
                        or article.regulated_attributes.energy_letter_label is not null
                        or article.regulated_attributes.sustainability is not null
                    %}
                        {% set dynamic_classes = [] %}
                        {% if article.regulated_attributes.sustainability is not null %}
                            {% set dynamic_classes = dynamic_classes|merge(['with-sustainability']) %}
                        {% endif %}
                        <div class="product-header-section product-header-regulated-attributes {{ dynamic_classes|join(' ') }}" data-context="product-header-optional-section">
                            <div class="row row-small-gutter">
                                <div class="col-12 d-flex flex-wrap gap-3">
                                    {% if article.regulated_attributes.energy_letter_label is not null %}
                                        {# Opens a slide over, search for the js side with "vm_energy_label_details" #}
                                        <div class="icon icon-sprite-energy-labelling-picto label-{{ article.regulated_attributes.energy_letter_label | formatEnergyLetterLabel }} mb-2"
                                            data-context="energy-letter-label" {{ energy_label_media_attribute }}
                                        >
                                            <span class="ct-txt">{{ article.regulated_attributes.energy_letter_label | formatEnergyLetterLabel }}</span>
                                        </div>
                                    {% endif %}

                                    {% if article.regulated_attributes.sustainability is not null %}
                                        {% set sustainability_document = (article.article_documents ?? []) | extract_document_by_name('Indice de durabilité')  %}
                                        {% if sustainability_document is not empty %}
                                            <a href="{{ asset(sustainability_document.media_variation.document.url, 'static_images') }}"
                                               target="_blank"
                                               data-context="sustainability"
                                            >
                                                <img src="{{ asset(article.regulated_attributes.sustainability | mapSustainabilitySvg, 'static_images') }}"
                                                     alt="Indice de durabilité {{ article.regulated_attributes.sustainability }}/10"
                                                     class="sustainability-picture"
                                                >
                                            </a>
                                        {% else %}
                                            <div data-context="sustainability">
                                                <img src="{{ asset(article.regulated_attributes.sustainability | mapSustainabilitySvg, 'static_images')  }}"
                                                     alt="Indice de durabilité {{ article.regulated_attributes.sustainability }}/10"
                                                     class="sustainability-picture">
                                            </div>
                                        {% endif %}

                                    {% elseif article.regulated_attributes.repairability_index is not null %}
                                        {% set repairability_index_document = (article.article_documents ?? []) | extract_document_by_name('Indice de réparabilité')  %}
                                        {% if repairability_index_document is not empty %}
                                            <a href="{{ asset(repairability_index_document.media_variation.document.url, 'static_images') }}"
                                                target="_blank"
                                                class="icon icon-sprite-repairability-index-picto {{ article.regulated_attributes.repairability_index | formatRepairabilityIndex }}"
                                                data-context="repairability-index"
                                            >
                                                <span class="ct-txt">{{ article.regulated_attributes.repairability_index | number_format(1, ',')  }}</span>
                                            </a>
                                        {% else %}
                                            <div class="icon icon-sprite-repairability-index-picto {{ article.regulated_attributes.repairability_index | formatRepairabilityIndex }}"
                                                data-context="repairability-index"
                                            >
                                                <span class="ct-txt">{{ article.regulated_attributes.repairability_index | number_format(1, ',')  }}</span>
                                            </div>
                                        {% endif %}
                                    {% endif %}

                                    {% set product_information_document = (article.article_documents ?? []) | extract_document_by_name('Fiche informations produit')  %}
                                    {% if product_information_document is not empty %}
                                        <ul class="list-unstyled m-0 w-100">
                                            <li>
                                                <a class="document" href="{{ asset(product_information_document.media_variation.document.url, 'static_images') }}" target="_blank" data-context="document">
                                                    <span class="txt">{{ product_information_document.meta.view }}</span>
                                                </a>
                                            </li>
                                        </ul>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    {# End regulated attributes Section #}

                    <div class="product-header-section">
                        <div class="row row-small-gutter">
                            <div class="col-12">
                                <button data-context="article-sidepanel-open-phone"
                                        data-immediate-recall="fiche article > numéro téléphone"
                                        id="button_show_contact"
                                        class="btn btn-primary btn-outline btn-block"
                                        data-no-scroll>{% trans from 'immediate_recall' %}any_question_contact_us{% endtrans %}</button>
                            </div>
                        </div>
                    </div>

                    {% if article.unbasketable_reason is null %}
                    <div class="mobile-add-to-basket">
                        <div class="container-price-add-to-basket">
                            {{ article_macro.article_basket_price(article) }}

                            {% if 
                                (article.parent_destock_url is defined and article.parent_destock_url is not empty)
                                or article.display_mode == display_mode.store_exclusivity
                            %}
                                {{ article_macro.without_declination(article) }}
                            {% else %}
                                {% set nb_articles = 0 %}
                                {% for declination in article.declinations if declination.parent_destock_price is empty %}
                                    {% set nb_articles = nb_articles + 1 %}
                                {% endfor %}
                                {{ article_macro.article_basket_button_full_width(article) }}
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <div class="sku">{{ article.sku }}</div>

                </div>
            </div>
        </div>
    </div>
</div>
{# End Article meta, price and stock #}

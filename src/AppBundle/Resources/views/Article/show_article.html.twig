{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% import 'AppBundle:Common:brand_macro.html.twig' as brand_macro %}

{% set locale = app.request.locale %}
{% set article_short_desc = (article.editorial_content.short_description is defined) ? article.editorial_content.short_description : '' %}

{% set meta_description = article.common_content.description|default('')|striptags|trim %}
{% if meta_description is empty %}
    {% set meta_description = article.common_content.editorial_content.introduction|default('')|striptags|trim %}
{% endif %}
{% set metas = metas|merge({'description': meta_description }) %}
{% if article.parent_destock_url is not null %}
    {% set metas = metas|merge({'robots': 'noindex, follow'}) %}
{% endif %}

{% block title %}
    {% if article.common_content.title|trim is not empty %}
        {{ article.common_content.title }}
    {% else %}
        {% set article_title = article.brand.name ~ ' ' ~ article.name %}
        {% set stand_title = article.main_stands|last.name %}
        {% set svd_title = 'on_son_video_com' | trans %}
        {% if (article_title ~ ' - ' ~ stand_title ~ ' ' ~ svd_title) | length > 60 %}
            {% if (article_title ~ ' - ' ~ stand_title) | length > 60 %}
                {% if (article_title ~ ' - Son-Vidéo.com') | length > 60 %}
                    {{ article_title }}
                {% else %}
                    {{ article_title ~ ' - Son-Vidéo.com' }}
                {% endif %}
            {% else %}
                {{ article_title ~ ' - ' ~ stand_title }}
            {% endif %}
        {% else %}
            {{ article_title ~ ' - ' ~ stand_title ~ ' ' ~ svd_title }}
        {% endif %}
    {% endif %}
{% endblock %}

{% block preload %}
    {{ parent() }}
    {% if article.article_medias[0] is defined and article.article_medias[0].url is defined  %}
        <link rel="preload" as="image" href="{{ preset(asset(article.article_medias[0].url, 'article_images'), '600') }}" imagesrcset="{{ presets(asset(article.article_medias[0].url, 'article_images'), [['450', '450w'], ['600', '600w']]) }}">
    {% endif %}
{% endblock %}

{# OpenGraph #}
{% block og %}
    <meta property='og:type' content='product.item'>
    <meta property='og:url' content='{{ base_url ~ article.main_article_url }}'>
    <meta property='og:title' content="{{ article.common_content.name }}">
    <meta property='og:description' content='{{ meta_description|meta_parse(175, true) }}'>
    {% if article.article_medias[0] is defined and article.article_medias[0].media_variation.image.referential[600] is defined  %}
        <meta property='og:image' content="{{ asset(article.article_medias[0].media_variation.image.referential[600], 'article_images') }}">
    {% endif %}
    <meta property='og:image:alt' content="{{ 'Image ' ~ article.common_content.name}}">
    <meta property='product:price:amount' content='{{ article.price.selling_price }}'>
    <meta property='product:price:currency' content='EUR'>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('article')) }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('article_style')) }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('characteristics_sheet')) }}
{% endblock %}

{% block structured_data %}
    <script type="application/ld+json">
{{ structured_data | raw }}
</script>
{% endblock %}

{% block canonical %}
    {% if article.main_article_url is defined and article.main_article_url is not empty %}
        <link rel="canonical" href="{{ base_url ~ article.main_article_url }}" />
    {% endif %}
{% endblock %}

{% block tracking %}
    {{ render(controller('AppBundle:Tracking/Tracking:show', {'location': 'article', 'extra_data': article.extract() }))|raw }}
    {{ parent() }}
{% endblock %}

{% block body_class %}body-article{% endblock %}

{% block content %}
    <div>
        <div class="SVDv3_ficheProduit_head ">
            {% embed "@App/Common/block/breadcrumbs.html.twig" %}
                {% block crumbs %}
                    {% for stand in article.main_stands %}
                        <li>
                            <a href="{{ path('show_stand', {'slug': stand.slug}) }}">{{ stand.name }}</a>
                        </li>
                    {% endfor %}
                    <li>{{ article.brand.name }} {{ article.name }}</li>
                {% endblock crumbs %}
            {% endembed %}
            <!-- End Breadcrumbs -->
            {% embed "@App/Article/show/header.html.twig" %}{% endembed %}
        </div>
        <div id="second-life-list-app"></div>
        <div class="container" id="product-header-trigger">
            <div class="row">
                <div class="col-lg-12">
                    <div class="tabs-section-height" id="tabs-section">
                        <div class="tabs-section">
                            <span class="arrow-tab left"></span>
                            <ul class="tabs-section-tabs-list">
                                <li class="tabs-section-tabs-item active">
                                    <a href="#ficheDescription"><span>{% trans from 'messages' %}description{% endtrans %}</span></a>
                                </li>
                                {% if(tabs is not empty) %}
                                    {% for anchor, tab in tabs if tab.content is defined and tab.content is not empty %}
                                        <li class="tabs-section-tabs-item">
                                            <a href="#{{ anchor }}" {% if anchor == 'fiche_Presse' %} id="tab-link-press" {% endif %}>
                                                <span>{{ tab.title }}</span>
                                            </a>
                                        </li>
                                    {% endfor %}
                                {% endif %}

                                {# Bazaarvoice tabs #}
                                <li class="tabs-section-tabs-item">
                                    <a href="#ficheAvis"><span>{% trans from 'bazaarvoice' %}reviews{% endtrans %}</span></a>
                                </li>
                                <li class="tabs-section-tabs-item">
                                    <a href="#ficheQuestionsReponses"><span>{% trans from 'bazaarvoice' %}questions_answers{% endtrans %}</span></a>
                                </li>
                            </ul>
                            <span class="arrow-tab right"></span>
                        </div>
                    </div>
                </div>
                {% embed "@App/Article/show/main.html.twig" %}{% endembed %}
            </div>

            <div id="energy-label-app"></div>

            {% set locations = ['common_content_' ~ article.common_content.common_content_id, 'mother_day'] %}
            {% for article_stand in article.main_stands %}
                {%  set locations = locations|merge(['parent_stand_' ~ article_stand.stand_id]) %}
            {% endfor %}
            {{ render(controller('CmsBundle:Widget/ClickbaitColumn:show', {'locations': locations, 'default_click_bait': true, 'is_article_page': true}))|raw }}

            {{ render(controller('AppBundle:Article/RelatedArticles:getComplementary', {'article_id': article.article_id})) }}

            {{ render(controller('CmsBundle:Widget/Article/AdvizedArticle:show', {'article_id': article.article_id, 'locale': app.request.locale}))|raw }}
        </div>
    </div>
{% endblock content %}

{% block javascripts %}
    {{ parent() }}
    <script defer src="{{ path('bazinga_jstranslation_js', { 'domain': 'comment' }, true) }}?locales={{ app.request.getLocale() }}"></script>
    <script defer src="{{ path('bazinga_jstranslation_js', { 'domain': 'pager' }, true) }}?locales={{ app.request.getLocale() }}"></script>
    <script defer src="{{ path('bazinga_jstranslation_js', { 'domain': 'promotion' }, true) }}?locales={{ app.request.getLocale() }}"></script>
    <script defer src="{{ path('bazinga_jstranslation_js', { 'domain': 'question' }, true) }}?locales={{ app.request.getLocale() }}"></script>
    <script defer src="{{ path('bazinga_jstranslation_js', { 'domain': 'review' }, true) }}?locales={{ app.request.getLocale() }}"></script>
    <script defer src="{{ path('bazinga_jstranslation_js', { 'domain': 'search' }, true) }}?locales={{ app.request.getLocale() }}"></script>

    {{ encore_entry_script_tags('advice/review/list') | defer | raw  }}
    {{ encore_entry_script_tags('advice/question/list') | defer | raw  }}
    {{ encore_entry_script_tags('characteristics_sheet') | defer | raw  }}
    {{ encore_entry_script_tags('tabs_basket') | defer | raw  }}
    {{ encore_entry_script_tags('article/desktop_basket_sticky') | defer | raw  }}
    {{ encore_entry_script_tags('article/add_arrows') | defer | raw  }}
    {{ encore_entry_script_tags('clickbait/clickbait_column_article') | defer | raw }}

    <script type="text/javascript">
        var article = {{ article.extractPublic()|json_encode()|raw }};
        article.nb_questions = {{ article.nb_questions|json_encode()|raw }};
        var article_images_path = {{ asset('{image_uri}', 'article_images')|json_encode()|raw }};
        var highlight_links = {
            'new'      : '{{ path('article_new') }}',
            'on_offer' : '{{ path('article_on_offer') }}',
            'sale' : '/soldes?tri=2',
            'sale_10' : '/soldes?filtres=%7B"highlights"%3A%5B"article.highlight.sale_10"%5D%7D&tri=2',
            'exclusive': '{{ path('article_exclusive') }}',
            'black_friday': '{{ path('show_shop', {'slug': 'promotions-black-friday'}) }}',
            'vp': '{{ path('private_sales') }}',
            'vp_10': '{{ path('private_sales') }}',
            'vp_15': '{{ path('private_sales') }}',
            'vp_20': '{{ path('private_sales') }}',
            'vp_25': '{{ path('private_sales') }}',
            'vp_30': '{{ path('private_sales') }}',
            'vp_40': '{{ path('private_sales') }}',
        };
        var links = {
            'submit_advice': '{{ path('review_submission_get', {'article': article.article_id}) }}'
        };
        var events_dates = {
            'before_christmas_delivery_start': new Date("{{ dates.promotional.before_christmas_delivery.start }}"),
            'before_christmas_delivery_end': new Date("{{ dates.promotional.before_christmas_delivery.end }}")
        };

        document.addEventListener('DOMContentLoaded', function (event) {
            // Add an offset to the smoothScrolling
            SonVideo.smoothScroll.setOffset(75)

            // Setup article tabs
            var container = document.querySelector('div#SVDv3_content_container')
            tabs(container);
        })

        function contactPopin() {
            swal({
                customClass: 'contact-popin',
                title: '{{ 'contact_us'|trans({}, 'messages')|e('js') }}',
                html: '{{ include('@App/Common/block/contact_popin.html.twig')|e('js') }}',
                width: 600,
                showConfirmButton: false,
                showCancelButton: false,
                showCloseButton: true,
            })
        }

        function linkToPress() {
            var tab_panes = document.getElementsByClassName('tab-pane');
            var i;
            for (i = 0; i < tab_panes.length; i++) {
                if (tab_panes[i].classList.contains('active')) {
                    tab_panes[i].classList.remove('active');
                }
            }

            var tab_links = document.getElementsByClassName('tab-link');
            for (i = 0; i < tab_links.length; i++) {
                if (tab_links[i].classList.contains('active')) {
                    tab_links[i].classList.remove('active');
                }
            }

            document.getElementById('fiche_Presse').className += ' active';
            document.getElementById('tab-link-press').className += ' active';
        }

        $(".select-declination-on-change").on("change", e => {window.location.replace(`${window.location.origin}${e.target.value}`)})

    </script>
    {{ encore_entry_script_tags('article') | defer | raw  }}
{% endblock %}

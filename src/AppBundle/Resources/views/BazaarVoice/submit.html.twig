{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% block title %}
    Son-Vidéo.com - {% trans from 'bazaarvoice' %}questions_answers{% endtrans %}
{% endblock %}

{% block content %}
    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% block crumbs %}
            <li>{% trans from 'bazaarvoice' %}questions_answers{% endtrans %}</li>
        {% endblock crumbs %}
    {% endembed %}

    <div class="SVDv3_colonnes_2colonnes1 clearfix">

        <div class="SVDv3_colonnes_colonne_gauche">
            {% if content_type == 'REVIEW_SUBMISSION' %}
                <h3>{% trans from 'bazaarvoice' %}advices_to_write_a_review{% endtrans %}</h3>
                <div class="SVDv3_colonne_element">
                    <div class="SVDv3_colonne_element_contenu">
                        <div class="SVDv3_colonne_element_contenu_marge">
                            <ul class="SVDv3_colonne_element_liste SVDv3_colonne_element_liste_paragraphes">
                                {% trans with {'%url%': path('contact_us_get') } from 'bazaarvoice' %}advices_to_write_a_review_list{% endtrans %}
                            </ul>
                        </div>
                    </div>
                </div>
            {% else %}
                <h3>{% trans from 'bazaarvoice' %}advices_to_write_an_answer{% endtrans %}</h3>
                <div class="SVDv3_colonne_element">
                    <div class="SVDv3_colonne_element_contenu">
                        <div class="SVDv3_colonne_element_contenu_marge">
                            <ul class="SVDv3_colonne_element_liste SVDv3_colonne_element_liste_paragraphes">
                                {% trans with {'%url%': path('contact_us_get') } from 'bazaarvoice' %}advices_to_write_an_answer_list{% endtrans %}
                            </ul>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <div class="SVDv3_colonnes_colonne_droite clearfix">
            <div id="BVSubmissionContainer" class="BVQASubmissionContainer"></div>
        </div>

    </div>
{% endblock content %}

{% block javascripts %}
    {{ parent() }}

    <script src="{{ bazaarvoice_script_url }}"></script>

    {# bazaarvoice config #}
    <script type="text/javascript" language="javascript">
        document.domain = '{{ bazaarvoice_document_domain }}';

        $BV.ui('submission_container', {
            userToken: "{{ encoded_token }}"
        });
    </script>
{% endblock %}



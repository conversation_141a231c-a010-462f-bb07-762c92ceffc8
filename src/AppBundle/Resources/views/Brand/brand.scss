@import '../../scss/style';
.show-brands-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  align-content: center;
  text-align: center;
  gap: $space_32;
  margin-top: $space_32;
  margin-bottom: $space_64;
  padding-right: $space_16;
  padding-left: $space_16;
  @include media_min($media_md) {
    padding-right: $space_32;
    padding-left: $space_32;
  }
  .show-brand-header {
    width: 100%;
    max-width: 1260px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: $space_32;
    .title {
      font-size: $font_size_26;
      line-height: $height_36;
      font-weight: $font_bold;
    }
    .letter-list {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      a {
        height: $height_34;
        width: $width_34;
        font-size: $font_size_14;
        line-height: $height_22;
        font-weight: $font_semi_bold;
        border-radius: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        align-content: center;
        &:hover {
          background-color: $color_blue_web_safe;
          color: $color_white;
        }
      }
    }
  }
  .show-brands {
    display: flex;
    flex-direction: column;
    gap: $space_16;
    max-width: 1260px;
    @include media_min($media_md) {
      gap: $space_32;
    }
    .brand-listing {
      display: flex;
      flex-direction: column;
      gap: $space_32;
      .brand-listing-section {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        width: 100%;
        .brand-listing-item {
          width: 50%;
          height: 198px;
          display: flex;
          flex-direction: column;
          gap: $space_16;
          padding: $space_16;
          border: solid 1px $color_grey_default;
          justify-content: center;
          align-items: center;
          align-content: center;
          text-align: center;
          font-size: $font_size_15;
          line-height: $height_22;
          font-weight: $font_semi_bold;
          color: $color_grey_typo;
          background-color: $color_white;
          cursor: pointer;
          overflow: hidden;
          position: relative;
          outline: solid 1px $color_grey_default;
          transition: $transition_all_02;
          .img-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            align-content: center;
            text-align: center;
            height: 90px;

            img {
              max-height: 90px;
            }
          }
          .brand-name {
            margin-top: $space_16;
          }
          @include media_min($media_md) {
            width: 33.33%;
            padding: $space_32;
          }

          @include media_min($media_lg) {
            width: 25%;
          }
          @include media_min($media_desktop) {
            width: 16.66%;
          }
          &:hover .content-wrapper {
            transform: scale(1.02);
          }
          &.letter {
            background-color: $color_dark_default;
            font-size: $font_size_32;
            color: $color_white;
            font-weight: $font_normal;
            cursor: unset;
            outline-color: $color_dark_default;
            border-color: $color_dark_default;
          }
        }
      }
    }
  }
}
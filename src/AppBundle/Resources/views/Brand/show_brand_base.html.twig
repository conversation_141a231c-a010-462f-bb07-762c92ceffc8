{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% import 'AppBundle:Common:article_macro.html.twig' as article_macro %}
{% import 'AppBundle:Common:brand_macro.html.twig' as brand_macro %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('editorial_style')) }}
{% endblock %}

{% set nbcar_histoire = 700 %}
{% set locale = app.request.locale %}
{% set has_banner = ('banner' in brand|keys) and (brand.banner is not empty) %}

{% block title %}
    {% if category_name is not defined %}
        {% trans with {'%brand.name%': brand.name} from 'brand' %}brand_products_title{% endtrans %}
    {% else %}
        {% trans with {'%brand.name%': brand.name, '%category_name%': category_name | lower} from 'brand' %}brand_category_title{% endtrans %}
    {% endif %}
{% endblock %}

{% block metas %}
    {% if brand.description is defined and brand.description is not empty %}
        {% set metas = metas|merge({'description': brand.description }) %}
    {% endif %}
    {{ parent() }}
{% endblock %}

{% block structured_data %}
<script type="application/ld+json">
{{ structured_data | raw }}
</script>
{% endblock %}

{% block tracking %}
    {{ render(controller('AppBundle:Tracking/Tracking:show', {'location': 'brand' }))|raw }}
    {{ parent() }}
{% endblock %}

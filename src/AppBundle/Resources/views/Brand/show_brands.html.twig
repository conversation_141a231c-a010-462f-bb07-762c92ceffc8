{% extends 'AppBundle:V5/Layout:base.html.twig' %}
{% import 'AppBundle:Common:brand_macro.html.twig' as brand_macro %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('page/brand')) }}
{% endblock %}

{% set metas = metas|merge({'description': 'brand_list_description' | trans({}, 'brand') }) %}

{% block title %}
    {% trans from 'brand' %}brand_list_title{% endtrans %}
{% endblock %}

{% block structured_data %}
    <script type="application/ld+json">
{{ structured_data | raw }}
</script>
{% endblock %}

{% block tracking %}
    {{ render(controller('AppBundle:Tracking/Tracking:show', {'location': 'brand_list' }))|raw }}
    {{ parent() }}
{% endblock %}

{% block content %}
    <div class="show-brands-container">
        <div class="show-brand-header">
            <span class="title">Notre espace marques</span>
            <div class="letter-list">
                <a href="#other" title="Autres"><strong>#</strong></a>
                {% for i in range('A', 'Z') %}
                    <a href="#{{ i }}" title="Marques ({{ i }})"><strong>{{ i }}</strong></a>
                {% endfor %}
            </div>
        </div>

        <div class="show-brands">
            {% set grouped_brands = {'other': []} %}

            {% for i in range('A', 'Z') %}
                {% set grouped_brands = grouped_brands|merge({(i): []}) %}
            {% endfor %}

            {% for brand in brands %}
                {% set initial = brand.brand_name|slice(0, 1)|capitalize %}
                {% if initial matches '/^[A-Z]$/' %}
                    {% set grouped_brands = grouped_brands|merge({(initial): grouped_brands[initial]|merge([brand])}) %}
                {% else %}
                    {% set grouped_brands = grouped_brands|merge({'other': grouped_brands['other']|merge([brand])}) %}
                {% endif %}
            {% endfor %}

            <div class="brand-listing">
                {% for letter, brands_list in grouped_brands %}
                    {% if brands_list is not empty %}
                        <div class="brand-listing-section" id="{{ letter }}">
                            <div class="brand-listing-item letter">{{ letter == 'other' ? '#' : letter }}</div>
                            {% for brand in brands_list %}
                                {{ brand_macro.listing(brand) }}
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
{% endblock content %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('page/brand') }}
{% endblock %}

{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% import 'AppBundle:Common:form_macro.html.twig' as form_macro %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('sennheiser_style')) }}
{% endblock %}

{% set metas = metas|merge({'description': 'lamp_catalog_description' | trans({}, 'catalog') }) %}

{% block title %}{{ 'lamp_catalog_title' | trans({}, 'catalog') }}{% endblock %}

{% block content %}
    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% block crumbs %}
            {% for stand in video_projector_section_hierarchy %}
                <li><a href="{{ path('show_stand', {'slug': stand.slug}) }}">{{ stand.name }}</a></li>
            {% endfor %}
            <li>{{ 'lamp_catalog_breadcrumb' | trans({}, 'catalog') }}</li>
        {% endblock crumbs %}
    {% endembed %}

    <div class="mceContentBody">
        <h1>{{ 'lamp_catalog_breadcrumb' | trans({}, 'catalog')  }}</h1>

        <div class="grid_container_12 SVDv3_article_element">
            <div class="grid_row">
                <div class="col_6_col">
                    <p>{{ 'lamp_intro' | trans({}, 'catalog') }}</p>
                    <div class="SVDv3_rappelImmediat_bouton widget-rappel-immediat">
                        <div class="SVDv3_colonne_element_appelBoutique">
                            <div class="SVDv3_appelBoutique"><a id="SVDv3_appelBoutique_6" class="uiV3_appelBoutique_rappelImmediat"></a>&nbsp;</div>
                        </div>
                    </div>
                </div>
                <div class="col_6_col col_last">
                    <p>{{ 'lamp_info' | trans({}, 'catalog') }}</p>

                    <p>{{ 'lamp_warning' | trans({}, 'catalog') | raw }}</p>
                </div>
            </div>
        </div>

        <h2 class="SVDv3_titre_page">{{ 'lamp_selection' | trans({}, 'catalog') }}</h2>

        <div class="grid_container_12 SVDv3_article_element">
            <div class="grid_row">
                <div class="col_6_col">

                    {{ form_start(form, {'method': 'POST', 'action': path('catalog_lamp')}) }}
                        <p><strong>1. </strong>{{ form.brand.vars.label | trans({},'catalog') }}</p>
                        <p>
                            {{ form_widget(form.brand) }}
                        </p>
                        {{ form_macro.show_error_for(form.brand) }}
                        <p>
                            <strong>2. </strong>{{ form.model.vars.label | trans({},'catalog') }}
                        </p>
                        <p>
                            {{ form_widget(form.model) }}
                        </p>
                        {{ form_macro.show_error_for(form.model) }}
                        <p>
                            <button type="submit" name="videoprojectionLampesButton" id="videoprojectionLampesButton" class="btn btn-primary btn-md">{{ 'find' | trans }}</button>
                        </p>
                    {{ form_end(form) }}

                </div>
                <div class="col_6_col col_last">
                    <p>
                        {{ 'lamp_contact' | trans({ '%url%': path('contact_us_get') }, 'catalog') | raw }}.
                    </p>
                </div>
            </div>
        </div>

    </div>

    {% if lamps is defined and lamps is not empty and lamps | length > 0 %}
        <div class="SVDv3_article_element">
            <div class="SVDv3_rayon_listingProduits">
                {% for lamp in lamps %}
                <div class="clearfix SVDv3_rayon_listingProduits_liste_ligne">
                    <div class="clearfix SVDv3_rayon_produit_content">
                        <div class="SVDv3_rayon_listingProduits_ref"><p><strong>{{ lamp.brand_name }}</strong><br />{{ lamp.product_name }}</p></div>
                        <div class="SVDv3_rayon_listingProduits_description"><p>{{ lamp.short_description }}<br />{{ lamp.sku }}</p></div>
                        <div class="SVDv3_rayon_listingProduits_prix"><p class="SVDv3_zonePrix_prix">{{ lamp.price.selling_price | localizedcurrency_svd(currency, app.request.locale) }}</p></div>
                        <div class="SVDv3_rayon_listingProduits_action">
                            <a href="" class="SVDv3_bouton_ajoutPanier SVDv3_bouton_largeurFixe"
                               data-type="accessory"
                               data-value="{{ lamp.accessory_id }}"
                               data-label="{{ lamp.short_description }}">
                                <span>{{ 'add_to_basket' | trans({}, 'common') }}</span>
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

{% endblock %}

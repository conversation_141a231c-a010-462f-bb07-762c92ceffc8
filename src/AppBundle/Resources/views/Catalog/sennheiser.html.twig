{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('sennheiser_style')) }}
{% endblock %}


{% block metas %}
    {% set metas = metas|merge({'description': 'sennheiser_meta_description' | trans({}, 'catalog') }) %}

    {{ parent() }}
{% endblock %}

{% block title %}{{ 'sennheiser_title' | trans({}, 'catalog') }}{% endblock %}

{% block additional_content %}
    <div class="SVDv3_sennheiserSav mceContentBody clearfix">
        <div class="SVDv3_masthead SVDv3_masthead_grey SVDv3_sennheiserSav_header">
            <div class="SVDv3_masthead_wrapper SVDv3_content_content">
                {% block breadcrumb_sennheiser %}{% endblock %}
                <div class="grid_container_12">
                    <div class="grid_row">
                        <div class="col_6_col col_last">
                            <h1 class="SVDv3_titres_noStyle"><img src="{{ asset('/images/illustration/pages/miniBoutique/sennheiser/SVDSENN_201410-Titre.png', 'static_images') }}" alt="{{ 'sennheiser_header_alt' | trans({}, 'catalog') }}"></h1>
                            <p class="text-xl">{% trans from "catalog" %}sennheiser_meta_description{% endtrans %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="SVDv3_hideSeekSearch SVDv3_texte_alignCentre">
            <div class="SVDv3_content_content">
                <form id="sennheiser_products">
                    <input id="SVDv3_sennheiser_product" placeholder="{{ 'sennheiser_placeholder' | trans({}, 'catalog') }}" type="text"/>
                </form>
            </div>
        </div>

        <div class="SVDv3_sennheiserSav_results_wrapper">
            <div class="SVDv3_content_content">
                <div class="SVDv3_article_element">
                    <p class="SVDv3_texte_alignCentre SVDv3_sennheiserSav_avertissement">
                        <strong>{{ 'sennheiser_warning' | trans({}, 'catalog') }}</strong></p>
                </div>

                {% block sennheiser %}{% endblock %}
            </div>
        </div>
    </div>

{% endblock %}

{% block javascripts %}
    {{ parent() }}

    <script type="text/javascript">
        var products = {{ products | json_encode() | raw }}
        var url = '{{ path('catalog_sennheiser_accessories', {'product_path' : 'path'}) }}'

        jQuery(document).ready(function () {
            new autoComplete({
                selector: '#SVDv3_sennheiser_product',
                minChars: 1,
                source: function (term, response) {
                    searchItems(term, response);
                },
                renderItem: function (item, search) {
                    return renderItem(item, search);
                },
                onSelect: function (e, term, item) {
                    selectItem(e, term, item);
                }
            });

            /**
             * searchItems
             *
             * @param {String} term
             * @param {Function} response
             */
            function searchItems(term, response)
            {
                var items = []
                var re = new RegExp(term, "gi");
                $.each(products, function (index, value) {
                    if (value.name.match(re)) {
                        items.push(value)
                    }
                })
                response(items)
            }

            /**
             * renderItem
             *
             * @param   {Object} item
             * @param   {String} search
             * @returns {String}
             */
            function renderItem(item, search)
            {
                search = search.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
                var re = new RegExp("(" + search.split(' ').join('|') + ")", "gi");

                var renderer = document.createElement('div');
                renderer.classList.add('autocomplete-suggestion');
                renderer.setAttribute('style', 'cursor: pointer;');
                renderer.setAttribute('data-val', item.name);
                renderer.setAttribute('data-path', item.path.slice(-1)[0]);
                renderer.innerHTML = (item.name).replace(re, "<b>$1</b>");

                return renderer.outerHTML;
            }

            /**
             * selectItem
             *
             * @param {Event}           event
             * @param {String}          term
             * @param {HtmlDivElement}  item
             */
            function selectItem(event, term, item)
            {
                if ((event.type == 'mousedown' && event.button == 0) || (event.type == 'keydown' && event.code == "Enter")) {
                    var item_path = item.getAttribute('data-path');

                    window.location.href = window.location.origin + url.replace('path', item_path);
                }

                event.preventDefault();
            }

            $('#sennheiser_products').on('submit', function (event) {

                window.location.href = window.location.origin + url.replace('path', $('#SVDv3_sennheiser_product').val());

                event.preventDefault();
            });
        });
    </script>

{% endblock %}

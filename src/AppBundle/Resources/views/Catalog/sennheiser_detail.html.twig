{% extends 'AppBundle:Catalog:sennheiser.html.twig' %}

{% block title %}{% trans with {'%product%': product.name } from "catalog" %}sennheiser_detail_title{% endtrans %}{% endblock %}

{% block breadcrumb_sennheiser %}
    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% block crumbs %}
            <li><a href="{{ path('catalog_sennheiser') }}">{{ 'sennheiser_breadcrumb' | trans({}, 'catalog') }}</a></li>
            {% if product['name'] is defined and product['name'] is not empty %}
                <li>{{ product.name }}</li>
            {% endif %}
        {% endblock %}
    {% endembed %}
{% endblock %}

{% block sennheiser %}
    {% if product is not defined or product is empty %}
        <div class="SVDv3_article_element">
            <div class="SVDv3_messageAlerte SVDv3_messageAlerte_alerte"><p>{{ 'missing_main_product' | trans({}, 'catalog') }}.</p></div>
        </div>
    {% else %}
        {% if accessories is defined and accessories is not empty %}
            <div class="SVDv3_article_element">
                <h2 class="SVDv3_titre_page">{{ 'accessories_for_product' | trans({'%product_name%': product.name}, 'catalog') }}</h2>
                <div class="clearfix SVDv3_rayon_listingProduits_liste_ligne SVDv3_rayon_listingProduits_liste_ligne_header">
                    <div class="SVDv3_rayon_listingProduits_ref"><p>{{ 'reference' | trans }}</p></div>
                    <div class="SVDv3_rayon_listingProduits_description"><p>{{ 'Descriptif' | trans }}</p></div>
                    <div class="SVDv3_rayon_listingProduits_prix"><p>{{ 'column_price' | trans({}, 'catalog') }}</p></div>
                    <div class="SVDv3_rayon_listingProduits_action"></div>
                </div>

                {% for accessory in accessories %}
                    <div class="clearfix SVDv3_rayon_listingProduits_liste_ligne"
                         data-name="{{ accessory.short_description }}"
                         data-category="Pièces détachées"
                         data-brand="Sennheiser"
                         data-id="{{ accessory.accessory_id }}"
                         data-price="{{ accessory.price.selling_price }}"
                    >
                        <div class="SVDv3_rayon_listingProduits_ref"><p><strong>{{ accessory.sku }}</strong></p></div>
                        <div class="SVDv3_rayon_listingProduits_description"><p>{{ accessory.short_description }}</p></div>
                        <div class="SVDv3_rayon_listingProduits_prix"><p class="SVDv3_zonePrix_prix">{{ accessory.price.selling_price | localizedcurrency_svd(currency, app.request.locale) }}</p></div>
                        <div class="SVDv3_rayon_listingProduits_action">
                            <a href="" class="SVDv3_bouton_ajoutPanier SVDv3_bouton_largeurFixe"
                               data-value="{{ accessory.accessory_id }}"
                               data-label="{{ accessory.short_description }}"
                               data-type="accessory">
                                <span>{{ 'add_to_basket' | trans({}, 'common') }}</span>
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="SVDv3_article_element">
                <div class="SVDv3_messageAlerte SVDv3_messageAlerte_alerte"><p>{{ 'missing_accessories_for_product' | trans({'product_name': product.name}, 'catalog') }}.</p></div>
            </div>
        {% endif %}
    {% endif %}
{% endblock %}

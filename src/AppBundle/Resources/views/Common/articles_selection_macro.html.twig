{% macro customer_article_line(article) %}
    {% import _self as article_selection_common %}
    <div class="basket-article basket-item">
        <div class="basket-row">
            <div class="basket-col item-picture basket-col-2 basket-col-m-3">{{ article_selection_common.article_picture(article) }}</div>
            <div class="basket-col basket-col-5 basket-col-m-8">
                <div class="item-description">
                    {{ article_selection_common.article_description(article) }}
                </div>
            </div>
            <div class="basket-col basket-col-2 basket-col-m-9 basket-push-m-3 item-amount">
                <span class="item-subtitle">{% trans from 'order' %}quantity{% endtrans %}&nbsp;:</span> {{ article.quantity }}
            </div>
            <div class="basket-col basket-col-3 basket-col-m-9 basket-push-m-3 item-price-total">
                <span class="item-subtitle">{% trans %}Total{% endtrans %}&nbsp;:</span> {{ article_selection_common.article_fullprice(article) }}
            </div>
        </div>
    </div>
    {% for warranty in article.warranties_in_cart %}
        <div class="basket-article basket-item">
            <div class="basket-row">
                <div class="basket-col item-picture basket-col-2 basket-col-m-3">&nbsp;</div>
                <div class="basket-col basket-col-5 basket-col-m-8">
                    <div class="item-description">
                        <!--
                        {% if warranty.tag_path|getWarrantyType == 'extension' %}
                            <img border="0" src="{{ asset('/images/illustration/garantie/ico-packageGarantie.gif', 'static_images') }}" alt="Ico-packageGarantie">
                        {% elseif warranty.tag_path|getWarrantyType == 'vol_casse' %}
                            <img border="0" src="{{ asset('/images/illustration/garantie/ico-packageVolEtCasse.gif', 'static_images') }}" alt="Ico-packageVolEtCasse">
                        {% endif %}
                        &nbsp;-->
                        {{ warranty.label }} {{ warranty.amount|localizedcurrency(currency, app.request.locale) }}
                    </div>
                </div>
                <div class="basket-col basket-col-2 basket-col-m-9 basket-push-m-3 item-amount">
                    <span class="item-subtitle">{% trans from 'order' %}quantity{% endtrans %}&nbsp;:</span> {{ article.quantity }}
                </div>
                <div class="basket-col basket-col-3 basket-col-m-9 basket-push-m-3 item-price-total">
                    <span class="item-subtitle">{% trans %}Total{% endtrans %}&nbsp;:</span> {{ (article.quantity * warranty.amount)|localizedcurrency(currency, app.request.locale) }}
                </div>
            </div>
        </div>
    {% endfor %}
{% endmacro %}


{% macro article_picture(article) %}
    {% if article.article_url is defined and article.article_url is not empty %}
        <a href="{{ article.article_url }}" title="{{ article.article_name }}" target="_blank">
            <img class="img-responsive" src="{{ preset(asset(article.image_url, 'article_images'), '140') }}" alt="{{ article.article_name }}"/></a>
    {% else %}
        <img src="{{ preset(asset(article.image_url, 'article_images'), '140') }}" alt="{{ article.article_name }}"/>
    {% endif %}
{% endmacro %}

{% macro article_description(article) %}
    {% import 'AppBundle:Common:article_macro.html.twig' as article_common %}
    <div class="item-title">
        <strong>
            <a href="{{ article.article_url }}" target="blank">
                {% if article.editorial_content.short_description is defined %}
                    {{ article.editorial_content.short_description }}
                {% else %}
                    {{ [article.brand_name, article.article_name]|join(' ') }}
                {% endif %}
            </a>
        </strong>
    </div>
    {% if article.editorial_content.basket_description is defined %}
        <div class="item-description-resume">
            {{ article.editorial_content.basket_description }}
        </div>
    {% endif %}

    {% if article.unbasketable_reason is not null %}
        <div class="mceContentBody">
            <span class="label label-danger label-md">{% trans %}Discontinued{% endtrans %}</span>
            {% if article['substitute_name'] is defined and article['substitute_name'] is not empty %}
                &nbsp;{% trans %}Remplacé par{% endtrans %} : <a href="{{ article.substitute_url }}"><strong>{{ article['substitute_name'] }}</strong></a>
            {% endif %}
        </div>
    {% else %}
        <span>{% trans from "order" %}price_per_unit{% endtrans %} {{ article.selling_price|localizedcurrency(currency, app.request.locale) }}</span>

        {% if article.ecotaxe is not null %}
            {% set ecotaxe = article.ecotaxe * article.quantity %}
            <div class="item-ecotaxe">
                {% trans with {'%ecotaxe_amount%': ecotaxe|localizedcurrency(currency, app.request.locale)} %}ecotaxe_amount{% endtrans %}
            </div>
        {% endif %}

        <div class="basket-availability">
            {{ article_common.article_availability(article) }}
        </div>
    {% endif %}

{% endmacro %}

{% macro article_fullprice (article) %}
    {% set final_price =  article.selling_price * article.quantity %}
    {{ final_price|localizedcurrency(currency, app.request.locale) }}
{% endmacro %}

<!-- Basket total price and voucher -->
{% macro customer_summary(articles) %}
    {% set full_articles_price = 0 %}
    {% set full_articles_ecotaxe = 0 %}
    {% for article in articles %}
        {% set full_articles_price = full_articles_price + article.full_selling_price %}
        {% if article.full_ecotaxe is not null %}
            {% set full_articles_ecotaxe = full_articles_ecotaxe + article.full_ecotaxe %}
        {% endif %}
    {% endfor %}

    <p>{% trans %}TOTAL TTC{% endtrans %}&nbsp;:<span> {{ full_articles_price|localizedcurrency(currency, app.request.locale) }}</span></p>

    {% if full_articles_ecotaxe > 0 %}
        <p class="item-ecotaxe">
            {% trans %}dont écotaxe{% endtrans %}&nbsp;: {{ full_articles_ecotaxe|localizedcurrency(currency, app.request.locale) }}
        </p>
    {% endif %}
{% endmacro %}

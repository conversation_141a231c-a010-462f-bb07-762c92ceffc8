{#
    var brand need to have following fields :
    string  name
    string  slug
    integer nb_advices
    float   avg_advices
    integer nb_recommendations

    var brand could have following fields
    array   subcategories

    if subcategories is defined, it must have following fields
    string  slug
    string  name
    array   articles
#}
{% macro header(brand, show_link = true) %}

{% if
    (brand.logo_url is not empty)
    or (brand.avg_advices > 0)
    or (brand.subcategories is defined and brand.subcategories is not empty)
%}
<div class="brand-header">
    <div class="title-gradient-roundcorner title-gradient-roundcorner-grey">
        {% trans with {'%brand.name%': brand.name|escape} from 'brand' %}brand_more_information{% endtrans %}
    </div>
    <div class="SVDv3_colonne_element">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-4">
                    {% if brand.logo_url is not empty %}
                        <div class="margin-sm">
                            <a href="{{ path('show_brand', {'slug': brand.slug}) }}" title="{{ brand.name|escape }}">
                                <img data-src="{{ asset(brand.logo_url, 'static_images') }}" alt="{{ brand.name|escape }}" class="brand-header-logo lozad" />
                            </a>
                        </div>
                    {% endif %}
                    {% if (brand.avg_advices > 0) %}
                        {% set count = brand.nb_advices|round %}
                        <div>
                            <span class="score-item-md">
                                <span class="score-item-row">
                                    <span class="score-item-stars">
                                        <span class="product-score"><span class="score-stars" style="width: {{ brand.avg_advices * 20 }}px;"></span></span>
                                    </span>
                                    <span class="score-item-text">
                                        <strong>{% if brand.avg_advices|number_format(1, ',')|last != 0 %}
                                            {{ brand.avg_advices|number_format(1, ',') }}
                                        {% else %}
                                            {{ brand.avg_advices|round }}
                                        {% endif %}</strong>
                                        {% transchoice count with {'%brand.nb_advices%': brand.nb_advices} from 'brand' %}brand_average{% endtranschoice %}<br />
                                    </span>
                                </span>
                            </span>
                        </div>
                        {% if (brand.nb_recommendations > 0) %}
                            <div>
                                <strong>{{ ((brand.nb_recommendations * 100) / brand.nb_advices)|round }}&#37;</strong>
                                {% trans from 'brand' %}brand_recommandations{% endtrans %}
                            </div>
                        {% endif %}
                    {% endif %}
                    {# almost empty content if nothing else to show to avoid breaking the structure #}
                    {% if brand.logo_url is empty and brand.avg_advices == 0 %}&nbsp;{% endif %}
                </div>
                {% if (brand.subcategories is defined and brand.subcategories is not empty) %}
                    <div class="col-12 col-md-8">
                        <div class="container">
                            <ul class="row list-unstyled mb-0">
                                {% for subcategory in brand.subcategories %}
                                    <li class="col-12 col-md-6 col-lg-4"><a href="{{ path('show_brand_category', {'slug': subcategory.slug, 'brand_slug': brand.slug}) }}">{{ subcategory.name }}</a>&nbsp;<span class="hidden-xs hidden-sm">({{ subcategory.nb_articles }})</span></li>
                                {% endfor %}
                            </ul>
                            {% if show_link %}
                            <ul class="list-unstyled pt-4 clearfix">
                                <li class="SVDv3_colonne_element_liste_avant">
                                    <a href="{{ path('show_brand', {'slug': brand.slug}) }}">
                                        <strong>
                                            {% trans with {'%brand.name%': brand.name|escape|capitalize} from 'brand' %}brand_show_all_products{% endtrans %}
                                        </strong>
                                    </a>&nbsp;
                                    <span class="hidden-xs hidden-sm">{% if brand.total_articles is defined %}({{ brand.total_articles }}){% endif %}</span>
                                </li>
                            </ul>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endmacro %}


{% macro listing(brand, show_link = true) %}
    <a href="{{ path('show_brand', {'slug': brand.slug}) }}" class="brand-listing-item">
        <div class="content-wrapper">
            <div class="img-container">
                <img data-src="{{ asset(brand.logo_url, 'static_images') }}" alt="{{ brand.name|escape }}" class="lozad" />
            </div>
            <div class="brand-name">
                {{ brand.name|escape|capitalize|raw }}
            </div>
        </div>
    </a>
{% endmacro %}


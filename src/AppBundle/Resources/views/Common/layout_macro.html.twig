{% macro link_stylesheet(stylesheets, attributes = {}) %}
    {% import _self as layout %}
    {% set link_attr = {'type':'text/css', 'media':'screen,print', 'rel':'stylesheet'}|merge(attributes) %}

    {% for item in stylesheets %}
        {% if item is iterable %}
            {% for key, value in item %}
                {% set link_attr = link_attr|merge({'href': asset(key)}) %}
                {% set link_attr = link_attr|merge(value) %}
            {% endfor %}
        {% else %}
            {% set link_attr = link_attr|merge({'href': asset(item)}) %}
        {% endif %}
        {{ layout._build_header_link(link_attr) }}
    {% endfor %}
{% endmacro %}

{% macro link_favicons(definitons) %}
    {% import _self as layout %}
    {% for item in definitons %}
        {% set link_attr = {} %}
        {% if item is iterable %}
            {% for key, value in item %}
                {% set link_attr = link_attr|merge({'href': asset(key)}) %}
                {% set link_attr = link_attr|merge(value) %}
            {% endfor %}
            {{ layout._build_header_link(link_attr) }}
        {% endif %}
    {% endfor %}
    <meta name="theme-color" content="#ffffff">
{% endmacro %}

{% macro _build_header_link(attributes) %}
    {% spaceless %}
        <link{% for attrname, attrvalue in attributes %} {{ attrname }}="{{ attrvalue }}"{% endfor %}/>
    {% endspaceless %}
{% endmacro %}

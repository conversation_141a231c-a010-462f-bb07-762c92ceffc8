{% macro display_pager(pager, path_name) %}
    {% set page_offset = 3 %}
    <div class="SVDv3_pagination panel" data-context="pager">
        <div class="panel-body padding-sm">
            <div class="grid_container_12">
                <div class="grid_row">
                    <div class="col_2_col text-left SVDv3_pagination_nbResults" data-context="count-result">
                        {{ 'count_result' | transchoice(pager.count, {'%count%': pager.count}, 'pager') | raw }}
                    </div>
                    <div class="col_8_col text-center SVDv3_pagination_listPages">
                        {% if pager.lastPage > 1 %}
                            <ul class="pagination">
                                {% if pager.previousPage %}
                                    <li>
                                        <a href="{{ path(path_name, {'page': pager.page - 1}) }}">
                                            <i class="icon icon-chevron-left"></i>
                                        </a>
                                    </li>
                                {% endif %}

                                {% if pager.page - page_offset > 1 %}
                                    <li class="disabled"><span>…</span></li>
                                {% endif %}

                                {% for i in 1..pager.lastPage if (i >= (pager.page - page_offset) and i <= (pager.page + page_offset)) %}
                                    <li class="{% if i == pager.page %}active{% endif %}">
                                        <a href="{{ path(path_name, {'page': i}) }}">{{ i }}</a>
                                    </li>
                                {% endfor %}

                                {% if pager.page + page_offset < pager.lastPage %}
                                    <li class="disabled"><span>…</span></li>
                                {% endif %}

                                {% if pager.nextPage %}
                                    <li>
                                        <a href="{{ path(path_name, {'page': pager.page + 1}) }}">
                                            <i class="icon icon-chevron-right"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        {% endif %}
                    </div>
                    <div class="col_2_col col_last text-right SVDv3_pagination_nbPages" data-context="current-page">
                        {{ 'page_current_out_of_last' | trans({'%current%': pager.page, '%last%': pager.lastPage}, 'pager') | raw }}
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endmacro %}
{% macro display_pager_v5(pager, path_name) %}
    {% set page_offset = 3 %}
<div class="flex flex-col">
    <div class="p-3 grid grid-cols-12 gap-4 items-center">
        <div class="col-span-2 text-left">
            {{ 'count_result' | transchoice(pager.count, {'%count%': pager.count}, 'pager') | raw }}
        </div>
        <div class="col-span-8 text-center">
            {% if pager.lastPage > 1 %}
                <ul class="pagination flex justify-center gap-2 items-center">
                    {% if pager.previousPage %}
                        <li>
                            <a href="{{ path(path_name, {'page': pager.page - 1}) }}">
                                {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                                    icon: 'arrow-left',
                                    size: 20,
                                    color: '#444444'
                                } %}
                            </a>
                        </li>
                    {% endif %}

                    {% if pager.page - page_offset > 1 %}
                        <li class="px-2 py-1">…</li>
                    {% endif %}

                    {% for i in 1..pager.lastPage if (i >= (pager.page - page_offset) and i <= (pager.page + page_offset)) %}
                        <li class="{% if i == pager.page %}bg-blue-200{% else %}bg-white text-black{% endif %} px-2 py-1 rounded-md">
                            <a href="{{ path(path_name, {'page': i}) }}">{{ i }}</a>
                        </li>
                    {% endfor %}

                    {% if pager.page + page_offset < pager.lastPage %}
                        <li class="px-2 py-1">…</li>
                    {% endif %}

                    {% if pager.nextPage %}
                        <li>
                            <a href="{{ path(path_name, {'page': pager.page + 1}) }}">
                                {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                                    icon: 'arrow-right',
                                    size: 20,
                                    color: '#444444'
                                } %}
                            </a>
                        </li>
                    {% endif %}
                </ul>
            {% endif %}
        </div>
        <div class="col-span-2 text-right">
            {{ 'page_current_out_of_last' | trans({'%current%': pager.page, '%last%': pager.lastPage}, 'pager') | raw }}
        </div>
    </div>
</div>

{% endmacro %}


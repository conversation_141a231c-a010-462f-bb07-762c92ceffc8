{% macro article_block(article) %}
    <!-- Add 'sales' in array to display block -->
    {% set promo_types = ['santa_claus_free_shipping', 'cetelem'] %}

    {% for promo_type in promo_types %}
        {% if
            (dates.promotional[promo_type] is defined and (date(dates.promotional[promo_type].start) < date()) and (date(dates.promotional[promo_type].end) > date()))
            or dates.promotional[promo_type] is not defined
        %}
            {% if promo_type != 'free_shipping' or (promo_type == 'free_shipping' and (date(dates.promotional['santa_claus_free_shipping'].start) > date() or date(dates.promotional['santa_claus_free_shipping'].end) < date())) %}
                {% embed "@App/Promotion/block/"~promo_type~".html.twig" with {'article': article, 'promotional_dates': dates.promotional} %}{% endembed %}
            {% endif %}
        {% endif %}
    {% endfor %}
{% endmacro %}

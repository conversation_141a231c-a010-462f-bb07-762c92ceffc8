<div class="mceContentBody">
    {% for setup in setups %}
        <div class="panel cptclient-cmd-resume">
            <div class="panel-heading">
                <div class="grid_container_12">
                    <div class="grid_row">
                        <div class="col_3_col cptclient-cmd-num">
                            <span class="text-xs text-uppercase">{% trans from "event" %}budget{% endtrans %}</span><br/>
                            <strong>{% trans from "event" %}around{% endtrans %} {{ setup.budget|localizedcurrency_svd(currency, app.request.locale) }}</strong>
                        </div>
                        <div class="col_3_col cptclient-cmd-num">
                            <span class="text-xs text-uppercase">{% trans from "event" %}area{% endtrans %}</span><br/>
                            <strong>{% trans from "event" %}around{% endtrans %} {{ setup.surface_area_m2 }}&nbsp;m<sup>2</sup></strong>
                        </div>
                        <div class="col_3_col cptclient-cmd-num">
                            <span class="text-xs text-uppercase">{% trans %}status{% endtrans %}</span><br/>
                            {% if setup.is_accepted %}
                                <span class="label label-success">{% trans from "event" %}online{% endtrans %}</span>
                            {% elseif setup.is_accepted is null %}
                                <span class="label label-default">{% trans from "event" %}awaiting_validation{% endtrans %}</span>
                            {% else %}
                                <span class="label label-warning">{% trans from "event" %}not_compliant{% endtrans %}</span>
                            {% endif %}
                        </div>
                        <div class="col_3_col col_last text-center cptclient-cmd-detail">
                            {% if setup.is_accepted %}
                                <a href="{{ path('show_setup', {'installation_id': setup.installation_id}) }}" class='btn btn-success btn-sm btn-block'>{% trans from "account" %}see_details{% endtrans %}</a>
                            {% else %}
                                <a href="{{ path('customer_account_setup_edit', {'installation_id': setup.installation_id}) }}" class='btn btn-grey btn-sm btn-block'>{% trans %}edit{% endtrans %}</a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="grid_row">
                        <br/>
                        <em>{% trans %}created_at{% endtrans %} {{ setup.created_at | localizeddate('full', 'short', app.request.locale) }}</em>
                    </div>
                </div>
            </div>

            <div class="panel-body">
                {% if setup.comment is not empty %}
                    <div>
                        <span><i class="icon icon-fw icon-comment"></i> {% trans from "event" %}comment{% endtrans %}</span><br/>
                        {{ setup.comment | truncate (150, true, '…')}}
                    </div>
                {% endif %}
                {% if setup.articles is not empty %}
                    {% if setup.comment is not empty %}<br/>{% endif %}
                    <div>
                        <span><i class="icon icon-fw icon-tag"></i>{% trans from "event" %}setup_made_up_of{% endtrans %}</span>
                        <ul class="margin-no text-sm">
                            {% for article in setup.articles %}
                                <li><strong>{{ article.quantity }}</strong>&nbsp;x {{ article.name }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
             </div>
        </div>
    {% else %}
        <div class="text-center pt-0 pt-lg-5">
            <div class="text-center mt-5">
                <p class="mb-5">{% trans from "account" %}no_setup{% endtrans %}</p>
            </div>
        </div>
    {% endfor %}
    <div class="grid_container_12">
        <div class="grid_row margin-no">
            <div class="col_6_col">
                <a class="btn btn-default btn-round btn-block" href="{{ path('setup_index') }}">
                    {% trans from 'event' %}see_all_setup_list{% endtrans %}
                </a>
            </div>
            <div class="col_6_col col_last">
                <a class="btn btn-purple btn-round btn-block" href="{{ path('customer_social_new_installation_get') }}">
                    {% trans from 'event' %}create_an_installation{% endtrans %}
                </a>
            </div>
        </div>
    </div>
</div>

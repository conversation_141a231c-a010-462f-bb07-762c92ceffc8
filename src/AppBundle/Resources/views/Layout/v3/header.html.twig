{% set main_menu_items = [
    { data_context: 'contact-us', icon: 'headset', mobile_class: 'hidden min-370px' },
    { data_context: 'stores', icon: 'location-dot', mobile_class: 'hidden min-440px', href: '/magasins-hifi-home-cinema', title: 'Nos magasins' },
    { data_context: 'account', icon: 'user', href: '/mon-compte', title: 'Mon compte' },
    { data_context: 'basket', id: 'button-basket-app', icon: 'shopping-basket', href: '/mon-panier', title: 'Mon panier' },
] %}
<div class='header-placeholder'>
    <header class="main">
        <div id="impersonation-app"></div>
        <div class="mobile" data-context="mobile" id="header-mobile-app">
            <section class="left" data-context="left">
                <div>
                    {{ 'large/bars' | svd_icon('menu-trigger-mobile icon-50 cursor-pointer', 'burger') | raw }}
                </div>
                <a href="{{ path('home') }}" class="mobile-logo" data-context="mobile-logo" title="Son-Vidéo.com la référence hi-fi et home-cinéma"></a>
            </section>

            <nav class="right d-flex align-items-center ml-auto" data-context="right">
                {# Stand alone search icon not shown on desktop #}
                {{ 'medium/magnifying-glass' | svd_icon('action icon-38 cursor-pointer', 'search') | raw }}

                {% for item in main_menu_items %}
                    {% if item.href is defined %}
                        <a
                                href="{{ item.href }}"
                                class="action {{ item.mobile_class ?? '' }}"
                                title="{{ item.title }}"
                                data-context="{{ item.data_context }}"
                        >
                            {{ ('medium/' ~ item.icon) | svd_icon('icon-38') | raw }}
                        </a>
                    {% else %}
                        <span
                                class="action cursor-pointer {{ item.mobile_class ?? '' }}"
                                data-context="{{ item.data_context }}"
                                {% if item.data_context is same as 'contact-us' %}
                                    data-immediate-recall="header"
                                {% endif %}
                        >
                            {{ ('medium/' ~ item.icon) | svd_icon('icon-38') | raw }}
                        </span>
                    {% endif %}
                {% endfor %}
            </nav>
        </div>

        <section class="top align-items-center" data-context="top" id="header-desktop-app">
            <a href="{{ path('home') }}" class="site-logo" data-context="logo" title="Son-Vidéo.com la référence hi-fi et home-cinéma"></a>

            <div class="desktop-search d-flex flex-grow-1" id="search-desktop-input-app">
                <input
                        type="text"
                        placeholder="Rechercher un produit, une marque..."
                       class="w-full rounded-full border-none text-base pl-6 pr-16 h-50px truncate text-[#101214] placeholder:text-[#101214] focus:ring-0"
                        data-context="search-input"
                />

                {{ 'large/magnifying-glass' | svd_icon('icon-50 absolute top-0 right-3 cursor-pointer text-[#1E2024]', 'search') | raw  }}
            </div>

            <div class="header-phone">
                <div class="header-phone-number">
                    01&nbsp;55&nbsp;09&nbsp;13&nbsp;30
                </div>
            </div>

            <div class="right" data-context="right">
                {% for item in main_menu_items %}
                    {% if item.href is defined %}
                        <a
                                class="action"
                                href="{{ item.href }}"
                                data-context="{{ item.data_context }}"
                        >
                            {{ ('large/' ~ item.icon) | svd_icon('icon-50') | raw }}
                        </a>
                    {% else %}
                        <span class="action cursor-pointer"
                              data-context="{{ item.data_context }}" {% if item.data_context is same as 'contact-us' %}
                                    data-immediate-recall="header"
                                {% endif %}>
                            {{ ('large/' ~ item.icon) | svd_icon('icon-50') | raw }}
                        </span>
                    {% endif %}
                {% endfor %}
            </div>
        </section>

        <div id="search-dropdown-app"></div>

        {% spaceless %}{{ render(controller('AppBundle:Layout/MegaMenu:show', {'version': 'v3' }))|raw }}{% endspaceless %}

        <section data-context="reassurance-header" class="reassurance-header">
            {% set reassurance_items = [
                { icon: 'truck', label: 'Expédition sous 24 heures', classes: 'hidden min-lg' },
                { icon: 'box', label: 'Retrait gratuit en magasin', classes: 'hidden min-lg' },
                { icon: 'phone', label: '', classes: 'phone' },
                { icon: 'credit-card', label: '3x ou 4x sans frais par CB', classes: 'hidden min-lg' },
                { icon: 'badge-check', label: 'Satisfait ou remboursé', classes: 'hidden min-lg' },
            ] %}
            {% for item in reassurance_items %}
                <div class="reassurance-item {{ item.classes ?? '' }}" data-context="reassurance">
                    {{ ('light/' ~ item.icon) | svd_icon('icon-24') | raw  }}
                    <span>{{ item.label }}</span>
                </div>
            {% endfor %}
        </section>
    </header>
</div>

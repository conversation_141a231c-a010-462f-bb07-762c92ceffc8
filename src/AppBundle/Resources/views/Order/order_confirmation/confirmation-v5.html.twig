{% extends 'AppBundle:V5/Layout:base.html.twig' %}

{% import 'AppBundle:Common:address_macro.html.twig' as address_macro %}

{% block title %}{% trans from "order" %}order_confirmation{% endtrans %}{% endblock %}

{% block body_class %}body-basket{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('page/confirmation')) }}
{% endblock %}

{% block content %}
    <div class='confirmation'>
        <div class='order'>
            <div class='title-message-summary'>
                {% include 'AppBundle:Order:order_confirmation/title.html.twig' %}
                {% include 'AppBundle:Order:order_confirmation/message.html.twig' %}
                {% include 'AppBundle:Order:order_confirmation/summary.html.twig' %}
            </div>
            
            {% include 'AppBundle:Order:order_confirmation/basket.html.twig' %}
        </div>

        {% if has_newsletter == false %}
            {% include 'AppBundle:Order:order_confirmation/newsletter.html.twig' %}
        {% endif %}
        {% include 'AppBundle:Order:order_confirmation/guides.html.twig' %}
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('page/confirmation') }}
{% endblock %}

{% block tracking %}
    {{ parent() }}
    {{ render(controller('AppBundle:Tracking/Tracking:show', {'location': 'order_confirm', 'extra_data': customer_order.extract() }))|raw }}
{% endblock %}
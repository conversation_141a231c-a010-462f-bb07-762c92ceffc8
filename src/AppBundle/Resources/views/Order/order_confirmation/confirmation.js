// *** STYLES *** //
require('./confirmation.scss')

// *** JS *** //
;(() => {
    const { post } = require('axios')

    // Handle newsletter subscription
    if (document.getElementById('newsletter-form')) {
        document.getElementById('newsletter-confirm').style.display = 'none'
        document.getElementById('newsletter-error').style.display = 'none'

        window.SonVideo.subscribe = () => {
            post('/api/newsletter/change-souscription', {
                origin_subscribe: 'command-confirmation',
                subscription: true,
            })
                .then(() => {
                    document.getElementById('newsletter-form').style.display = 'none'
                    document.getElementById('newsletter-confirm').style.display = 'block'
                })
                .catch(() => {
                    document.getElementById('newsletter-form').style.display = 'none'
                    document.getElementById('newsletter-error').style.display = 'block'
                })
        }
    }

    window.SonVideo.contactUs = (label) => {
        window.SonVideo.contact.open({ label: `confirmation commande ${label}> Contactez-nous`, action: '' })
    }

    window.SonVideo.copyToClipboard = (value) => {
        window.navigator.clipboard.writeText(value)
    }
})()

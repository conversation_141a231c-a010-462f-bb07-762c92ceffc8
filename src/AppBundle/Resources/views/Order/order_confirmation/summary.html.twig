{# Confirmation data blocks #}
<div data-context='data' class='data'>
	<div class='content'>
		<div class='number-delivery'>
			<div class='number'>
				{%
					set info = is_delayed_payment_method ? 'En attente de paiement'
						: 'Payée par ' ~ payment_method.label_i18n.FR
				%}
				<div class='title'>Commande #{{ customer_order.customer_order_id }}</div>
				<div class='info'>{{ info }}</div>
			</div>
			<div class='delivery'>
				{%
					set title = shipment_method.is_store ? 'Livraison en magasin'
					: shipment_method.is_relay ? 'Livraison en point relais'
					: 'Livraison à domicile'
				%}
				{%
					set description = is_delayed_payment_method ? 'Expédition à réception du paiement'
					: shipment_method.is_store ? 'Un sms de confirmation vous sera envoyé pour vous prévenir de la disponibilité.'
					: customer_order.estimated_delivery_date ? customer_order.estimated_delivery_date|format_delivery_date_french
					: shipment_method.no_delay_fallback
				%}
				<div class='title'>{{ title }}</div>
				<div class='info'>{{ description }}</div>
			</div>
		</div>
		<div class='address' data-context="address">
			<div class='title'>Adresse de livraison</div>
			<div class='info'>
				<div>{{ customer_order.shipping_address.company_name }}</div>
				{% if shipment_method.is_store == false and shipment_method.is_relay == false  %}
					<div>{{ customer_order.shipping_address.first_name ~ ' ' ~ customer_order.shipping_address.last_name }}</div>
				{% endif %}
				<div>{{ customer_order.shipping_address.address }}</div>
				<div>{{ customer_order.shipping_address.postal_code }} {{ customer_order.shipping_address.city }}</div>
				<div>{{ countries[customer_order.shipping_address.country_code] }}</div>
			</div>
		</div>
	</div>
	<a class='action' href='/mon-compte/ma-commande/{{ customer_order.customer_order_id }}' data-context="link-to-order">
		{% include 'AppBundle:V5/Component/button:svd_primary.html.twig' with {
			label: "Suivre ma commande"
		} %}
	</a>
</div>
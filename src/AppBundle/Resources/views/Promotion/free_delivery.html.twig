{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% import 'AppBundle:Common:event_macro.html.twig' as event_macro %}

{% block title %}
    {% if ((date(dates.promotional.free_shipping_mondial_relay.start) < date()) and (date(dates.promotional.free_shipping_mondial_relay.end) > date())) %}
        {% trans from 'promotion' %}free_shipping_mondial_relay_on_short{% endtrans %}
    {% else %}
        {% trans from 'promotion' %}free_shipping{% endtrans %}
    {% endif %}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
{% endblock %}

{% block metas %}
    {% if ((date(dates.promotional.free_shipping.start) < date()) and (date(dates.promotional.free_shipping.end) > date())) or ((date(dates.promotional.santa_claus_free_shipping.start) < date()) and (date(dates.promotional.santa_claus_free_shipping.end) > date())) or ((date(dates.promotional.free_shipping_mondial_relay.start) < date()) and (date(dates.promotional.free_shipping_mondial_relay.end) > date())) %}
        {% if (date(dates.promotional.santa_claus_free_shipping.start) < date()) and (date(dates.promotional.santa_claus_free_shipping.end) > date()) %}
            {% set metas = metas|merge({'description': ('free_shipping_on_description') | trans({'%end%': dates.promotional.santa_claus_free_shipping.end|localizeddate('none', 'none', null, null, 'EEEE d MMMM Y') }, 'promotion') | truncate(165, true, '...') }) %}
        {% else %}
            {% set metas = metas|merge({'description': ('free_shipping_on_description') | trans({'%end%': dates.promotional.free_shipping.end|localizeddate('none', 'none', null, null, 'EEEE d MMMM Y') }, 'promotion') | truncate(165, true, '...') }) %}
        {% endif %}
    {% else %}
        {% set metas = metas|merge({'description': ('free_shipping_off_description') | trans({}, 'promotion') | truncate(165, true, '...') }) %}
    {% endif %}
    {{ parent() }}
{% endblock %}

{% block content %}

    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% block crumbs %}
            <li>
                {% if ((date(dates.promotional.free_shipping_mondial_relay.start) < date()) and (date(dates.promotional.free_shipping_mondial_relay.end) > date())) %}
                    {% trans from 'promotion' %}free_shipping_mondial_relay_on_short{% endtrans %}
                {% else %}
                    {% trans from 'promotion' %}free_shipping{% endtrans %}
                {% endif %}
            </li>
        {% endblock crumbs %}
    {% endembed %}
    <div class="mceContentBody">
        <h1 class="SVDv3_mobile_navbar">
            {% if ((date(dates.promotional.free_shipping_mondial_relay.start) < date()) and (date(dates.promotional.free_shipping_mondial_relay.end) > date())) %}
                {% trans from 'promotion' %}free_shipping_mondial_relay_on_short{% endtrans %}
            {% else %}
                {% trans from 'promotion' %}free_shipping{% endtrans %}
            {% endif %}
        </h1>
        <div class="SVDv3_page_content editorial clearfix">
            {% if ((date(dates.promotional.free_shipping.start) < date()) and (date(dates.promotional.free_shipping.end) > date())) or ((date(dates.promotional.free_shipping_mondial_relay.start) < date()) and (date(dates.promotional.free_shipping_mondial_relay.end) > date())) or ((date(dates.promotional.santa_claus_free_shipping.start) < date()) and (date(dates.promotional.santa_claus_free_shipping.end) > date())) or ((date(dates.promotional.black_friday.start) < date()) and (date(dates.promotional.black_friday.end) > date())) %}
                {% embed "@App/Promotion/block/free_delivery_on.html.twig" %}{% endembed %}
            {% else %}
                {% embed "@App/Promotion/block/free_delivery_off.html.twig" %}{% endembed %}
            {% endif %}
        </div>
    </div>
{% endblock %}

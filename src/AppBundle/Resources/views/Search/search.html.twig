{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% use 'AppBundle:Search:search_widget.html.twig' %}
{% import 'AppBundle:Common:event_macro.html.twig' as event_macro %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('stand_v2_style')) }}
    {{ block('search_css') }}
{% endblock %}

{% block body_class %}body-search{% endblock %}

{% block title %}{% trans from 'search' %}your_search_on_son-video{% endtrans %}{% endblock %}

{% block tracking %}
    {{ render(controller('AppBundle:Tracking/Tracking:show', {'location': 'search' }))|raw }}
    {{ parent() }}
{% endblock %}

{% block content %}
        {% embed "@App/Common/block/breadcrumbs.html.twig" %}
            {% block crumbs %}
                <li>{% trans from 'search' %}search{% endtrans %}</li>
            {% endblock crumbs %}
        {% endembed %}

        {% if total_items == 0 %}
            <h1 class="SVDv3_mobile_navbar hidden-xs hidden-sm">{% trans from 'search' %}search{% endtrans %}</h1>
            <div class="clearfix SVDv3_article_element">
                <div class="grid_container_12">
                    <div class="grid_row">
                        <div class="col_8_col">
                            <div class="mceContentBody">
                                <p>{% trans %}empty_search_result{% endtrans %}<br>{% trans %}empty_search_invitation_to_complete{% endtrans %}
                                </p>
                                <p>{% trans %}contact_sentence_start{% endtrans %}
                                    <strong>{{ telephone.hotline_numero|raw }}</strong> {{ telephone.hotline_mention|raw }}, {% trans %}contact_sentence_end{% endtrans %}
                                </p>
                            </div>
                        </div>
                        <div class="col_4_col col_last">
                            {{ render(controller('CmsBundle:Widget/ClickbaitColumn:show', {'locations': ['search'], 'limit': 2}))|raw }}
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div id="search_app" class="SVDv3_colonnes_2colonnes1 clearfix" data-location="resultats de recherche">
                <div class="SVDv3_colonnes_colonne_gauche visible-xs visible-sm visible-md visible-lg">
                    {{ block('search_filter') }}
                </div>
                <div class="SVDv3_colonnes_colonne_droite">
                    {{ block('search_result') }}
                </div>
            </div>
        {% endif %}
{% endblock %}

{% block additional_content %}
    {% if total_items == 0 %}
        <div class="SVDv3_masthead SVDv3_masthead_grey" id="search-results-more">
            <div class="SVDv3_masthead_wrapper SVDv3_content_content clearfix">
                <div class="mceContentBody font-opensans">
                    <div class="SVDv3_section_titre">
                        <h2 class="SVDv3_titre_texte_niveau1">{%  trans from 'search' %}no_idea{% endtrans %}</h2>
                        <p>
                            {% trans with {
                                '%clearances_link%'   : path('show_destock_stand'),
                                '%promotions_link%'   : path('article_on_offer'),
                                '%new_products_link%' : path('article_new'),
                                '%exclusivities_link%': path('article_exclusive'),
                                '%shops_link%'        : path('show_shops')
                            }
                            from 'search' %}
                            no_idea_hook
                            {% endtrans %}
                        </p>
                    </div>
                    {{ event_macro.recurring_events(true) }}
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block  javascripts %}
    {{ parent() }}

    {{ block('search_javascript') }}
    <script>
        window.SonVideo.header.initSearchTerms("{{ search_terms | replace({'"': '\\"'}) | raw }}")
    </script>

    {{ encore_entry_script_tags('clickbait/clickbait_column') }}

    <script>
        SonVideo.pushAnalytics('search', {
            articles: {{ total_items }},
            brands: 0,
            stands: 0,
            search_term: window.location.search.split('=')[1],
            search_origin: 'result page'
        });
    </script>
{% endblock %}


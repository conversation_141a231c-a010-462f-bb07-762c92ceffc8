{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% use 'AppBundle:Search:search_widget.html.twig' %}
{% import 'AppBundle:Search:search_macro.html.twig' as search %}
{% import 'AppBundle:Common:guide_macro.html.twig' as guide_macro %}

{% block stylesheets %}
    {{ parent() }}
    {{ block('search_css') }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('stand_level_2_style')) }}
{% endblock %}

{% block metas %}
    {% if shop.description is defined and shop.description is not empty %}
        {% set metas = metas|merge({'description': shop.description }) %}
    {% endif %}

    {{ parent() }}
{% endblock %}

{# OpenGraph #}
{% block og %}
    <meta property='og:type' content='product.group'>
    <meta property='og:url' content="{{ url('show_shop', {'slug': shop.slug}) }}">
    <meta property='og:title' content="{{ shop.title }}">
    <meta property='og:description' content="{{ shop.description | html_entity_decode }}">
    <meta property='og:image' content="{{ shop.image_banner ? asset(shop.image_banner, 'static_images') : base_url ~ '/images/ui/uiV5/logo-partage.png' }}">
    <meta property='og:image:alt' content="{{ 'Image ' ~ shop.title }}">
{% endblock %}

{% block title %}
    {%  if shop.title is not empty %}
        {{ shop.title }}
    {% else %}
        {{ shop.name }}
    {% endif %}
{% endblock %}

{% block structured_data %}
    <script type="application/ld+json">
{{ structured_data | raw }}
</script>
{% endblock %}

{% block tracking %}
    {% set extra_data = shop.extract()|merge({'search_data': search_data }) %}
    {{ render(controller('AppBundle:Tracking/Tracking:show', {'location': 'shop', 'extra_data': extra_data }))|raw }}
    {{ parent() }}
{% endblock %}

{% block content %}
    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% use 'AppBundle:Shop:blocks.html.twig' %}
    {% endembed %}

    <h1 class="SVDv3_mobile_navbar">{{ shop.name }}</h1>
    {% if shop.editorial_content.presentation is defined and shop.editorial_content.presentation is not empty %}
        <div class="editorial">
            {% if shop.image_banner is not null %}
                <div class='editorial-head'>
                    <img alt="{{ shop.title }}" src="{{ asset(shop.image_banner, 'static_images') }}"/>
                </div>
            {% endif %}

            <div class='editorial-body'>
                {{ shop.editorial_content.presentation | lazy_load_content | raw }}
            </div>
        </div>
    {% endif %}
    <div class="SVDv3_colonnes_2colonnes1 mb-5 clearfix" id="search_app" data-location="sélection">
        <div class="SVDv3_colonnes_colonne_gauche visible-xs visible-sm visible-md visible-lg">
            {% if total_items is defined and total_items > 0 %}
                {{ block('search_filter') }}
            {% endif %}

            <div class="visible-md visible-lg">
                {{ render(controller('CmsBundle:Widget/ClickbaitColumn:show', {'locations': ['shop_' ~ shop.shop_id]}))|raw }}
            </div>

        </div>
        <div class="SVDv3_colonnes_colonne_droite">
            <div class="SVDv3_colonnes_2colonnes1 clearfix">
                {% if total_items is defined %}
                    {% if total_items > 0 %}
                        {{ search.display_search_result('true', 'grid', 3, '') }}
                    {% else %}
                        <div class="mceContentBody">
                            <p>{% trans %}sorry_no_article_is_available_in_this_section_for_the_moment{% endtrans %}</p>
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>

    {# how to choose #}
    {{ guide_macro.understand_to_choose_guide(guide) }}
{% endblock %}
{% block additional_content %}
    {# Advice sheets #}
    {{ render(controller('CmsBundle:Widget/Guide/Guide:showAssociatedGuideForShop',{'shop_id': shop.shop_id, '_locale': app.request.getLocale()}))|raw }}
{% endblock %}

{% block  javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('clickbait/clickbait_column') }}
    {% if total_items is defined %}
        {{ block('search_javascript') }}
    {% endif %}

{% endblock %}

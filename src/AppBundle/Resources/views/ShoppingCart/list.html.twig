{% extends 'AppBundle:V5/Layout:base.html.twig' %}

{% block body_class %}body-basket{% endblock %}

{% block tracking %}
    {{ render(controller('AppBundle:Tracking/Tracking:show', {'location': 'shopping_cart', 'extra_data': {
        'articles': articles,
    }}))|raw }}
    {{ parent() }}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('page/basket')) }}
{% endblock %}

{% block message %}
{% endblock %}

{% block content %}
    {% include 'AppBundle:V5/Layout:flash_message.html.twig' with { renderer: "js"} %}
    <div id="app_basket" class='basket-container'>
        <div class='title'>Mon panier</div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    
    <script type="text/javascript">
        window.SonVideo = window.SonVideo || {}
        window.SonVideo.basket_data = {
            api: {
                cart_add_promo:                 '{{ path('cart_add_promo') }}',
                cart_remove_promo:              '{{ path('cart_remove_promo') }}',
                cart_remove_article:            '{{ path('cart_remove_article') }}',
                cart_update_article_quantity:   '{{ path('cart_update_article_quantity') }}',
                cart_remove_quote:              '{{ path('cart_remove_quote') }}',
                home:                           '{{ path('home') }}',
                order_create:                   '{{ path('order_create') }}',
                back_to_shopping:               '{{ app.session.get('back_to_shopping_url', path('home')) }}',
                save_cart_selection:            '{{ path('save_cart_selection') }}',
                cart_add_warranty:              '{{ path('cart_add_warranty') }}',
                cart_remove_warranty:           '{{ path('cart_remove_warranty') }}',
                cart_update_accessory_quantity: '{{ path('cart_update_accessory_quantity') }}',
                cart_remove_accessory:          '{{ path('cart_remove_accessory') }}',
                catalog_sennheiser:             '{{ path('catalog_sennheiser') }}',
                catalog_lamp:                   '{{ path('catalog_lamp') }}',
                cart_add_catalog:               '{{ path('cart_add_catalog') }}',
                cart_add_magazine:              '{{ path('cart_add_magazine') }}',
                cart_remove_catalog:            '{{ path('cart_remove_catalog') }}',
                cart_remove_magazine:           '{{ path('cart_remove_magazine') }}',
            },
            summary: {{ summary | json_encode | raw }},
            articles: {{ articles | json_encode | raw }},
            quotes: {{ quotes | json_encode | raw }},
            accessories: {{ accessories | json_encode | raw }},
            promotions: {{ promotions | json_encode | raw }},
            is_authenticated: {{ (app.user and is_granted('IS_AUTHENTICATED_FULLY'))? 'true' : 'false' }},
            available_catalog: {{ available_catalog | json_encode | raw }},
            available_magazine: {{ available_magazine | json_encode | raw }},
            catalog_in_basket_info: {{ catalog_in_basket_info | json_encode | raw }},
            magazine_in_basket_info: {{ magazine_in_basket_info | json_encode | raw }},
            previous_cart_item_ids: {{ previous_cart_item_ids | json_encode | raw }},
        }
    </script>

    {{ encore_entry_script_tags('order/basket')  }}
{% endblock %}

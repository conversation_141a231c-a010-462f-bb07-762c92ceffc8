{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% set locale = app.request.locale %}
{% set stand_parent = stand_ancestors | last %}
{% set further_information = false %} {# TODO #}

{# TODO REMOVE THIS HACK WHEN MOBILE NAVIGATION IS ONLINE #}
{% set hack_menu = {
    "Accessoires" : [
        {"name": "Meubles hi-fi", "slug": "accessoires/meubles-et-supports/meubles-hi-fi"},
        {"name": "Meubles TV", "slug": "accessoires/meubles-et-supports/meubles-tv-video"},
        {"name": "Pieds pour enceintes", "slug": "accessoires/meubles-et-supports/pieds-pour-enceintes"},
        {"name": "Supports pour enceintes", "slug": "accessoires/supports/supports-pour-enceintes"},
        {"name": "Supports TV", "slug": "accessoires/meubles-et-supports/supports-tv"},
        {"name": "Supports pour vidéoprojecteurs", "slug": "accessoires/meubles-et-supports/supports-pour-videoprojecteurs"},
        {"name": "Routeurs WiFi", "slug": "accessoires/distributeurs-et-transmetteurs/routeurs-wifi"},
        {"name": "Récepteurs Bluetooth", "slug": "accessoires/distributeurs-et-transmetteurs/recepteurs-bluetooth"},
        {"name": "Câbles vidéo", "slug": "accessoires/cables-video"},
        {"name": "Câbles audio", "slug": "accessoires/cables-audio"},
        {"name": "Câbles d'alimentation secteur", "slug": "accessoires/alimentation/cables-d-alimentation-secteur"},
        {"name": "Alimentation", "slug": "accessoires/alimentation"},
        {"name": "Multiprises", "slug": "accessoires/alimentation/multiprises"},
        {"name": "Fiches bananes et fourches", "slug": "accessoires/extras/fiches-bananes-et-fourches"},
        {"name": "Accessoires de câblage", "slug": "accessoires/extras/accessoires-de-cablage"}
    ],

    "Enceintes" : [
        {"name": "Enceintes colonne", "slug": "enceintes/enceintes/enceintes-colonne"},
        {"name": "Enceintes bibliothèque", "slug": "enceintes/enceintes/enceintes-compactes"},
        {"name": "Enceintes centrales", "slug": "enceintes/enceintes/enceintes-centrales"},
        {"name": "Enceintes satellites", "slug": "enceintes/enceintes/enceintes-satellites"},
        {"name": "Enceintes surround", "slug": "enceintes/enceintes/enceintes-surround"},
        {"name": "Enceintes Atmos", "slug": "enceintes/enceintes/enceintes-atmos"},
        {"name": "Enceintes encastrables", "slug": "enceintes/enceintes/enceintes-encastrables"},
        {"name": "Enceintes d'extérieur", "slug": "enceintes/enceintes/enceintes-d-exterieur"},
        {"name": "Enceintes sans fil hi-fi", "slug": "enceintes/enceintes/enceintes-sans-fil-hi-fi"},
        {"name": "Enceintes connectées", "slug": "enceintes/enceintes/enceintes-sans-fil-compactes"},
        {"name": "Enceintes Bluetooth portables", "slug": "enceintes/enceintes/enceintes-bluetooth-portables"},
        {"name": "Enceintes de soirée", "slug": "enceintes/enceintes/enceintes-de-soiree"},
        {"name": "Caissons de basses", "slug": "home-cinema/caissons-et-vibreurs/caissons-de-basses"},
        {"name": "Barres de son", "slug": "home-cinema/chaines-home-cinema/barres-de-son"},
        {"name": "Packs d'enceintes", "slug": "home-cinema/packs-d-enceintes"},
        {"name": "Pieds pour enceintes", "slug": "accessoires/meubles-et-supports/pieds-pour-enceintes"},
        {"name": "Câbles audio", "slug": "accessoires/cables-audio"}
    ],

    "Haute-fidélité" : [
        {"name": "Amplis hi-fi stéréo", "slug": "haute-fidelite/amplificateurs/amplis-hi-fi-stereo"},
        {"name": "Amplis hi-fi WiFi / Bluetooth", "slug": "haute-fidelite/amplificateurs/amplis-hi-fi-connectes"},
        {"name": "Amplis à tubes", "slug": "haute-fidelite/amplificateurs/amplis-a-tubes"},
        {"name": "Amplis de puissance", "slug": "haute-fidelite/amplificateurs/amplis-de-puissance"},
        {"name": "Amplis casques", "slug": "haute-fidelite/amplificateurs/amplis-casques"},
        {"name": "Platines vinyle hi-fi", "slug": "vinyle/platines-vinyle"},
        {"name": "Lecteurs CD", "slug": "haute-fidelite/lecteurs-cd-sacd/lecteurs-cd"},
        {"name": "Lecteurs réseau audio", "slug": "haute-fidelite/streaming-et-reseau/lecteurs-reseau-audio"},
        {"name": "Récepteurs Bluetooth", "slug": "accessoires/distributeurs-et-transmetteurs/recepteurs-bluetooth"},
        {"name": "Casques hi-fi", "slug": "nomade/casques-et-ecouteurs/casques-hi-fi"},
        {"name": "Enceintes colonne", "slug": "enceintes/enceintes/enceintes-colonne"},
        {"name": "Enceintes bibliothèque", "slug": "enceintes/enceintes/enceintes-compactes"},
        {"name": "Enceintes sans fil hi-fi", "slug": "enceintes/enceintes/enceintes-sans-fil-hi-fi"},
        {"name": "Enceintes connectées", "slug": "enceintes/enceintes/enceintes-sans-fil-compactes"},
        {"name": "Caissons de basses", "slug": "home-cinema/caissons-et-vibreurs/caissons-de-basses"},
        {"name": "Meubles hi-fi", "slug": "accessoires/meubles-et-supports/meubles-hi-fi"},
        {"name": "Câbles audio", "slug": "accessoires/cables-audio"}
    ],

    "Home-cinéma" : [
        {"name": "Amplis home-cinéma", "slug": "haute-fidelite/amplificateurs/amplis-home-cinema"},
        {"name": "Lecteurs Blu-ray 4K", "slug": "home-cinema/lecteurs-blu-ray-dvd/lecteurs-blu-ray"},
        {"name": "Lecteurs multimédia", "slug": "haute-fidelite/streaming-et-reseau/lecteurs-reseau-av"},
        {"name": "Barres de son", "slug": "home-cinema/chaines-home-cinema/barres-de-son"},
        {"name": "Packs d'enceintes", "slug": "home-cinema/packs-d-enceintes"},
        {"name": "Téléviseurs", "slug": "television/televiseurs"},
        {"name": "Vidéoprojecteurs", "slug": "television/videoprojection/videoprojecteurs"},
        {"name": "Écrans de projection", "slug": "accessoires/ecrans-de-projection"},
        {"name": "Enceintes colonne", "slug": "enceintes/enceintes/enceintes-colonne"},
        {"name": "Enceintes bibliothèque", "slug": "enceintes/enceintes/enceintes-compactes"},
        {"name": "Enceintes centrales", "slug": "enceintes/enceintes/enceintes-centrales"},
        {"name": "Enceintes surround", "slug": "enceintes/enceintes/enceintes-surround"},
        {"name": "Enceintes Atmos", "slug": "enceintes/enceintes/enceintes-atmos"},
        {"name": "Caissons de basses", "slug": "home-cinema/caissons-et-vibreurs/caissons-de-basses"},
        {"name": "Préamplis home-cinéma", "slug": "haute-fidelite/amplificateurs/preamplis-home-cinema"},
        {"name": "Amplis de puissance", "slug": "haute-fidelite/amplificateurs/amplis-de-puissance"},
        {"name": "Câbles HDMI", "slug": "accessoires/cables-video/cables-hdmi"},
        {"name": "Câbles audio", "slug": "accessoires/cables-audio"}
    ],

    "Maison connectée" : [
        {"name": "Amplis connectés", "slug": "haute-fidelite/amplificateurs/amplis-hi-fi-connectes"},
        {"name": "Enceintes multiroom", "slug": "haute-fidelite/systemes-multiroom/enceintes-multiroom"},
        {"name": "Enceintes connectées", "slug": "enceintes/enceintes/enceintes-sans-fil-compactes"},
        {"name": "Éclairage connecté", "slug": "maison-connectee/domotique/eclairage-connecte"},
        {"name": "Caméras de surveillance", "slug": "maison-connectee/securite-connectee/camera-surveillance"},
        {"name": "Sonnettes connectées", "slug": "maison-connectee/securite-connectee/sonnette-connectee"},
        {"name": "Alarme maison", "slug": "maison-connectee/domotique/alarmes-connectees"},
        {"name": "Détecteurs et capteurs", "slug": "maison-connectee/securite-connectee/detecteur-et-capteur"},
        {"name": "Contrôle domotique", "slug": "maison-connectee/domotique"},
        {"name": "Assistants vocaux", "slug": "maison-connectee/domotique/assistant-vocal"},
        {"name": "Thermostats connectés", "slug": "maison-connectee/domotique/thermostat-connecte"},
        {"name": "Interrupteurs connectés", "slug": "maison-connectee/domotique/interrupteur-connecte"},
        {"name": "Prises connectées", "slug": "maison-connectee/domotique/prise-connectee"},
        {"name": "Écrans de contrôle tactiles", "slug": "maison-connectee/domotique/tablettes-tactiles"},
        {"name": "Routeurs WiFi", "slug": "accessoires/distributeurs-et-transmetteurs/routeurs-wifi"},
        {"name": "Répéteurs WiFi", "slug": "accessoires/distributeurs-et-transmetteurs/repeteurs-wifi"},
        {"name": "Câbles Ethernet", "slug": "accessoires/cables-informatiques/cables-ethernet"}
    ],

    "Casques & baladeurs" : [
        {"name": "Casques hi-fi", "slug": "nomade/casques-et-ecouteurs/casques-hi-fi"},
        {"name": "Casques Bluetooth", "slug": "nomade/casques-et-ecouteurs/casques-bluetooth"},
        {"name": "Casques à réduction de bruit", "slug": "nomade/casques-et-ecouteurs/casques-a-reduction-de-bruit"},
        {"name": "Écouteurs intra-auriculaires", "slug": "nomade/casques-et-ecouteurs/ecouteurs-intra-auriculaires"},
        {"name": "Écouteurs Bluetooth", "slug": "nomade/casques-et-ecouteurs/ecouteurs-bluetooth"},
        {"name": "Écouteurs True Wireless", "slug": "nomade/casques-et-ecouteurs/ecouteurs-true-wireless"},
        {"name": "Écouteurs sport", "slug": "nomade/casques-et-ecouteurs/ecouteurs-sport"},
        {"name": "Baladeurs audiophiles", "slug": "nomade/baladeurs/baladeurs-audiophiles"},
        {"name": "Amplis casques portables", "slug": "haute-fidelite/amplificateurs/amplis-casques-portables"},
        {"name": "DAC audio portables", "slug": "haute-fidelite/streaming-et-reseau/dac-audio-portables"},
        {"name": "Câbles casques", "slug": "accessoires/cables-audio/cables-casques"},
        {"name": "Câbles jack et mini-jack", "slug": "accessoires/cables-audio/cables-jack-mini-jack"}
    ],

    "TV & projecteurs" : [
        {"name": "Téléviseurs", "slug": "television/televiseurs"},
        {"name": "TV QLED", "slug": "television/televiseurs/televiseurs-qled"},
        {"name": "TV OLED", "slug": "television/televiseurs/televiseurs-oled"},
        {"name": "TV Lifestyle", "slug": "television/televiseurs/lifestyle-tv"},
        {"name": "Supports TV", "slug": "accessoires/meubles-et-supports/supports-tv"},
        {"name": "Meubles TV", "slug": "accessoires/meubles-et-supports/meubles-tv-video"},
        {"name": "Barres de son", "slug": "home-cinema/chaines-home-cinema/barres-de-son"},
        {"name": "Vidéoprojecteurs", "slug": "television/videoprojection/videoprojecteurs"},
        {"name": "Vidéoprojecteurs UHD-4K", "slug": "television/videoprojection/videoprojecteurs-uhd-4k"},
        {"name": "Vidéoprojecteurs ultra courte focale", "slug": "television/videoprojection/videoprojecteurs-ultra-courte-focale"},
        {"name": "Picoprojecteurs", "slug": "television/videoprojection/picoprojecteurs"},
        {"name": "Écrans de projection", "slug": "accessoires/ecrans-de-projection"},
        {"name": "Supports pour vidéoprojecteurs", "slug": "accessoires/meubles-et-supports/supports-pour-videoprojecteurs"},
        {"name": "Lecteurs Blu-ray 4K", "slug": "home-cinema/lecteurs-blu-ray-dvd/lecteurs-blu-ray"},
        {"name": "Lecteurs multimédia", "slug": "haute-fidelite/streaming-et-reseau/lecteurs-reseau-av"},
        {"name": "Casques Bluetooth", "slug": "nomade/casques-et-ecouteurs/casques-bluetooth"},
        {"name": "Câbles HDMI", "slug": "accessoires/cables-video/cables-hdmi"}
    ],

    "Vinyle" : [
        {"name": "Platines vinyle hi-fi", "slug": "vinyle/platines-vinyle/platines-vinyle-hi-fi"},
        {"name": "Platines vinyle Bluetooth", "slug": "vinyle/platines-vinyle/platines-vinyle-bluetooth"},
        {"name": "Platines vinyle USB", "slug": "vinyle/platines-vinyle/platines-vinyle-usb"},
        {"name": "Préamplis phono", "slug": "vinyle/platines-vinyle/preamplis-phono-riaa"},
        {"name": "Cellules et diamants", "slug": "vinyle/cellules-et-diamants"},
        {"name": "Portes-cellules vinyle", "slug": "vinyle/cellules-et-diamants/porte-cellule"},
        {"name": "Plateaux et couvres-plateaux", "slug": "vinyle/accessoires-vinyle/plateaux-et-couvres-plateaux"},
        {"name": "Disques vinyles", "slug": "https://www.son-video.com/selection/cd-vinyle-selection-de-disques-audiophiles"},
        {"name": "Pochettes vinyles", "slug": "vinyle/platines-vinyle/pochettes-vinyles"},
        {"name": "Produits d'entretien vinyles", "slug": "vinyle/platines-vinyle/produits-d-entretien-vinyles"},
        {"name": "Outils de réglage vinyle", "slug": "vinyle/accessoires-vinyle/outils-de-reglage"},
        {"name": "Accessoires vinyle", "slug": "vinyle/accessoires-vinyle"},
        {"name": "Câbles phono", "slug": "accessoires/cables-audio/cables-phono"}
    ],

    "Gaming" : [
        {"name": "Écrans gaming", "slug": "gaming/peripheriques/ecrans-gaming"},
        {"name": "Casques gaming", "slug": "nomade/casques-et-ecouteurs/casques-gamer"},
        {"name": "Consoles de jeu", "slug": "gaming/consoles-et-bornes/consoles-de-jeux"},
        {"name": "Manettes de jeu", "slug": "gaming/peripheriques/manettes-de-jeu"},
        {"name": "Volants gaming", "slug": "gaming/peripheriques/volants-gaming"},
        {"name": "Sièges de simulation gaming", "slug": "gaming/peripheriques/sieges-simulation-gaming"},
        {"name": "Casques VR", "slug": "gaming/peripheriques/casques-vr"},
        {"name": "Claviers gaming", "slug": "gaming/peripheriques/claviers-gaming"},
        {"name": "Souris gaming", "slug": "gaming/peripheriques/souris-gaming"},
        {"name": "Accessoires gaming", "slug": "gaming/peripheriques/accessoires-gaming"},
        {"name": "Câbles HDMI", "slug": "accessoires/cables-video/cables-hdmi"}
    ]
}%}

{% block metas %}
    {% if stand.description is defined and stand.description is not empty %}
        {% set metas = metas|merge({'description': stand.description }) %}
    {% endif %}
    {{ parent() }}
{% endblock %}

{# OpenGraph #}
{% block og %}
    <meta property='og:type' content='product.group'>
    <meta property='og:url' content="{{ url('show_stand', {'slug': stand.slug}) }}">
    <meta property='og:title' content="{{ stand.title }}">
    <meta property='og:description' content="{{ stand.description | html_entity_decode }}">
    <meta property='og:image' content="{{ base_url ~ '/images/ui/uiV5/logo-partage.png' }}">
    <meta property='og:image:alt' content="{{ 'Image ' ~ stand.title }}">
{% endblock %}

{% block body_class %}body-stands-level-1{% endblock %}
{% block title %}
    {% if stand.title is not empty %}
        {{ stand.title }}
    {% else %}
        {% trans with {'%stand.name%': stand.name} from 'stand' %}%stand.name%_head_title{% endtrans %}
    {% endif %}
{% endblock %}

{% block structured_data %}
    <script type="application/ld+json">
{{ structured_data | raw }}

    </script>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('stand_domain_style')) }}
{% endblock %}

{% block tracking %}
    {% set extra_data = stand.extract()|merge({'ancestors': stand_ancestors.extract(), 'articles': articles is null ? [] : articles.extract()}) %}
    {{ render(controller('AppBundle:Tracking/Tracking:show', {'location': 'stand', 'extra_data': extra_data}))|raw }}
    {{ parent() }}
{% endblock %}

{% block content %}

    <h1 class="SVDv3_mobile_navbar">{{ stand.name }}</h1>

    <div class="SVDv3_colonnes_2colonnes1 mb-5 clearfix">
        <div class="SVDv3_colonnes_colonne_gauche visible-xs visible-sm visible-md visible-lg">
            {# TODO REMOVE THIS HACK WHEN MOBILE NAVIGATION IS ONLINE #}
            {% if (stand.name in hack_menu|keys) %}
                {% set stand_children = hack_menu[stand.name] %}
                {% embed "@Cms/Widget/stand/children.html.twig" %}{% endembed %}
            {% else %}
                {# TODO KEEP ONLY THIS LINE #}
                {{ render(controller('CmsBundle:Widget/Stand/Children:show', { 'stand': stand, '_locale': locale }))|raw }}
            {% endif %}

            <div class="hidden-xs hidden-sm">
                {# associated guide #}
                {{ render(controller('CmsBundle:Widget/Guide/Guide:showAssociatedGuideForStand',{'stand_id': stand.stand_id, '_locale': app.request.getLocale(), 'display': 'left_column'}))|raw }}

                {{ render(controller('CmsBundle:Widget/ClickbaitColumn:show', {'locations': ['stand_' ~ stand.stand_id, 'parent_stand_' ~ stand.stand_id]}))|raw }}

            </div>
        </div>
        <div class="SVDv3_colonnes_colonne_droite">

            <div class="homepage-section homepage-bestsellers">
                <div class="homepage-section-header">
                    <h2 class="homepage-section-header-title">{% trans %}our_best_sales{% endtrans %} {{ stand.name }}</h2>
                    <div class="homepage-section-header-more"><a href="{{ path('show_shop', {'slug': 'les-produits-les-mieux-notes'}) }}">{% trans %}see_all_best_rated_products{% endtrans %}</a></div>
                </div>
                <div class="homepage-section-body" id='stand-products'>
                    {{ render(controller('CmsBundle:Widget/Stand/BestSeller:show', {'stand_tag': stand.tag_path|join('.'), 'locale': app.request.getLocale()}))|raw }}
                </div>
            </div>
        </div>
    </div>

    {% if stand.editorial_content is defined %}
        <div id="comprendrePourChoisir">
            <div class="guide editorial">
                <div class="guide-body">
                    <h2>{% trans with {'%stand.name%': stand.name} from 'stand' %}how_to_choose_in_%stand.name%{% endtrans %}</h2>
                    {{ (stand.editorial_content|first)|lazy_load_content|raw }}
                </div>
            </div>
        </div>
    {% endif %}

{% endblock content %}

{% block  javascripts %}
    {{ encore_entry_script_tags('clickbait/clickbait_column') | defer | raw }}
{% endblock %}

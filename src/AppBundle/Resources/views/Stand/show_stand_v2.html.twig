{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% use 'AppBundle:Article:article_lister_widget.html.twig' %}
{% import 'AppBundle:Common:guide_macro.html.twig' as guide_macro %}

{% block stylesheets %}
    {{ parent() }}
    {{ block('article_lister_css') }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('stand_v2_style')) }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('fancybox_style')) }}
{% endblock %}

{% set locale = app.request.locale %}
{% set stand_parent = stand_ancestors | last %}

{% block metas %}
    {% if stand.description is defined and stand.description is not empty %}
        {% set metas = metas|merge({'description': stand.description }) %}
    {% endif %}
    {{ parent() }}
{% endblock %}

{# OpenGraph #}
{% block og %}
    <meta property='og:type' content='product.group'>
    <meta property='og:url' content="{{ url('show_stand', {'slug': stand.slug}) }}">
    <meta property='og:title' content="{{ stand.title }}">
    <meta property='og:description' content="{{ stand.description | html_entity_decode }}">
    <meta property='og:image' content="{{ base_url ~ '/images/ui/uiV5/logo-partage.png' }}">
    <meta property='og:image:alt' content="{{ 'Image ' ~ stand.title }}">
{% endblock %}

{% block tracking %}
    {# TODO: check that block rendering with new articles structure #}
    {% set extra_data = stand.extract()|merge({
        'ancestors': stand_ancestors.extract(),
        'articles': articles is null ? [] : articles.extract(),
        'search_data': search_data is defined ? search_data : null
    }) %}
    {{ render(controller('AppBundle:Tracking/Tracking:show', {'location': 'stand', 'extra_data': extra_data }))|raw }}
    {{ parent() }}
{% endblock %}

{% block title %}
    {% if stand.level is defined and stand.level == 3 %}
        {% set title = stand.name|title_case ~ ' - Retrait Gratuit Magasin - Son-Vidéo.com'  %}
        {% if title|length > 60 %}
            {{ stand.name|title_case }} - Son-Vidéo.com
        {% else %}
            {{ title }}
        {% endif %}
    {% else %}
        {% if stand.title is not empty %}
            {{ stand.title }}
        {% else %}
            {% trans with {'%stand.name%': stand.name} from 'stand' %}%stand.name%_head_title{% endtrans %}
        {% endif %}
    {% endif %}
{% endblock %}

{% block structured_data %}
<script type="application/ld+json">{{ structured_data | raw }}</script>
{% endblock %}

{% block body_class %}body-stand{% endblock %}

{% block content %}
    {# breadcrumb #}
    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% block crumbs %}
            {% for ancestor in stand_ancestors %}
                <li>
                    <a href="{{ path('show_stand', {'slug': ancestor.slug}) }}">{{ ancestor.name }}</a>
                </li>
            {% endfor %}
            <li>{{ stand.name }}</a></li>
        {% endblock crumbs %}
    {% endembed %}

    {# Island display #}
    <h1 class="SVDv3_mobile_navbar" id="stand-title" data-location="rayon niveau 3">{{stand.name}}</h1>

    <div class="gondoles-wrapper">
        <div class="container">
            <div class="row">
                <div class="col-12 col-lg-8 d-none d-lg-block">
                    {{ render(controller('CmsBundle:Widget/Stand/Headline:show', { 'stand': stand, '_locale': locale }))|raw }}
                </div>
                <div class="col-12 col-lg-4">
                    <div class="gondoles-text">
                        {{ (stand.editorial_content|first)|lazy_load_content|raw }}
                        {% if guide is not empty %}
                            <div>
                                <a href="#{% trans from 'stand' %}anchor_understanding_how_to_choose{% endtrans %}" class="link-arrow link-arrow-after link-arrow-right">
                                    {{ guide.name }}
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="article_lister" class="SVDv3_colonnes_2colonnes1 mb-5 clearfix" data-slug="{{ stand.slug }}">
        <div class="SVDv3_colonnes_colonne_gauche visible-xs visible-sm visible-md visible-lg">
            {% if articles | length > 0 %}
                {{ block('article_lister_filter') }}
            {% endif %}

            <div class="visible-md visible-lg">
                {{ render(controller('CmsBundle:Widget/Stand/Siblings:show', { 'stand': stand, '_locale': locale }))|raw }}

                {% set locations = ['stand_' ~ stand.stand_id] %}
                {% for stand_ancestor in stand_ancestors %}
                    {% set locations = locations|merge(['parent_stand_' ~ stand_ancestor.stand_id]) %}
                {% endfor %}
                {{ render(controller('CmsBundle:Widget/ClickbaitColumn:show', {'locations': locations}))|raw }}
            </div>
        </div>

        <div class="SVDv3_colonnes_colonne_droite">
            {{ block('article_lister_result') }}
        </div>
    </div>

    {# how to choose #}
    {{ guide_macro.understand_to_choose_guide(guide) }}

{% endblock content %}

{% block additional_content %}
    {# Advice sheets #}
    {{ render(controller('CmsBundle:Widget/Guide/Guide:showAssociatedGuideForStand',{'stand_id': stand.stand_id, '_locale': app.request.getLocale()}))|raw }}
{% endblock %}


{% block javascripts %}
    {{ parent() }}

    {% set total_items = articles | length %}
    {{ block('article_lister_javascript') }}
    {{ encore_entry_script_tags('clickbait/clickbait_column') }}
{% endblock %}

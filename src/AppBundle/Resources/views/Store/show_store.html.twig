{% extends 'AppBundle:Layout:v3/base.html.twig' %}

{% use 'AppBundle:Search:search_widget.html.twig' %}
{% import 'AppBundle:Search:search_macro.html.twig' as search %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('fancybox_style')) }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('trombinoscope_style')) }}
    <link href="{{ asset('css/v3/modules/faq/pointRetrait.css') }}" rel="stylesheet" type="text/css"/>
    <link href="https://fonts.googleapis.com/css?family=Yellowtail&amp;display=swap" rel="stylesheet">
    {{ layout_macro.link_stylesheet(encore_entry_css_files('editorial_style')) }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('leaflet_style')) }}
{% endblock %}

{% set store_name = 'the_stores_hifi_store'|trans({}, 'store') ~ ' ' ~ store.name %}

{% set store_title = 'the_stores_hifi_store_title'|trans({'%city%': store.name}, 'store') %}

{% set metas = metas|merge({'description': 'the_stores_hifi_store_meta_description'|trans({'%city%': store.name}, 'store')}) %}

{% block title %}{{ store_title }}{% endblock %}

{% block content %}
    {% embed "@App/Common/block/breadcrumbs.html.twig" %}
        {% block crumbs %}
            <li><a href="{{ path('store_index') }}">{% trans from 'store' %}the_stores_title{% endtrans %}</a></li>
            <li>{{ store_name }}</li>
        {% endblock crumbs %}
    {% endembed %}

    <div class="store-page">

        <div class="store-header">
            <h1 class="store-header-title">
                {{ store_name }}
            </h1>
            {#
            <div class="store-reviews">
                <div class="store-review-score">
                    <span class="store-score"><span class="store-score-stars" style="width: 86px;"></span></span>
                    <span class="store-score-description"><strong>4,7</strong> {{ 'on'|trans }} 5 (<a href="#">3 {{ 'google_advice' | trans({}, 'store') }}</a>)</span>
                </div>
            </div>
            #}
        </div>

        {% if date( dates.stores.inventory_closing.start ) < date() and date( dates.stores.inventory_closing.end ) > date() and ( store.id=="paris-8" or store.id=="antibes" or store.id=="champigny" ) %}
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-8">
                        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                            {% trans with {'%name%': store.name } from 'store' %}inventory_day_info{% endtrans %}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if date( dates.stores.store_closed_christmas.start ) < date() and date( dates.stores.store_closed_christmas.end ) > date() %}
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-8">
                        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                            <p>Votre magasin Son-Vidéo.com {{ store.name_html }} sera exceptionnellement <strong>fermé à 18h00 le 24 décembre</strong>.</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if date( dates.stores.store_closed_new_year.start ) < date() and date( dates.stores.store_closed_new_year.end ) > date() %}
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-8">
                        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                            <p>Votre magasin Son-Vidéo.com {{ store.name_html }} sera exceptionnellement <strong>fermé à 18h00 le 31 décembre</strong>.</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if store.id not in ["paris-8", "antibes", "marseille-plan-de-campagne"] %}
            {% if date( dates.stores.store_open_monday_before_christmas.start ) < date() and date( dates.stores.store_open_monday_before_christmas.end ) > date() %}
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-12 col-lg-8">
                            <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                                <p>Votre magasin Son-Vidéo.com {{ store.name_html }} sera exceptionnellement <strong>ouvert lundi 21 décembre de 10h à 13h30 et de 14h30 à 19h</strong>.</p>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% endif %}

        {% if store.id=="paris-8" and date( dates.stores.store_openings_paris8_christmas.start ) < date() and date( dates.stores.store_openings_paris8_christmas.end ) > date() %}
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-8">
                        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                            <p>Votre magasin Son-Vidéo.com {{ store.name_html }} sera exceptionnellement <strong>ouvert les samedis 19 et 26 décembre de 10h à 19h</strong>.</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if store.id=="marseille-plan-de-campagne" and date( dates.stores.store_closed_marseille_lockdown.start ) < date() and date( dates.stores.store_closed_marseille_lockdown.end ) > date() %}
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-8">
                        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                            <p>Votre magasin Son-Vidéo.com {{ store.name_html }} est exceptionnellement fermé <strong>jusqu'au mardi 18 mai 2021 inclus</strong>.</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if store.id=="bordeaux" and date( dates.stores.store_closed_bordeaux_relocation.start ) < date() and date( dates.stores.store_closed_bordeaux_relocation.end ) > date() %}
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-8">
                        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                            <p>Votre magasin Son-Vidéo.com {{ store.name_html }} déménage et sera exceptionnellement fermé <strong>du 24 au 31 mai 2021 inclus</strong> pour une réouverture à partir du 1er Juin.</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if store.id=="champigny" and date( dates.stores.store_closed_champigny_move.start ) < date() and date( dates.stores.store_closed_champigny_move.end ) > date() %}
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-8">
                        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                            <p>Suite au d&eacute;m&eacute;nagement de notre site de production du 20/04/2021 au 23/04/2021, votre point de retrait Son-Vid&eacute;o.com {{ store.name_html }} sera ferm&eacute; jusqu'au dimanche 25/04/2021 inclus.</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if store.id=="strasbourg" and date( dates.stores.store_closed_good_friday_strasbourg.start ) < date() and date( dates.stores.store_closed_good_friday_strasbourg.end ) > date() %}
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-8">
                        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                            <p>Votre magasin Son-Vidéo.com {{ store.name_html }} sera exceptionnellement <strong>fermé vendredi 2 avril</strong>.</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if store.id=="champigny" and date( dates.stores.store_openings_champigny_christmas.start ) < date() and date( dates.stores.store_openings_champigny_christmas.end ) > date() %}
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-8">
                        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                            <p>Votre magasin Son-Vidéo.com {{ store.name_html }} sera exceptionnellement <strong>ouvert le samedi 19 décembre de 9h à 13h et de 14h à 18h</strong>.</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% set currentTime = "now"|date("U") %}
        {% set closeEnd = "2024-09-03 10:00:00 Europe/Paris"|date("U") %}

        {% if store.id=="saint-germain-en-laye" and currentTime <= closeEnd %}
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-8">
                        <div class="SVDv3_messageAlerte SVDv3_messageAlerte_info">
                            <p>Votre magasin Son-Vidéo.com {{ store.name_html }} sera exceptionnellement <strong>en travaux du 13 août 19h00 au 3 septembre 10h</strong>.</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if store.id=="paris-est" and date( dates.stores.store_paris_est_inauguration.start ) < date() and date( dates.stores.store_paris_est_inauguration.end ) > date() %}
            <div class="store-section">
                <div class="text-center">
                    <a href="#invitation">
                        <img src="{{ asset('/images/illustration/entete/SVDMAG_202111_InaugurationParisEst_980x260.jpg', 'static_images') }}" alt="{% trans from 'store' %}inauguration_cocktail_east_Paris{% endtrans %}" class="img-responsive">
                    </a>
                </div>
            </div>
        {% endif %}

        {% if store.id=="antibes" and date( dates.stores.store_antibes_portes_ouvertes.start ) < date() and date( dates.stores.store_antibes_portes_ouvertes.end ) > date() %}
            <div class="store-section">
                <div class="text-center">
                    <a href="#invitation">
                        <img src="{{ asset('/images/illustration/entete/SVDMAG_202112-Antibes-DemoJVC_980x260.jpg', 'static_images') }}" alt="{% trans from 'store' %}open_days_antibes{% endtrans %}" class="img-responsive">
                    </a>
                </div>
            </div>
        {% endif %}

        <div class="store-section store-section-location" id="carte">

            <div class="container store-locations">
                <div class="row row-no-gutter">
                    <div class="col-12 col-lg-4 order-2 order-lg-1">

                        <div class="store-page-information">
                            <div class="store-location-item store-page-information-section">
                                <div class="store-location-item-body">
                                    <h2 class="store-location-item-title">Son-Vidéo.com {{ store.name_html }}</h2>
                                    <ul class="store-location-item-informations list-unstyled">
                                        {% if store.street is defined and store.street is not empty and store.zip_code is defined and store.zip_code is not empty and store.city is defined and store.city is not empty %}
                                            <li>{{ store.street | raw }}</li>
                                            <li>{{ store.zip_code }} {{ store.city }}</li>
                                        {% endif %}
                                        {% if store.mail is defined and store.mail is not empty %}
                                            {# TODO: uncomment when functionality is ready (see issue #207) #}
                                            {#<a rel="nofollow" href="#todo_link-to-contact">{% trans from 'store' %}the_stores_send_message{% endtrans %}</a>#}
                                            <li><a href="mailto:{{ store.mail }}">{{ store.mail }}</a></li>
                                        {% endif %}
                                        {% if store.phone is defined and store.phone is not empty %}
                                            <li>Tél.&nbsp;: {{ store.phone | raw  }}</li>
                                        {% else %}
                                            {% if store.city is defined and store.city is not empty and store.city == "Champigny-sur-Marne" %}
                                                <li><img alt="{{ telephone.hotline_numero ~ ' ' ~ telephone.hotline_mention }}" src="{{ asset('/images/ui/uiV3/tel-sva/uiV3_tel-sva_0826960290_sm.gif', 'static_images') }}"></li>
                                            {% else %}
                                                <li><img alt="{{ telephone.magasins_numero ~ ' ' ~ telephone.magasins_mention }}" src="{{ asset('/images/ui/uiV3/tel-sva/uiV3_tel-sva_0826006007_sm.gif', 'static_images') }}"></li>
                                            {% endif %}
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>

                            {% if store.closed is defined and store.closed is not empty and store.closed == true %}

                                <div class="store-openings-status store-page-information-section">
                                    <span class="store-openings-status-close">{% trans from 'store' %}closed_due_to_unforeseen_circumstances{% endtrans %}</span>
                                </div>
                            {% elseif store.openings_list is defined and store.openings_list is not empty %}

                                {% set trans_day_hash = {
                                    "mon": "Lundi",
                                    "tue": "Mardi",
                                    "wed": "Mercredi",
                                    "thu": "Jeudi",
                                    "fri": "Vendredi",
                                    "sat": "Samedi",
                                    "sun": "Dimanche"
                                }
                                %}
                                {% set is_open = false %}
                                {% set plage_count = 0 %}
                                {% set plage_length = 0 %}
                                {% set plage_nb = 0 %}
                                {% set plage_ended_nb = 0 %}
                                {% set today_found = false %}
                                {% set first_opening_day_of_week = '' %}
                                {% set next_opening_day_of_this_week = '' %}
                                {% set next_opening_day = '' %}

                                {% if store.openings_list["now"|date('D')] is defined and store.openings_list["now"|date('D')] is not empty %}

                                    {% if store.openings_list["now"|date('D')] != '' %}
                                        {% set plage_length = store.openings_list["now"|date('D')]|length - 1 %}
                                        {% for plage in store.openings_list["now"|date('D')] %}

                                            {# test if it is open #}
                                            {% if plage['open'] <= 'now' | date('H:i', 'Europe/Paris') and plage['close'] >= 'now' | date('H:i', 'Europe/Paris') %}
                                                {% set is_open = true %}
                                                {% set plage_nb = plage_count %}
                                            {% endif %}

                                            {# count number of ended openings today #}
                                            {% if plage['open'] <= 'now' | date('H:i', 'Europe/Paris') and plage['close'] <= 'now' | date('H:i', 'Europe/Paris') %}
                                                {% set plage_ended_nb = plage_ended_nb + 1 %}
                                            {% endif %}

                                            {% set plage_count = plage_count + 1 %}
                                        {% endfor %}
                                    {% endif %}

                                    {# TODO no cache for the content inside the div.store-openings-status #}

                                    {# Status Open / close hidden. Waiting for a workaround for the cache #}
                                    {#
                                    <div class="store-openings-status store-page-information-section">
                                        {% if is_open %}
                                            <span class="store-openings-status-open">Ouvert</span>, ferme à
                                            {{ store.openings_list["now"|date('l')][plage_nb]['close']|replace(':', 'h') }}.
                                            {% if plage_nb < plage_length %}Ouvre de nouveau à {{ store.openings_list["now"|date('l')][plage_nb + 1]['open']|replace(':', 'h') }}.{% endif %}
                                        {% else %}
                                            <span class="store-openings-status-close">Fermé</span>,
                                            {% if plage_ended_nb < plage_length + 1 %}
                                                ouvre à {{ store.openings_list["now"|date('l')][plage_ended_nb]['open']|replace(':', 'h') }}.
                                            {% else %}
                                                {% for key, value in store.openings_list %}
                                                    {% if value != 'closed' and first_opening_day_of_week == '' %}
                                                        {% set first_opening_day_of_week = key %}
                                                    {% endif %}

                                                    {% if value != 'closed' and today_found == true and next_opening_day_of_this_week is empty %}
                                                        {% set next_opening_day_of_this_week = key %}
                                                    {% endif %}

                                                    {% if key == "now"|date('l') %}
                                                        {% set today_found = true %}
                                                    {% endif %}
                                                {% endfor %}
                                                {% if next_opening_day_of_this_week is not empty or first_opening_day_of_week is not empty %}
                                                    {% if next_opening_day_of_this_week is not empty %}
                                                        {% set next_opening_day = next_opening_day_of_this_week %}
                                                    {% else %}
                                                        {% set next_opening_day = first_opening_day_of_week %}
                                                    {% endif %}
                                                {% endif %}
                                                {% if next_opening_day is not empty %}
                                                    ouvre {{ trans_day_hash[next_opening_day]|lcfirst }} à {{ store.openings_list[next_opening_day][0]['open']|replace(':', 'h') }}.
                                                {% endif %}
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                    #}

                                {% endif %}

                                {# TODO no cache for the content inside the div.store-openings-list #}

                                {# This section should not be cached too #}
                                <div class="store-openings-list store-page-information-section">
                                    <div class="row row-small-gutter">
                                        {% for jour, horaires in store.openings_list %}
                                            <div class="col-4">
                                                {% if jour == "now"|date('D')|lower %}<strong>{% endif %}
                                                    {{ trans_day_hash[jour] }}
                                                    {% if jour == "now"|date('D')|lower %}</strong>{% endif %}
                                            </div>
                                            <div class="col-8 text-right">
                                                {% if jour == "now"|date('D')|lower %}<strong>{% endif %}
                                                    {% if horaires == [] %}
                                                        Fermé
                                                    {% else %}
                                                        {% set horaire_item = 1 %}
                                                        {% for key in horaires %}
                                                            {{ key.0 }}-{{ key.1 }}{% if horaire_item < horaires|length %}, {% endif %}
                                                            {% set horaire_item = horaire_item + 1 %}
                                                        {% endfor %}
                                                    {% endif %}
                                                    {% if jour == "now"|date('D')|lower %}</strong>{% endif %}
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}

                            {% if store.street is defined and store.street is not empty and store.zip_code is defined and store.zip_code is not empty and store.city is defined and store.city is not empty %}
                                {% set routeGoogleMaps = 'son+video+com+' ~ store.city|slugify ~ '+' ~ store.street|slugify ~ '+' ~ store.zip_code|slugify ~ '+' ~ store.city|slugify %}
                                {% set routeUber = 'Son-Vidéo.com ' ~ store.city ~ '%2C%20' ~ store.street ~ '%2C%20' ~ store.city %}
                                <ul class="list-unstyled list-inline store-route-icon-list store-page-information-section">
                                    <li class="store-route-icon-item"><a href="https://maps.google.com/?saddr=Ma+position&daddr={{ routeGoogleMaps }}" target="_blank"><img src="{{ asset('/images/illustration/magasins/assets/store-icon-googlemap.png', 'static_images') }}" width="48" height="48" alt="Calculer mon itinéraire avec Google Maps" /></a></li>
                                    <li class="store-route-icon-item"><a href="https://m.uber.com/ul/?action=setPickup&client_id=oJuAZDwDQyV5WkwaEqyoVudzV-hy3L59&dropoff%5Bformatted_address%5D={{ routeUber }}%2C%20null&dropoff%5Blatitude%5D={{ store.latitudeDD }}&dropoff%5Blongitude%5D={{ store.longitudeDD }}&dropoff%5Bnickname%5D={{ 'Son-Vidéo.com ' ~ store.city }}&pickup=my_location" target="_blank"><img src="{{ asset('/images/illustration/magasins/assets/store-icon-uber.png', 'static_images') }}" width="48" height="48" alt="Calculer mon itinéraire avec Uber" /></a></li>
                                    <li class="store-route-icon-item"><a href="https://www.waze.com/ul?ll={{ store.latitudeDD }}%2C{{ store.longitudeDD }}&navigate=yes" target="_blank"><img src="{{ asset('/images/illustration/magasins/assets/store-icon-waze.png', 'static_images') }}" width="48" height="48" alt="Calculer mon itinéraire avec Waze" /></a></li>
                                </ul>
                            {% endif %}
                        </div>

                    </div>
                    <div class="col-12 col-lg-8 order-1 order-lg-2">
                        <div id="pointsRetrait_map"></div>
                    </div>
                </div>
            </div>
        </div>

        {% if store.id=="paris-est" and date( dates.stores.store_paris_est_inauguration.start ) < date() and date( dates.stores.store_paris_est_inauguration.end ) > date() %}
            <div class="store-section" id="invitation">
                <div style="margin: 0 auto; max-width: 640px;" class="margin-md">
                    <iframe src="https://docs.google.com/forms/d/e/1FAIpQLSfuHAedsMLXr--DW96GHwE8fPfTQSDexNIVQiNNMUZQdUpXsQ/viewform?embedded=true" width="100%" height="1600" frameborder="0" marginheight="0" marginwidth="0">Chargement...</iframe>
                </div>
            </div>
        {% endif %}

        {% if store.id=="antibes" and date( dates.stores.store_antibes_portes_ouvertes.start ) < date() and date( dates.stores.store_antibes_portes_ouvertes.end ) > date() %}
            <div class="store-section" id="invitation">
                <div style="margin: 0 auto; max-width: 640px;" class="margin-md">
                    <iframe src="https://docs.google.com/forms/d/e/1FAIpQLSfQ4YAIuunj8g0F3bAB7mQBS-TG5FVkVvVdl_09lTma0VzLpQ/viewform?embedded=true" width="100%" height="1450" frameborder="0" marginheight="0" marginwidth="0">Chargement...</iframe>
                </div>
            </div>
        {% endif %}

        {% if store.id=="paris-est" and date( dates.stores.store_paris_est_portes_ouvertes.start ) < date() and date( dates.stores.store_paris_est_portes_ouvertes.end ) > date() %}
            <div class="store-section" id="invitation">
                <p class="text-center"><a href="https://www.son-video.com/portes-ouvertes-sony-vpl-xw5000es-et-vpl-xw7000es-a-paris-est"><img src="{{ asset('/images/static/Promos/LettreInfo/BasNews/SVDMAG_202205-ParisEst-SonyVPL_BasNews.jpg', 'static_images') }}" class="img-responsive" alt="La Grande Braderie Son-Vidéo.com. Déstockage, produits neufs & reconditionnés. Jusqu’à -80%. Animations, tombola, jeux pour enfants, restauration sur place. Samedi 11 & dimanche 12 juin de 10h à 18h. 309 avenue du Général de Gaulle 94500 Champigny-sur-Marne." /></a></p>
            </div>
        {% endif %}

        {% if store.id=="lyon" and date( dates.stores.store_lyon_braderie_tombola.start ) < date() and date( dates.stores.store_lyon_braderie_tombola.end ) > date() %}
            <div class="store-section" id="invitation">
                <p class="text-center"><img src="{{ asset('images/static/Promos/LettreInfo/BasNews/SVDMAG_202211-BraderieLyon_BasNews.jpg', 'static_images') }}" class="img-responsive" alt="La Braderie de Lyon Déstockage, Le meilleur de la hi-fi et du home-cinéma à prix mini. Tombola gagnez une platine vinyle elipson chroma 400 et des bons d'achats. Du vendredi 2 et samedi 3 décembre." /></p>
            </div>
        {% endif %}

            {% if (store.brands is defined and store.brands is not empty) or (store.shop_slug is defined and store.shop_slug is not empty) %}
                <div class="store-section" id="marques">
                    <h2 class="store-section-title">{% trans from 'store' %}discover_and_test_at_your_store{% endtrans %}</h2>
                    {% if store.brands is defined and store.brands is not empty %}
                        <div class="store-brand-list">
                            <div class="container">
                                <div class="row">
                                    {% for key in store.brands %}
                                        <div class="col-6 col-sm-4 col-md-3 col-lg-2">
                                            <div class="store-brand-logo"><a href="{{ brands[key].url }}"><img src="{{ asset(brands[key].logo, 'static_images') }}" alt="{{ brands[key].name }}" class="img-responsive" /></a></div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    {% if store.shop_slug is defined and store.shop_slug is not empty and ( store.display_shop_button is not defined or ( store.display_shop_button is defined and store.display_shop_button==true ) ) %}
                        <p class="text-center"><a href="{{ path('show_shop', {'slug': store.shop_slug}) }}?tri=2" class="btn btn-primary">{% trans from 'store' %}the_stores_see_available_products{% endtrans %}</a></p>
                    {% endif %}
                </div>
            {% endif %}

            <div class="store-section" id="presentation">
                <h2 class="store-section-title">
                    {% if store.responsible is defined and store.responsible is not empty %}
                        {% if store.responsible is not iterable %}
                            {{ store.responsible }} {% trans from 'store' %}and_all_the_team_welcome_you{% endtrans %}
                        {% else %}
                            {% set responsible_item = 1 %}
                            {% for responsible in store.responsible %}
                                {{ responsible }}{% if responsible_item < store.responsible|length %}, {% endif %}
                                {% set responsible_item = responsible_item + 1 %}
                            {% endfor %}
                            {% if store.responsible|length > 1 %}
                                {% trans from 'store' %}and_all_their_team_welcome_you{% endtrans %}
                            {% else %}
                                {% trans from 'store' %}and_all_the_team_welcome_you{% endtrans %}
                            {% endif %}
                        {% endif %}
                    {% else %}
                        {% trans from 'store' %}welcome{% endtrans %}
                    {% endif %}
                    {% trans with {'%city%': store.name_html } from 'store' %}at_the_son_video_store{% endtrans %}
                </h2>
                <div class="container">
                    <div class="row">
                        <div class="col-12 {% if store.social['facebook'] is defined and store.social['facebook'] is not empty %}col-lg-6 col-xl-7{% endif %}">
                            <div class="store-page-presentation">
                                {{ store.description|raw }}
                                <h3 class="store-title">{% trans from 'store' %}services_and_advantages{% endtrans %}</h3>
                                {% if store.services is defined and store.services is not empty %}
                                    <ul class="store-services">
                                        {% for service in store.services %}
                                            <li>{{ service | trans({}, 'store') }}</li>
                                        {% endfor %}
                                    </ul>
                                {% endif %}
                            </div>
                        </div>
                        {% if store.social['facebook'] is defined and store.social['facebook'] is not empty %}
                            <div class="col-12 col-lg-6 col-xl-5">
                                <div class="fb-page" data-href="{{ store.social['facebook'] }}" data-tabs="timeline" data-width="475" data-height="550" data-small-header="false" data-adapt-container-width="true" data-hide-cover="false" data-show-facepile="true"><blockquote cite="{{ store.social['facebook'] }}" class="fb-xfbml-parse-ignore"><a href="{{ store.social['facebook'] }}">Son-Vidéo.com</a></blockquote></div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            {% if store.id=="paris-est" %}
                <div class="store-section">
                    <div class="text-center">
                        <a href="https://www.son-video.com/l-experience-home-cinema"><img src="{{ asset('/images/static/Promos/LettreInfo/BasNews/SVDEXHC_202206-BasNews.jpg', 'static_images') }}" width="600" height="360" class="img-responsive" alt="L'Expérience Home-Cinéma" /></a>
                    </div>
                </div>
            {% endif %}

            {% if store.gallery is defined and store.gallery is not empty %}
                {% set gallery_item = 1 %}
                <div class="store-section store-section-gallery" id="galerie">
                    <h2 class="store-section-title">{% trans from "store" %}the_stores_a_look_at_your_store{% endtrans %}</h2>
                    <div class="container">
                        <div class="row row-small-gutter">
                            {% for key in store.gallery %}
                                {% if gallery_item == 2 %}
                                    <div class="col-12 col-md-6">
                                        <div class="row row-small-gutter">
                                {% endif %}
                                <div class="{% if gallery_item == 1 %}col-12 col-md-6{% elseif gallery_item > 5 %}col-6 col-md-3{% else %}col-6{% endif %}">
                                    {% set p = (gallery_item == 1) ? '600' : '300' %}
                                    {% set sizes = (gallery_item == 1) ? '(min-width: 992px) 600px, (min-width: 640px) 400px, 600px' : '(min-width: 992px) 300px, (min-width: 640px) 180px, 300px' %}
                                    <div class="store-gallery-item">
                                        <a href="{{ asset(key.photo, 'static_images') }}" data-fancybox="gallery">
                                            <img src="{{ preset(asset(key.thumb, 'static_images'), p) }}"
                                                 srcset="{{ presets(asset(key.thumb, 'static_images'), [['180', '180w'], ['300', '300w'], ['400', '400w'], ['600', '600w']]) }}"
                                                 sizes="{{ sizes }}"
                                                 alt="Magasin Son-Vidéo.com {{ store.name_html }}"
                                                 class="img-responsive" />
                                        </a>
                                    </div>
                                </div>
                                {% if ( gallery_item == store.gallery|length and store.gallery|length < 6 ) or ( gallery_item == 5 and store.gallery|length > 5 ) %}
                                        </div>
                                    </div>
                                {% endif %}
                                {% set gallery_item = gallery_item + 1 %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}

            {% if store.virtual_visit is defined and store.virtual_visit is not empty %}
                <div class="store-section store-section-street-view" id="visite_virtuelle">
                    <h2 class="store-section-title">{% trans from "store" %}the_stores_virtual_shop_tour{% endtrans %}</h2>
                    <iframe src="{{ store.virtual_visit }}" width="980" height="750" frameborder="0" scrolling="no" allowfullscreen></iframe>
                </div>
            {% endif %}

            {% if store.team is defined and store.team is not empty %}
                <div class="store-section store-section-team" id="vos_conseillers">
                    <h2 class="store-section-title">{% trans with {'%city%': store.name_html } from 'store' %}your_store_experts{% endtrans %}</h2>
                    <div class="container margin-lg">
                        <div class="row row-small-gutter justify-content-center">
                            {% for key in store.team %}
                                {% if ( key.photo is defined and key.photo is not empty ) or ( key.name is defined and key.name is not empty ) or ( key.function is defined and key.function is not empty ) or ( key.movie is defined and key.movie is not empty ) or ( key.music is defined and key.music is not empty ) %}
                                    <div class="col-6 col-md-4 col-lg-3">
                                        <div class="trombi-item">
                                            {% if key.photo is defined and key.photo is not empty %}
                                                <div class="trombi-item-image"><img class="img-responsive" src="{{ asset(key.photo, 'static_images') }}"
                                                    {% if key.name is defined and key.name is not empty %}
                                                        alt="{{ key.name }}"
                                                    {% endif %}
                                                /></div>
                                            {% endif %}
                                            {% if ( key.name is defined and key.name is not empty ) or ( key.function is defined and key.function is not empty ) or ( key.movie is defined and key.movie is not empty ) or ( key.music is defined and key.music is not empty ) %}
                                                <div class="trombi-item-body">
                                                    {% if key.name is defined and key.name is not empty %}
                                                        <div class="trombi-item-name">{{ key.name }}</div>
                                                    {% endif %}
                                                    {% if key.function is defined and key.function is not empty %}
                                                        <div class="trombi-item-fonction">{{ key.function }}</div>
                                                    {% endif %}
                                                    {% if key.movie is defined and key.movie is not empty %}
                                                        <div class="trombi-item-movie"><strong>{% trans from "store" %}trombi_movie{% endtrans %}</strong> {{ key.movie }}</div>
                                                    {% endif %}
                                                    {% if key.music is defined and key.music is not empty %}
                                                        <div class="trombi-item-music"><strong>{% trans from "store" %}trombi_music{% endtrans %}</strong> {{ key.music }}</div>
                                                    {% endif %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% endif %}
                            {% endfor %}

                        </div>
                    </div>
                </div>
            {% endif %}

        </div>

        <div class="SVDv3_article_element mceContentBody">
            <p>
                <a class="btn btn-default" href="{{ path('store_index') }}">
                    <i class="icon icon-angle-left icon-fw icon-3-4x"></i> {% trans from "store" %}the_stores_see_all{% endtrans %}
                </a>
            </p>
        </div>

    </div>

{% endblock %}

{% block javascripts %}
    {{ parent() }}

    {% if store.latitudeDD is defined and store.latitudeDD is not empty and store.longitudeDD is defined and store.longitudeDD is not empty %}
        <script src="https://maps.googleapis.com/maps/api/js?key={{ google.maps_api_key }}" type="text/javascript"></script>

        <script type="text/javascript">
            window.SonVideo = window.SonVideo || {}
            window.SonVideo.maps = {
                locations: [{
                    label: '{{ 'the_stores_store' | trans({}, 'store') }} {{ store.name }}',
                    openings: '{{ store.openings|e('js') }}',
                    street: '{{ store.street|e('js') }}',
                    zip_code: '{{ store.zip_code|e('js') }}',
                    city: '{{ store.city|e('js') }}',
                    latitudeDD: {{ store.latitudeDD }},
                    longitudeDD: {{ store.longitudeDD }},
                }]
            }
        </script>

        {{ encore_entry_script_tags('map/utils')  }}
        {{ encore_entry_script_tags('svdmaps')  }}
    {% endif %}

    <div id="fb-root"></div>
    <script async defer crossorigin="anonymous" src="https://connect.facebook.net/fr_FR/sdk.js#xfbml=1&version=v9.0&appId=762840473814062&autoLogAppEvents=1" nonce="4yGCBs2a"></script>

{% endblock %}

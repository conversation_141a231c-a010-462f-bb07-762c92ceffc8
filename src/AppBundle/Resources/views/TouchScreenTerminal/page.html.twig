{% extends 'AppBundle:TouchScreenTerminal:layout.html.twig' %}

{% block content %}
<div id="VITRTACTL_wrapper">
    <header id="VITRTACTL_header_logo"></header>

    {# Beginning of shelves #}
    <div id="myCarouselList" class="carousel slide active">
        <div class="carousel-inner">
        {% for article in articles %}
            {# beginning of a group of 8 articles#}
            {% if loop.index0 % 8 == 0 %}
            <div class="item {% if loop.first %}active{% endif %}" data-page="{{ (loop.index0 // 8) + 1 }}">
                <section class="VITRTACTL_home_products">
            {% endif %}

                    {# beginning of a group of 2 articles#}
                    {% if loop.index0 % 2 == 0 %}
                    <section class="VITRTACTL_home_etagere_wrapper">
                        <div class="VITRTACTL_home_etagere">
                    {% endif %}

                            {# display one article #}
                            <article class="VITRTACTL_home_item" id="sommaire_{{ article.article_id }}">
                                <p class="VITRTACTL_item_title">{{ article.brand_name }} {{ article.name }}</p>
                                <p class="VITRTACTL_item_picture">
                                    <span>
                                        {% if article.media_variation is not empty and article.media_variation['image']['referential']['140'] is defined %}
                                            <img src="{{ asset(article.media_variation['image']['referential']['140'], 'article_images') }}">
                                        {% endif %}
                                    </span>
                                </p>
                            </article>

                    {# end of a group of 2 articles or last #}
                    {% if loop.index0 % 2 == 1 or loop.last %}
                        </div>
                    </section>
                    {% endif %}

            {# end of a group of 8 articles or last #}
            {% if loop.index0 % 8 == 7 or loop.last %}
                </section>
            </div>
            {% endif %}

        {% endfor %}
        </div>
    </div>
    {# End of shelves #}

    {# Beginning of products cards #}
    <div id="myCarouselProducts" class="carousel slide" data-interval="20000">
        <div class="carousel-inner">
        {% for article in articles %}
            <div class="item" id="fiche_{{ article.article_id }}" data-fiche="{{ loop.index0 }}">
                <article class="VITRTACTL_product_item">
                    <div class="VITRTACTL_product_item_wrapper">
                        <section class="VITRTACTL_home_etagere_wrapper">
                            <div class="VITRTACTL_home_etagere">
                                <div class="VITRTACTL_product_image">
                                    <p class="VITRTACTL_item_picture">
                                        <span>
                                            {% if article.media_variation is not empty %}
                                            <img border="0" alt="{{ article.brand_name }} {{ article.name }}"
                                                 src="{{ asset(article.media_variation['image']['referential'][article.media_variation['image']['largest']], 'article_images') }}">
                                            {% endif %}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </section>

                        <section class="VITRTACTL_product_information">
                            <div class="VITRTACTL_product_header">
                                <p class="VITRTACTL_product_title">{{ article.brand_name }} {{ article.name }}</p>
                                <div class="VITRTACTL_product_price_wrapper">
                                    <p class="VITRTACTL_product_price">{{ article.selling_price | localizedcurrency_svd(currency, app.request.locale) }}</p>
                                </div>
                            </div>

                            <div class="VITRTACTL_product_description_wrapper">
                                <div class="VITRTACTL_product_description">
                                    {{ article.editorial_content.touch_screen_description | default('') }}
                                </div>
                            </div>
                        </section>
                    </div>
                </article>
            </div>
        {% endfor %}
        </div>
    </div>
    {# End of products cards #}

    {# Navigation buttons #}
    <span class="VITRTACTL_roundButton VITRTACTL_arrow_left" id="arrow_left"><span></span></span>
    <span class="VITRTACTL_roundButton VITRTACTL_arrow_right" id="arrow_right"><span></span></span>
    <span class="VITRTACTL_roundButton VITRTACTL_button_home" id="button_home"><span></span></span>
</div>

{% endblock %}
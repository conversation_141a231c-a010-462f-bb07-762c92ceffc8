{% import _self as google_tags %}

{% macro transaction_tracking(order) %}
    {# Start Google Analytics transaction tracking #}
    <script type="text/javascript">
        (function() {
            {% if (order.status == 'AWAITING_PROCESSING') %}
                {# addItem might be called for every item in the shopping cart #}
                var items = [];
                {% for product in order.complete_products %}
                items.push({
                    'item_id': '{{ product.article_id }}',                                                      {# Product ID. Required #}
                    'item_name': "{{ product.brand_name|escape('js') ~ ' ' ~ product.name|escape('js') }}",     {# Product name. Required #}
                    'item_category': '{{ product.category_name|escape('js') }}',                                {# Category or variation #}
                    'item_brand': '{{ product.brand_name|default('')|escape('js') }}',                          {# Brand name #}
                    'price': {{ product.selling_price }},                                                       {# Unit price #}
                    'quantity': {{ product.quantity }}                                                          {# Quantity #}
                });
                {% endfor %}

                dataLayer.push({ecommerce: null})
                dataLayer.push({
                    'event': 'purchase',
                    'ecommerce': {
                        'currency' : 'EUR',
                        'transaction_id': '{{ order.customer_order_id }}',        {# Transaction ID. Required #}
                        'affiliation': '{{ order.affiliation }}',                 {# Affiliation or store name #}
                        'value': {{ order.getOrderAmountWithoutShippingCost() }}, {# Grand Total #}
                        'tax': {{ order.getOrderTaxeAmount() }},                  {# Tax #}
                        'shipping': {{ order.shipping_price }} ,                  {# Shipping #}
                        'items': items
                    },
                });

                {# addItem might be called for every item in the shopping cart #}
                var products = [];
                {% for product in order.complete_products %}
                products.push({
                    'id': '{{ product.article_id }}',                              {# Product ID. Required #}
                    'name': '{{ product.brand_name|default('') ~ product.name }}', {# Product name. Required #}
                    'category': '{{ product.category_name }}',                     {# Category or variation #}
                    'brand': '{{ product.brand_name|default('') }}',               {# Brand name #}
                    'price': '{{ product.selling_price }}',                        {# Unit price #}
                    'quantity': '{{ product.quantity }}'                           {# Quantity #}
                });
                {% endfor %}

                dataLayer.push({ecommerce: null})
                dataLayer.push({
                    'ecommerce': {
                        'purchase': {
                            'actionField': {
                                'id': '{{ order.customer_order_id }}',     {# Transaction ID. Required #}
                                'affiliation': '{{ order.affiliation }}',  {# Affiliation or store name #}
                                'revenue': '{{ order.total_price }}',      {# Grand Total #}
                                'tax': '{{ order.getOrderTaxeAmount() }}', {# Tax #}
                                'shipping': '{{ order.shipping_price }}'   {# Shipping #}
                            },
                            'products': products
                        }
                    },
                    'event': 'transaction'
                });
            {% endif %}
        })()
    </script>
{% endmacro transaction_tracking %}


{% macro adwords_conversion(order) %}
    {# Initiate variables for Google AdWords Conversion #}
    <script type="text/javascript">
        if (window.SonVideo.cookies.hasConsent('google_ads')) {
            dataLayer.push({
                'order_id': '{{ order.customer_order_id }}',
                'order_amount_without_shipping_price': '{{ order.getOrderAmountWithoutShippingCost() }}',
                'order_currency': 'EUR',
                'event': 'conversion'
            })
        }
    </script>
{% endmacro adwords_conversion %}


{% if tracker_data.page_type == 'order_confirm' %}
    {# Start step 5 of Google Analytics objectif #}
    <script type="text/javascript">
        dataLayer.push({ecommerce: null})
        dataLayer.push({
            'event': 'checkout',
            'ecommerce': {
                'checkout': {
                    'actionField': {
                        'step': 5,
                        'version': localStorage.getItem('funnel_version') || 'v1'
                    }
                }
            }
        });
        dataLayer.push({
            'event':'Virtual Page View',
            'virtual-page-url':'/mon-panier-etape-4-finalise'
        });
    </script>
    {# End step 5 of Google Analytics objectif #}

    {# Start Google Avis client#}
    {% if tracker_data.customer_order.getEstimatedDeliveryDate() is not null and tracker_data.customer_order.status is same as('AWAITING_PROCESSING') %}
        <script>
            window.renderOptIn = function() {
                if (window.SonVideo.cookies.hasConsent('g_review')) {
                    setTimeout(() => {
                        window.gapi.load('surveyoptin', function() {
                            window.gapi.surveyoptin.render(
                                {
                                    "merchant_id": {{ google.merchant_id }},
                                    "order_id": {{ tracker_data.customer_order.customer_order_id }},
                                    "email": '{{ app.user.email }}',
                                    "delivery_country": '{{ tracker_data.customer_order.shipping_address.country_code }}',
                                    "estimated_delivery_date": '{{ tracker_data.customer_order.getEstimatedDeliveryDate() }}'
                                });
                        });
                    }, 3000)

                }
            }
        </script>
        <script src="https://apis.google.com/js/platform.js?onload=renderOptIn" async defer></script>
    {% endif %}
    {# End Google Avis client#}

    {{ google_tags.transaction_tracking(tracker_data.customer_order) }}
    {{ google_tags.adwords_conversion(tracker_data.customer_order) }}

{% elseif tracker_data.page_type == 'shopping_cart' %}
    {# Start step 1 of Google Analytics objectif #}
    <script type="text/javascript">
        window.dataLayer.push({ecommerce: null})
        window.dataLayer.push({
            'event': 'checkout',
            'ecommerce': {
                'checkout': {
                    'actionField': {
                        'step': 1,
                        'version': (window.SonVideo?.basket_data.is_authenticated ?
                                ((window.SonVideo.basket_data.quotations?.length > 0) ? 'v2-devis' :
                                    (window.SonVideo?.basket_data?.quotes?.length > 0) ? 'v2-offre' : 'v2') :
                                'customer_not_connected'
                        )
                    }
                }
            }
        });
        window.dataLayer.push({
            'event':'Virtual Page View',
            'virtual-page-url': '/mon-panier-etape-1'
        });

        {# addItem might be called for every item in the shopping cart #}
        var items = [];
        {% for product in tracker_data.shopping_cart %}
        items.push({
            'item_id': '{{ product.article_id }}',                                                                                  {# Product ID. Required #}
            'item_name': "{{ product.brand_name|default('')|escape('js') ~ ' ' ~ product.article_name|default('')|escape('js') }}", {# Product name. Required #}
            'item_category': '{{ product.category_name|default('')|escape('js') }}',                                                {# Category or variation #}
            'item_brand': '{{ product.brand_name|default('')|escape('js') }}',                                                      {# Brand name #}
            'price': {{ product.selling_price }},                                                                                   {# Unit price #}
            'quantity': {{ product.quantity }}                                                                                      {# Quantity #}
        });
        {% endfor %}

        {% if tracker_data.shopping_cart|length > 0 %}
            window.dataLayer.push({ecommerce: null})
            window.dataLayer.push({
                event: 'view_cart',
                ecommerce: {
                    currency: 'EUR',
                    value: window.SonVideo.basket_data?.summary?.prices?.raw_amount_selling_price ?? 0,
                    items: items
                },
            })
        {% endif %}
    </script>
    {# End step 1 of Google Analytics objectif #}
{% endif %}

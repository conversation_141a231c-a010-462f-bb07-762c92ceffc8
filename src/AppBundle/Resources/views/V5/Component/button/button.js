// *** STYLES *** //
require('./button.scss')

window.addEventListener('load', () => {
    const btnList = document.getElementsByTagName('button')
    for (let btn of btnList) {
        if (btn.getAttribute('data-disabled')) {
            btn.disabled = btn.getAttribute('data-disabled') === '1'
        }

        const href = btn.getAttribute('data-href')
        if (href) {
            btn.addEventListener('click', (e) => {
                if (!btn.disabled) {
                    window.location.href = href
                }
            })
        }
    }
})

@import '../../../../scss/style';

.modal-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.66);
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: $fade_in;

    .modal {
        position: relative;
        background: $color_white;
        width: calc(100% - 32px);
        max-height: 95vh;
        margin: 16px;
        overflow: auto;

        @include media_min($media_md) {
            width: 980px;
            margin: 0;
        }

        &.smallest {
            @include media_min($media_md) {
                width: 600px;
            }
        }

        &.biggest {
            @include media_min($media_md) {
                width: 95vw;
                height: 95vh;
            }
        }

        &.triggeredBySystem {
            width: 95vw;
        }

        .header {
            position: sticky;
            top: 0;
            left: 0;
            right: 0;
            background: $color_white;
            padding: $space_8;
            border-bottom: 1px solid $color_grey_default;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: $font_size_20;
            line-height: $height_30;
            font-weight: $font_bold;
            z-index: 2;

            @include media_min($media_md) {
                padding: $space_10 $space_20;
            }

            .modal-title {
                color: $color_dark_default;
            }

            .close {
                display: flex;
                justify-content: center;
                align-items: center;
                height: $height_min_clickable;
                width: $width_min_clickable;
                cursor: pointer;
            }
        }

        .body {
            color: $color_grey_typo;
            height: auto;
        }

        .close-headerless {
            position: absolute;
            top: $space_8;
            right: $space_8;
            z-index: 2;

            .close-modal-container {
                display: flex;
                justify-content: center;
                align-items: center;
                height: $height_min_clickable;
                width: $width_min_clickable;
                cursor: pointer;
            }
        }
    }
}

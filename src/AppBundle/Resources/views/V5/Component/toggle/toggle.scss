@import '../../../../scss/style';

.toggle-section {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    width: 100%;
}

.toggle-wrapper {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: $space_16;
    width: 100%;
    text-align: left;
    user-select: none;

    &:has(.toggle-input:disabled) {
        opacity: 0.6;
    }
}

.toggle-toggle {
    flex-shrink: 0;
    margin-top: 4px;
}

.toggle-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
}

.toggle-input {
    position: absolute;
    opacity: 0;
    height: 0;
    width: 0;
}

.toggle-slider {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    background-color: $color_grey_typo;
    border-radius: 24px;
    transition: $transition_all_02;
    cursor: pointer;

    &:before {
        content: '';
        position: absolute;
        height: 16px;
        width: 16px;
        left: 2px;
        bottom: 2px;
        background-color: $color_white;
        border-radius: 50%;
        transition: $transition_all_02;
    }
}

.toggle-input:checked + .toggle-slider {
    background-color: $color_blue_web_safe;
}

.toggle-input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

.toggle-label {
    color: $color_grey_typo;
    font-size: $font_size_15;
    line-height: $space_24;
    font-weight: $font_bold;
    font-family: $font_open_sans;
    margin: 0;
    display: block;
    word-break: break-word;
}

.toggle-help-text {
    color: $color_grey_typo;
    font-size: $font_size_13;
    line-height: $space_18;
    margin-top: 2px;
    display: block;
    max-width: 100%;
    word-break: break-word;
}
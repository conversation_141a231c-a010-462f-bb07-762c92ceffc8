// *** STYLES *** //

require('./tooltip.scss')

// *** JS *** //
import { computePosition, offset, autoPlacement, autoUpdate, shift } from '@floating-ui/dom'
const elements = document.getElementsByClassName('svd-tooltip')
window.addEventListener('DOMContentLoaded', () => {
    ;[...elements].forEach(function (element) {
        const button = document.getElementById(`tooltip-action-${idWithoutTag(element.id)}`)
        const tooltip = document.getElementById(`tooltip-reaction-${idWithoutTag(element.id)}`)

        addClassToAllChild(element)

        autoUpdate(button, tooltip, update)

        function update() {
            getPosition(button, tooltip)
        }

        const cleanup = autoUpdate(button, tooltip, update, {
            ancestorScroll: false,
        })

        button.addEventListener('click', () => {
            showTooltip(tooltip)
            getPosition(button, tooltip)
        })
        button.addEventListener('touchstart', () => {
            showTooltip(tooltip)
            getPosition(button, tooltip)
        })

        document.addEventListener('mouseup', (event) => {
            clickOutside(event.target, tooltip)
            cleanup()
        })
        document.addEventListener('touchstart', (event) => {
            clickOutside(event.target, tooltip)
            cleanup()
        })
    })
})

function getPosition(button, tooltip) {
    computePosition(button, tooltip, {
        middleware: [autoPlacement(), offset(10), shift()],
    }).then(({ x, y }) => {
        Object.assign(tooltip.style, {
            left: `${x}px`,
            top: `${y}px`,
        })
    })
}

function showTooltip(tooltip_to_show) {
    ;[...elements].forEach(function (element) {
        const tooltip_to_test = document.getElementById(`tooltip-reaction-${idWithoutTag(element.id)}`)
        if (tooltip_to_test === tooltip_to_show) {
            tooltip_to_show.style.display = 'block'
        } else {
            tooltip_to_test.style.display = 'none'
        }
    })
}

function clickOutside(selected_element, tooltip) {
    if ([...selected_element.classList].indexOf('is-tooltip-action')) {
        ;[...elements].forEach(function () {
            tooltip.style.display = 'none'
        })
    }
}

const addClassToAllChild = (element) => {
    Object.values(element.children).forEach((e) => {
        e.classList.add('is-tooltip-action')
        if (e.children.length > 0) addClassToAllChild(e)
    })
}
function idWithoutTag(value) {
    return value.replace('svd-tooltip-', '')
}

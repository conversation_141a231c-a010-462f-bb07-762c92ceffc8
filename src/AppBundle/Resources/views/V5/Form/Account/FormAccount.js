// *** STYLES *** //
require('./FormAccount.scss')

const selectionToCartElement = document.getElementById('selection_to_cart_js')

if (selectionToCartElement) {
    const api = window.SonVideo.header_data.api
    selectionToCartElement.addEventListener('click', function (event) {
        event.preventDefault()

        const xhr = new XMLHttpRequest()
        xhr.open('POST', selectionToCartElement.dataset.path)
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded')
        xhr.onload = function () {
            if (xhr.status === 200) {
                window.location.href = api.cart_list
            }
        }
        xhr.send('slug=' + encodeURIComponent(selectionToCartElement.dataset.value))
    })
}

document.addEventListener('DOMContentLoaded', () => {
    let message = document.getElementById('customer_order_message_form_content')
    let counter = document.getElementById('counter')
    if (message && counter) {
        message.addEventListener('input', function () {
            let remaining = 1200 - message.value.length
            counter.textContent = remaining.toString()
        })
    }
})

{% import 'AppBundle:Common:pager_macro.html.twig' as pager_macro %}

{% block content %}
    <div class="items-basket">
        {% for item in items %}
            {% set quote =  item.data %}
            <div class="item-with-split" data-context="quote">
                <div class="item-basket">
                    <div class="data" data-context="item-data-name">
                        {% if quote.expired_at is null or date(quote.expired_at) >= date() %}
                            {% if quote.type == 'offer' %}
                                {% include 'AppBundle:V5/Component/link:svd_link.html.twig' with {
                                    label: 'Offre n°' ~ quote.quote_id,
                                    href: path('customer_account_quote_offer_view', {'id': quote.quote_id}),
                                    big: true
                                } %}
                            {% else %}
                                {% include 'AppBundle:V5/Component/link:svd_link.html.twig' with {
                                    label: 'Devis n°' ~ quote.quote_id,
                                    href: path('customer_account_quote_quotation_view', {'id': quote.quote_id}),
                                    big: true
                                } %}
                            {% endif %}
                            <div class="date" data-context="available-date">Valide jusqu'au {{ quote.expired_at | localizeddate('long', 'none', app.request.locale) }}</div>
                        {% else %}
                            <div class="title">
                                {% if quote.type == 'offer' %}
                                    Offre n°{{ quote.quote_id }}
                                {% else %}
                                    Devis n°{{ quote.quote_id }}
                                {% endif %}
                            </div>
                            <div class="date" data-context="expire-date">Expiré depuis le {{ quote.expired_at | localizeddate('long', 'none', app.request.locale) }}</div>
                        {% endif %}
                    </div>
                    <div class="data right">
                        <div class="price" data-context="item-data-price">{{ quote.prices.total_price_tax_included | localizedcurrency_svd(currency, app.request.locale) }}</div>
                    </div>
                </div>
                <div class="lines">
                    <div class="all-images">
                        {% for line in quote.lines %}
                            <div class="single-image" data-context="single-image">
                                {% if line.data.product.image is defined %}
                                    <img
                                        class="lozad"
                                        data-src="{{ preset(asset(line.data.product.image, 'article_images'), '55') }}"
                                        alt="{{ line.data.product.short_description }}"
                                    />
                                {% else %}
                                    <img
                                        class="lozad"
                                        data-src="{{ preset(asset('/images/ui/uiV3/graphics/no-img-300.png', 'article_images'), '55') }}"
                                        alt="Image indisponible"
                                    />
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>

                    {% if quote.expired_at is null or date(quote.expired_at) >= date() %}
                        <div class="all-actions">
                            {% if quote.type == 'offer' %}
                                {% if not item.contains_unbasketable_product %}
                                    <a data-context="add-to-cart-btn" href="{{ path('cart_add_quote', {'quote_id': quote.quote_id}) }}">
                                        {% include 'AppBundle:V5/Component/button:svd_primary.html.twig' with {
                                            label: "Ajouter au panier",
                                        } %}
                                    </a>
                                {% endif %}
                                <a data-context="see-details-btn" href="{{ path('customer_account_quote_offer_view', {'id': quote.quote_id}) }}">
                                    {% include 'AppBundle:V5/Component/button:svd_primary.html.twig' with {
                                        label: "Voir le détail",
                                        link: true
                                    } %}
                                </a>
                            {% else %}
                                {% if not item.contains_unbasketable_product %}
                                    <a data-context="pay-btn" href="{{ path('pay_quote', {'quote_id': quote.quote_id}) }}">
                                        {% include 'AppBundle:V5/Component/button:svd_primary.html.twig' with {
                                            label: "Payer mon devis",
                                        } %}
                                    </a>
                                {% endif %}
                                <a data-context="see-details-btn" href="{{ path('customer_account_quote_quotation_view', {'id': quote.quote_id}) }}">
                                    {% include 'AppBundle:V5/Component/button:svd_primary.html.twig' with {
                                        label: "Voir le détail",
                                        link: true
                                    } %}
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endfor %}

        {{ pager_macro.display_pager_v5(pager, 'customer_account_quote_list') }}
    </div>
{% endblock %}
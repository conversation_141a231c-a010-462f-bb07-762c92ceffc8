{% import 'AppBundle:Common:layout_macro.html.twig' as layout_macro %}

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="fr" lang="fr">
<head>
    <script type="application/javascript">
        window.dataLayer = [{
            'google_analytics_id': '{{ google.analytics_id }}',
            'google_analytics_v4_id': '{{ google.ga4_id }}'
        }];
    </script>
    {% if app.environment != 'test' %}
        {# Google Tag Manager #}
        <script>(function (w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start':
                        new Date().getTime(), event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl +
                    '&gtm_auth={{ google.tagmanager_auth }}&gtm_preview={{ google.tagmanager_preview }}&gtm_cookies_win=x';
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', '{{ google.tagmanager_id }}');</script>
        {#  End Google Tag Manager #}
    {% endif %}

    <meta http-equiv="Content-Type" content="{{ http_metas.content_type }}"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5">
    <meta name="robots" content="max-image-preview:large">

    <link rel="preload" fetchpriority="high" as="image" href="{{ asset('/images/ui/icons-svg/v5/logo-mobile.svg', 'static_images') }}" media="(max-width: 767.9px)">
    <link rel="preload" fetchpriority="high" as="image" href="{{ asset('/images/ui/icons-svg/v5/logo-fr.svg', 'static_images') }}" media="(min-width: 768px)">

    {% block preload %}{% endblock %}

    <link rel="preconnect" href="{{ asset('', 'generated_css') }}">
    <link rel="dns-prefetch" href="{{ asset('', 'generated_css') }}">
    <link rel="preconnect" href="{{ asset('', 'article_images') }}">
    <link rel="dns-prefetch" href="{{ asset('', 'article_images') }}">
    <link rel="preconnect" href="https://www.google-analytics.com">

    {% block preloaded_fonts %}
    <link rel="preload" href="{{ asset('/fonts/open-sans/open-sans-v15-latin-regular.woff2', 'generated_css') }}" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="{{ asset('/fonts/open-sans/open-sans-v15-latin-700.woff2', 'generated_css') }}" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="{{ asset('/fonts/open-sans/open-sans-v15-latin-600.woff2', 'generated_css') }}" as="font" type="font/woff2" crossorigin>
    <link href="{{ asset('/fonts/montserrat/Montserrat-Bold.woff2', 'generated_css') }}" as="font" type="font/woff2" crossorigin>
    {% endblock %}

    {# Tailwind #}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('v5/main')) }}

    {% block stylesheets %}
        {# Header, footer, sidepanel... #}
        {{ layout_macro.link_stylesheet(encore_entry_css_files('v5/common')) }}
        {{ layout_macro.link_stylesheet(encore_entry_css_files('reference_price')) }}
        {{ encore_entry_link_tags('layout/newsletter') }}

        {% if not isProd() %}
            {{ encore_entry_link_tags('developer_settings/app') }}
        {% endif %}
    {% endblock %}

    {% block metas %}
        {% if metas.description is defined and metas.description is not empty %}
            {% set metas = metas|merge({'description': metas.description|meta_parse(175, true) }) %}
        {% endif %}
        {% for key, value in metas %}
            <meta name="{{ key }}" content="{{ value|default('')|meta_parse }}"/>
        {% endfor %}
    {% endblock %}

    {# Open Graph #}
    {% block og %}
        <meta property='og:type' content='website'>
        <meta property='og:url' content="{{ (canonical_url is defined and canonical_url is not empty) ? base_url ~ canonical_url : base_url }}">
        <meta property='og:title' content='Son-Vidéo.com, leader français en hi-fi et home-cinéma'>
        <meta property='og:description' content='{{ metas.description | html_entity_decode }}'>
        <meta property='og:image' content='{{ base_url ~ '/images/ui/uiV5/logo-partage.png' }}'>
    {% endblock %}
    <meta property='og:site_name' content='Son-vidéo.com'>
    <meta property='og:local' content='fr_FR'>

    <title>{% spaceless %}{% block title %}Son-Vidéo.com{% endblock %}{% endspaceless %}</title>

    {% block structured_data %}{% endblock %}

    {% block canonical %}
        {% if canonical_url is defined and canonical_url is not empty %}
            <link rel="canonical" href="{{ base_url ~ canonical_url }}"/>
        {% endif %}
    {% endblock %}

    {{ layout_macro.link_favicons(favicons) }}
</head>
<body class="bg-transparent overflow-x-hidden">
{% if app.environment != 'test' %}
    {#  Google Tag Manager (noscript) #}
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id={{ google.tagmanager_id }}&gtm_auth={{ google.tagmanager_auth }}&gtm_preview={{ google.tagmanager_preview }}&gtm_cookies_win=x"
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    {#  End Google Tag Manager (noscript) #}

    {# Affilae script loading #}
    <script type="text/javascript">
        window._ae = {
            'pid': '5eec80a4c2bf5a6c1814a02a',
            'advertiserFirst': 0.1,
            'allowSiteUnder': false,
            'allowIframing': false,
            'host': 'https://lb.affilae.com'
        };
        (function () {
            var element = document.createElement('script');
            element.type = 'text/javascript';
            element.async = true;
            element.src = '//static.affilae.com/ae-v3.5.js';
            var scr = document.getElementsByTagName('script')[0];
            scr.parentNode.insertBefore(element, scr);
        })();
    </script>
    {# End Affilae script loading #}
{% endif %}

<div class='hidden min-[768px]:flex w-full' data-context='promotional-desktop' id="promotional-desktop">
    {% block promotional_message %}
        {% set excluded_pages = {
            order_creation: '\\/ma-commande\\/creation(\\/|\\?).*',
            order_confirm: '\\/ma-commande\\/confirmation(\\/|\\?).*',
            basket: '\\/mon-panier(\\/|\\?).*',
        } %}
        
        {% if dates.promotional | checkIfPromotionShouldBeShown('french_days', app.request, [excluded_pages.order_creation, excluded_pages.order_confirm]) %}
            <div class="text-13px leading-5 flex font-semibold min-h-36px items-center p-0 w-full">
                <a href="/selection/french-days" class="bg-svd-french-days w-full text-white block m-x-auto m-h-0 text-center leading-5 hover:text-white cursor-pointer p-2.5">
                    <strong class="uppercase">{% trans from 'promotion' %}frenchdays_title{% endtrans %}</strong> {% trans from 'promotion' %}frenchdays_text{% endtrans %} ({% trans from 'promotion' %}see_conditions_lower{% endtrans %})
                </a>
            </div>
        {% elseif dates.promotional | checkIfPromotionShouldBeShown('black_friday', app.request, [excluded_pages.order_creation, excluded_pages.order_confirm]) %}
            <div class="text-13px leading-5 flex font-semibold min-h-36px items-center p-0 w-full">
                <a href="/livraison-gratuite" class="bg-svd-black-friday bg-center w-full text-[#101214] block m-x-auto m-h-0 text-center leading-5 hover:text-[#101214] cursor-pointer p-2.5">
                    <strong class="uppercase">{% trans from 'promotion' %}free_shipping_on_short{% endtrans %}</strong> {% trans from 'promotion' %}see_conditions{% endtrans %}
                </a>
            </div>
        {% elseif dates.promotional | checkIfPromotionShouldBeShown('free_shipping', app.request, [excluded_pages.order_creation, excluded_pages.order_confirm]) %}
            <div class="text-13px leading-5 flex font-semibold min-h-36px items-center p-0 w-full">
                <a href="/soldes" class="bg-svd-free-shipping w-full text-white block m-x-auto m-h-0 text-center leading-5 hover:text-white cursor-pointer p-2.5">
                    <strong class="uppercase">{% trans from 'promotion' %}free_shipping_on_short{% endtrans %}</strong> {% trans from 'promotion' %}see_conditions{% endtrans %}.
                </a>
            </div>
        {% elseif dates.promotional | checkIfPromotionShouldBeShown('free_shipping_mondial_relay', app.request, [excluded_pages.order_creation, excluded_pages.order_confirm]) %}
            <div class="text-13px leading-5 flex font-semibold min-h-36px items-center p-0 w-full">
                <a href="/livraison-gratuite" class="bg-svd-free-shipping w-full text-white block m-x-auto m-h-0 text-center leading-5 hover:text-white cursor-pointer p-2.5">
                    <strong class="uppercase">{% trans from 'promotion' %}free_shipping_mondial_relay_on_short{% endtrans %}</strong> {% trans from 'promotion' %}see_conditions_mondial_relay_lower{% endtrans %}.
                </a>
            </div>
        {% elseif dates.promotional | checkIfPromotionShouldBeShown('birthday', app.request, [excluded_pages.order_creation, excluded_pages.order_confirm, excluded_pages.basket]) %}
            <div class="text-13px leading-5 flex font-semibold min-h-36px items-center p-0 w-full">
                <a href="/le-mois-anniversaire-son-video-com" class="bg-svd-darkblue w-full text-white block m-x-auto m-h-0 text-center leading-5 hover:text-white cursor-pointer p-2.5">
                    <strong class="uppercase">{% trans from 'promotion' %}birthday_month_title{% endtrans %}</strong> {% trans from 'promotion' %}birthday_month_contest{% endtrans %} ({% trans from 'promotion' %}see_conditions_lower{% endtrans %})
                </a>
            </div>
        {% elseif dates.promotional | checkIfPromotionShouldBeShown('sales', app.request, [excluded_pages.order_creation, excluded_pages.order_confirm]) %}
            <div class="text-13px leading-5 flex font-semibold min-h-36px items-center p-0 w-full">
                <a href="/soldes" class="w-full text-white block m-x-auto m-h-0 text-center leading-5 bg-svd-sales hover:text-white cursor-pointer p-2.5">
                    <strong class="uppercase">Soldes d’hiver -10% -30% -50%</strong> (jusqu’au 4 février)
                </a>
            </div>
        {% elseif dates.promotional | checkIfPromotionShouldBeShown('header_message', app.request, [excluded_pages.order_creation, excluded_pages.order_confirm]) %}
            <div class="text-13px leading-5 flex font-semibold min-h-36px items-center p-0 w-full">
                <a class="w-full text-font block m-x-auto m-h-0 text-center leading-5 bg-svd-christmas hover:text-font cursor-pointer p-2.5" id="home_cinema_month" href="/promos/mois-du-casque">
                    <strong class="uppercase">{{ 'home_cinema_month' | trans({}, 'promotion') }}</strong> {{ 'home_cinema_month_next' | trans({}, 'promotion') }} ({{ 'see_conditions_lower' | trans({}, 'promotion') }})
                </a>
            </div>
        {% endif %}
    {% endblock %}
</div>


{% block header %}
    {% include 'AppBundle:V5:Layout/header.html.twig' %}
{% endblock %}

<div class='flex min-[768px]:hidden flex-col' data-context='promotional-mobile'>
    {{ block('promotional_message') }}

    <section class="reassurance-header">
        <div class="reassurance-item phone" data-context="reassurance-header-mobile">
            {{ ('light/phone') | svd_icon('icon-24') | raw  }}
            <span class="number"></span>
        </div>
    </section>
</div>

{% block menu %}{% endblock %}

{% block message %}
    {% include "@App/Common/block/message.html.twig" %}
{% endblock %}

{% block breadcrumb %}{% endblock %}
{% block content %}{% endblock %}
{% block additional_content %}{% endblock %}

{% block footer %}
    {% include 'AppBundle:V5:Layout/footer/footer.html.twig' %}
{% endblock %}

{% block back_to_top %}
    {% if back_to_top_hidden is not defined or not back_to_top_hidden %}
    <button class="fixed bottom-20 right-5 z-10 flex w-12 h-12 bg-svd-neutral-100 transition ease duration-200 hover:bg-svd-neutral-500 hover:text-white rounded shadow opacity-0"
            id="back-to-top"
            data-context="back-to-top"
    >
        {{ 'regular/arrow-up' | svd_icon('icon-24 m-auto') | raw }}
        <span class="sr-only">Retour en haut</span>
    </button>
    {% endif %}
{% endblock %}

<div id="burger-menu-app"></div>
<div id="cookies-app"></div>
<div id="contact-app"></div>
<div id="reference-price-app"></div>
<div id="installments-app"></div>
<div id="newsletter-app"></div>
<div id="developer-settings-container"></div>

{% block javascripts_globals %}
    <script type="text/javascript">
        window.SonVideo = window.SonVideo || {}

        window.SonVideo.debug = {{ app.environment != 'prod' ? 'true' : 'false' }}

                {# Expose to javascript the different assets path generated by SF #}
            window.SonVideo.assets = {
                generated_js: '{{ asset('{{asset}}', 'generated_js') }}',
                generated_css: '{{ asset('{{asset}}', 'generated_css') }}',
                static_css: '{{ asset('{{asset}}', 'static_css') }}',
                article_images: '{{ asset('{{asset}}', 'article_images') }}',
                clickbait_images: '{{ asset('{{asset}}', 'clickbait_images') }}',
                static_images: '{{ asset('{{asset}}', 'static_images') }}',
            }

        {# Expose to javascript the cart summary api #}
        window.SonVideo.header_data = {
            api: {
                'cart_list': '{{ path('cart_list') }}',
                'cart_summary': '{{ path('cart_summary') }}',
                'cart_add_article': '{{ path('cart_add') }}',
                'login': '{{ path('login') }}',
                'logout': '{{ path('logout') }}',
                'api_customer_info': '{{ path('api_customer_info') }}',
                'customer_account_customer_area': '{{ path('customer_account_customer_area') }}',
                'customer_account_article_selection_list': '{{ path('customer_account_article_selection_list') }}',
                'customer_account_order_list': '{{ path('customer_account_order_list') }}',
                'customer_account_quote_list': '{{ path('customer_account_quote_list') }}',
            },
        }

        {# Expose recaptcha public key and if it is enabled #}
        window.SonVideo.recaptcha = {
            enabled: {{ recaptcha_enabled | json_encode }},
            public_key: '{{ recaptcha_public_key }}',
        }

        {# Expose google maps API key #}
        window.SonVideo.google_maps = {
            api_key: '{{ google.maps_api_key }}',
        }

        {# Cookies acceptance handling : Expose partners config to javascript #}
        window.SonVideo.cookies = {
            consent_configs: [
                {
                    key: 'site',
                    name: 'Fonctionnalités',
                    description: 'Ces cookies comprennent l’ensemble des fonctionnalités essentielles à Son-Vidéo.com (sauvegarde panier, compte client, préférences de navigation…)',
                    consents: [
                        {
                            key: 'essentials',
                            name: 'Fonctionnalités essentielles au site',
                            description: null,
                            required: true
                        },
                    ],
                },
                {
                    key: 'enhancement',
                    name: 'Mesures d’audience',
                    description: `Ces cookies nous permettent de mesurer la fréquentation de Son-Vidéo.com à des fins d’analyse et d’amélioration de l’expérience de chaque visite.`,
                    consents: [
                        {
                            key: 'g_a',
                            name: 'Google Analytics',
                            description: 'mesure d’audience et de fréquentation',
                            required: true
                        },
                        {
                            key: 'g_review',
                            name: 'Google Avis Client',
                            description: 'mesure de la satisfaction clients',
                            required: false
                        },
                        {
                            key: 'hotjar',
                            name: 'Hotjar',
                            description: 'mesure de l’utilisation des pages du site',
                            required: false
                        },
                    ],
                },
                {
                    key: 'customization',
                    name: 'Personnalisation',
                    description: `Ces cookies nous permettent de personnaliser votre expérience Son-Vidéo.com et de vous recommander les meilleurs produits.`,
                    consents: [
                        {
                            key: 'bloomreach',
                            name: 'Bloomreach',
                            description: 'recommandations et personnalisation',
                            required: false
                        },
                    ],
                },
                {
                    key: 'ads',
                    name: 'Marketing & publicité',
                    description: `Ces cookies assurent la personnalisation des publicités afin de vous proposer des contenus personnalisés et éviter les publicités trop génériques.`,
                    consents: [
                        { key: 'affilae', name: 'Affilae', description: 'programme d’affiliation', required: true },
                        {
                            key: 'bing_ads',
                            name: 'Bing Ads',
                            description: 'personnalisation de la publicité',
                            required: false
                        },
                        {
                            key: 'fb_ads',
                            name: 'Facebook Ads',
                            description: 'personnalisation de la publicité',
                            required: false
                        },
                        {
                            key: 'google_ads',
                            name: 'Google Ads',
                            description: 'personnalisation de la publicité',
                            required: false
                        },
                        {
                            key: 'google_rmk',
                            name: 'Google Ads Remarketing',
                            description: 'personnalisation de la publicité',
                            required: false
                        },
                        {
                            key: 'google_display',
                            name: 'Google Display Network',
                            description: 'personnalisation de la publicité',
                            required: false
                        },
                        { key: 'kimple', name: 'Kimple', description: 'évènements et jeux concours', required: false },
                        { key: 'twenga', name: 'Twenga', description: 'comparateur de prix', required: false },
                    ],
                },
            ]
        }

        window.SonVideo.version = 'v5'
    </script>

    {{ encore_entry_script_tags('v5/globals') }}
    {{ encore_entry_script_tags('cookies') }}
	{{ encore_entry_script_tags('br') }}
    {{ render(controller('AppBundle:Customer/Bloomreach:identify'))|raw }}
    {{ encore_entry_script_tags('reference_price') | defer | raw }}
{% endblock %}

{% block javascripts %}
    {# Slide-over #}
    {{ encore_entry_script_tags('contact') }}
    {{ encore_entry_script_tags('layout/newsletter') | defer | raw }}
    {{ encore_entry_script_tags('installments') }}

    {# Header, footer, GA... #}
    {{ encore_entry_script_tags('v5/common') }}
    {% if not isProd() %}
        {{ encore_entry_script_tags('developer_settings/app') | defer | raw }}
    {% endif %}
{% endblock %}

{% block tracking %}{% endblock %}
</body>
</html>

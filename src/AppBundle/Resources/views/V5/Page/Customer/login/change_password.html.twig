{% extends 'AppBundle:V5/Layout:base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('page/change_password')) }}
{% endblock %}

{% block message %}
{% endblock %}

{% block content %}
    <div class='edit-password-section'>
        {{ form_start(change_password_form, {
            'method': 'POST',
            'attr': {'class' : 'edit-password-form', 'id': 'edit-password-form'}
        }) }}
            {{ form_row(change_password_form._token) }}
            <div class='title-panel'>Modifier mon mot de passe</div>
            <div class='explain'>Modifier le mot de passe de votre compte <span class='no_wrap'>Son-Vidéo.com</span></div>
            {% include 'AppBundle:V5/Component/message:svd_message.html.twig' %}
            <div id='password-verif'>
                {% embed 'AppBundle:V5/Component/input:svd_password.html.twig' with {
                    label: 'Mot de passe',
                    name: "change_password_form[password][first]",
                    isForm: true,
                    form: change_password_form.password.first
                } %}
                    {% block helper %}
                        <div class="password-complexity" id='password-complexity' style='display: none'>
                            <div id='rule-min-length' class='rule invalid'>
                                <div data-icon='hide'>
                                    {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                                        icon: 'xmark',
                                        size: 20,
                                        color: 'e2020d'
                                    } %}
                                </div>
                                <div data-icon='show' style='display: none'>
                                    {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                                        icon: 'check',
                                        size: 20,
                                        color: '328806'
                                    } %}
                                </div>
                                <div>Minimum de 8 caractères</div>
                            </div>
                            <div id='rule-min-maj' class='rule invalid'>
                                <div data-icon='hide'>
                                    {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                                        icon: 'xmark',
                                        size: 20,
                                        color: 'e2020d'
                                    } %}
                                </div>
                                <div data-icon='show' style='display: none'>
                                    {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                                        icon: 'check',
                                        size: 20,
                                        color: '328806'
                                    } %}
                                </div>
                                <div>Une minuscule et une majuscule</div>
                            </div>
                            <div id='rule-num' class='rule invalid'>
                                <div data-icon='hide'>
                                    {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                                        icon: 'xmark',
                                        size: 20,
                                        color: 'e2020d'
                                    } %}
                                </div>
                                <div data-icon='show' style='display: none'>
                                    {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                                        icon: 'check',
                                        size: 20,
                                        color: '328806'
                                    } %}
                                </div>
                                <div>Un chiffre</div>
                            </div>
                            <div id='rule-special' class='rule invalid'>
                                <div data-icon='hide'>
                                    {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                                        icon: 'xmark',
                                        size: 20,
                                        color: 'e2020d'
                                    } %}
                                </div>
                                <div data-icon='show' style='display: none'>
                                    {% include 'AppBundle:V5/Component/icon:svd_icon.html.twig' with {
                                        icon: 'check',
                                        size: 20,
                                        color: '328806'
                                    } %}
                                </div>
                                <div>Un caractère spécial (!?&#$*)</div>
                            </div>
                        </div>
                        {{ parent() }}
                    {% endblock %}
                {% endembed %}
            </div>
            <div id='password-same'>
                {% embed 'AppBundle:V5/Component/input:svd_password.html.twig' with {
                    label: 'Confirmation du mot de passe',
                    name: "change_password_form[password][second]",
                    isForm: true,
                    form: change_password_form.password.second
                } %}
                    {% block helper %}
                        <div id='password-identical' style='display: none'>
                            <div class='error'>Les mots de passe doivent être identiques.</div>
                        </div>
                        {{ parent() }}
                    {% endblock %}
                {% endembed %}
            </div>
            {% include 'AppBundle:V5/Component/button:svd_primary.html.twig' with {
                label: "Modifier",
                is_submit: true,
                disabled: true,
                id: "btnChangePassword"
            } %}
        {{ form_end(change_password_form) }}
    </div>
{% endblock %}


{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('page/change_password') }}
{% endblock %}
// *** STYLES *** //
require('./change_password.scss')

// *** JS *** //
let passwordVerif = null
let passwordSame = null
let rulesValid = [false, false, false, false]
let ruleSame = false
window.onload = function () {
    passwordVerif = document.querySelector('#password-verif input')
    passwordVerif.addEventListener('keyup', verifyPassword)
    passwordSame = document.querySelector('#password-same input')
    passwordSame.addEventListener('keyup', verifyPasswordSame)
}

function verifyPasswordSame(e = passwordSame) {
    const value = e?.target?.value ?? passwordSame.value
    const passwordIdentical = document.getElementById('password-identical')

    if (document.querySelector('#password-verif .input-type .helper .error')) {
        document.querySelector('#password-verif .input-type .helper .error').remove()
    }

    if (value.length > 0 && passwordVerif.value !== value) {
        passwordIdentical.style.display = 'unset'
        ruleSame = false
    } else {
        passwordIdentical.style.display = 'none'
        ruleSame = true
    }
    disabledBtn()
}
function verifyPassword(e) {
    const value = e.target.value
    const hasMaj = /[A-Z]/g.test(value)
    const hasMin = /[a-z]/g.test(value)
    const hasNum = /[0-9]/g.test(value)
    const hasSpecial = /[^a-zA-Z\d\s:]/g.test(value)
    const passwordComplexity = document.getElementById('password-complexity')

    if (value.length > 0) {
        passwordComplexity.style.display = 'unset'
    } else {
        passwordComplexity.style.display = 'none'
    }
    checkRule('rule-min-length', 0, value.length >= 8)
    checkRule('rule-min-maj', 1, hasMaj && hasMin)
    checkRule('rule-num', 2, hasNum)
    checkRule('rule-special', 3, hasSpecial)

    verifyPasswordSame()
    disabledBtn()
}

function disabledBtn() {
    const btn = document.getElementById('btnChangePassword')
    if (btn !== null) {
        let passwordComplexity = true
        rulesValid.forEach((e) => {
            if (e === false) passwordComplexity = false
        })

        if (passwordComplexity && ruleSame) {
            btn.disabled = false
            btn.classList.remove('disabled')
        } else {
            btn.disabled = true
            btn.classList.add('disabled')
        }
    }
}

function checkRule(id, ruleIndex, condition) {
    const element = document.getElementById(id)
    if (condition) {
        element.classList.remove('invalid')
        element.classList.add('valid')

        element.children[0].style.display = 'none'
        element.children[1].style.display = 'unset'

        rulesValid[ruleIndex] = true
    } else {
        element.classList.remove('valid')
        element.classList.add('invalid')

        element.children[0].style.display = 'unset'
        element.children[1].style.display = 'none'

        rulesValid[ruleIndex] = false
    }
}

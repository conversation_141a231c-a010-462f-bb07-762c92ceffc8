{% extends 'AppBundle:V5/Layout:base.html.twig' %}

{% block title %}{% trans from "account" %}login_meta_title{% endtrans %}{% endblock %}

{% block tracking %}
    {{ render(controller('AppBundle:Tracking/Tracking:show', {'location': 'login' }))|raw }}
    {{ parent() }}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ layout_macro.link_stylesheet(encore_entry_css_files('page/login')) }}
{% endblock %}


{% block message %}
{% endblock %}

{% block content %}
    <div class='login-section'>
        <form id='login-form' class='login-form' action="{{ path('login') }}" method="post" >
            {% include 'AppBundle:V5/Component/message:svd_message.html.twig' %}
            <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
            <div class='title-panel'>Se connecter</div>
            {% include 'AppBundle:V5/Component/input:svd_text.html.twig' with {
                label: 'Email',
                required: true,
                default: last_username,
                name: "_username"
            } %}
            {% embed 'AppBundle:V5/Component/input:svd_password.html.twig' with {
                label: 'Mot de passe',
                required: true,
                name: "_password"
            } %}
                {% block helper %}
                    {% include 'AppBundle:V5/Component/link:svd_link.html.twig' with {
                        label: "Mot de passe oublié ?",
                        href: path('customer_reset_password', { email: last_username}),
                        small: true
                    } %}
                    {% if error %}
                        <span class="error">{{ error.messageKey|trans(error.messageData) }}</span>
                    {% endif %}
                    {{ parent() }}
                {% endblock %}
            {% endembed %}
            {% include 'AppBundle:V5/Component/button:svd_primary.html.twig' with {
                label: "Je me connecte",
                is_submit: true,
                id: 'bttnValiderContact'
            } %}
            <div class='subtext'>
                <div>
                    Vous n'avez pas de compte ?
                </div>
                <div>
                    {% include 'AppBundle:V5/Component/link:svd_link.html.twig' with {
                        label: "Créer un compte",
                        href: return_to is defined ? path('customer_create', {'_target_path': return_to}) : path('customer_create')
                    } %}
                </div>
            </div>
        </form>
    </div>
{% endblock %}


{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('page/login') }}
    {% if error %}
        <script type="text/javascript">
            localStorage.setItem('user.failed_to_connect', JSON.stringify(true))
        </script>
    {% endif %}
{% endblock %}
<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\ContextFilter;

/**
 * Class FulltextContextFilter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\ContextFilter
 * <AUTHOR> <<EMAIL>>
 */
class FulltextContextFilter implements ContextFilterInterface
{
    private array $words = [];

    /**
     * @var string
     */
    public $culture = '';

    /**
     * FulltextContextFilter constructor.
     */
    public function __construct(string $culture)
    {
        $this->culture = $culture;
    }

    /**
     * setWords
     */
    public function setWords(string $words): self
    {
        $this->words = $this->tokenize($words);

        return $this;
    }

    /**
     * getWords
     */
    public function getWords(): array
    {
        return $this->words;
    }

    /**
     * isEmpty
     */
    public function isEmpty(): bool
    {
        return $this->words === [];
    }

    /**
     * tokenize
     *
     * Split the words in tokens.
     */
    private function tokenize(string $words): array
    {
        $words = preg_replace('/[^[:alnum:][:space:]-]/u', ' ', $words);
        $words = trim(strtolower($words));

        return preg_split('/[\s]+/', $words);
    }

    /**
     * jsonSerialize
     */
    public function jsonSerialize(string $filter_key): array
    {
        return [];
    }
}

<?php
/*
 * This file is part of bo-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter;

use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\CommonContentModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\CommonContentReviewModel;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\ContextFilter\RangeContextFilter;

/**
 * Class AmountReviewFilter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter
 * <AUTHOR> <<EMAIL>>
 */
class AmountReviewFilter extends AbstractFilter implements FilterInterface
{
    /**
     * getFilterKey
     */
    public static function getFilterKey(): string
    {
        return 'amount_reviews';
    }

    /**
     * getFilterType
     */
    public static function getFilterType(): string
    {
        return RangeContextFilter::class;
    }

    /**
     * makeQuery
     *
     * @param   mixed $filter
     */
    public function makeQuery(Where $conditions, $filter): string
    {
        if (!$filter instanceof RangeContextFilter) {
            throw new \RuntimeException('Filter must be an instance of RangeContextFilter.');
        }

        $sql = <<<SQL
        SELECT
          a.article_id,
          count(common_content_review_id) AS amount_reviews
        FROM {article} a
          INNER JOIN {common_content} cc
            ON a.common_content_id = cc.common_content_id
          INNER JOIN {common_content_review} ccr
            ON ccr.common_content_id = cc.common_content_id
        GROUP BY
          a.article_id
        HAVING {conditions}
        ORDER BY
          amount_reviews DESC
        SQL;
        $sub_cond = Where::create();
        if ($filter->getMinValue() !== null) {
            $sub_cond->andWhere('count(ccr.common_content_review_id) >= $*', [$filter->getMinValue()]);
        }

        if ($filter->getMaxValue() !== null) {
            $sub_cond->andWhere('count(ccr.common_content_review_id) <= $*', [$filter->getMaxValue()]);
        }

        $conditions->andWhere($sub_cond);

        return strtr($sql, [
            '{conditions}' => $sub_cond,
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{common_content}' => $this->getModelRelation(CommonContentModel::class),
            '{common_content_review}' => $this->getModelRelation(CommonContentReviewModel::class),
        ]);
    }
}

<?php
/*
 * This file is part of bo-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter;

use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\BrandModel;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\ContextFilter\MultipleValuesContextFilter;

/**
 * Class BrandFilter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class BrandFilter extends AbstractFilter implements FilterInterface
{
    /**
     * getFilterKey
     */
    public static function getFilterKey(): string
    {
        return 'brands';
    }

    /**
     * getFilterType
     */
    public static function getFilterType(): string
    {
        return MultipleValuesContextFilter::class;
    }

    /**
     * makeQuery
     * Create a Brand sub query for search.
     *
     * @param mixed $filter
     */
    public function makeQuery(Where $conditions, $filter, bool $search_only_basketable = true): string
    {
        if (!$filter instanceof MultipleValuesContextFilter) {
            throw new \RuntimeException('Filter must be an instance of MultipleValuesContextFilter.');
        }

        $sub_cond = Where::create('is_active');
        if ($filter->getValues() !== []) {
            $sub_cond->andWhere(Where::createWhereIn('b.slug', $filter->getValues()));
        }

        if ($filter->getNotValues() !== []) {
            $sub_cond->andWhere(Where::createWhereNotIn('b.slug', $filter->getNotValues()));
        }

        if ($search_only_basketable) {
            $sub_cond->andWhere('a.unbasketable_reason is null');
        }

        $sql = <<<SQL
        SELECT article_id
        FROM {article} a
          INNER JOIN {brand} b USING (brand_id)
        WHERE
          {condition}
        SQL;
        // Add to "general" condition by reference.
        $conditions->andWhere($sub_cond);

        return strtr($sql, [
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{brand}' => $this->getModelRelation(BrandModel::class),
            '{condition}' => $sub_cond,
        ]);
    }
}

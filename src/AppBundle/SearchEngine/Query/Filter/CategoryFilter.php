<?php
/*
 * This file is part of bo-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter;

use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleCategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryModel;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\ContextFilter\MultipleValuesContextFilter;

/**
 * Class CategoryFilter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter
 * <AUTHOR> <<EMAIL>>
 */
class CategoryFilter extends AbstractFilter implements FilterInterface
{
    /**
     * getFilterKey
     */
    public static function getFilterKey(): string
    {
        return 'category';
    }

    /**
     * getFilterType
     */
    public static function getFilterType(): string
    {
        return MultipleValuesContextFilter::class;
    }

    /**
     * makeQuery
     *
     * @param mixed $filter
     */
    public function makeQuery(Where $conditions, $filter, bool $search_only_basketable = true): string
    {
        $this->checkFilter($filter);

        $sub_cond = Where::createWhereIn('c.slug', array_merge($filter->getValues(), $filter->getNotValues()));
        $sub_cond2 = Where::create($search_only_basketable ? 'a.unbasketable_reason IS NULL' : 'true');
        if ((is_countable($filter->getValues()) ? count($filter->getValues()) : 0) > 0) {
            $sub_cond2->andWhere(Where::create('ct.category_id IS NOT NULL'));
        }

        if ((is_countable($filter->getNotValues()) ? count($filter->getNotValues()) : 0) > 0) {
            $sub_cond2->andWhere(Where::create('ct.category_id IS NULL'));
        }

        $conditions->andWhere($sub_cond);

        $sql = <<<SQL
        WITH RECURSIVE
          category_tree AS (
              SELECT c.category_id
              FROM {category} c
              WHERE {condition}
              UNION ALL
              SELECT c.category_id
              FROM {category} c
                INNER JOIN category_tree AS parent ON parent.category_id = c.category_parent_id
        )
        SELECT DISTINCT
          a.article_id
        FROM {article} a
          LEFT JOIN {article_category} ac USING (article_id)
          LEFT JOIN category_tree ct USING (category_id)
        WHERE {condition2}
        SQL;

        return strtr($sql, [
            '{article}' => $this->getModelRelation(ArticleModel::class),
            '{article_category}' => $this->getModelRelation(ArticleCategoryModel::class),
            '{category}' => $this->getModelRelation(CategoryModel::class),
            '{condition}' => $sub_cond,
            '{condition2}' => $sub_cond2,
        ]);
    }

    /**
     * checkFilter
     *
     * @param mixed $filter
     */
    protected function checkFilter($filter): self
    {
        if (!$filter instanceof MultipleValuesContextFilter) {
            throw new \RuntimeException('Filter must be an instance of MultipleValuesContextFilter.');
        }

        if ($filter->getValues() === [] && $filter->getNotValues() === []) {
            throw new \InvalidArgumentException('Array categories must not be empty.');
        }

        return $this;
    }
}

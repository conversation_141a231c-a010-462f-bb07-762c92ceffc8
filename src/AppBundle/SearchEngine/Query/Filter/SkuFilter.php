<?php
/*
 * This file is part of bo-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter;

use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\ContextFilter\MultipleValuesContextFilter;

/**
 * Class SkuFilter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class SkuFilter extends AbstractFilter implements FilterInterface
{
    /**
     * getFilterKey
     */
    public static function getFilterKey(): string
    {
        return 'skus';
    }

    /**
     * getFilterType
     */
    public static function getFilterType(): string
    {
        return MultipleValuesContextFilter::class;
    }

    /**
     * makeQuery
     *
     * @param   mixed $filter
     */
    public function makeQuery(Where $conditions, $filter): string
    {
        if (!$filter instanceof MultipleValuesContextFilter) {
            throw new \RuntimeException('Filter must be an instance of MultipleValuesContextFilter.');
        }

        $sub_cond = Where::create('true');
        if ($filter->getValues() !== []) {
            $sub_cond->andWhere(Where::createWhereIn('a.sku', $filter->getValues()));
        }

        if ($filter->getNotValues() !== []) {
            $sub_cond->andWhere(Where::createWhereNotIn('a.sku', $filter->getNotValues()));
        }

        // Add to "general" condition by reference.
        $conditions->andWhere($sub_cond);

        return strtr('SELECT a.article_id FROM {article} a WHERE {condition}', [
            '{condition}' => $sub_cond,
            '{article}' => $this->getModelRelation(ArticleModel::class),
        ]);
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query;

use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter\FilterInterface;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter\IsFilter;
use PommProject\Foundation\Where;
use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\BoContext;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\SearchContextInterface;

/**
 * Class SearchQueryProvider
 *
 * Provide a query that fetch article ids in function of the search context.
 * This query can be executed like that, or can be used in a WITH subquery in order to use a largest projection.
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query
 * <AUTHOR> <<EMAIL>>
 */
class SearchQueryProvider
{
    use SearchQueriesMakerTrait;

    private PommSession $pomm_session;

    /**
     * SearchQueryProvider constructor.
     */
    public function __construct(PommSession $pomm_session, bool $search_only_basketable = true)
    {
        $this->pomm_session = $pomm_session;
        $this->search_only_basketable = $search_only_basketable;
    }

    /**
     * getPommSession
     */
    public function getPommSession(): PommSession
    {
        return $this->pomm_session;
    }

    /**
     * getSearchQuery
     *
     * Returns a sql query which provides a list of article ids matching the search context.
     */
    public function getSearchQuery(SearchContextInterface $context, Where $condition, string $culture): string
    {
        $sqltab = ['culture as (select $*::text as culture_id)'];
        $condition->andWhere('', [$culture]);

        $sub_queries = $this->makeSubQueries($context, $condition, $culture);
        foreach ($sub_queries as $name => $query) {
            $sqltab[] = sprintf('%s as (%s)', $name, $query);
        }

        $sqltab[] = sprintf(
            'regroup_search as (%s)',
            implode(
                ' INTERSECT ',
                array_map(fn($name): string => sprintf('select article_id from %s', $name), array_keys($sub_queries)),
            ),
        );

        $sql = <<<SQL
        WITH {sub_queries}
        SELECT
          r.article_id,
          {ranking} AS ranking
        FROM
          regroup_search r
          {subquery_words_join}
        GROUP BY 1,2
        SQL;

        return strtr($sql, [
            '{sub_queries}' => implode(",\n", $sqltab),
            '{ranking}' => isset($sub_queries['subquery_words']) ? 'coalesce(sw.ranking, 1)' : '1',
            '{subquery_words_join}' => isset($sub_queries['subquery_words'])
                ? 'LEFT JOIN subquery_words sw ON sw.article_id = r.article_id'
                : '',
        ]);
    }

    /**
     * makeSubQueries
     */
    protected function makeSubQueries(SearchContextInterface $context, Where $condition, string $culture): array
    {
        $sub_queries = [
            'article_with_price' => $this->makeArticleWithPriceQuery(),
            'basketable_article' => $this->search_only_basketable ? $this->makeBasketableQuery() : null,
        ];

        $filters = $context instanceof BoContext ? $this->getBoFilters() : $this->getFilters();
        foreach ($filters as $filter) {
            if (!$filter instanceof FilterInterface) {
                throw new \RuntimeException('Filter must be an instance of FilterInterface.');
            }

            $filter_key = $filter::getFilterKey();
            if ($context->has($filter_key) || $filter instanceof IsFilter) {
                $sub_queries['subquery_' . $filter_key] = $filter->makeQuery(
                    $condition,
                    $context->get($filter_key),
                    $this->search_only_basketable,
                );
            }
        }

        return array_filter($sub_queries);
    }

    /**
     * getModelRelation
     */
    protected function getModelRelation(string $class_name): string
    {
        return $this->getPommSession()
            ->getModel($class_name)
            ->getStructure()
            ->getRelation();
    }
}

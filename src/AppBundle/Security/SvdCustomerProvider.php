<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Security;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Account;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\PommAwareServiceTrait;
use Symfony\Component\Security\Core\Authentication\Token\RememberMeToken;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\Exception\UsernameNotFoundException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;

/**
 * Class SvdCustomerProvider
 *
 * @package CMS Front-Office
 * <AUTHOR> Audic <<EMAIL>>
 * @see     UserProviderInterface
 */
class SvdCustomerProvider implements UserProviderInterface
{
    use PommAwareServiceTrait;

    /**
     * loadUserByUsername
     *
     * @param string $username
     *
     * @return mixed
     * @throws UsernameNotFoundException
     */
    public function loadUserByUsername($username)
    {
        $user = $this->getPomm()
            ->getDefaultSession()
            ->getModel(AccountModel::class)
            ->refreshAccount($username);

        if (!$user) {
            throw new UsernameNotFoundException(sprintf('Username "%s" does not exist.', $username));
        }

        return $user;
    }

    /**
     * refreshUser
     *
     *
     * @return mixed
     * @throws UnsupportedUserException
     */
    public function refreshUser(UserInterface $user)
    {
        if (!$this->supportsClass(\get_class($user))) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', \get_class($user)));
        }

        return $this->loadUserByUsername($user->getUsername());
    }

    /**
     * supportsClass
     *
     * @param string $class
     *
     * @return bool
     */
    public function supportsClass($class)
    {
        return $class === Account::class || $class === RememberMeToken::class;
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\CustomerPromoCode;

use SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Type\Advantage;
use SonVideo\Cms\FrontOffice\AppBundle\Exception\PromoCodeException;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\PromoCode\PromoCodeResolver;
use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Repository\ShoppingCartRepositoryInterface;
use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\ShoppingCartEntityProvider;

/**
 * Trait ShoppingCartCustomerPromoCodeTrait
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\CustomerPromoCode
 * <AUTHOR> <<EMAIL>>
 */
trait ShoppingCartCustomerPromoCodeTrait
{
    /**
     * getPromoCodeResolver
     */
    abstract protected function getPromoCodeResolver(): PromoCodeResolver;

    /**
     * getSpecificShoppingCart
     */
    abstract protected function getSpecificShoppingCart(): ShoppingCartRepositoryInterface;

    /**
     * getCustomerOrSessionId
     *
     * @return mixed
     */
    abstract public function getCustomerOrSessionId();

    /**
     * inform
     *
     * @return mixed
     */
    abstract protected function inform(string $subject, array $data = []);

    /**
     * getEntityProvider
     */
    abstract protected function getEntityProvider(): ShoppingCartEntityProvider;

    /**
     * tinySummary
     */
    abstract public function tinySummary(): array;

    /**
     * addCustomerPromoCode
     *
     * @return $this
     * @throws PromoCodeException
     */
    protected function addCustomerPromoCode(string $code, int $customer_id)
    {
        // Find promotional code with code
        $customer_promo_code = $this->getEntityProvider()->getCustomerPromoCode($code, $customer_id);

        $this->getPromoCodeResolver()->isEligible($customer_promo_code, $this->tinySummary());

        $this->getSpecificShoppingCart()->setCustomerPromoCode(
            $customer_promo_code->get('code'),
            $customer_promo_code->get('customer_id'),
        );

        $this->inform('shopping_cart_customer_promo_code.add.fo-cms', [
            'customer_id' => $customer_promo_code->get('customer_id'),
            'code' => $customer_promo_code->get('code'),
        ]);

        return $this;
    }

    /**
     * removeCustomerPromoCode
     */
    protected function removeCustomerPromoCode(): bool
    {
        $this->getSpecificShoppingCart()->removeCustomerPromoCode();

        return true;
    }

    /**
     * getCustomerPromoCodeAdvantage
     */
    protected function getCustomerPromoCodeAdvantage()
    {
        // Retrieve promotional code from customer selection
        [$code, $customer_id] = $this->getCustomerPromoCode();
        if ($code === null || $customer_id === null) {
            return null;
        }

        $customer_promo_code = $this->getEntityProvider()->getCustomerPromoCode($code, $customer_id);

        try {
            // Retrieve and format advantage of promotional code
            return $this->getPromoCodeResolver()->getAdvantage(
                $customer_promo_code,
                $this->tinySummary(),
                Advantage::CUSTOMER_PROMO_CODE_PROVIDER_TYPE,
            );
        } catch (PromoCodeException $pce) {
            // If we are here, the promotional code is no longer eligible. We remove it
            $this->removeCustomerPromoCode();
        }

        return null;
    }

    /**
     * getPromoOfferId
     */
    private function getCustomerPromoCode(): array
    {
        return $this->getSpecificShoppingCart()->getCustomerPromoCode();
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Magazine;

/**
 * Interface MagazineInBasketInterface
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Magazine
 * <AUTHOR> <<EMAIL>>
 */
interface MagazineInBasketInterface
{
    /**
     * hasMagazine
     */
    public function hasMagazine(): bool;

    /**
     * extract
     */
    public function extract(): array;

    /**
     * jsonSerialize
     */
    public function jsonSerialize(): array;
}

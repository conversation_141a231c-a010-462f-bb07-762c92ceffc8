<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Repository;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use PommProject\ModelManager\Session as PommSession;
use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\QuoteManager;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleWarrantyExtension;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\BasketableInterface;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Account;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Basket;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketLineModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketModel;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

/**
 * Class ShoppingCartAuthenticatedRepository
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Repository
 * <AUTHOR> Marniesse <<EMAIL>>
 */
class ShoppingCartAuthenticatedRepository implements ShoppingCartRepositoryInterface
{
    private PommSession $pomm_session;

    private QuoteManager $quote_manager;

    private TokenStorageInterface $token_storage;

    private string $locale;

    /**
     * ShoppingCartManager constructor.
     */
    public function __construct(
        PommSession $pomm_session,
        QuoteManager $quote_manager,
        TokenStorageInterface $token_storage,
        string $locale
    ) {
        $this->pomm_session = $pomm_session;
        $this->quote_manager = $quote_manager;
        $this->token_storage = $token_storage;
        $this->locale = $locale;
    }

    public function getArticles(): CollectionIterator
    {
        return $this->pomm_session
            ->getModel(BasketLineModel::class)
            ->listArticles($this->getCustomerId(), $this->locale);
    }

    /**
     * getQuotes
     */
    public function getQuotes(): array
    {
        $quotes = $this->pomm_session
            ->getModel(BasketLineModel::class)
            ->listQuotesForCustomer($this->getCustomerId())
            ->extract();

        foreach ($quotes as $quote_key => $quote) {
            $quotes[$quote_key] = $this->quote_manager->addProductDetails($quote, 'quote_lines');
        }

        return $quotes;
    }

    /**
     * getAccessories
     *
     * @return CollectionIterator
     */
    public function getAccessories()
    {
        return $this->pomm_session
            ->getModel(BasketLineModel::class)
            ->listAccessories($this->getCustomerId(), $this->locale);
    }

    public function addBasketable(BasketableInterface $basketable, int $quantity): ShoppingCartRepositoryInterface
    {
        $customer_id = $this->getCustomerId();
        // Create shopping cart if not exists
        $this->getCustomerShoppingCart($customer_id);

        // Insert article
        $this->pomm_session->getModel(BasketLineModel::class)->addBasketable($basketable, $customer_id, $quantity);

        return $this;
    }

    /**
     * updateBasketableQuantity
     */
    public function updateBasketableQuantity(
        BasketableInterface $basketable,
        int $quantity
    ): ShoppingCartRepositoryInterface {
        $this->pomm_session
            ->getModel(BasketLineModel::class)
            ->updateBasketableQuantity($basketable, $this->getCustomerId(), $quantity);

        return $this;
    }

    /**
     * removeBasketable
     *
     *
     */
    public function removeBasketable(BasketableInterface $basketable): ShoppingCartRepositoryInterface
    {
        $this->pomm_session->getModel(BasketLineModel::class)->removeBasketable($basketable, $this->getCustomerId());

        return $this;
    }

    /**
     * clear
     */
    public function clear(): bool
    {
        $this->pomm_session->getModel(BasketModel::class)->deleteByPK(['customer_id' => $this->getCustomerId()]);

        return true;
    }

    /**
     * summary
     */
    public function summary(): array
    {
        return $this->pomm_session->getModel(BasketModel::class)->summary($this->getCustomerId(), $this->locale) ?? [];
    }

    /**
     * tinySummary
     */
    public function tinySummary(): array
    {
        return array_merge(
            $this->pomm_session->getModel(BasketModel::class)->tinySummary($this->getCustomerId()) ?? [],
            ['customer_id' => $this->getCustomerId()],
        );
    }

    /**
     * getTotalAmount
     */
    public function getTotalAmount(): float
    {
        return (float) ($this->summary()['amount_selling_price'] ?? 0);
    }

    /**
     * setPromoOfferId
     *
     * @param int|null $promo_offer_id
     */
    public function setPromoOfferId($promo_offer_id): bool
    {
        // Create or retrieve customer selection for shopping cart
        $customer_selection = $this->getCustomerShoppingCart($this->getCustomerId());
        // Add advantage
        $this->pomm_session
            ->getModel(BasketModel::class)
            ->updateByPK(
                ['customer_id' => $customer_selection->get('customer_id')],
                ['promo_offer_id' => $promo_offer_id],
            );

        return true;
    }

    /**
     * getPromoCodeId
     *
     * @return int|null
     */
    public function getPromoOfferId()
    {
        return $this->getCustomerShoppingCart($this->getCustomerId())->get('promo_offer_id');
    }

    /**
     * getWarranties
     */
    public function getWarranties(BasketableInterface $basketable): array
    {
        return $this->pomm_session
            ->getModel(BasketLineModel::class)
            ->getWarranties($basketable, $this->getCustomerId());
    }

    /**
     * addWarranty
     */
    public function addWarranty(
        BasketableInterface $basketable,
        ArticleWarrantyExtension $article_warranty
    ): ShoppingCartRepositoryInterface {
        $this->pomm_session
            ->getModel(BasketLineModel::class)
            ->addWarranty($basketable, $this->getCustomerId(), $article_warranty);

        return $this;
    }

    /**
     * removeWarranty
     */
    public function removeWarranty(
        BasketableInterface $basketable,
        string $warranty_tag_path
    ): ShoppingCartRepositoryInterface {
        $this->pomm_session
            ->getModel(BasketLineModel::class)
            ->removeWarranty($basketable, $this->getCustomerId(), $warranty_tag_path);

        return $this;
    }

    /**
     * setCustomerPromoCode
     */
    public function setCustomerPromoCode(string $code, int $promo_code_customer_id): ShoppingCartRepositoryInterface
    {
        $customer_selection = $this->getCustomerShoppingCart($this->getCustomerId());

        $this->pomm_session->getModel(BasketModel::class)->updateByPK(
            ['customer_id' => $customer_selection->get('customer_id')],
            [
                'customer_promo_code_code' => $code,
                'customer_promo_code_customer_id' => $promo_code_customer_id,
            ],
        );

        return $this;
    }

    /**
     * removeCustomerPromoCode
     */
    public function removeCustomerPromoCode(): bool
    {
        $customer_selection = $this->getCustomerShoppingCart($this->getCustomerId());

        $this->pomm_session->getModel(BasketModel::class)->updateByPK(
            ['customer_id' => $customer_selection->get('customer_id')],
            [
                'customer_promo_code_code' => null,
                'customer_promo_code_customer_id' => null,
            ],
        );

        return true;
    }

    /**
     * getCustomerPromoCode
     */
    public function getCustomerPromoCode(): array
    {
        $customer_selection = $this->getCustomerShoppingCart($this->getCustomerId());

        return [
            $customer_selection->get('customer_promo_code_code'),
            $customer_selection->get('customer_promo_code_customer_id'),
        ];
    }

    /**
     * getCustomerShoppingCart
     *
     * Get or create shopping cart for customer.
     */
    private function getCustomerShoppingCart(int $customer_id): Basket
    {
        return $this->pomm_session->getModel(BasketModel::class)->fetchOne($customer_id);
    }

    /**
     * getCustomerId
     *
     * @return int|null
     */
    public function getCustomerId()
    {
        $token = $this->token_storage->getToken();
        $account = $token instanceof TokenInterface ? $token->getUser() : null;

        return $account instanceof Account ? $account->get('customer_id') : null;
    }

    /**
     * getSessionId
     */
    public function getSessionId(): string
    {
        return '';
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Repository;

use PommProject\ModelManager\Session as PommSession;
use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleWarrantyExtension;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\BasketableInterface;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccessorySessionModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\ArticleSessionSelectionModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\ShoppingCartSessionTrait;
use Symfony\Component\HttpFoundation\Session\Session;

/**
 * Class ShoppingCartSessionRepository
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Repository
 * @copyright 2017 Son-Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 */
class ShoppingCartSessionRepository implements ShoppingCartRepositoryInterface
{
    use ShoppingCartSessionTrait;

    private PommSession $pomm_session;

    private string $locale;

    /**
     * ShoppingCartSessionRepository constructor.
     */
    public function __construct(PommSession $pomm_session, Session $session, string $locale)
    {
        $this->pomm_session = $pomm_session;
        $this->session = $session;
        $this->locale = $locale;
    }

    public function getArticles(): CollectionIterator
    {
        return $this->pomm_session->getModel(ArticleSessionSelectionModel::class)->listArticles(
            $this->getCartSession()
                ->getArticles()
                ->extract(),
            $this->locale,
        );
    }

    /**
     * getQuotes
     */
    public function getQuotes(): array
    {
        return [];
    }

    /**
     * getAccessories
     */
    public function getAccessories(): CollectionIterator
    {
        return $this->pomm_session->getModel(AccessorySessionModel::class)->listAccessories(
            $this->getCartSession()
                ->getAccessories()
                ->extract(),
            $this->locale,
        );
    }

    public function addBasketable(BasketableInterface $basketable, int $quantity): ShoppingCartRepositoryInterface
    {
        $cart_session = $this->getCartSession()->addBasketable(
            $basketable->getBasketableName(),
            $basketable->getId(),
            $quantity,
        );

        $this->updateCartSession($cart_session);

        return $this;
    }

    /**
     * updateBasketableQuantity
     *
     *
     */
    public function updateBasketableQuantity(
        BasketableInterface $basketable,
        int $quantity
    ): ShoppingCartRepositoryInterface {
        $cart_session = $this->getCartSession()->setBasketableQuantity(
            $basketable->getBasketableName(),
            $basketable->getId(),
            $quantity,
        );

        $this->updateCartSession($cart_session);

        return $this;
    }

    /**
     * removeBasketable
     *
     *
     */
    public function removeBasketable(BasketableInterface $basketable): ShoppingCartRepositoryInterface
    {
        $cart_session = $this->getCartSession()->removeBasketable(
            $basketable->getBasketableName(),
            $basketable->getId(),
        );

        $this->updateCartSession($cart_session);

        return $this;
    }

    /**
     * clear
     */
    public function clear(): bool
    {
        $this->session->remove(self::$session_shopping_cart_key);

        return true;
    }

    /**
     * summary
     */
    public function summary(): array
    {
        $summary = $this->pomm_session->getModel(ArticleSessionSelectionModel::class)->summary(
            $this->getCartSession()
                ->getArticles()
                ->extract(),
            $this->locale,
        );

        $accessories_summary = $this->pomm_session->getModel(AccessorySessionModel::class)->summary(
            $this->getCartSession()
                ->getAccessories()
                ->extract(),
        );

        $summary->merge($accessories_summary);

        if (!$summary->isEmpty()) {
            $summary->setPromoCodeId($this->getShoppingCartSessionPromoPromoOfferId());
        }

        return $summary->extract();
    }

    /**
     * tinySummary
     */
    public function tinySummary(): array
    {
        $tiny_summary = [];

        $articles = $this->getCartSession()->getArticles();
        if (count($articles) > 0) {
            $tiny_summary = $this->pomm_session
                ->getModel(ArticleSessionSelectionModel::class)
                ->summary($articles->extract(), $this->locale)
                ->extract();
            if ($tiny_summary !== null) {
                $tiny_summary['has_quote'] = false;
            }
        }

        return $tiny_summary ?? [];
    }

    /**
     * getTotalAmount
     */
    public function getTotalAmount(): float
    {
        return (float) ($this->summary()['amount_selling_price'] ?? 0);
    }

    /**
     * getPromoOfferId
     *
     * @return int|null
     */
    public function getPromoOfferId()
    {
        return $this->getShoppingCartSessionPromoPromoOfferId();
    }

    /**
     * setPromoOfferId
     *
     * @param int|null $promo_offer_id
     */
    public function setPromoOfferId($promo_offer_id): bool
    {
        $this->updateShoppingCartSessionPromoOfferId($promo_offer_id);

        return true;
    }

    /**
     * getWarranties
     */
    public function getWarranties(BasketableInterface $basketable): array
    {
        return $this->getCartSession()
            ->getBasketableCollection($basketable->getBasketableName())
            ->getBasketable($basketable->getId())
            ->getWarranties();
    }

    /**
     * addWarranty
     */
    public function addWarranty(
        BasketableInterface $basketable,
        ArticleWarrantyExtension $article_warranty
    ): ShoppingCartRepositoryInterface {
        $cart_session = $this->getCartSession();
        $cart_session
            ->getBasketableCollection($basketable->getBasketableName())
            ->getBasketable($basketable->getId())
            ->addWarranty($article_warranty);

        $this->updateCartSession($cart_session);

        return $this;
    }

    /**
     * addWarranty
     */
    public function removeWarranty(
        BasketableInterface $basketable,
        string $warranty_tag_path
    ): ShoppingCartRepositoryInterface {
        $cart_session = $this->getCartSession();
        $cart_session
            ->getBasketableCollection($basketable->getBasketableName())
            ->getBasketable($basketable->getId())
            ->removeWarranty($warranty_tag_path);

        $this->updateCartSession($cart_session);

        return $this;
    }

    /**
     * setCustomerPromoCode
     *
     * @throws \RuntimeException
     * @return never
     */
    public function setCustomerPromoCode(string $code, int $promo_code_customer_id)
    {
        throw new \RuntimeException('Session shopping cart can not add customer promo code.');
    }

    /**
     * removeCustomerPromoCode
     */
    public function removeCustomerPromoCode(): bool
    {
        return false;
    }

    /**
     * getCustomerPromoCode
     */
    public function getCustomerPromoCode(): array
    {
        return [null, null];
    }

    /**
     * getCustomerId
     */
    public function getCustomerId()
    {
        return null;
    }
}

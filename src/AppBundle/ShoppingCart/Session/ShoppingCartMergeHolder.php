<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Session;

use PommProject\ModelManager\Session as PommSession;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketLine;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketLineModel;
use Symfony\Component\HttpFoundation\Session\SessionInterface;

/**
 * Class ShoppingCartMergeHolder
 *
 * This class holds the shopping cart's products which are merged into logged shopping cart
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Session
 * <AUTHOR> <PERSON> <<EMAIL>>
 */
class ShoppingCartMergeHolder
{
    public const SESSION_KEY = 'previous_shopping_cart_items';

    private PommSession $pomm_session;

    private SessionInterface $session;

    /**
     * ShoppingCartMergeHolder constructor.
     */
    public function __construct(PommSession $pomm_session, SessionInterface $session)
    {
        $this->pomm_session = $pomm_session;
        $this->session = $session;
    }

    /**
     * holdBasketItemIds
     */
    public function holdBasketItemIds(int $customer_id): int
    {
        $item_ids = $this->session->has(static::SESSION_KEY) ? $this->session->get(static::SESSION_KEY) ?? [] : [];

        $basket_lines = $this->pomm_session
            ->getModel(BasketLineModel::class)
            ->findWhere('customer_id = $*', [$customer_id]);

        /** @var BasketLine $basket_line */
        foreach ($basket_lines as $basket_line) {
            $type = $basket_line->get('content')->get('type');
            if (strpos($type, '.') !== false) {
                $type = substr($type, strpos($type, '.') + 1);
            }

            $item_ids[] = sprintf('%s:%s', $type, $basket_line->get('content')->get('id'));
        }

        $this->session->set(static::SESSION_KEY, $item_ids);

        return is_countable($item_ids) ? count($item_ids) : 0;
    }

    /**
     * retrieveItemIds
     *
     * Return a list of item ids. Id formatting is : "article:10", "accessory:10", "quotation:10", ...
     */
    public function retrieveItemIds(): array
    {
        return $this->session->has(static::SESSION_KEY) ? $this->session->get(static::SESSION_KEY) ?? [] : [];
    }

    /**
     * clean
     */
    public function clean(): self
    {
        $this->session->remove(static::SESSION_KEY);

        return $this;
    }
}

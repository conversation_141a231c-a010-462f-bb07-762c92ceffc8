<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart;

use PommProject\ModelManager\Model\FlexibleEntity;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\ArticleSessionSelection;

/**
 * Class ShoppingCartSummary
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart
 * <AUTHOR> <<EMAIL>>
 */
class ShoppingCartSummary
{
    /**
     * @var float
     */
    private $nb_articles = 0;

    /**
     * @var array
     */
    private $article_ids = [];

    private array $article_ids_without_destock = [];

    /**
     * @var array
     */
    private $accessory_ids = [];

    /**
     * @var float
     */
    private $amount_selling_price = 0;

    /**
     * @var float
     */
    private $amount_selling_price_for_promo_code = 0;

    /**
     * @var float
     */
    private $amount_ecotaxe = 0;

    /**
     * @var float
     */
    private $weight_g = 0;

    /**
     * @var null|int
     */
    private $customer_id;

    /**
     * @var null|int
     */
    private $promo_code_id;

    /**
     * @var array
     */
    private $details = [];

    /**
     * ShoppingCartSummary constructor.
     */
    public function __construct(FlexibleEntity $entity)
    {
        $this->checkInputEntity($entity);

        $fields = [
            'nb_articles',
            'amount_selling_price',
            'weight_g',
            'customer_id',
            'amount_selling_price_for_promo_code',
        ];
        foreach ($fields as $field) {
            $this->$field = $entity->get($field);
        }

        $this->amount_ecotaxe = $entity->get('amount_ecotaxe') ?? 0;

        if ($entity->has('article_ids')) {
            $this->article_ids = $entity->get('article_ids');
        }

        if ($entity->has('article_ids_without_destock') && is_array($entity->get('article_ids_without_destock'))) {
            $this->article_ids_without_destock = $entity->get('article_ids_without_destock');
        }

        if ($entity->has('accessory_ids')) {
            $this->accessory_ids = $entity->get('accessory_ids');
        }

        if ($entity->has('details')) {
            $this->details = $entity->get('details');
        }
    }

    /**
     * checkInputEntity
     */
    protected function checkInputEntity(FlexibleEntity $entity): self
    {
        if (
            (!$entity->has('article_ids') || !$entity->has('article_ids_without_destock')) &&
            !$entity->has('accessory_ids')
        ) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Entity must have \'article_ids\' or \'accessory_ids\', have %s.',
                    implode(', ', array_keys($entity->extract())),
                ),
            );
        }

        foreach (['nb_articles', 'amount_selling_price', 'weight_g', 'amount_selling_price_for_promo_code'] as $field) {
            $this->mustBeNumeric($entity->get($field), $field);
        }

        $this->mustBeNumeric($entity->get('amount_ecotaxe'), 'amount_ecotaxe', true);

        foreach (['article_ids', 'accessory_ids', 'article_ids_without_destock', 'details'] as $field) {
            if ($entity->has($field)) {
                $this->mustBeArray($entity->get($field), $field);
            }
        }

        return $this;
    }

    /**
     * createEmptySummary
     */
    public static function createEmptySummary($customer_id = null): ShoppingCartSummary
    {
        return new ShoppingCartSummary(
            new ArticleSessionSelection([
                'nb_articles' => 0,
                'article_ids' => [],
                'article_ids_without_destock' => [],
                'accessory_ids' => [],
                'amount_selling_price' => 0,
                'amount_selling_price_for_promo_code' => 0,
                'amount_ecotaxe' => 0,
                'weight_g' => 0,
                'customer_id' => $customer_id,
                'details' => [],
            ]),
        );
    }

    public function getNbArticles(): int
    {
        return $this->nb_articles;
    }

    public function getArticleIds(): array
    {
        return $this->article_ids;
    }

    public function getAccessoryIds(): array
    {
        return $this->accessory_ids;
    }

    public function getAmountSellingPrice(): float
    {
        return $this->amount_selling_price;
    }

    public function getAmountEcotaxe(): float
    {
        return $this->amount_ecotaxe;
    }

    public function getWeightG(): int
    {
        return $this->weight_g;
    }

    /**
     * @return int|null
     */
    public function getCustomerId()
    {
        return $this->customer_id;
    }

    /**
     * @return int|null
     */
    public function getPromoCodeId()
    {
        return $this->promo_code_id;
    }

    /**
     * setPromoCodeId
     *
     * @param int|null $promo_code_id
     */
    public function setPromoCodeId($promo_code_id): self
    {
        $this->promo_code_id = $promo_code_id;

        return $this;
    }

    public function getArticleIdsWithoutDestock(): array
    {
        return $this->article_ids_without_destock;
    }

    public function getAmountSellingPriceForPromoCode(): float
    {
        return $this->amount_selling_price_for_promo_code;
    }

    public function getDetails(): array
    {
        return $this->details;
    }

    /**
     * isEmpty
     */
    public function isEmpty(): bool
    {
        return $this->nb_articles === 0;
    }

    /**
     * extract
     * @return array{
     *     nb_articles: float,
     *     article_ids: mixed[],
     *     article_ids_without_destock: mixed[],
     *     accessory_ids: mixed[],
     *     amount_selling_price: float,
     *     amount_selling_price_for_promo_code: float,
     *     amount_ecotaxe: float,
     *      weight_g: float,
     *     customer_id: int|null,
     *     promo_code_id: int|null,
     *     details: mixed[]
     * }
     */
    public function extract(): array
    {
        return [
            'nb_articles' => $this->nb_articles,
            'article_ids' => $this->article_ids,
            'article_ids_without_destock' => $this->article_ids_without_destock,
            'accessory_ids' => $this->accessory_ids,
            'amount_selling_price' => $this->amount_selling_price,
            'amount_selling_price_for_promo_code' => $this->amount_selling_price_for_promo_code,
            'amount_ecotaxe' => $this->amount_ecotaxe,
            'weight_g' => $this->weight_g,
            'customer_id' => $this->customer_id,
            'promo_code_id' => $this->promo_code_id,
            'details' => $this->details,
        ];
    }

    /**
     * merge
     */
    public function merge(ShoppingCartSummary $shopping_cart_summary): self
    {
        $this->nb_articles += $shopping_cart_summary->getNbArticles();
        $this->article_ids = array_values(
            array_unique(array_merge($this->article_ids, $shopping_cart_summary->getArticleIds())),
        );
        $this->article_ids_without_destock = array_values(
            array_unique(
                array_merge($this->article_ids_without_destock, $shopping_cart_summary->getArticleIdsWithoutDestock()),
            ),
        );
        $this->accessory_ids = array_values(
            array_unique(array_merge($this->accessory_ids, $shopping_cart_summary->getAccessoryIds())),
        );
        $this->amount_selling_price += $shopping_cart_summary->getAmountSellingPrice();
        $this->amount_selling_price_for_promo_code += $shopping_cart_summary->getAmountSellingPriceForPromoCode();
        $this->amount_ecotaxe += $shopping_cart_summary->getAmountEcotaxe();
        $this->weight_g += $shopping_cart_summary->getWeightG();
        $this->customer_id ??= $shopping_cart_summary->getCustomerId();
        $this->promo_code_id ??= $shopping_cart_summary->getPromoCodeId();
        $this->details = array_values(array_merge($this->details, $shopping_cart_summary->getDetails()));

        return $this;
    }

    /**
     * mustBeNumeric
     *
     * @param        $value
     */
    private function mustBeNumeric($value, string $key, bool $nullable = false): void
    {
        if (!is_numeric($value) && (!$nullable || $value !== null)) {
            throw new \InvalidArgumentException(
                sprintf('Key \'%s\' must be numeric, have %s.', $key, json_encode($value, JSON_THROW_ON_ERROR)),
            );
        }
    }

    /**
     * mustBeArray
     *
     * @param        $value
     */
    private function mustBeArray($value, string $key): void
    {
        if (!is_array($value)) {
            throw new \InvalidArgumentException(sprintf('Key \'%s\' must be an array.', $key));
        }
    }
}

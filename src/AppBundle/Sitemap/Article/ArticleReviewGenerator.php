<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Sitemap\Article;

use PommProject\Foundation\Pomm;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Sitemap\BaseUrlGenerator;
use SonVideo\Cms\FrontOffice\AppBundle\Sitemap\UrlGeneratorInterface;

class ArticleReviewGenerator extends BaseUrlGenerator implements UrlGeneratorInterface
{
    public const ARTICLE_REVIEW_SITEMAP_FILENAME = 'sitemap_article_review';
    protected Pomm $pomm;

    public function getFileName(): string
    {
        return self::ARTICLE_REVIEW_SITEMAP_FILENAME;
    }

    public function generate(): array
    {
        $urls = $this->pomm
            ->getDefaultSession()
            ->getModel(ArticleModel::class)
            ->getUrlHistoricReview()
            ->extract();
        $reviewUrls = [];
        foreach ($urls as $url) {
            $reviewUrls[] = $url['url'];
        }

        return $reviewUrls;
    }
}

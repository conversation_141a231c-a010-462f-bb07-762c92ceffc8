<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Sitemap\Brand;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\BrandModel;
use SonVideo\Cms\FrontOffice\AppBundle\Sitemap\BaseUrlGenerator;
use SonVideo\Cms\FrontOffice\AppBundle\Sitemap\UrlGeneratorInterface;

/**
 * Class BrandUrlGenerator
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Sitemap\Brand
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class BrandUrlGenerator extends BaseUrlGenerator implements UrlGeneratorInterface
{
    public const BRAND_SITEMAP_FILENAME = 'sitemap_brand';
    public const BRAND_VIEW_ROUTE_NAME = 'show_brand';
    public const CATEGORY_BRAND_VIEW_ROUTE_NAME = 'show_brand_category';
    public const INDEX_BRAND_VIEW_ROUTE_NAME = 'show_brands';

    /**
     * getFileName
     */
    public function getFileName(): string
    {
        return self::BRAND_SITEMAP_FILENAME;
    }

    /**
     * generate
     */
    public function generate(): array
    {
        $brands = $this->pomm
            ->getDefaultSession()
            ->getModel(BrandModel::class)
            ->getAllActiveSlug()
            ->extract();

        $urls = [$this->router->generate(self::INDEX_BRAND_VIEW_ROUTE_NAME)];
        foreach ($brands as $brand) {
            $urls[] = $this->router->generate(self::BRAND_VIEW_ROUTE_NAME, ['slug' => $brand['slug']]);
        }

        return $urls;
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Sitemap\Guide;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\FamilyModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\GuideModel;
use SonVideo\Cms\FrontOffice\AppBundle\Sitemap\BaseUrlGenerator;
use SonVideo\Cms\FrontOffice\AppBundle\Sitemap\UrlGeneratorInterface;

/**
 * Class GuideUrlGenerator
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Sitemap\Guide
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class GuideUrlGenerator extends BaseUrlGenerator implements UrlGeneratorInterface
{
    public const SITEMAP_FILENAME = 'sitemap_guide';
    public const GUIDE_VIEW_ROUTE_NAME = 'show_guide';
    public const FAMILY_GUIDE_VIEW_ROUTE_NAME = 'show_guides_family';
    public const INDEX_GUIDES_VIEW_ROUTE_NAME = 'show_guides_families';

    /**
     * getFileName
     */
    public function getFileName(): string
    {
        return self::SITEMAP_FILENAME;
    }

    /**
     * generate
     */
    public function generate(): array
    {
        return array_merge(
            [[
                'url' => $this->router->generate(self::INDEX_GUIDES_VIEW_ROUTE_NAME),
                'image_url' => '',
                'lastmod' => ''
            ]],
            $this->generateFamilyGuideUrls(),
            $this->generateGuideUrls(),
        );
    }

    /**
     * generateFamilyGuideUrls
     */
    protected function generateFamilyGuideUrls(): array
    {
        $families = $this->pomm
            ->getDefaultSession()
            ->getModel(FamilyModel::class)
            ->findAllOrderedWithGuides(APP_DEFAULT_LOCALE);

        $urls = [];
        foreach ($families as $family) {
            $urls[] = [
                'url' => $this->router->generate(self::FAMILY_GUIDE_VIEW_ROUTE_NAME, ['slug' => $family->get('slug')]),
                'image_url' => '',
                'lastmod' => ''
            ];
        }

        return $urls;
    }

    /**
     * generateGuideUrls
     */
    protected function generateGuideUrls(): array
    {
        $urls = $this->pomm
            ->getDefaultSession()
            ->getModel(GuideModel::class)
            ->getAllSlugWithLastmod();

        foreach ($urls as $index => $url) {
            $urls[$index]['url'] = $this->router->generate(self::GUIDE_VIEW_ROUTE_NAME, ['slug' => $url['url']]);
        }
        
        return $urls;
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Sitemap\StaticPage;

use SonVideo\Cms\FrontOffice\AppBundle\Routing\StaticPageRouter;
use SonVideo\Cms\FrontOffice\AppBundle\Sitemap\UrlGeneratorInterface;

/**
 * Class StaticPageUrlGenerator
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Sitemap\StaticPage
 * <AUTHOR> <<EMAIL>>
 */
class StaticPageUrlGenerator implements UrlGeneratorInterface
{
    public const STATIC_PAGE_SITEMAP_FILENAME = 'sitemap_static_page';

    private StaticPageRouter $router;

    /**
     * StandUrlGenerator constructor.
     */
    public function __construct(StaticPageRouter $router)
    {
        $this->router = $router;
    }

    /**
     * getFileName
     */
    public function getFileName(): string
    {
        return self::STATIC_PAGE_SITEMAP_FILENAME;
    }

    /**
     * generate
     */
    public function generate(): array
    {
        $collection = $this->router->getRouteCollection();

        $urls = [];
        foreach ($collection->all() as $route) {
            $urls[] = $route->getPath();
        }

        sort($urls);

        return $urls;
    }
}

import { Canvas, Controls } from '@storybook/blocks';
import * as ButtonStories from '../Resources/js/v5/components/button/SvdButton.stories'
import {StyledContainer, StyledGlobal, StyledContentsTable} from './styled'

# Button

## Table des matières
<StyledGlobal>
    <StyledContentsTable>
        * Primary
            * [Primary](#chapter_button_primary)
            * [Primary Disabled](#chapter_button_primary_disabled)
            * [Primary Outline](#chapter_button_primary_outline)
            * [Primary Outline Disabled](#chapter_button_primary_outline_disabled)
            * [Primary Link](#chapter_button_primary_link)
            * [Primary Link Disabled](#chapter_button_primary_link_disabled)
    </StyledContentsTable>
    <StyledContentsTable>
        * Secondary
            * [Secondary](#chapter_button_secondary)
            * [Secondary Disabled](#chapter_button_secondary_disabled)
            * [Secondary Outline](#chapter_button_secondary_outline)
            * [Secondary Outline Disabled](#chapter_button_secondary_outline_disabled)
            * [Secondary Link](#chapter_button_secondary_link)
            * [Secondary Link Disabled](#chapter_button_secondary_link_disabled)
    </StyledContentsTable>
    <StyledContentsTable>
        * Light
            * [Light](#chapter_button_light)
            * [Light Disabled](#chapter_button_light_disabled)
            * [Light Outline](#chapter_button_light_outline)
            * [Light Outline Disabled](#chapter_button_light_outline_disabled)
            * [Light Link](#chapter_button_light_link)
            * [Light Link Disabled](#chapter_button_light_link_disabled)
    </StyledContentsTable>
    <StyledContentsTable>
        * White
            * [White](#chapter_button_white)
            * [White Disabled](#chapter_button_white_disabled)
            * [White Outline](#chapter_button_white_outline)
            * [White Outline Disabled](#chapter_button_white_outline_disabled)
            * [White Link](#chapter_button_white_link)
            * [White Link Disabled](#chapter_button_white_link_disabled)
    </StyledContentsTable>
    <StyledContentsTable>
        * [Sandbox](#bac_a_sable)
    </StyledContentsTable>
</StyledGlobal>

## Propriétés
<StyledGlobal>
    <StyledContainer>
            ### Primary <a className="anchor" id="chapter_button_primary"></a>
            <Canvas
                of={ButtonStories.Primary}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Primary Disabled <a className="anchor" id="chapter_button_primary_disabled"></a>
            <Canvas
                of={ButtonStories.PrimaryDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Primary Outline <a className="anchor" id="chapter_button_primary_outline"></a>
            <Canvas
                of={ButtonStories.PrimaryOutline}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Primary Outline Disabled<a className="anchor" id="chapter_button_primary_outline_disabled"></a>
            <Canvas
                of={ButtonStories.PrimaryOutlineDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Primary Link<a className="anchor" id="chapter_button_primary_link"></a>
            <Canvas
                of={ButtonStories.PrimaryLink}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Primary Link Disabled<a className="anchor" id="chapter_button_primary_link_disabled"></a>
            <Canvas
                of={ButtonStories.PrimaryLinkDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>


    <StyledContainer>
            ### Secondary <a className="anchor" id="chapter_button_secondary"></a>
            <Canvas
                of={ButtonStories.Secondary}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Secondary Disabled <a className="anchor" id="chapter_button_secondary_disabled"></a>
            <Canvas
                of={ButtonStories.SecondaryDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Secondary Outline <a className="anchor" id="chapter_button_secondary_outline"></a>
            <Canvas
                of={ButtonStories.SecondaryOutline}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Secondary Outline Disabled<a className="anchor" id="chapter_button_secondary_outline_disabled"></a>
            <Canvas
                of={ButtonStories.SecondaryOutlineDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Secondary Link<a className="anchor" id="chapter_button_secondary_link"></a>
            <Canvas
                of={ButtonStories.SecondaryLink}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Secondary Link Disabled<a className="anchor" id="chapter_button_secondary_link_disabled"></a>
            <Canvas
                of={ButtonStories.SecondaryLinkDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>


    <StyledContainer>
            ### Light <a className="anchor" id="chapter_button_light"></a>
            <Canvas
                of={ButtonStories.Light}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Light Disabled <a className="anchor" id="chapter_button_light_disabled"></a>
            <Canvas
                of={ButtonStories.LightDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Light Outline <a className="anchor" id="chapter_button_light_outline"></a>
            <Canvas
                of={ButtonStories.LightOutline}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Light Outline Disabled<a className="anchor" id="chapter_button_light_outline_disabled"></a>
            <Canvas
                of={ButtonStories.LightOutlineDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Light Link<a className="anchor" id="chapter_button_light_link"></a>
            <Canvas
                of={ButtonStories.LightLink}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### Light Link Disabled<a className="anchor" id="chapter_button_light_link_disabled"></a>
            <Canvas
                of={ButtonStories.LightLinkDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>


    <StyledContainer>
            ### White <a className="anchor" id="chapter_button_white"></a>
            <Canvas
                of={ButtonStories.White}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### White Disabled <a className="anchor" id="chapter_button_white_disabled"></a>
            <Canvas
                of={ButtonStories.WhiteDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### White Outline <a className="anchor" id="chapter_button_white_outline"></a>
            <Canvas
                of={ButtonStories.WhiteOutline}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### White Outline Disabled<a className="anchor" id="chapter_button_white_outline_disabled"></a>
            <Canvas
                of={ButtonStories.WhiteOutlineDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### White Link<a className="anchor" id="chapter_button_white_link"></a>
            <Canvas
                of={ButtonStories.WhiteLink}
                sourceState={'shown'}
            />
    </StyledContainer>
    <StyledContainer>
            ### White Link Disabled<a className="anchor" id="chapter_button_white_link_disabled"></a>
            <Canvas
                of={ButtonStories.WhiteLinkDisabled}
                sourceState={'shown'}
            />
    </StyledContainer>
</StyledGlobal>

## Sandbox<a className="anchor" id="bac_a_sable"></a>
<Canvas
    of={ButtonStories.Sandbox}
    sourceState={'shown'}
/>
<Controls
    of={ButtonStories.Sandbox}
/>
import { Canvas, Controls } from '@storybook/blocks';
import * as LinkStories from '../Resources/js/v5/components/link/SvdLink.stories'
import {StyledContainer, StyledGlobal, StyledContentsTable} from './styled'

# Lien

## Table des matières
<StyledGlobal>
    <StyledContentsTable>
        * [Basic](#chapter_link_basic)
        * [Small](#chapter_link_small)
        * [Sandbox](#bac_a_sable)
    </StyledContentsTable>
</StyledGlobal>

## Propriétés
<StyledGlobal>
    <StyledContainer>
        ### Basic <a className="anchor" id="chapter_link_basic"></a>
        <Canvas
            of={LinkStories.Default}
            sourceState={'shown'}
        />
    </StyledContainer>
    <StyledContainer>
        ### Small <a className="anchor" id="chapter_link_small"></a>
        <Canvas
            of={LinkStories.Small}
            sourceState={'shown'}
        />
    </StyledContainer>
</StyledGlobal>
## Link sandbox<a className="anchor" id="link_bac_a_sable"></a>
<Canvas
    of={LinkStories.Sandbox}
    sourceState={'shown'}
/>
<Controls
    of={LinkStories.Sandbox}
/>

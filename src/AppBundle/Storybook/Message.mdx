import { Canvas, Controls } from '@storybook/blocks';
import * as MessageStories from '../Resources/js/v5/components/message/SvdMessage.stories'
import {StyledContainer, StyledGlobal, StyledContentsTable} from './styled'

# Message

## Table des matières
<StyledGlobal>
    <StyledContentsTable>
        * [Info](#chapter_message_info)
        * [Error](#chapter_message_error)
        * [Success](#chapter_message_success)
        * [Sandbox](#sandbox_message_success)
    </StyledContentsTable>
</StyledGlobal>

## Propriétés
<StyledGlobal>
    <StyledContainer>
        ### Info <a className="anchor" id="chapter_message_info"></a>
        <Canvas
            of={MessageStories.Info}
            sourceState={'shown'}
        />
    </StyledContainer>
    <StyledContainer>
        ### Error <a className="anchor" id="chapter_message_error"></a>
        <Canvas
            of={MessageStories.Error}
            sourceState={'shown'}
        />
    </StyledContainer>
    <StyledContainer>
        ### Success <a className="anchor" id="chapter_message_success"></a>
        <Canvas
            of={MessageStories.Success}
            sourceState={'shown'}
        />
    </StyledContainer>
</StyledGlobal>
## Sandbox<a className="anchor" id="sandbox_message_success"></a>
<Canvas
    of={MessageStories.Sandbox}
    sourceState={'shown'}
/>
<Controls
    of={MessageStories.Sandbox}
/>

<?php
/*
 * This file is part of CMS Front Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context;

use Behat\Mink\Element\NodeElement;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\TableNode;
use Behat\Mink\Exception\ElementNotFoundException;
use Behat\MinkExtension\Context\RawMinkContext;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context\Dictionary\CommonDictionaryTrait;

/**
 * FormInteractionContext
 *
 * Specific context used to interact with form.
 *
 * @package     SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context
 * @copyright   2017 Son Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
class FormInteractionContext extends RawMinkContext implements Context
{
    use CommonDictionaryTrait;

    /**
     * fillFormField
     *
     * @throws  ElementNotFoundException
     * @When /^(?:I )?fill in the "(?P<label>(?:[^"]|\\")*)" field with "(?P<value>(?:[^"]|\\")*)"$/
     * @When /^(?:I )?(?:put|select) "(?P<value>(?:[^"]|\\")*)" (?:for|in) "(?P<label>(?:[^"]|\\")*)"$/
     */
    public function fillFormField(string $label, string $value): void
    {
        $label = $this->fixStepArgument($label);
        $found = $this->findLabelInCssElement('label', $label);
        $field = $this->findParentByTagName($found, 'div')->find('css', 'input,select,textarea');

        if (is_null($field)) {
            throw new ElementNotFoundException(
                $this->getSession(),
                sprintf('The field attached to the label %s was not found', $label),
            );
        }

        if ($field->hasClass('editor_advanced')) {
            // tinyMCE field
            $this->getSession()->evaluateScript(
                sprintf("tinyMCE.get('%s').setContent('%s');", $field->getAttribute('id'), addcslashes($value, "'")),
            );

            return;
        }

        $field->getTagName() === 'select' ? $field->selectOption($value) : $field->setValue($value);
    }

    /**
     * Fills in form fields with provided table
     * Example: When I fill in the form fields with following"
     *              | username | bruceWayne |
     *              | password | iLoveBats123 |
     * Example: And I fill in the form fields with following"
     *              | username | bruceWayne |
     *              | password | iLoveBats123 |
     *
     * @throws  ElementNotFoundException
     * @When /^(?:|I )fill in the form fields with following:$/
     */
    public function fillFormFields(TableNode $fields): void
    {
        foreach ($fields->getRowsHash() as $label => $value) {
            $this->fillFormField($label, $value);
        }
    }

    /**
     * submitFormWithoutButton
     *
     * Example : I submit form ".edit-menu"
     *
     * @throws  ElementNotFoundException
     * @Given /^(?:I )?submit form "(?P<class>[^"]*)"$/
     */
    public function submitFormWithoutButton(string $class): void
    {
        $form = $this->getSession()
            ->getPage()
            ->find('css', "form$class");
        if (!$form instanceof NodeElement) {
            throw new ElementNotFoundException($this->getSession(), 'form ' . $class);
        }

        $form->submit();
    }

    /**
     * pressButtonInNthFormBlocks
     *
     *
     * @throws ElementNotFoundException
     * @Given /^(?:|I )press the "(?P<button>(?:[^"]|\\")*)" button$/
     */
    public function pressButton(string $button): void
    {
        $button = $this->getSession()
            ->getPage()
            ->find('css', sprintf('.btn:contains(%s)', $button));

        if (is_null($button)) {
            throw new ElementNotFoundException(
                $this->getSession(),
                sprintf('No button "%s" was found  in the current scope', $button),
            );
        }

        $this->tryWithScroll(
            function ($button): void {
                $button->press();
            },
            [$button],
        );
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context;

use Behat\Behat\Context\Context;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Behat\Mink\Element\NodeElement;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context\Dictionary\CommonDictionaryTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context\Dictionary\SpinCapableTrait;

/**
 * Class InspectElementContext
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Bootstrap
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class InspectElementContext implements Context
{
    use CommonDictionaryTrait;
    use SpinCapableTrait;

    private string $inspected_element = '';
    private ?NodeElement $inspected_element_node = null;

    /**
     * @BeforeScenario
     */
    public function gatherContexts(BeforeScenarioScope $scope): void
    {
        $environment = $scope->getEnvironment();
        $this->feature_context = $environment->getContext(FeatureContext::class);
    }

    /**
     * inspectElement
     *
     * @Given /^(?:|I )inspect element "(?P<selector>[^"]*)"$/
     */
    public function inspectElement(string $selector): void
    {
        $this->spins(function () use ($selector): void {
            $this->inspected_element_node = $this->feature_context->assertSession()->elementExists('css', $selector);
        });

        $this->inspected_element = $selector;
    }

    /**
     * Checks, that inspected element contains specified text
     * Example: Then I should see "Who is the Batman?" in inspected element
     * Example: And I should see "Who is the Batman?" in inspected element
     *
     *
     * @Then /^(?:|I )should see "(?P<text>(?:[^"]|\\")*)" in inspected element$/
     */
    public function assertElementContainsText(string $text): void
    {
        $this->spins(function () use ($text): void {
            $this->feature_context->assertElementContainsText($this->inspected_element, $text);
        });
    }

    /**
     * Checks that inspected element not contains specified text
     * Example: Then I should not see "Who is the Batman?" in inspected element
     * Example: And I should not see "Who is the Batman?" in inspected element
     *
     *
     * @Then /^(?:|I )should not see "(?P<text>(?:[^"]|\\")*)" in inspected element$/
     */
    public function assertElementNotContainsText(string $text): void
    {
        $this->spins(function () use ($text): void {
            $this->feature_context->assertElementNotContainsText($this->inspected_element, $text);
        });
    }

    /**
     * Checks, that inspected element contains specified text
     * Replace any space characters by a simple space.
     *
     *
     * @Then /^(?:|I )should see "(?P<text>(?:[^"]|\\")*)" in inspected element with simple space$/
     */
    public function assertElementContainsTextWithConvertedSpace(string $text): void
    {
        $this->spins(function () use ($text): void {
            $this->feature_context->assertSimpleSpaceElementContainsText($this->inspected_element, $text);
        });
    }

    /**
     * Checks, that (?P<num>\d+) CSS elements exist on the page
     * Example: Then I should see 5 "div" elements in inspected element
     * Example: And I should see 5 "div" elements in inspected element
     *
     * @param int    $num
     * @param string $element
     *
     * @Then /^(?:|I )should see (?P<num>\d+) "(?P<element>[^"]*)" elements? in inspected element?$/
     */
    public function assertAmountElements($num, $element): void
    {
        $this->spins(function () use ($num, $element): void {
            $this->feature_context
                ->assertSession()
                ->elementsCount('css', $this->inspected_element . ' ' . $element, $num);
        });
    }

    /**
     * Checks, that element with specified CSS contains specified text
     * Example: Then I should see "Batman" in the "heroes_list" element of inspected element
     * Example: And I should see "Batman" in the "heroes_list" element of inspected element
     *
     *
     * @Then /^(?:|I )should see "(?P<text>(?:[^"]|\\")*)" in the "(?P<element>[^"]*)" element of inspected element$/
     */
    public function assertOtherElementContainsText(string $element, string $text): void
    {
        $this->feature_context->assertElementContainsText($this->inspected_element . ' ' . $element, $text);
    }

    /**
     * Checks, that element with specified CSS contains specified text
     * Example: Then I should see "Batman" in the "heroes_list" element of inspected element
     * Example: And I should see "Batman" in the "heroes_list" element of inspected element
     *
     *
     * @Then /^(?:|I )should see "(?P<text>(?:[^"]|\\")*)" in the simple space "(?P<element>[^"]*)" element of inspected element$/
     */
    public function assertOthersSimpleSpaceElementContainsText(string $element, string $text): void
    {
        $this->feature_context->assertSimpleSpaceElementContainsText($this->inspected_element . ' ' . $element, $text);
    }

    /**
     * Spin version of assertOtherElementContainsText
     *
     *
     * @Then /^(?:|I )should see "(?P<text>(?:[^"]|\\")*)" in the "(?P<element>[^"]*)" element of inspected element waiting js$/
     */
    public function spinAssertOtherElementContainsText(string $element, string $text): void
    {
        $this->spins(function () use ($element, $text): void {
            $this->assertOtherElementContainsText($element, $text);
        });
    }

    /**
     * Checks, that element with specified CSS exists on page
     * Example: Then I should see a "body" element in inspected element
     * Example: And I should see a "body" element in inspected element
     *
     *
     * @Then /^(?:|I )should see an? "(?P<element>[^"]*)" element in inspected element$/
     */
    public function assertElementOnPage(string $element): void
    {
        $this->spins(function () use ($element): void {
            $this->feature_context->assertElementOnPage($this->inspected_element . ' ' . $element);
        });
    }

    /**
     * Checks, that element with specified CSS does not exist on page
     * Example: Then I should not see a "canvas" element in inspected element
     * Example: And I should not see a "canvas" element in inspected element
     *
     *
     * @Then /^(?:|I )should not see an? "(?P<element>[^"]*)" element in inspected element$/
     */
    public function assertElementNotOnPage(string $element): void
    {
        $this->feature_context->assertElementNotOnPage($this->inspected_element . ' ' . $element);
    }

    /**
     * Checks, that form field with specified id|name|label|value in inspected element has specified value
     * Example: Then the "username" field in inspected element should contain "bwayne"
     * Example: And the "username" field in inspected element should contain "bwayne"
     *
     *
     * @Then /^the "(?P<field>(?:[^"]|\\")*)" field in inspected element should contain "(?P<value>(?:[^"]|\\")*)"$/
     */
    public function assertFieldContains(string $field, string $value): void
    {
        $field = $this->fixStepArgument($field);
        $value = $this->fixStepArgument($value);
        $this->feature_context->assertSession()->fieldValueEquals($field, $value, $this->inspected_element_node);
    }

    /**
     * Fills in form field with specified id|name|label|value in inspected element
     * Example: When I fill in "username" in inspected element with: "bwayne"
     * Example: And I fill in "bwayne" for "username" in inspected element
     *
     *
     * @When /^(?:|I )fill in "(?P<field>(?:[^"]|\\")*)" in inspected element with "(?P<value>(?:[^"]|\\")*)"$/
     * @When /^(?:|I )fill in "(?P<field>(?:[^"]|\\")*)" in inspected element with:$/
     * @When /^(?:|I )fill in "(?P<value>(?:[^"]|\\")*)" for "(?P<field>(?:[^"]|\\")*)" in inspected element$/
     */
    public function fillField(string $field, string $value): void
    {
        $field = $this->fixStepArgument($field);
        $value = $this->fixStepArgument($value);
        $this->inspected_element_node->fillField($field, $value);
    }

    /**
     * theSourceOfImageShouldContainFromPackage
     *
     *
     * @Then /^the source of image "(?P<locator>(?:[^"]|\\")*)" in inspected element should contain "(?P<value>[^"]*)" from package "(?P<package>[^"]*)"$/
     */
    public function theSourceOfImageShouldContainFromPackage(string $locator, string $value, string $package): void
    {
        $this->feature_context->theSourceOfImageShouldContainFromPackage(
            $this->inspected_element . ' ' . $locator,
            $value,
            $package,
        );
    }
}

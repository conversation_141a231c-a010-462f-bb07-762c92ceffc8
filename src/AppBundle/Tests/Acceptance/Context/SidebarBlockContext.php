<?php
/*
 * This file is part of CMS Front Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context;

use Behat\Mink\Element\NodeElement;
use Behat\Behat\Context\Context;
use Behat\Mink\Exception\ElementNotFoundException;
use Behat\Mink\Exception\ExpectationException;
use Behat\MinkExtension\Context\RawMinkContext;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context\Dictionary\CommonDictionaryTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Context\Dictionary\SpinCapableTrait;

/**
 * Class SidebarBlockContext
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @package Acceptance\Bootstrap
 */
class SidebarBlockContext extends RawMinkContext implements Context
{
    use CommonDictionaryTrait;
    use SpinCapableTrait;

    /**
     * Example: I should see a "h2" element which contains "Produits associés" in the sidebar
     *
     *
     * @Then /^I should see a "(?P<element>[^"]*)" element which contains "(?P<value>[^"]*)" in the sidebar$/
     *
     * @throws ElementNotFoundException
     */
    public function shouldSeeAnElementWhichContainsText(string $element, string $value): void
    {
        $title = $this->getSession()
            ->getPage()
            ->find('css', sprintf('%s:contains(%s)', $element, $value));
        if (!$title instanceof NodeElement) {
            throw new ElementNotFoundException(
                $this->getSession(),
                sprintf('% element containing %s', $element, $value),
            );
        }
    }

    /**
     * Example: I should see 2 complementary articles associated to the current article
     *
     * shouldSeeNumberOfComplementaryArticles
     *
     *
     * @Then /^I should see (?P<number>[^"]*) complementary articles associated to the current article$/
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     */
    public function shouldSeeNumberOfComplementaryArticles(int $number): void
    {
        $complementary_articles = $this->getSession()
            ->getPage()
            ->findAll('css', '.SVDv3_list_products.associated_products .SVDv3_produitAssocie');
        if ($complementary_articles === []) {
            throw new ElementNotFoundException($this->getSession(), 'No complementary element');
        }

        if (count($complementary_articles) !== $number) {
            throw new ExpectationException(
                sprintf('Expected %d complementary articles, got %d', $number, count($complementary_articles)),
                $this->getSession(),
            );
        }
    }

    /**
     * Example : I should see a link "Toto" in the sidebar
     *
     * shouldSeeALinkWithTextInTheSidebar
     *
     *
     * @Then /^I should see a link "(?P<text>[^"]*)" in the sidebar$/
     *
     * @throws ElementNotFoundException
     * @throws ExpectationException
     */
    public function shouldSeeALinkWithTextInTheSidebar(string $text): void
    {
        $link = $this->getSession()
            ->getPage()
            ->find('css', sprintf('a:contains(%s)', $text));
        if (!$link instanceof NodeElement) {
            throw new ElementNotFoundException($this->getSession(), sprintf('link containing %s', $text));
        }
    }

    /**
     * clickOnButtonInTheSidebar
     *
     * Example: I click on the 2nd "Ajouter au panier" button in the sidebar
     *
     *
     * @Then /^(?:I )?click on the (?P<umpteenth>\d+)(?:st|nd|rd|th)? "(?P<label>[^"]*)" button in the sidebar$/
     * @throws ElementNotFoundException
     * @throws ExpectationException
     */
    public function clickOnButtonInTheSidebar(int $umpteenth, string $label): void
    {
        $index = $umpteenth - 1;
        $selector = sprintf('.SVDv3_colonnes_colonne_gauche a:contains(%s)', $label);
        $this->spins(function () use ($index, $umpteenth, $selector, $label): void {
            $links = $this->getSession()
                ->getPage()
                ->findAll('css', $selector);

            if (count($links) == 0) {
                throw new ElementNotFoundException($this->getSession(), sprintf('links containing %s', $label));
            }

            if (!isset($links[$index])) {
                throw new ElementNotFoundException(
                    $this->getSession(),
                    sprintf('%d link containing %s', $umpteenth, $label),
                );
            }

            $this->scrollIntoView($selector, $index);

            $links[$index]->press();
        });
    }
}

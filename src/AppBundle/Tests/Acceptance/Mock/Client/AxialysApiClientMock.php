<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Mock\Client;

use SonVideo\Cms\FrontOffice\AppBundle\Client\AxialysApiClientInterface;

/**
 * Class AxialysApiClientMock
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Mock\Client
 */
class AxialysApiClientMock implements AxialysApiClientInterface
{
    private array $result;

    private string $result_raw;

    /**
     * AxialysApiClientMock constructor.
     */
    public function __construct(array $result, string $result_raw)
    {
        $this->result = $result;
        $this->result_raw = $result_raw;
    }
    /**
     * setMethod
     *
     *
     */
    public function setMethod(string $method): AxialysApiClientInterface
    {
        return $this;
    }

    /**
     * addParameter
     *
     *
     */
    public function addParameter(string $key, string $value): AxialysApiClientInterface
    {
        return $this;
    }

    /**
     * addParameters
     *
     *
     */
    public function addParameters(array $values): AxialysApiClientInterface
    {
        return $this;
    }

    /**
     * getParameters
     */
    public function getParameters(): string
    {
        return 'tests';
    }

    /**
     * call
     */
    public function call(): AxialysApiClientInterface
    {
        return $this;
    }

    /**
     * getResult
     */
    public function getResult(): array
    {
        return $this->result;
    }

    /**
     * getRawResult
     */
    public function getRawResult(): string
    {
        return $this->result_raw;
    }
}

Feature: Customers can answer to questions or other answers.

    Scenario: Access to the form require customer to be authenticated
        Given I go to "/repondre-a-une-question/626b9e3f-87f5-47dc-87e4-1d4dfbe175d8"
        Then  I should be on "/mon-compte/connexion"

    Scenario: The page need to have an existing article in parameter and no cache control
        Given I am authenticated as "<EMAIL>" with password "123456"
        Given I go to "/repondre-a-une-question/626b9e3f-87f5-47dc-87e4-000000000000"
        Then  the response status code should be 404
        Given I go to "/repondre-a-une-question/626b9e3f-87f5-47dc-87e4-1d4dfbe175d8"
        Then  the response status code should be 200
        And   I should see a header "Cache-Control" with value "no-cache"
        And   I should see "Poster une réponse" in page title

    @javascript
    Scenario: Some fields are dynamic and based on current customer data
        Given I am authenticated as "<EMAIL>" with password "123456"
        And   I go to "/repondre-a-une-question/626b9e3f-87f5-47dc-87e4-1d4dfbe175d8"
        # data already known and not asked
        Then  the "Votre pseudonyme" field should contain "<PERSON>"

        When  I go to "/mon-compte/deconnexion"
        Given I am authenticated as "<EMAIL>" with password "123456"
        When  I go to "/repondre-a-une-question/626b9e3f-87f5-47dc-87e4-1d4dfbe175d8"
        # location automatically filled with main address' city, if any
        Then  the "Lieu" field should contain "Champigny-sur-marne"

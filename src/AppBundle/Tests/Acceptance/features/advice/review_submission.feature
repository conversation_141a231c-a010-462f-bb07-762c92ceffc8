Feature: Customers can post reviews and evaluation of articles and the site.

    Scenario: Access to the form require customer to be authenticated
        Given I go to "/poster-un-avis"
        Then  I should be on "/mon-compte/connexion"

    Scenario: The page need to have an existing article in parameter and no cache control
        Given I am authenticated as "<EMAIL>" with password "123456"
        And   I go to "/poster-un-avis"
        Then  the response status code should be 404
        Given I go to "/poster-un-avis?article=1"
        Then  the response status code should be 404
        Given I go to "/poster-un-avis?article=109810"
        Then  the response status code should be 200
        And   I should see a header "Cache-Control" with value "no-cache"
        And   I should see "Soumettre un nouvel avis" in page title

    @javascript
    Scenario: Some fields are dynamic and based on the article's category and/or current customer data
        Given I am authenticated as "<EMAIL>" with password "123456"
        And   I go to "/poster-un-avis?article=123465"
        # review criteria from the category
        Then  I should see "Encombrement"
        And   I should see "Qualité de son"
        And   I should see "Design"
        And   I should see "Fonctionnalités"
        # data already known and not asked
        And   I should not see "Votre pseudonyme"
        And   I should not see "Vous êtes"

        When  I go to "/mon-compte/deconnexion"
        Given I am authenticated as "<EMAIL>" with password "123456"
        When  I go to "/poster-un-avis?article=123465"
        # location automatically filled with main address' city, if any
        Then  the "Lieu" field should contain "Champigny-sur-marne"

    @javascript
    Scenario: User have a dedicated message if he has already submit a review on this product
        Given I am authenticated as "<EMAIL>" with password "123456"
        And   The next advice api call to check if user has already submit a review will return true
        When  I go to "/poster-un-avis?article=123456"
        Then  I should see "Vous avez déjà soumis un avis pour cet article et nous vous en remercions"
        And   I should not see a "form#review_submit" element
        Then  clear advice api cache

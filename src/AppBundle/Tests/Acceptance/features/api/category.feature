Feature: check the CMS front API
  In order to retrieve categories
  As a guest visitor
  I need to have an API that returns category infos

  Scenario: Get all categories for existing brand
    Given   I add "X-Requested-With" header equal to "XMLHttpRequest"
    And     I send a "GET" request to "/api/categories" with parameters:
    | key         | value |
    | brand_id    | 343   |
    Then    the response status code should be 200
    And     the JSON node "status" should be equal to the string "success"
    And     the JSON node "data" should exist
    And     the JSON node "data->categories" should exist
    And     the JSON node "data->categories" should have 2 elements
    And     the JSON node "data->categories[0]->category_id" should exist
    And     the JSON node "data->categories[0]->name" should exist
    And     the JSON node "data->categories[0]->slug" should exist

  Scenario: Get all categories for non existing brand
    Given   I add "X-Requested-With" header equal to "XMLHttpRequest"
    And     I send a "GET" request to "/api/categories" with parameters:
    | key         | value |
    | brand_id    | 0   |
    Then    the response status code should be 200
    And     the JSON node "status" should be equal to the string "success"
    And     the JSON node "data" should exist
    And     the JSON node "data->categories" should exist
    And     the JSON node "data->categories" should have 0 element

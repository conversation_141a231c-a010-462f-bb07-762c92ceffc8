Feature: check the CMS front is running with test of full article
    In order to see the good working of son-video website
    As a guest visitor
    I need to see the article details, article list group by shelf.

    Scenario: Check status code
        Given   I am on "/article/elipson-prestige-4i-calvados-fr"
        Then    the response status code should be 200

    Scenario: Check headers and metas
        Given   I am on "/article/elipson-prestige-4i-calvados-fr"
        Then    I should see a header "Cache-Control" with value "max-age=86400"
        And     I should see a header "X-Cache-Tags" with value "article:123456"
        And     I should see a header "X-Cache-Tags" with value "menu"
        And     I should see a header "X-Cache-Tags" with value "clickbait:af6e4e7a-8634-4240-b1bd-e74b67579c88"
        And     I should see a header "X-Cache-Tags" with value "clickbait:fbebd9aa-ae69-413e-ba0a-13fa53de82c7"
        And     I should see a header "X-Cache-Tags" with value "clickbait:b974be9f-0fd0-43ff-9b6e-9a16a7bf5aee"
        And     I should see a header "X-Cache-Tags" with value "article:94315"
        And     I should see a header "X-Cache-Tags" with value "article:96234"
        And     I should see a header "X-Cache-Tags" with value "article:98537"
        # with custom title
        And     I should see a page title with "Enceinte Elipson Prestige 4i super titre!"
        And     I should see a meta "description" with value "L'enceinte Elipson Prestige 4i est une grande et puissante colonne"
        # with short pattern, on unbasketable article having a substitute
        When    I am on "/article/enceintes-centrales/triangle/elara-ln03-jaune-de-damas"
        And     I should see a page title with "Triangle Elara LN03 Jaune de Damas - Enceinte colonne"
        And     I should see a meta "description" with value "introduction"
        And     I should see a header "X-Cache-Tags" with value "article:111523"
        # with complete pattern
        When    I am on "/article/enceintes-centrales/triangle/elara-ln02-blanc-laque"
        And     I should see a page title with "Triangle Elara LN02 Blanc laqué - Enceinte colonne sur Son-Vidéo.com"
        And     I should see a meta "description" with value "introduction"
        # with truncated pattern
        When    I am on "/article/super-enceinte-destock"
        And     I should see a page title with "Cambridge La super enceinte destockée pas cher du tout avec un long title..."
        And     I should see a meta "description" with value "introduction"

    Scenario: Check metas have fallbacks when no data are specified
        Given   I am on "/article/enceinte-colonne/triangle/gaia-ez-noyer"
        Then    I should see a page title with "Triangle Gaïa EZ Noyer - Enceinte colonne sur Son-Vidéo.com"
        And     I should see a meta "description" with value "introduction"

    Scenario: Check structured data
        Given   I am on "/article/super-enceinte"
        Then    I should see "\"@context\":\"http:\/\/schema.org\"" in the "script[type='application/ld+json']" element
        And     I should see "\"@type\":\"Product\"" in the "script[type='application/ld+json']" element
        And     I should see "\"name\":\"super enceinte\"" in the "script[type='application/ld+json']" element
        And     I should see "\"image\":\"https:\/\/www.son-video.com\/images\/article\/cambridge\/CAMBMINXMIN12NR\/minx-min-12-noir_6214c63b7ee3f_1200.jpg\"" in the "script[type='application/ld+json']" element
        And     I should see "\"description\":\"introduction\"" in the "script[type='application/ld+json']" element
        And     I should see "\"category\":\"Enceinte encastrable mini\"" in the "script[type='application/ld+json']" element
        And     I should see "\"brand\":{\"@type\":\"Brand\",\"name\":\"Cambridge\",\"url\":\"https:\/\/www.son-video.com\/marque\/cambridge\"}" in the "script[type='application/ld+json']" element
        And     I should see "\"offers\":{\"@type\":\"Offer\",\"priceCurrency\":\"EUR\",\"price\":100,\"availability\":\"http://schema.org/InStock\"}" in the "script[type='application/ld+json']" element
        And     I should see "\"review\":[{\"@type\":\"Review\",\"author\":{\"@type\":\"Person\",\"name\":\"Jane\"},\"reviewBody\":\"Ce produit est bien\",\"inLanguage\":\"fr\",\"datePublished\":\"2014-01-01\",\"reviewRating\":{\"@type\":\"Rating\",\"ratingValue\":\"4\",\"bestRating\":\"5\",\"worstRating\":\"1\"},\"publisher\":{\"@type\":\"Organization\",\"name\":\"son-video.com\"}}]" in the "script[type='application/ld+json']" element
        And     I should see "\"aggregateRating\":{\"@type\":\"AggregateRating\",\"ratingValue\":\"4.6\",\"ratingCount\":7,\"bestRating\":\"5\",\"worstRating\":\"1\"}" in the "script[type='application/ld+json']" element

    Scenario: Showing article content
        Given   I am on "/article/elipson-prestige-4i-calvados-fr"
        Then    I should see "Elipson Prestige 4i Calvados" in the ".product-header-title-model" element
        Then    I should see "Amplis home-cinéma" in the ".product-header-title-stand a[href='/rayon/haute-fidelite/amplis-home-cinema']" element
        And     I should see "L'enceinte Elipson Prestige 4i est une grande et puissante colonne" in the ".SVDv3_ficheProduit_descriptif" element

    Scenario: Showing strong points
        Given   I am on "/article/elipson-prestige-4i-calvados-fr"
        Then    I should see "Points forts" in the "#SVDv3_colonnes_colonne_gauche_article .content-presssheet-strongpoints h3" element
        And     I should see 5 ".content-presssheet-strongpoints > div > ul > li" elements

    Scenario: brand info
        Given   I am on "/article/elipson-prestige-4i-calvados-fr"
        When    I inspect element ".product-header-brand-logo"
        Then    the source of image "a[href='/marque/elipson'] img[alt='Elipson']" in inspected element should contain "/images/static/marques/Elipson.gif" from package "static_images"

    Scenario: Showing warranty and "within within 24 hours" one time for an article with declinations
        Given   I am on "/article/elipson-prestige-4i-calvados-fr"
        When    I inspect element "div.product-header-warranties"
        And     I should see 3 ".product-header-warranty" elements in inspected element
        When    I inspect element "div.product-header-warranty:nth-child(1)"
        Then    I should see "Livraison à votre domicile" in inspected element
        Then    I should see "Expédition sous 24h" in inspected element
        When    I inspect element "div.product-header-warranty:nth-child(3)"
        Then    I should see "Garantie 2 ans" in inspected element
        Then    I should see "Extension de garantie à 2 ans disponible dans le panier" in inspected element
        Then    I should see "Extension de garantie à 5 ans disponible dans le panier" in inspected element
        Then    I should see "Garantie vol et casse 1 an disponible dans le panier" in inspected element

    Scenario: Showing warranty and no "within 24 hours" for a destock
        Given   I am on "/article/super-enceinte-destock"
        When    I inspect element "div.product-header-warranties"
        And     I should see 3 ".product-header-warranty" elements in inspected element
        When    I inspect element "div.product-header-warranty:nth-child(1)"
        And     I should not see "Expédition sous 24h" in inspected element
        When    I inspect element "div.product-header-warranty:nth-child(3)"
        Then    I should see "Garantie 2 ans" in inspected element

    Scenario: Showing SVD warranty and no "within 24 hours" for an article not in stock with declinations
        Given   I am on "/article/super-enceinte"
        When    I inspect element "div.product-header-warranties"
        And     I should see 3 ".product-header-warranty" elements in inspected element
        When    I inspect element "div.product-header-warranty:nth-child(1)"
        And     I should not see "Expédition sous 24h" in inspected element
        When    I inspect element "div.product-header-warranty:nth-child(3)"
        Then    I should see "Garantie 2 ans" in inspected element

    Scenario: Showing no warranty and no "within 24 hours" for a specific article
        Given   I am on "/article/davotulipnr"
        When    I inspect element "div.product-header-warranties"
        And     I should see 2 ".product-header-warranty" elements in inspected element
        When    I inspect element "div.product-header-warranty:nth-child(1)"
        And     I should not see "Expédition sous 24h" in inspected element
        When    I inspect element "div.product-header-warranty:nth-child(2)"
        Then    I should not see "Garantie 2 ans" in inspected element

    Scenario: Showing lifetime warranty for an article
        Given   I am on "/article/enceintes-centrales/triangle/elara-ln02-blanc-laque"
        When    I inspect element "div.product-header-warranties"
        And     I should see 3 ".product-header-warranty" elements in inspected element
        When    I inspect element "div.product-header-warranty:nth-child(3)"
        Then    I should see "Garantie à vie" in inspected element

    Scenario: Showing 2 years warranty for an article with a one-year warranty
        Given   I am on "/article/super-enceinte"
        When    I inspect element "div.product-header-warranties"
        And     I should see 3 ".product-header-warranty" elements in inspected element
        When    I inspect element "div.product-header-warranty:nth-child(3)"
        Then    I should see "Garantie 2 ans" in inspected element

    Scenario: Check advised article widget when no article advized
        Given   I am on "/article/super-enceinte"
        Then    I should not see a "div.advised_articles" element

    Scenario: Check root controller redirection
        Given   I am on "/article"
        Then    I should be on url ending by "/"
        When    I am on "/article/"
        Then    I should be on url ending by "/"

    Scenario: Check partial article urls redirection
        Given   I am on "/article/enceinte-colonne/triangle"
        Then    I should be on url ending by "/marque/enceinte-colonne/triangle"
        When    I am on "/article/enceinte-colonne"
        Then    I should be on url ending by "/categorie/enceinte-colonne"

    Scenario: Check no editorial promo code content is displayed for irrelevant articles (see above for reverse test)
       Given  I am on "/article/davotulipnr"
       Then   I should not see an "div.SVDv3_colonnes_colonne_droite div.mceContentBody.clearfix div.grid_container_12" element

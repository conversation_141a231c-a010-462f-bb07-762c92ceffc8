Feature: check the CMS front brand display
  In order to see the good working of brand display on son-video website
  As a guest visitor
  I need to have working brand list and details pages

  Scenario: Check headers
    Given   I am on "/marque/elipson"
    Then    I should see a header "Cache-Control" with value "max-age=86400"
    And     I should see a header "X-Cache-Tags" with value "brand:343"
    And     I should see a header "X-Cache-Tags" with value "category:1"
    And     I should see a header "X-Cache-Tags" with value "category:2"
    And     I should see a header "X-Cache-Tags" with value "menu"

  Scenario: Showing brand insert content in brand page
    Given   I am on "/marque/elipson"
    Then    I should see "Tout savoir sur Elipson" in the ".title-gradient-roundcorner" element
    Then    I should see an "//img[@alt='Elipson']" xpath element
    Then    I should see a "span.product-score" element
    Then    I should see an "//a[text()='Enceinte']" xpath element
    Then    I should see an "//a[text()='Enceinte encastrable']" xpath element

  Scenario: Display article images from the CDN
    Given I am on "/marque/elipson"
    Then  the source of image "img[alt='Elipson Prestige 4i']" should contain "/images/dynamic/Baladeurs/articles/Fiio/FIIOX5IIGD/Fiio-X5-II-Gold_P_95.jpg" from package "article_images"

  Scenario: Showing brand page with link to passion area
    Given   I am on "/marque/elipson"
    Then    the response status code should be 200
    And     I should see "TOUT SAVOIR SUR LA MARQUE" in the "div.card-body" element

  Scenario: Showing brand page with no link to passion area
    Given   I am on "/marque/triangle"
    Then    the response status code should be 200
    And     I should not see "TOUT SAVOIR SUR LA MARQUE"

  Scenario: Check structured data
    Given   I am on "/marque/enceinte/elipson"
    Then    I should see "\"@context\":\"http:\/\/schema.org\"" in the "script[type='application/ld+json']" element
    And     I should see "\"@type\":\"BreadcrumbList\"" in the "script[type='application/ld+json']" element
    And     I should see "{\"@type\":\"ListItem\",\"position\":1,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/marques\",\"name\":\"Notre espace marques\"}}" in the "script[type='application/ld+json']" element
    And     I should see "{\"@type\":\"ListItem\",\"position\":2,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/marque\/elipson\",\"name\":\"Elipson\"}}" in the "script[type='application/ld+json']" element
    And     I should see "{\"@type\":\"ListItem\",\"position\":3,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/marque\/enceinte\/elipson\",\"name\":\"Enceinte\"}}" in the "script[type='application/ld+json']" element
    When    I am on "/marque/elipson"
    Then    I should see "\"@context\":\"http:\/\/schema.org\"" in the "script[type='application/ld+json']" element
    And     I should see "\"@type\":\"BreadcrumbList\"" in the "script[type='application/ld+json']" element
    And     I should see "{\"@type\":\"ListItem\",\"position\":1,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/marques\",\"name\":\"Notre espace marques\"}}" in the "script[type='application/ld+json']" element
    And     I should see "{\"@type\":\"ListItem\",\"position\":2,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/marque\/elipson\",\"name\":\"Elipson\"}}" in the "script[type='application/ld+json']" element
    And     I should not see "{\"@type\":\"ListItem\",\"position\":3,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/marque\/enceinte\/elipson\",\"name\":\"Enceinte\"}}" in the "script[type='application/ld+json']" element
    When    I am on "/marques"
    And     I should see "{\"@type\":\"ListItem\",\"position\":1,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/marques\",\"name\":\"Notre espace marques\"}}" in the "script[type='application/ld+json']" element
    And     I should not see "{\"@type\":\"ListItem\",\"position\":2,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/marque\/elipson\",\"name\":\"Elipson\"}}" in the "script[type='application/ld+json']" element
    And     I should not see "{\"@type\":\"ListItem\",\"position\":3,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/marque\/enceinte\/elipson\",\"name\":\"Enceinte\"}}" in the "script[type='application/ld+json']" element

  @javascript
  Scenario: Check brand list trackers
    Given   I am on "/marques"
    Then    The Iadvize custom data variable should be set to "{\"page_type\":\"listing\",\"device\":\"desktop\",\"flag_panier_initie\":0,\"erreur_connexion\":0,\"cart_amount\":0}"

  Scenario: Check root controller redirection
    Given   I am on "/marque"
    Then    I should be on url ending by "/marques"
    When    I am on "/marque/"
    Then    I should be on url ending by "/marques"

Feature: A customer can visualize the detail of one order
  In order to manage order
  As an authenticated customer
  I need to be able to view my order and interact with it

  <PERSON><PERSON><PERSON>: Customer can not see an order that don't belong to him
    Given   I am authenticated as "<EMAIL>" with password "123456"
    And     I am on "/mon-compte/ma-commande/1333187"
    Then    the response status code should be 404
    And     I should not see "COMMANDE N° 1333187"

  Scenario: Fail to add a message to an order
    Given   I am authenticated as "<EMAIL>" with password "123456"
    And     I am on "/mon-compte/ma-commande/1268510"
    Then    I should see "COMMANDE N° 1268510"
    When    I fill in "Saisissez votre message" with "Not enough"
    And     I press "Envoyer"
    Then    I should see "Votre message est trop court, 15 caractères minimum."
    When    I expect RPC call to "bridge" "order_message:submit" to respond with:
        """
{
  "is_valid": false,
  "error": "UNKNOWN_ORDER"
}
        """
    And     I fill in "Saisissez votre message" with "Enough chars but wrong response from bridge"
    And     I press "Envoyer"
    Then    I should see "Une erreur est survenue lors de la sauvegarde de votre message."
    And     I should see "Merc<PERSON> de tenter à nouveau ou contacter notre service client"

  Scenario: Try to send a too long message
    Given   I am authenticated as "<EMAIL>" with password "123456"
    And     I am on "/mon-compte/ma-commande/1268510"
    Then    I should see "COMMANDE N° 1268510"
    When    I fill in "Saisissez votre message" with "Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. Nulla consequat massa quis enim. Donec pede justo, fringilla vel, aliquet nec, vulputate eget, arcu. In enim justo, rhoncus ut, imperdiet a, venenatis vitae, justo. Nullam dictum felis eu pede mollis pretium. Integer tincidunt. Cras dapibus. Vivamus elementum semper nisi. Aenean vulputate eleifend tellus. Aenean leo ligula, porttitor eu, consequat vitae, eleifend ac, enim. Aliquam lorem ante, dapibus in, viverra quis, feugiat a, tellus. Phasellus viverra nulla ut metus varius laoreet. Quisque rutrum. Aenean imperdiet. Etiam ultricies nisi vel augue. Curabitur ullamcorper ultricies nisi. Nam eget dui. Etiam rhoncus. Maecenas tempus, tellus eget condimentum rhoncus, sem quam semper libero, sit amet adipiscing sem neque sed ipsum. Nam quam nunc, blandit vel, luctus pulvinar, hendrerit id, lorem. Maecenas nec odio et ante tincidunt tempus. Donec vitae sapien ut libero venenatis faucibus. Nullam quis ante. Etiam sit amet orci eget."
    And     I press "Envoyer"
    Then    I should see "Votre message est trop long, 1200 caractères maximum."

  Scenario: Fail to add a message to an order
    Given   I am authenticated as "<EMAIL>" with password "123456"
    And     I am on "/mon-compte/ma-commande/1268510"
    Then    I should see "COMMANDE N° 1268510"
    When    I expect RPC call to "bridge" "order_message:submit" to respond with:
        """
{
  "is_valid": true,
  "message": {
    "message_id": 999999,
    "customer_order_id": 1268510,
    "created_at": "2016-09-22 20:45:21",
    "sender": "customer",
    "content": "I need to know more about my order"
  }
}
        """
    And     I fill in "Saisissez votre message" with "I need to know more about my order"
    And     I press "Envoyer"
    Then    I should see "Votre message a bien été envoyé"
    And     I should see "I need to know more about my order"

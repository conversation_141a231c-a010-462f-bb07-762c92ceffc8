Feature: edit a setup
  In order make modification on a setup
  As a customer
  I need to be able to edit it

#  Sc<PERSON>rio: Edit a setup
#    Given   I am authenticated as "<EMAIL>" with password "sonvideo"
#    When    I am on "/mon-compte/mon-installation/4333"
#    And     I should see "Ajouter des images pour votre installation"
#    And     I fill in "installation_edit_form[comment]" with "new comment"
#    And     I fill in "installation_edit_form[budget]" with "2"
#    And     I fill in "installation_edit_form[surface_area_m2]" with "888"
#    And     I fill in "installation_edit_form[description]" with "new description"
#    And     I press "Sauvegarder"
#    Then    I should see "Merci de partager avec nous votre installation, nous vous informerons par email dès qu’elle aura été modérée et publiée. N'oubliez pas d'ajouter vos images sur cette page et de cliquer sur sauvegarder après tout ajout ou suppression d'image."

  Scenario: Try to edit a setup which belong to another customer
    Given   I am authenticated as "<EMAIL>" with password "sonvideo"
    When    I am on "/mon-compte/mon-installation/4316"
    And     I should see "Vous n'avez pas l'autorisation d'éditer cette installation"
    And     I should see a link to href "/mon-compte/mes-installations" in the page

  Scenario: Try to edit a validated setup
    Given   I am authenticated as "<EMAIL>" with password "123456"
    When    I am on "/mon-compte/mon-installation/4316"
    And     I should see "Vous ne pouvez pas éditer une installation déjà validée"
    And     I should see a link to href "/mon-compte/mes-installations" in the page

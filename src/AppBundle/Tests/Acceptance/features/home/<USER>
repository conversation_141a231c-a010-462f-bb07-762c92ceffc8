Feature: check the CMS front home page
  In order to see the good working of son-video website
  As an authenticated customer
  I need to see the home clickbait, page head, footer, meta and header.

  Scenario: Check headers
    Given   I am on "/"
    Then    I should see a header "Cache-Control" with value "max-age=86400"
    And     I should see a header "X-Cache-Tags" with value "home"
    And     I should see a header "X-Cache-Tags" with value "menu"
    And     I should see a header "X-Cache-Tags" with value "clickbait:1a2e9bca-db9c-4025-90c4-060478868ec0"
    And     I should see a header "X-Cache-Tags" with value "clickbait:a4f70dea-b49b-4f1f-8d99-6f0389fd0c9b"
    And     I should see a header "X-Cache-Tags" with value "clickbait:e6a30050-0be1-4953-ac1c-998b3bbf1cba"
    And     I should see a header "X-Cache-Tags" with value "clickbait:45c5a70c-3fee-4a93-9692-b5086bd59a9d"
    And     I should see a header "X-Cache-Tags" with value "clickbait:d6d57cfe-3a10-4f92-b1ae-0f9deca08b9c"
    And     I should see a header "X-Cache-Tags" with value "clickbait:e1227c8e-309c-4ad4-9d6b-1617b4b72931"
    # front products
    And     I should see a header "X-Cache-Tags" with value "article:101262"
    And     I should see a header "X-Cache-Tags" with value "article:123456"
    And     I should see a header "X-Cache-Tags" with value "article:123457"
    And     I should see a header "X-Cache-Tags" with value "article:123458"

  Scenario: Check structured data
    Given   I am on "/"
    Then    I should see "\"@context\":\"http:\/\/schema.org\"" in the "script[type='application/ld+json']" element
    Then    I should see "\"@type\":\"WebSite\"" in the "script[type='application/ld+json']" element
    Then    I should see "\"url\":\"https:\/\/www.son-video.com\"" in the "script[type='application/ld+json']" element
    Then    I should see "\"potentialAction\":{\"@type\":\"SearchAction\",\"target\":\"https:\/\/www.son-video.com\/recherche?termes={search_term_string}\",\"query-input\":\"required name=search_term_string\"}" in the "script[type='application/ld+json']" element

  Scenario: Check page footer
    Given   I am on "/"
    Then    I inspect element "footer"
    And     I should see a ".footer-customer-contact" element in inspected element
    And     I should see a ".footer-reassurance" element in inspected element
    And     I should see a ".site-social" element in inspected element
    And     I should see a ".site-links" element in inspected element
    And     I should see a ".payment-methods" element in inspected element
    Then    I inspect element ".footer-customer-contact"
    And     I should see a ".customer-service-icon" element in inspected element
    And     I should see "Rappel immédiat" in inspected element
    And     I should see "Envoyez-nous un message" in inspected element
    Then    I inspect element ".footer-reassurance"
    And     I should see a ".reassurance-icon" element in inspected element
    Then    I inspect element ".site-social"
    And     I should see "Le Blog Son-Vidéo.com" in inspected element
    And     I should see a "#newsletter-subscription" element in inspected element
    And     I should see "Rejoignez la communauté" in inspected element
    And     I should see "Webradio Son-Vidéo.com" in inspected element
    And     I should see a "a.facebook-icon" element in inspected element
    And     I should see a "a.twitter-icon" element in inspected element
    And     I should see a "a.youtube-icon" element in inspected element
    And     I should see a "a.instagram-icon" element in inspected element
    Then    I inspect element ".site-links"
    And     I should see "Infos et contacts" in inspected element
    And     I should see "Son-Vidéo.com... et vous" in inspected element
    And     I should see "Nos offres" in inspected element
    And     I should see "Nos magasins" in inspected element
    Then    I inspect element ".payment-methods"
    And     I should see "Paiement sécurisé" in inspected element

  Scenario: Check canonical link
    Given   I am on "/"
    Then    I should have a canonical link with value "https://www.son-video.com"

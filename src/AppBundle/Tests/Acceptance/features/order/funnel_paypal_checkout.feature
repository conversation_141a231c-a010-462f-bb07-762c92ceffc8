Feature: check the CMS front order funnel when paying with paypal
  In order to accept a payment made via paypal
  As an authenticated customer
  I need to use a dedicated funnel allowing to select the shipment method and validate the payment

  @javascript
  Scenario: Check error message when Paypal address country is not eligible
    Given   I am authenticated as "<EMAIL>" with password "123456"
    And     I clear RPC cache
    And     I expect R<PERSON> call to "bridge" "order:get_paypal_express_checkout_details" to respond with:
        """
{
    "details":{
       "TOKEN":"EC-4MM00949EL410954L",
       "BILLINGAGREEMENTACCEPTEDSTATUS":"0",
       "CHECKOUTSTATUS":"PaymentActionNotInitiated",
       "TIMESTAMP":"2018-06-22T13:56:37Z",
       "CORRELATIONID":"6923b76589aa9",
       "ACK":"Success",
       "VERSION":"61.0",
       "BUILD":"47201799",
       "FIRSTNAME":"<PERSON>",
       "LASTNAME":"<PERSON><PERSON>",
       "SHIPTONAME":"Paypal",
       "SHIPTOSTREET":"rue de l'ami molette",
       "SHIPTOCITY":"AMSTERDAM",
       "SHIPTOZIP":"1011",
       "SHIPTOCOUNTRYCODE":"FR",
       "SHIPTOPHONENUM":"0607080910",
       "SHIPTOCOUNTRYNAME":"France",
       "CURRENCYCODE":"EUR",
       "AMT":"1473.96",
       "ITEMAMT":"1473.96",
       "SHIPPINGAMT":"0.00",
       "HANDLINGAMT":"0.00",
       "TAXAMT":"0.00",
       "DESC":"1509531",
       "INVNUM":"1509531",
       "INSURANCEAMT":"0.00",
       "SHIPDISCAMT":"0.00",
       "INSURANCEOPTIONOFFERED":"false"
    }
 }
        """
    And     I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
        """
[
  {"shipment_method_id": 37, "cost": 3}
]
        """
    And     I expect RPC call to "bridge" "payment:check_country_eligibility" to respond with:
        """
{"is_eligible": false}
        """
    When  I am on "/paypal/confirmation/succes?token=EC-4MM00949EL410954L"
    Then  I should be on "/mon-panier"
    And   I should see "Nous ne pouvons malheureusement pas livrer dans le pays indiqué dans votre adresse de livraison Paypal." in the "#messageBox" element
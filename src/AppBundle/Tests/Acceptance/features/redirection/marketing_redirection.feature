Feature: check the CMS front can redirect urls from marketing sites
  In order to see the good working of son-video website
  As a guest visitor
  I need to be able to see good redirects for marketing sites and creation of specific cookies

Scenario: check redirection from Tradedoubler
  Given   I am on "/marketing-redirection?url=/article/elipson-prestige-4i-calvados-fr&tduid=2e99a99f58c9dca50cde77a2162234fd"
  Then    the response status code should be 200
  And     I should be on url matching "/article/elipson-prestige-4i-calvados-fr"
  And     I should see a cookie "TRADEDOUBLER" set to "TD"
  And     I should see a cookie "tradedoubler_id" set to "2e99a99f58c9dca50cde77a2162234fd"

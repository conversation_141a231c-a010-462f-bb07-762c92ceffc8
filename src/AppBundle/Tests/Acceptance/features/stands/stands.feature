Feature: The CMS front have working stands.
    On son-video website (ie. front CMS)
    As a guest visitor
    I need to navigate in stands with good content.

    Scenario: Display a stand
        Given   I am on "/rayon/haute-fidelite/enceintes/enceintes-colonne"
        Then    the response status code should be 200
        # headers
        And     I should see a header "Cache-Control" with value "max-age=86400"
        And     I should see a header "X-Cache-Tags" with value "stand:9"
        And     I should see a header "X-Cache-Tags" with value "stand:3"
        And     I should see a header "X-Cache-Tags" with value "stand:13"
        And     I should see a header "X-Cache-Tags" with value "stand:12"
        And     I should see a header "X-Cache-Tags" with value "menu"
        And     I should see a header "X-Cache-Tags" with value "clickbait:f04ea36a-b875-491e-a8d3-e008e2989274"
        And     I should see a header "X-Cache-Tags" with value "clickbait:f04ea36a-b875-491e-a8d3-e008e2989274"
        # page title
        And     I should see "Le rayon Enceintes colonne" in page title
        # meta checking
        And     I should see "meta-description des enceintes" in meta "description"
        And     I should see an empty meta "keywords"
        # title is displayed
        And     I should see "Enceintes colonne" in the "#SVDv3_content_container h1" element
        # editorial content is present
        And     I should see "Les enceintes colonne sont très appréciées"
        Given   I am on "/rayon/photo-video"
        Then    the response status code should be 200
        # page title
        And     I should see "L'univers Photo & Vidéo hyper chiadé chez Son-Vidéo.com" in page title

    Scenario: Check structured data
        Given   I am on "/rayon/haute-fidelite/amplis-home-cinema"
        Then    I should see "\"@context\":\"http:\/\/schema.org\"" in the "script[type='application/ld+json']" element
        And     I should see "\"@type\":\"BreadcrumbList\"" in the "script[type='application/ld+json']" element
        And     I should see "{\"@type\":\"ListItem\",\"position\":1,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/rayon\/haute-fidelite\",\"name\":\"Haute fid\u00e9lit\u00e9\"}}" in the "script[type='application/ld+json']" element
        And     I should see "{\"@type\":\"ListItem\",\"position\":2,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/rayon\/haute-fidelite\/amplis-home-cinema\",\"name\":\"Amplis home-cin\u00e9ma\"}}" in the "script[type='application/ld+json']" element
        When    I am on "/rayon/haute-fidelite"
        Then    I should see "\"@context\":\"http:\/\/schema.org\"" in the "script[type='application/ld+json']" element
        And     I should see "\"@type\":\"BreadcrumbList\"" in the "script[type='application/ld+json']" element
        And     I should see "{\"@type\":\"ListItem\",\"position\":1,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/rayon\/haute-fidelite\",\"name\":\"Haute fid\u00e9lit\u00e9\"}}" in the "script[type='application/ld+json']" element
        And     I should not see "{\"@type\":\"ListItem\",\"position\":2,\"item\":{\"@id\":\"https:\/\/www.son-video.com\/rayon\/haute-fidelite\/amplis-home-cinema\",\"name\":\"Amplis home-cin\u00e9ma\"}}" in the "script[type='application/ld+json']" element

    @javascript
    Scenario: Check trackers on stand landing page with alternative template
        Given   I am on "/rayon/haute-fidelite"
        Then    The Iadvize custom data variable should be set to "{\"page_type\": \"listing\", \"product_category\": \"Haute fidélité\", \"device\": \"desktop\",\"flag_panier_initie\":0,\"erreur_connexion\":0,\"cart_amount\":0}"

    Scenario: Stand landing page with default template should show a list of stand siblings
        Given   I am on "/rayon/haute-fidelite/enceintes/enceintes-colonne"
        And     I should see a title with "Rayons associés"
        And     I should see a "ul.SVDv3_colonne_menu_liste a[href='/rayon/haute-fidelite/enceintes/enceintes-centrales']" element
        And     I should see a "ul.SVDv3_colonne_menu_liste a[href='/rayon/haute-fidelite/enceintes/enceintes-compactes']" element

    Scenario: Level 2 stands display sub stands "linkable"
        Given   I am on "/rayon/home-cinema/chaines-home-cinema"
        Then    I should see a title with "Chaînes home cinéma compactes"
        And     I should see 2 "div.SVDv3_sommaireCategorie_cellule" elements
        When    I inspect element "div.SVDv3_article_element"
        Then    I should see "Chaînes Home-Cinéma grand spectacle" in inspected element
        Then    I should see "Chaînes Home-Cinéma compactes" in inspected element
        Then    I should not see "Chaînes Home-Cinéma petit spectacle" in inspected element

    Scenario: Check root controller redirection
        Given   I am on "/rayon"
        Then    I should be on url ending by "/"
        When    I am on "/rayon/"
        Then    I should be on url ending by "/"

    Scenario: Check canonical link
        Given   I am on "/rayon/home-cinema/chaines-home-cinema"
        Then    I should have a canonical link with value "https://www.son-video.com/rayon/home-cinema/chaines-home-cinema"
        When    I am on "/rayon/haute-fidelite"
        Then    I should have a canonical link with value "https://www.son-video.com/rayon/haute-fidelite"

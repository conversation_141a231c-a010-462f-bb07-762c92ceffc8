<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\DataFormatter\Account;

use SonVideo\Cms\FrontOffice\AppBundle\DataFormatter\Account\AddressDataFormatter as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;

/**
 * AddressDataFormatter
 *
 * @package     cms-front
 * @copyright   2018 Son-Video Distribution
 *
 * @see Test
 */
class AddressDataFormatter extends Test
{
    /**
     * testFormat
     */
    public function testFormat(): void
    {
        $data = [
            'name' => 'My Spéciale ADdress',
            'title' => 'Mrs',
            'lastname' => 'MÉné',
            'firstname' => 'Édith-léonie',
            'company_name' => 'L’Éxtra-Fantastique',
            'address' => 'N°13 ruE De <PERSON>’École<>!?;:[]{}.',
            'postal_code' => '33000',
            'city' => 'L’ÉCLuse',
            'country_code' => 'fr',
            'phone' => '**********',
            'cellphone' => '',
            'created_at' => '',
            'extra_field' => 'A value',
        ];

        $this->assert('Test format function')
            ->given($formatted = (new TestedClass())->format($data))
            ->array($formatted)
            ->string['name']->isEqualTo('My Spéciale ADdress')
            ->string['title']->isEqualTo('Mrs')
            ->string['lastname']->isEqualTo('MEne')
            ->string['firstname']->isEqualTo('Edith-Leonie')
            ->string['company_name']->isEqualTo('L\'Extra-Fantastique')
            ->string['address']->isEqualTo('N13 ruE De l\'Ecole.')
            ->string['postal_code']->isEqualTo('33000')
            ->string['city']->isEqualTo('L\'ECLUSE')
            ->string['country_code']->isEqualTo('FR')
            ->string['phone']->isEqualTo('**********')
            ->string['cellphone']->isEqualTo('')
            ->string['created_at']->isEqualTo('')
            ->notHasKey('extra_field');
    }
}

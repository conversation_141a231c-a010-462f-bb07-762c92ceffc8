<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Elector\PromoCode\Rule;

use SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Rule\PromoCodeLifetimeIsActiveRule as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Elector\PromoCode\PromoCodeFetcherTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class PromoCodeLifetimeIsActiveRule
 *
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class PromoCodeLifetimeIsActiveRule extends PommTest
{
    use PromoCodeFetcherTrait;
    use RuleTestTrait;

    /**
     * getTestedInstance
     */
    protected function getTestedInstance(): TestedClass
    {
        return new TestedClass();
    }

    /**
     * getContextEligible
     */
    protected function getContextEligible(): array
    {
        return [
            [[], $this->getPromoOfferObject(869)],
            [[], $this->getPromoOfferObject(777)],
            [[], $this->getPromoOfferObject(916)],
            [[], $this->getPromoOfferObject(908)],
            [[], $this->getCustomerPromoCodeObject(24, 'PAPAYOU')],
        ];
    }

    /**
     * getContextNotEligible
     */
    protected function getContextNotEligible(): array
    {
        return [[[], $this->getPromoOfferObject(879)], [[], $this->getCustomerPromoCodeObject(24, 'BIGBISOUS')]];
    }
}

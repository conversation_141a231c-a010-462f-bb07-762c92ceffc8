<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Elector\PromoCode\Rule;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PromoOfferSchema\PromoCodeInterface;

/**
 * Trait RuleTestTrait
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Elector\PromoCode\Rule
 * <AUTHOR> <<EMAIL>>
 */
trait RuleTestTrait
{
    abstract protected function getTestedInstance();
    abstract protected function getContextEligible(): array;
    abstract protected function getContextNotEligible(): array;

    /**
     * testIsEligible
     *
     * @dataProvider getContextEligible
     */
    public function testIsEligible(array $summary, PromoCodeInterface $promo_code): void
    {
        $this->assert('Context is eligible.')
            ->given($res = $this->getTestedInstance()->isEligible($summary, $promo_code))
            ->boolean($res)->isTrue;
    }

    /**
     * testIsNotEligible
     *
     * @dataProvider getContextNotEligible
     */
    public function testIsNotEligible(array $summary, PromoCodeInterface $promo_code): void
    {
        $this->assert('Context is not eligible.')
            ->given($res = $this->getTestedInstance()->isEligible($summary, $promo_code))
            ->boolean($res)->isFalse;
    }
}

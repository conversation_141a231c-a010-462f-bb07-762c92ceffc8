<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Elector\PromoCode\Rule;

use SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Rule\ShoppingCartIsNotEmptyRule as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Elector\PromoCode\PromoCodeFetcherTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class PromoCodeLifetimeIsActiveRule
 *
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class ShoppingCartIsNotEmptyRule extends PommTest
{
    use PromoCodeFetcherTrait;
    use RuleTestTrait;

    /**
     * getTestedInstance
     */
    protected function getTestedInstance(): TestedClass
    {
        return new TestedClass();
    }

    /**
     * getContextEligible
     */
    protected function getContextEligible(): array
    {
        return [
            [['has_quote' => false], $this->getPromoOfferObject(869)],
            [['has_quote' => false], $this->getPromoOfferObject(777)],
            [['has_quote' => false], $this->getPromoOfferObject(916)],
            [['has_quote' => false], $this->getPromoOfferObject(908)],
            [['has_quote' => false], $this->getCustomerPromoCodeObject(24, 'PAPAYOU')],
        ];
    }

    /**
     * getContextNotEligible
     */
    protected function getContextNotEligible(): array
    {
        return [[[], $this->getPromoOfferObject(777)], [[], $this->getPromoOfferObject(916)]];
    }
}

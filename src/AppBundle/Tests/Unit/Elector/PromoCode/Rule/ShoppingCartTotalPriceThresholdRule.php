<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Elector\PromoCode\Rule;

use SonVideo\Cms\FrontOffice\AppBundle\Elector\PromoCode\Rule\ShoppingCartTotalPriceThresholdRule as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Elector\PromoCode\PromoCodeFetcherTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class PromoCodeLifetimeIsActiveRule
 *
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class ShoppingCartTotalPriceThresholdRule extends PommTest
{
    use PromoCodeFetcherTrait;
    use RuleTestTrait;

    /**
     * getTestedInstance
     */
    protected function getTestedInstance(): TestedClass
    {
        return new TestedClass();
    }

    /**
     * getContextEligible
     */
    protected function getContextEligible(): array
    {
        return [
            [['amount_selling_price_for_promo_code' => 50], $this->getPromoOfferObject(869)],
            [['amount_selling_price_for_promo_code' => 50], $this->getPromoOfferObject(777)],
            [['amount_selling_price_for_promo_code' => 50], $this->getPromoOfferObject(916)],
            [['amount_selling_price_for_promo_code' => 1524.23], $this->getPromoOfferObject(908)],
            [['amount_selling_price_for_promo_code' => 50], $this->getCustomerPromoCodeObject(24, 'PAPAYOU')],
            [['amount_selling_price_for_promo_code' => 300], $this->getCustomerPromoCodeObject(24, 'TIRELIPIMPON')],
        ];
    }

    /**
     * getContextNotEligible
     */
    protected function getContextNotEligible(): array
    {
        return [
            [['amount_selling_price_for_promo_code' => 49.99], $this->getPromoOfferObject(869)],
            [['amount_selling_price_for_promo_code' => 5.99], $this->getPromoOfferObject(908)],
            [['amount_selling_price_for_promo_code' => 5.99], $this->getCustomerPromoCodeObject(24, 'TIRELIPIMPON')],
        ];
    }
}

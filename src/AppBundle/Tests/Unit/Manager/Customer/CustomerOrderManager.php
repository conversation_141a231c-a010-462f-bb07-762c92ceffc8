<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Customer;

use PommProject\ModelManager\Exception\ModelException;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrder;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrderModel;
use SonVideo\Cms\FrontOffice\AppBundle\Referential\PaymentApiV2;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\RpcClientServiceMock;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\CustomerOrderManager as TestedClass;
use SonVideo\Cms\FrontOffice\Application\Client\PaymentV2Client;

/**
 * Class CustomerOrderManager
 *
 * <AUTHOR> Boulard <<EMAIL>>
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\BasketOrder
 * @engine inline
 */
class CustomerOrderManager extends PommTest
{
    /**
     * @throws \Exception
     */
    private function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get('app.manager.customer_order_manager');
    }

    /**
     * @throws ModelException
     */
    public function testUpdateStatusAfterPaymentExceptions(): void
    {
        $this->assert('Fail to update status on an inexistant customer order')
            ->if($this->mockUserLogin(24))
            ->and($tested_instance = $this->getTestedInstance())
            ->exception(function () use ($tested_instance): void {
                $tested_instance->updateStatusAfterPayment(9_999_999, true);
            })
            ->isInstanceOf(\UnexpectedValueException::class)
            ->hasMessage(sprintf('Customer order n°%d does not exists', 9_999_999))

            ->assert('Fail to update status on a customer order not awaiting payment')
            ->if($this->mockUserLogin(24))
            ->and($tested_instance = $this->getTestedInstance())
            ->exception(function () use ($tested_instance): void {
                $tested_instance->updateStatusAfterPayment(1_305_003, true);
            })
            ->isInstanceOf(\UnexpectedValueException::class)
            ->hasMessage(
                sprintf(
                    'Customer order n°%d is not awaiting a payment update. status = \'%s\'',
                    1_305_003,
                    CustomerOrder::STATUS_SHIPPED,
                ),
            )

            ->assert('Order activation failed on erp when no additional payment required')
            ->if($this->mockUserLogin(24))
            ->and(
                RpcClientServiceMock::savedResult(
                    'erp',
                    TestedClass::RPC_METHOD_ACTIVATE_CUSTOMER_ORDER_IN_ERP,
                    json_encode(false, JSON_THROW_ON_ERROR),
                ),
            )
            ->and(
                $this->getPommSession()
                    ->getModel(CustomerOrderModel::class)
                    ->updateByPk(
                        ['customer_order_id' => 1_333_188],
                        [
                            'payments' => [
                                ['code' => 'VIR', 'amount' => 599.0, 'customer_order_payment_id' => '1234567'],
                            ],
                        ],
                    ),
            )
            ->then()
            ->when($tested_instance = $this->getTestedInstance())
            ->exception(function () use ($tested_instance): void {
                $tested_instance->updateStatusAfterPayment(1_333_188, true);
            })
            ->isInstanceOf(\Exception::class)
            ->hasMessage(sprintf('Customer order n° %d was not updated in the ERP', 1_333_188));
    }

    /**
     * @throws \Exception
     */
    public function testUpdateStatusAfterIncompletePaymentAndBeingProcessedWithCheckoutV2(): void
    {
        $this->loadSqlFixtures(['update_status_after_payment.sql']);

        $this->assert('Order update succeeded and order is being processed')
            ->if($this->mockUserLogin(24))
            ->and(
                RpcClientServiceMock::savedResult(
                    'erp',
                    TestedClass::RPC_METHOD_ACTIVATE_CUSTOMER_ORDER_IN_ERP,
                    json_encode(true, JSON_THROW_ON_ERROR),
                ),
            )
            ->then()
            ->when($tested_instance = $this->getTestedInstance())
            ->and(
                $this->getPommSession()
                    ->getModel(CustomerOrderModel::class)
                    ->updateByPk(
                        ['customer_order_id' => 1_305_004],
                        ['status' => CustomerOrder::STATUS_AWAITING_PAYMENT],
                    ),
            )
            ->when($customer_order = $tested_instance->updateStatusAfterPayment(1_305_004, true))
            ->then()
            ->string($customer_order->get('status'))
            ->isEqualTo(CustomerOrder::STATUS_AWAITING_PAYMENT);
    }

    /**
     * @throws ModelException
     */
    public function testUpdateStatusAfterPaymentFailedAndAbandonedWithCheckoutV2(): void
    {
        $this->loadSqlFixtures(['update_status_after_payment.sql']);

        $this->assert('update payments when it has failed with checkout process v2')
            ->if($this->mockUserLogin(24))
            ->and(
                $this->getContainer()
                    ->get(PaymentV2Client::class)
                    ->mock(PaymentApiV2::TUNNEL_ABORT, '[]'),
            )
            ->and(
                RpcClientServiceMock::savedResult(
                    'erp',
                    TestedClass::RPC_METHOD_FETCH_CUSTOMER_ORDER_FROM_ERP,
                    json_encode(['payments' => []]),
                ),
            )
            ->then()
            ->when($tested_instance = $this->getTestedInstance())
            ->and(
                $this->getPommSession()
                    ->getModel(CustomerOrderModel::class)
                    ->updateByPk(
                        ['customer_order_id' => 1_304_980],
                        ['status' => CustomerOrder::STATUS_AWAITING_PAYMENT],
                    ),
            )
            ->when($customer_order = $tested_instance->updateStatusAfterPayment(1_304_980, false))
            ->then()
            ->string($customer_order->get('status'))
            ->isEqualTo(CustomerOrder::STATUS_AWAITING_PAYMENT)
            ->assert('Check amount for abandoned payment after order update')
            ->array($this->getSpecificOrderPayment(1_304_980, 666))
            ->hasSize(0);
    }

    public function testUpdateAfterPaymentAccepted(): void
    {
        $this->loadSqlFixtures(['update_status_after_payment.sql']);

        $this->mockUserLogin(24);

        RpcClientServiceMock::savedResult(
            'erp',
            TestedClass::RPC_METHOD_ACTIVATE_CUSTOMER_ORDER_IN_ERP,
            json_encode(true, JSON_THROW_ON_ERROR),
        );

        $tested_instance = $this->getTestedInstance();
        $this->getPommSession()
            ->getModel(CustomerOrderModel::class)
            ->updateByPk(
                ['customer_order_id' => 1_304_980],
                [
                    'status' => CustomerOrder::STATUS_AWAITING_PAYMENT,
                    'payments' => [['code' => 'VIR', 'amount' => 96.9, 'customer_order_payment_id' => '1234567']],
                ],
            );

        $customer_order = $tested_instance->updateStatusAfterPayment(1_304_980, true);

        $this->assert('the customer order status is correctly set');
        $this->string($customer_order->get('status'))->isEqualTo(CustomerOrder::STATUS_AWAITING_PROCESSING);

        $this->assert('the customer order status is persisted');
        $order = $this->getPommSession()
            ->getModel(CustomerOrderModel::class)
            ->findByPK(['customer_order_id' => 1_304_980]);
        $this->string($order['status'])->isEqualTo(CustomerOrder::STATUS_AWAITING_PROCESSING);

        $this->assert('the customer order status activated in erp');
        $rpc_call = RpcClientServiceMock::getLastCall('erp', TestedClass::RPC_METHOD_ACTIVATE_CUSTOMER_ORDER_IN_ERP);
        $this->array($rpc_call['args'])->isEqualTo([1_304_980]);
    }

    /**
     * getSpecificOrderPayment
     *
     *
     */
    private function getSpecificOrderPayment(int $customer_order_id, int $payment_id): array
    {
        $order = $this->getPommSession()
            ->getModel(CustomerOrderModel::class)
            ->findByPK(['customer_order_id' => $customer_order_id]);

        if ($order === null) {
            return [];
        }

        $payment = array_filter(
            $order->get('payments'),
            fn($payment): bool => $payment['customer_order_payment_id'] == $payment_id,
        );

        return count((array) $payment) === 1 ? current($payment) : [];
    }
}

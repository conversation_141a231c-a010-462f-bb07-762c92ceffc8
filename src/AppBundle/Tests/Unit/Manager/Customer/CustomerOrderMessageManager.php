<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Customer;

use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\CustomerOrderMessageManager as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\Message;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\MessageModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Synapps\Client\RpcClientService;

/**
 * Class CustomerOrderMessageManager
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Customer
 */
class CustomerOrderMessageManager extends PommTest
{
    /**
     * getTestedInstance
     *
     *
     */
    private function getTestedInstance(array $rpc_response): TestedClass
    {
        $this->__get('mockGenerator')->orphanize('__construct');
        $mock = $this->newMockInstance(RpcClientService::class);
        $this->calling($mock)->call = $rpc_response;

        return new TestedClass($this->getPommSession(), $mock);
    }

    /**
     * testaddNewMessageForOrder
     */
    public function testaddNewMessageForOrder(): void
    {
        $this->assert('Fail to create a new message on an inexisting customer order')
            ->if($this->mockUserLogin(24))
            ->and($tested_instance = $this->getTestedInstance([]))
            ->exception(function () use ($tested_instance): void {
                $tested_instance->addNewMessageForOrder(9_999_999, 24, 'test');
            })
            ->isInstanceOf(\UnexpectedValueException::class)
            ->hasMessage(sprintf('Customer order n°%d does not exists', 9_999_999))

            ->assert('RPC call fails')
            ->if($this->mockUserLogin(24))
            ->and($tested_instance = $this->getTestedInstance([]))
            ->exception(function () use ($tested_instance): void {
                $tested_instance->addNewMessageForOrder(1_305_004, 24, 'test');
            })
            ->isInstanceOf(\Exception::class)
            ->hasMessage('RPC call fails for order_message:submit')

            ->assert('Message creation failed on bridge')
            ->if($this->mockUserLogin(24))
            ->and(
                $response = [
                    'result' => [
                        'is_valid' => false,
                        'error' => 'UNKNOWN_ORDER',
                    ],
                ],
            )
            ->and($tested_instance = $this->getTestedInstance($response))
            ->exception(function () use ($tested_instance): void {
                $tested_instance->addNewMessageForOrder(1_305_004, 24, 'test');
            })
            ->isInstanceOf(\Exception::class)
            ->hasMessage(sprintf('Customer order message could not be saved : %s', $response['result']['error']))

            ->assert('Message creation fail locally due to missing required key from payload')
            ->if($this->mockUserLogin(24))
            ->and(
                $tested_instance = $this->getTestedInstance([
                    'result' => [
                        'is_valid' => true,
                        'message' => [
                            'message_id' => 999999,
                            'customer_order_id' => 1_305_004,
                            'sender' => 'customer',
                            'content' => 'test',
                        ],
                    ],
                ]),
            )
            ->exception(function () use ($tested_instance): void {
                $tested_instance->addNewMessageForOrder(1_305_004, 24, 'test');
            })
            ->isInstanceOf(\InvalidArgumentException::class)
            ->hasMessage(
                sprintf(
                    'Can\'t create customer order message, the following keys are missing : [%s]',
                    implode(', ', ['created_at']),
                ),
            )

            ->assert('New message creation succeed')
            ->if($this->mockUserLogin(24))
            ->and(
                $tested_instance = $this->getTestedInstance([
                    'result' => [
                        'is_valid' => true,
                        'message' => [
                            'message_id' => 999999,
                            'customer_order_id' => 1_305_004,
                            'created_at' => date('Y-m-d', strtotime('now')),
                            'sender' => 'customer',
                            'content' => 'test',
                        ],
                    ],
                ]),
            )
            ->when($tested_instance->addNewMessageForOrder(1_305_004, 24, 'test'))
            ->then()
            ->object(
                $this->getPommSession()
                    ->getModel(MessageModel::class)
                    ->findByPK(['message_id' => 999999]),
            )
            ->isInstanceOf(Message::class);
    }
}

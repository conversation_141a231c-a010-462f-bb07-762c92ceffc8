<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Customer;


use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\NewsletterManager as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\RpcClientServiceMock;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class NewsletterManager
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Customer
 * <AUTHOR> <<EMAIL>>
 */
class NewsletterManager extends PommTest
{
    /**
     * getTestedInstance
     */
    private function getTestedInstance(): TestedClass
    {
        $rpcClient = new RpcClientServiceMock(
            'bo-cms',
            $this->getContainer()->get('pomm')->getDefaultSession(),
            []
        );
        $this->getContainer()->set('sonvideo.synapps.rpc_client', $rpcClient);

        return $this->getContainer()->get(TestedClass::class);
    }

    /**
     * testSubscribe
     */
    public function testSubscribe(): void
    {
        $this->assert('Test newsletter subscription')
            
            ->and(
                RpcClientServiceMock::savedResult(
                    'bo-cms',
                    TestedClass::RPC_METHOD_SUBSCRIBE_NEWSLETTER,
                    json_encode(['is_changed' => true])
                ),
            )
            ->given($this->getTestedInstance()->subscribe('<EMAIL>', 'create-account'))


            ->assert('Test bad newsletter subscription')
            ->given(
               
                RpcClientServiceMock::savedResult(
                    'bo-cms',
                    TestedClass::RPC_METHOD_SUBSCRIBE_NEWSLETTER,
                    json_encode(['is_changed' => false, 'message' => 'Oups, something goes wrong'])
                ),
            )
            ->exception(function() {
                $this->getTestedInstance()->subscribe('<EMAIL>', 'create-account');
            })
            ->isInstanceOf(\Exception::class)
            ->hasMessage('Oups, something goes wrong');
    }

    /**
     * testUnsubscribe
     */
    public function testUnsubscribe(): void
    {
        $this->assert('Test newsletter unsubscription')
            ->given(
                RpcClientServiceMock::savedResult(
                    'bo-cms',
                    TestedClass::RPC_METHOD_UNSUBSCRIBE_NEWSLETTER,
                    json_encode(['is_changed' => true])
                ),
            )
            ->given($this->getTestedInstance()->unsubscribe('<EMAIL>'))

            ->assert('Test bad newsletter unsubscription')
            ->given(
                RpcClientServiceMock::savedResult(
                    'bo-cms',
                    TestedClass::RPC_METHOD_UNSUBSCRIBE_NEWSLETTER,
                    json_encode(['is_changed' => false, 'message' => 'Oups, something goes wrong'])
                ),
            )
            ->exception(function() {
                $this->getTestedInstance()->unsubscribe('<EMAIL>');
            })
            ->isInstanceOf(\Exception::class)
            ->hasMessage('Oups, something goes wrong')
            
            ->assert('Test unsubscription with invalid email format')
            ->given(
                RpcClientServiceMock::savedResult(
                    'bo-cms',
                    TestedClass::RPC_METHOD_UNSUBSCRIBE_NEWSLETTER,
                    json_encode(['is_changed' => false, 'message' => 'Error'])
                ),
            )
            ->exception(function() {
                $this->getTestedInstance()->unsubscribe('<EMAIL>');
            })
            ->isInstanceOf(\Exception::class)
            ->hasMessage('Error');
    }
}

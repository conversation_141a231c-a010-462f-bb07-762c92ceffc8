<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Customer;

use SonVideo\Cms\FrontOffice\AppBundle\Manager\Customer\TokenManager as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;

/**
 * Class TokenManager
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Customer
 * <AUTHOR> <<EMAIL>>
 */
class TokenManager extends Test
{
    /**
     * getTestedInstance
     */
    private function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get('app.manager.token_manager');
    }

    /**
     * testIsValidTrue
     */
    public function testIsValidTrue(): void
    {
        $this->mockRpcClient(['valid' => true]);

        $this->assert('Test token validation')
            ->given($res = $this->getTestedInstance()->isValid('my-token'))
            ->boolean($res)
            ->isTrue();
    }

    /**
     * testIsValidFalse
     */
    public function testIsValidFalse(): void
    {
        $this->mockRpcClient(['valid' => false]);

        $this->assert('Test token validation')
            ->given($res = $this->getTestedInstance()->isValid('my-token'))
            ->boolean($res)
            ->isFalse();
    }

    /**
     * testIsValidFalse2
     */
    public function testIsValidFalse2(): void
    {
        $this->mockRpcClient(['whatever' => true]);

        $this->assert('Test token validation')
            ->given($res = $this->getTestedInstance()->isValid('my-token'))
            ->boolean($res)
            ->isFalse();
    }
}

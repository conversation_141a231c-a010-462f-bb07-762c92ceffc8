<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\CustomerService;

use SonVideo\Cms\FrontOffice\AppBundle\Exception\ValidatorException;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\CustomerService\ImmediateRecallManager as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Acceptance\Mock\Client\AxialysApiClientMock;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class ImmediateRecallManager
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager
 */
class ImmediateRecallManager extends PommTest
{
    /**
     * getTestedInstance
     *
     *
     */
    private function getTestedInstance(array $result = [], string $raw_result = ''): TestedClass
    {
        $mock = new AxialysApiClientMock($result, $raw_result);
        return new TestedClass(
            $this->getContainer()->get('pomm.default_session'),
            $this->getContainer()->get('app.validation.validator'),
            $mock,
            $this->getContainer()->get('logger'),
        );
    }

    /**
     * testIsActive
     */
    public function testIsActive(): void
    {
        $this->assert('Service is inactive on french holidays')
            ->when($is_active = $this->getTestedInstance()->isActive('01-05-2017'))
            ->then()
            ->boolean($is_active)
            ->isFalse()
            ->assert('Service is inactive out of working hours')
            ->when($is_active = $this->getTestedInstance()->isActive('31-05-2021 08:59:59'))
            ->then()
            ->boolean($is_active)
            ->isFalse()
            ->when($is_active = $this->getTestedInstance()->isActive('31-05-2021 18:01:00'))
            ->then()
            ->boolean($is_active)
            ->isFalse()

            ->assert('Service is inactive out of working days')
            ->when($is_active = $this->getTestedInstance()->isActive('30-05-2021 10:59:59'))
            ->then()
            ->boolean($is_active)
            ->isFalse()

            ->assert('Service is active on weekday')
            ->when($is_active = $this->getTestedInstance()->isActive('31-05-2021 10:25:59'))
            ->then()
            ->boolean($is_active)
            ->isTrue()

            ->assert('Service is active on weekday in afternoon')
            ->when($is_active = $this->getTestedInstance()->isActive('31-05-2021 15:59:59'))
            ->then()
            ->boolean($is_active)
            ->isTrue()

            ->assert('Service is inactive out of working hours')
            ->when($is_active = $this->getTestedInstance()->isActive('04-06-2021 09:00:59'))
            ->then()
            ->boolean($is_active)
            ->isFalse()

            ->assert('Service is active on weekday in afternoon')
            ->when($is_active = $this->getTestedInstance()->isActive('04-06-2021 18:25:59'))
            ->then()
            ->boolean($is_active)
            ->isTrue()

            ->assert('Service is active on saturday')
            ->when($is_active = $this->getTestedInstance()->isActive('29-05-2021 10:59:59'))
            ->then()
            ->boolean($is_active)
            ->isTrue();
    }

    /**
     * testGetEligibleCountries
     */
    public function testGetEligibleCountries(): void
    {
        $this->assert('Get eligible countries for immediate recall (with fixtures country list)')
            ->when($countries = $this->getTestedInstance()->getEligibleCountries())
            ->then()
            ->array($countries)
            ->hasSize(5);
    }

    /**
     * testGetEligibleCountries
     */
    public function testRecallWith(): void
    {
        $this->assert('Provided data failed validation')
            ->when($tested_instance = $this->getTestedInstance())
            ->exception(function () use ($tested_instance): void {
                $tested_instance->recallWith([]);
            })
            ->isInstanceOf(ValidatorException::class)

            ->assert('Failed to retrieve popup data from axialys')
            ->when($tested_instance = $this->getTestedInstance())
            ->exception(function () use ($tested_instance): void {
                $tested_instance->recallWith([
                    'email' => '<EMAIL>',
                    'phone' => '0153403020',
                    'country_code' => 'FR',
                ]);
            })
            ->isInstanceOf(\UnexpectedValueException::class)
            ->hasMessage('Could not retrieve popup from Axialys service')

            ->assert('Failed to retrieve popup data from axialys')
            ->when(
                $tested_instance = $this->getTestedInstance(
                    [
                        0 => [
                            'id_popup' => 123,
                        ],
                    ],
                    'NOK',
                ),
            )
            ->exception(function () use ($tested_instance): void {
                $tested_instance->recallWith([
                    'email' => '<EMAIL>',
                    'phone' => '0153403020',
                    'country_code' => 'FR',
                ]);
            })
            ->isInstanceOf(\ErrorException::class)
            ->hasMessage('Axialys popup call returned an invalid response : NOK')

            ->assert('Successfully launch the call me back request')
            ->when(
                $tested_instance = $this->getTestedInstance(
                    [
                        0 => [
                            'id_popup' => 123,
                        ],
                    ],
                    'OK',
                ),
            )
            ->if(
                $succeeded = $tested_instance->recallWith([
                    'email' => '<EMAIL>',
                    'phone' => '0153403020',
                    'country_code' => 'FR',
                ]),
            )
            ->then()
            ->boolean($succeeded)
            ->isTrue();
    }
}

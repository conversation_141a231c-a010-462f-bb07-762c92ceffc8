<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2015 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Stand;

use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Manager\Stand\StandFetcher as TestedClass;

/**
 * Class CategoryFetcher
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\Stand
 */
class StandFetcher extends PommTest
{
    /**
     * getTestedInstance
     */
    protected function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get('app.manager.stand.stand_fetcher');
    }

    /**
     * testGetVideoProjectorStandHierarchy
     */
    public function testGetVideoProjectorStandHierarchy(): void
    {
        $this->assert('Test category hierarchy for video projector')
            ->given($res = $this->getTestedInstance()->getVideoProjectorStandHierarchy('fr'))
            ->array($res)
            ->string($res[0]['name'])
            ->isEqualTo('Télévision')
            ->string($res[1]['name'])
            ->isEqualTo('Vidéoprojection')
            ->string($res[2]['name'])
            ->isEqualTo('Vidéoprojecteurs');
    }
}

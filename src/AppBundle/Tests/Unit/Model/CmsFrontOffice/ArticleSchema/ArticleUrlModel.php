<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\ArticleSchema;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Article;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleModel as TestedArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleUrl;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleUrlModel as TestedModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * ArticleUrlModel
 *
 * Test class for ArticleUrlModel
 *
 * @package     CMS Front-Office
 * @copyright   2016 Son Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 */
class ArticleUrlModel extends PommTest
{
    /**
     * getModel
     */
    protected function getModel(): TestedModel
    {
        return $this->getPommSession()->getModel(TestedModel::class);
    }

    /**
     * testFetchRedirectInformation
     */
    public function testFetchRedirectInformation(): void
    {
        $this->assert('Check redirection from old url')
            ->given(
                $redirect = $this->getModel()->fetchRedirectInformation(
                    '/article/old-url-elipson-prestige-4i-calvados-fr',
                ),
            )
            ->object($redirect)
            ->isInstanceOf(ArticleUrl::class)
            ->array($redirect->extract())
            ->isNotEmpty()
            ->integer['article_id']->isEqualTo(123456)
            ->string['article_url']->isEqualTo('/article/elipson-prestige-4i-calvados-fr')
            ->assert('Check that url of redirection is equals to the current article url')
            ->given(
                $article = $this->getPommSession()
                    ->getModel(TestedArticleModel::class)
                    ->findByUriAndCulture($redirect['article_url'], 'fr'),
            )
            ->object($article)
            ->isInstanceOf(Article::class)
            ->array($article->extract())
            ->isNotEmpty()
            ->assert('Test with invalid url')
            ->given($redirect = $this->getModel()->fetchRedirectInformation('invalid-url.html'))
            ->variable($redirect)
            ->isNull();
    }
}

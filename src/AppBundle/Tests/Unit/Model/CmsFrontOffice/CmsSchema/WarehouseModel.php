<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CmsSchema;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CmsSchema\WarehouseModel as TestedModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class WarehouseModel
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CmsSchema
 */
class WarehouseModel extends PommTest
{
    /**
     * getModel
     */
    protected function getModel(): TestedModel
    {
        return $this->getPommSession()->getModel(TestedModel::class);
    }

    /**
     * testFindByShipmentMethodId
     */
    public function testFindByShipmentMethodId(): void
    {
        $this->assert('Find a warehouse by its shipment method id')
            ->given($res = $this->getModel()->findByShipmentMethodId(37))
            ->array($res)
            ->integer(count($res))
            ->isEqualTo(1);
    }
}

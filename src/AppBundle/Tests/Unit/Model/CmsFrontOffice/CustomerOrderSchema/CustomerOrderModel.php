<?php
/*
 * This file is part of CMS BackOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerOrderSchema;

use PommProject\Foundation\Pager;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrder;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\QuoteModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrderModel as TestedModel;

/**
 * Class CustomerOrderModel
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerOrderSchema
 * @copyright 2017 Son-Video Distribution
 * <AUTHOR> <<EMAIL>>
 */
class CustomerOrderModel extends PommTest
{
    /**
     * getModel
     */
    protected function getModel(): TestedModel
    {
        return $this->getPommSession()->getModel(TestedModel::class);
    }

    /**
     * testFindLastAwaitingPayment
     */
    public function testFindLastAwaitingPayment(): void
    {
        $this->assert('Find last order awaiting payment for given customer and order_id')
            ->given($order = $this->getModel()->findLastAwaitingPayment(24))
            ->variable($order)
            ->isNotNull()

            ->assert('User should not have any order awaiting payment')
            ->given($order = $this->getModel()->findLastAwaitingPayment(1))
            ->variable($order)
            ->isNull();
    }

    /**
     * testFindCompleteByID
     */
    public function testFindCompleteByID(): void
    {
        $this->assert('Find complete order by order_id')
            ->given($order = $this->getModel()->findCompleteByIdForCustomer(1_268_510, 24))
            ->object($order)
            ->isInstanceOf(CustomerOrder::class);

        $this->array($order->get('delivery_notes'))->hasSize(1);

        $this->array($order->get('credit_notes'))->hasSize(1);

        $this->array($order->get('return_notes'))->hasSize(1);

        $this->array($order->get('invoices'))->hasSize(2);

        $this->array($order->get('messages'))->hasSize(3);

        $this->assert('Find complete order by order_id with no invoices, credit notes, delivery notes nor return notes')
            ->given($order = $this->getModel()->findCompleteByIdForCustomer(1_304_980, 24))
            ->object($order)
            ->isInstanceOf(CustomerOrder::class);

        $this->variable($order->get('delivery_notes'))->isNull();

        $this->variable($order->get('credit_notes'))->isNull();

        $this->variable($order->get('return_notes'))->isNull();

        $this->variable($order->get('invoices'))->isNull();

        $this->variable($order->get('messages'))->isNull();

        $this->assert('Find no order with the supplied order_id')
            ->given($order = $this->getModel()->findCompleteByIdForCustomer(1_268_510, 970481))
            ->variable($order)
            ->isNull();

        $this->assert('Computed product has all required keys')
            ->given($order = $this->getModel()->findCompleteByIdForCustomer(1_225_131, 123444))
            ->then()
            ->array($order->get('products')[0])
            ->hasKeys([
                'sku',
                'article_id',
                'quantity',
                'total_amount',
                'basket_description',
                'amount_extension_warranty',
                'amount_theft_break_extension_warranty',
                'article_name',
                'brand_name',
                'category_name',
                'selling_price',
            ]);
    }

    /**
     * testGetCompleteProducts
     */
    public function testGetCompleteProducts(): void
    {
        $this->assert('Test product informations for a customer order')
            ->given($res = $this->getModel()->getCompleteProducts(1_225_131))
            ->given($products = $res->extract())
            ->array($products[0])
            ->hasKeys([
                'sku',
                'quantity',
                'selling_price',
                'name',
                'brand_name',
                'category_name',
                'total_amount',
                'margin_rate',
                'ecotaxe',
                'sorecop',
                'amount_extension_warranty',
                'amount_theft_break_extension_warranty',
            ])
            ->string['sku']->isEqualTo('ELIPSPRESTIGE4I')
            ->integer['quantity']->isEqualTo(2)
            ->string['selling_price']->isEqualTo(599)
            ->integer['total_amount']->isEqualTo(1198)
            ->string['ecotaxe']->isEqualTo('0.3')
            ->string['sorecop']->isEqualTo('1')
            ->integer['amount_extension_warranty']->isEqualTo(0)
            ->integer['amount_theft_break_extension_warranty']->isEqualTo(0)
            ->float['margin_rate']->isEqualTo('0.27')
            ->string['name']->isEqualTo('Prestige 4i Calvados')
            ->string['brand_name']->isEqualTo('Elipson')
            ->string['category_name']->isEqualTo('Enceinte');

        $this->array($products[1])
            ->string['sku']->isEqualTo('TRIGAIAEZNY')
            ->integer['quantity']->isEqualTo(1)
            ->string['selling_price']->isEqualTo(1990)
            ->integer['total_amount']->isEqualTo(1990)
            ->string['ecotaxe']->isEqualTo('0.6')
            ->string['sorecop']->isEqualTo('0')
            ->integer['amount_extension_warranty']->isEqualTo(0)
            ->integer['amount_theft_break_extension_warranty']->isEqualTo(0)
            ->integer['margin_rate']->isEqualTo(0)
            ->string['name']->isEqualTo('Gaïa EZ Noyer')
            ->string['brand_name']->isEqualTo('Triangle')
            ->string['category_name']->isEqualTo('Enceinte colonne');
    }

    /**
     * testGetNumberOfCustomer
     */
    public function testGetNumberOfCustomer(): void
    {
        $this->assert('Return the number of unique customer with an order')
            ->given($number = $this->getModel()->getNumberOfCustomer())
            ->integer($number)
            ->isEqualTo(6);
    }

    /**
     * testDateOfCertifiedPurchase
     */
    public function testDateOfCertifiedPurchase(): void
    {
        $this->assert('Purchase is certified if customer an invoice containing the product')
            ->object($date = $this->getModel()->dateOfCertifiedPurchase(1, 94318))
            ->isInstanceOf(\DateTime::class);
        $this->assert('Date of invoice is used as proof')
            ->string($date->setTimezone(new \DateTimeZone('UTC'))->format(\DATE_ATOM))
            ->isEqualTo('2016-08-20T13:37:23+00:00');

        $this->assert('Purchase is certified if customer an invoice sharing the common content')
            ->object($date = $this->getModel()->dateOfCertifiedPurchase(1, 123459))
            ->isInstanceOf(\DateTime::class);

        $this->assert('Purchase is NOT certified if customer has never bought the article')
            ->variable($this->getModel()->dateOfCertifiedPurchase(1, 96234))
            ->isNull();

        $this->assert('Purchase is NOT certified if order has no invoice containing the product')
            ->variable($this->getModel()->dateOfCertifiedPurchase(123444, 123456))
            ->isNull();

        $this->variable($this->getModel()->dateOfCertifiedPurchase(123444, 78225))->isNull();

        $this->variable($this->getModel()->dateOfCertifiedPurchase(25, 112822))->isNull();
    }

    public function testGetCustomerOrderDetail(): void
    {
        $this->assert('retrieve customer order detail')
            ->given($order = $this->getModel()->getCustomerOrderDetail(1_814_657, 'fr'))
            ->array($order)
            ->keys->isEqualTo([
                'customer_order_id',
                'created_at',
                'customer_id',
                'modified_at',
                'billing_address',
                'shipping_address',
                'status',
                'total_price',
                'total_price_vat_excluded',
                'ecotax_price',
                'shipping_price',
                'payments',
                'shipment_method_id',
                'payment_agreement',
                'shipment_details',
                'is_pickup_store',
                'products',
                'invoices',
                'return_notes',
                'credit_notes',
            ]);

        $this->integer($order['customer_order_id'])->isEqualTo(1_814_657);

        $this->integer($order['customer_id'])->isEqualTo(24);

        $this->object($date = $order['created_at'])
            ->string($date->setTimezone(new \DateTimeZone('UTC'))->format(\DATE_ATOM))
            ->isEqualTo('2016-11-04T06:38:04+00:00');

        $this->object($date = $order['modified_at'])->isInstanceOf(\DateTime::class);

        $this->string($date->setTimezone(new \DateTimeZone('UTC'))->format(\DATE_ATOM))->isEqualTo(
            '2016-11-04T06:39:23+00:00',
        );

        $this->array($order['products'])
            ->hasSize(5)
            ->array($order['products'][0])
            ->hasKeys([
                'sku',
                'url',
                'quantity',
                'article_id',
                'total_amount',
                'media_variation',
                'customer_order_id',
                'basket_description',
                'media_variation_meta',
                'amount_extension_warranty',
                'duration_extension_warranty',
                'amount_theft_break_extension_warranty',
                'duration_theft_break_extension_warranty',
                'estimated_delivery_time',
                'parent_media_variation',
                'parent_media_variation_meta',
            ]);

        $this->array($order['invoices'])->hasSize(1);

        $this->array($order['return_notes'])->hasSize(0);

        $this->array($order['credit_notes'])->hasSize(0);

        $this->boolean($order['is_pickup_store'])->isEqualTo(false);
    }

    /**
     * testGetPaginatedCustomerOrders
     */
    public function testGetPaginatedCustomerOrders(): void
    {
        $this->assert('One customer order contains expected extra data')
            ->given($customer_orders = $this->getModel()->getPaginatedCustomerOrders(24, 1, 5))
            ->object($customer_orders)
            ->isInstanceOf(Pager::class);
        $this->integer($customer_orders->getCount())->isEqualTo(10);

        $this->object($customer_order = $customer_orders->getIterator()->get(2))->isInstanceOf(CustomerOrder::class);
        $this->integer($customer_order['customer_order_id'])->isEqualTo(1_305_004);
        $this->integer($customer_order['customer_id'])->isEqualTo(24);
        $this->string($customer_order['created_at'])->isEqualTo('2017-02-13T12:03:49+00:00');
        $this->string($customer_order['modified_at'])->isEqualTo('2017-02-13T12:03:49+00:00');
        $this->string($customer_order['status'])->isEqualTo('AWAITING_PAYMENT');
        $this->float($customer_order['total_price'])->isEqualTo(1000.0);
        $this->array($customer_order['products']);
        $this->array($customer_order['products'][0])->hasKeys([
            'sku',
            'quantity',
            'article_id',
            'total_amount',
            'basket_description',
            'amount_extension_warranty',
            'amount_theft_break_extension_warranty',
            'url',
            'media_variation',
            'media_variation_meta',
            'parent_media_variation',
            'parent_media_variation_meta',
        ]);

        $this->integer($customer_order['shipment_method_id'])->isEqualTo(3);
    }

    public function testDeduceAffiliation(): void
    {
        $customer_order_id = 1_268_510;

        $this->given($affiliation = $this->getModel()->deduceAffiliation($customer_order_id))
            ->assert('Deduce the tunnel v1 affiliation')
            ->string($affiliation)
            ->isEqualTo('son-video');

        $this->getModel()->updateByPk(
            ['customer_order_id' => $customer_order_id],
            [
                'checkout_version' => CustomerOrder::CHECKOUT_VERSION_v2,
            ],
        );
        $affiliation = $this->getModel()->deduceAffiliation($customer_order_id);

        $this->assert('Deduce the tunnel v2 affiliation without a quote')
            ->string($affiliation)
            ->isEqualTo('v2-son-video');

        $quote_id = 1;

        $this->getModel()->updateByPk(['customer_order_id' => $customer_order_id], ['quote_id' => $quote_id]);
        $affiliation = $this->getModel()->deduceAffiliation($customer_order_id);

        $this->assert('Deduce the tunnel v2 affiliation with a quote of type quotation')
            ->string($affiliation)
            ->isEqualTo('v2-devis');

        $this->getPommSession(PommTest::ADMIN_SESSION)
            ->getModel(QuoteModel::class)
            ->updateByPk(
                ['quote_id' => $quote_id],
                [
                    'type' => 'offer',
                ],
            );

        $affiliation = $this->getModel()->deduceAffiliation($customer_order_id);

        $this->assert('Deduce the tunnel v2 affiliation with a quote of type offer')
            ->string($affiliation)
            ->isEqualTo('v2-offre');
    }
}

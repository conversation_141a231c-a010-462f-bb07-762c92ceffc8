<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSchema;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Account;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountModel as TestedModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AuthenticationToken;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * AccountModel
 *
 * Test class for AccountModel
 *
 * @package     CMS Front-Office
 * @copyright   2016 Son Video Distribution
 * <AUTHOR> Audic <<EMAIL>>, <PERSON> <<EMAIL>>
 */
class AccountModel extends PommTest
{
    /**
     * getModel
     */
    protected function getModel(): TestedModel
    {
        return $this->getPommSession()->getModel(TestedModel::class);
    }

    /**
     * testAuthenticate
     */
    public function testAuthenticate(): void
    {
        $this->assert('Authenticate an existing customer')
            ->given($customer = $this->authenticateCustomer('<EMAIL>', '123456'))
            ->object($customer)
            ->isInstanceOf(Account::class)
            ->integer($customer->get('customer_id'))
            ->isEqualTo(24)
            ->string($customer->get('email'))
            ->isEqualTo('<EMAIL>')
            ->assert('No authentication for a customer with a bad password')
            ->given(
                $customer_bad_password = $this->authenticateCustomer(
                    '<EMAIL>',
                    'badpassword',
                ),
            )
            ->variable($customer_bad_password)
            ->isNull()
            ->assert('No authentication for a deactivate account')
            ->given($customer_deactivate = $this->authenticateCustomer('<EMAIL>', 'tennis'))
            ->variable($customer_deactivate)
            ->isNull();
    }

    /**
     * testAuthenticateWithToken
     */
    public function testAuthenticateWithToken(): void
    {
        $outdated_token = 'c19dc6823ba24e93daa505db47498b20';
        $valid_token = 'a803d25a46c8ea732ecb1525a3971c0f';
        $token_for_deactivate_user = '0b7bfae48d5cbf03396c35f17a40e620';

        $this->assert('Authenticate an existing customer with valid token')
            ->given(
                $customer = $this->consumeCustomerToken($valid_token, AuthenticationToken::TOKEN_PURPOSE_IMPERSONATION),
            )
            ->object($customer)
            ->isInstanceOf(Account::class)
            ->integer($customer->get('customer_id'))
            ->isEqualTo(24)
            ->string($customer->get('email'))
            ->isEqualTo('<EMAIL>')

            ->assert('No authentication for a customer with a bad token')
            ->given(
                $customer_bad_token = $this->consumeCustomerToken(
                    'NOT_VALID',
                    AuthenticationToken::TOKEN_PURPOSE_IMPERSONATION,
                ),
            )
            ->variable($customer_bad_token)
            ->isNull()

            ->assert('No authentication for a deactivate account')
            ->given(
                $customer = $this->consumeCustomerToken(
                    $token_for_deactivate_user,
                    AuthenticationToken::TOKEN_PURPOSE_IMPERSONATION,
                ),
            )
            ->variable($customer)
            ->isNull()

            ->assert('No authentication for a bad purpose')
            ->given($customer = $this->consumeCustomerToken($valid_token, '___NOT_VALID___'))
            ->variable($customer)
            ->isNull()

            ->assert('No authentication for an outdated token')
            ->given(
                $customer = $this->consumeCustomerToken(
                    $outdated_token,
                    AuthenticationToken::TOKEN_PURPOSE_IMPERSONATION,
                ),
            )
            ->variable($customer)
            ->isNull();
    }

    /**
     * testRefreshAccount
     */
    public function testRefreshAccount(): void
    {
        $this->assert('Authenticate a customer and check refresh of his account')
            ->given($account = $this->authenticateCustomer('<EMAIL>', '123456'))
            ->given($account_refresh = $this->getModel()->refreshAccount($account->getUserName()))
            ->object($account_refresh)
            ->isInstanceOf(Account::class)
            ->integer($account_refresh->get('customer_id'))
            ->isEqualTo($account->get('customer_id'))
            ->string($account_refresh->get('email'))
            ->isEqualTo($account->get('email'))
            ->array($account_refresh->get('roles'))
            ->isEqualTo($account->get('roles'))
            ->assert('No refresh for deactivate account')
            ->given($deactivate = $this->getModel()->refreshAccount('<EMAIL>'))
            ->variable($deactivate)
            ->isNull();
    }

    /**
     * authenticateCustomer
     *
     * @return  null|Account
     */
    protected function authenticateCustomer(string $email, string $password)
    {
        return $this->getModel()->authenticate($email, $password);
    }

    /**
     * consumeCustomerToken
     *
     * @return  null|Account
     */
    protected function consumeCustomerToken(string $token, string $purpose)
    {
        return $this->getModel()->consumeToken($token, $purpose);
    }
}

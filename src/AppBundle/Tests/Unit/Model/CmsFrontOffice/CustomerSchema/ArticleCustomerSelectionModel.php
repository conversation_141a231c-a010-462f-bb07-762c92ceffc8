<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Article;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\ArticleCustomerSelectionModel as TestedModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\CustomerSelectionModel as TestedCustomerSelectionModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\BasketLineModel as TestedBasketLineModel;

/**
 * ArticleCustomerSelectionModel
 *
 * Test class for ArticleCustomerSelectionModel
 *
 * @package     CMS Front-Office
 * @copyright   2016 Son Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 */
class ArticleCustomerSelectionModel extends PommTest
{
    /**
     * getModel
     */
    protected function getModel(): TestedModel
    {
        return $this->getPommSession()->getModel(TestedModel::class);
    }

    /**
     * testListArticles
     */
    public function testListArticles(): void
    {
        $this->assert('List articles selected by a customer')
            ->given($articles = $this->getModel()->listArticles(123456, 'ma liste de cadeaux', 'fr'))
            ->object($articles)
            ->isInstanceOf(CollectionIterator::class)
            ->array($articles_list = $articles->extract())
            ->isNotEmpty()
            ->assert('Check data of first article')
            ->array($articles_list[0])
            ->integer['article_id']->isEqualTo(123457)
            ->integer['quantity']->isEqualTo(2)
            ->string['article_name']->isEqualTo('super ampli')
            ->string['article_sku']->isEqualTo('BOI0W7PX1769-N65KTO-A')
            ->string['brand_name']->isEqualTo('Elipson')
            ->float['selling_price']->isEqualTo(200)
            ->float['full_selling_price']->isEqualTo(
                $articles_list[0]['selling_price'] * $articles_list[0]['quantity'] + 79.8,
            ) // 79.8 is the warranty
            ->float['ecotaxe']->isEqualTo(0.5)
            ->float['full_ecotaxe']->isEqualTo($articles_list[0]['ecotaxe'] * $articles_list[0]['quantity'])
            ->string['image_url']->isEqualTo(
                '/images/dynamic/Accessoires/articles/Stabren/STABRIND352535/Stabren-Industrie-35-25-35-la-piece-_P_140.jpg',
            )
            ->array['editorial_content']->isNotEmpty()
            ->array($articles_list[0]['warranties_in_cart'])
            ->hasSize(1)
            ->assert('Check that selection name and customer id is identical for all articles')
            ->array($articles_list[1])
            ->string['name']->isEqualTo($articles_list[0]['name'])
            ->integer['customer_id']->isEqualTo($articles_list[0]['customer_id'])
            ->assert('No results for a non existing selection')
            ->given($no_articles = $this->getModel()->listArticles(27, 'Not a real selection', 'fr'))
            ->object($no_articles)
            ->isInstanceOf(CollectionIterator::class)
            ->array($no_articles->extract())
            ->isEmpty();
    }

    /**
     * testMergeFromBasket
     *
     * @throws \Exception
     */
    public function testMergeFromBasket(): void
    {
        $this->assert('Merge articles from basket into selection')
            ->given($articles_src = $this->getBasketArticles(24))
            ->given($articles_dst = $this->getArticlesSelection(24, 'la sélection de Jack'))
            ->given(
                $merge = $this->getModel()->mergeFromBasket(
                    $this->getPommSession()
                        ->getModel(TestedCustomerSelectionModel::class)
                        ->findByPK(['customer_id' => 24, 'name' => 'la sélection de Jack']),
                ),
            )
            ->object($merge)
            ->isInstanceOf(CollectionIterator::class)
            ->given($articles_result = $this->getArticlesSelection(24, 'la sélection de Jack'))

            ->assert('Check name of merged selection')
            ->string($articles_result[0]['name'])
            ->isEqualTo('la sélection de Jack')

            ->assert('Check articles identifier values after merge')
            ->given($articles_id_src = array_column($articles_src, 'article_id'))
            ->given($articles_id_dst = array_column($articles_dst, 'article_id'))
            ->given($articles_id_result = array_column($articles_result, 'article_id'))
            ->array($articles_id_result)
            ->containsValues(array_unique([...$articles_id_src, ...$articles_id_dst]))

            ->assert('Check quantity for article 123457 which is presents in the basket and the selection')
            ->integer($articles_result[array_search('123457', $articles_id_result)]['quantity'])
            ->isEqualTo(
                $articles_src[array_search('123457', $articles_id_src)]['quantity'] +
                    $articles_dst[array_search('123457', $articles_id_dst)]['quantity'],
            );
    }

    /**
     * testMergeSelections
     */
    private function testMergeSelections(): void
    {
        $this->assert('Merge an existing cart with a non empty selection')
            ->given($articles_src = $this->getArticlesSelection(24, '.shopping_cart'))
            ->given($articles_dst = $this->getArticlesSelection(24, 'la sélection de Jack'))
            ->given($merge = $this->getModel()->mergeSelections(24, '.shopping_cart', 'la sélection de Jack'))
            ->object($merge)
            ->isInstanceOf(CollectionIterator::class)
            ->given($articles_result = $this->getArticlesSelection(24, 'la sélection de Jack'))
            ->assert('Check name of merged selection')
            ->string($articles_result[0]['name'])
            ->isEqualTo('la sélection de Jack')
            ->assert('Check articles identifier values after merge')
            ->given($articles_id_src = array_column($articles_src, 'article_id'))
            ->given($articles_id_dst = array_column($articles_dst, 'article_id'))
            ->given($articles_id_result = array_column($articles_result, 'article_id'))
            ->array($articles_id_result)
            ->containsValues(array_unique([...$articles_id_src, ...$articles_id_dst]))
            ->assert('Check quantity for article 123457 which is presents in the two selections')
            ->integer($articles_result[array_search('123457', $articles_id_result)]['quantity'])
            ->isEqualTo(
                $articles_src[array_search('123457', $articles_id_src)]['quantity'] +
                    $articles_dst[array_search('123457', $articles_id_dst)]['quantity'],
            );
    }

    /**
     * getArticlesSelection
     */
    protected function getArticlesSelection(int $customer_id, string $selection_name): array
    {
        return $this->getModel()
            ->findWhere('customer_id = $* AND name = $*', [$customer_id, $selection_name])
            ->extract();
    }

    /**
     * getBasketArticles
     */
    protected function getBasketArticles(int $customer_id): array
    {
        return $this->getPommSession()
            ->getModel(TestedBasketLineModel::class)
            ->getBasketables(new Article(), $customer_id)
            ->extract();
    }
}

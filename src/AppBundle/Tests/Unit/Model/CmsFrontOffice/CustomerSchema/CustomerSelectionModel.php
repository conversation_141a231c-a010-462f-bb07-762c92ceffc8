<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\CustomerSelection;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\CustomerSelectionModel as TestedModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\ArticleCustomerSelectionModel as TestedArticleCustomerSelectionModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * CustomerSelectionModel
 *
 * Test class for CustomerSelectionModel
 *
 * @package     CMS Front-Office
 * @copyright   2016 Son Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 */
class CustomerSelectionModel extends PommTest
{
    /**
     * getModel
     */
    protected function getModel(): TestedModel
    {
        return $this->getPommSession()->getModel(TestedModel::class);
    }

    /**
     * testListSelections
     */
    public function testListSelections(): void
    {
        $this->assert('List selections of a given customer')
            ->given($lists = $this->getModel()->listSelections(24, 'fr'))
            ->object($lists)
            ->isInstanceOf(CollectionIterator::class)
            ->array($full_lists = $lists->extract())
            ->hasSize(2)
            ->assert('Check first list data')
            ->array($full_lists[0])
            ->string['name']->isEqualTo('la sélection de Jack')
            ->integer['nb_articles']->isEqualTo(4)
            // 39.9*3 is for warranty
            ->float['amount']->isEqualTo(1199 + 39.9 * 3)
            ->assert('Check sum for the second selection')
            ->given(
                $selection = $this->getPommSession()
                    ->getModel(TestedArticleCustomerSelectionModel::class)
                    ->listArticles(24, 'ma sélection', 'fr')
                    ->extract(),
            )
            ->array($full_lists[1])
            ->integer['nb_articles']->isEqualTo(array_sum(array_column($selection, 'quantity')))
            ->float['amount']->isEqualTo(array_sum(array_column($selection, 'full_selling_price')))
            ->assert('Check result for a customer with no selections')
            ->given($no_selections = $this->getModel()->listSelections('123457', 'fr'))
            ->object($no_selections)
            ->isInstanceOf(CollectionIterator::class)
            ->array($no_selections->extract())
            ->isEmpty();
    }

    /**
     * testCreateNewSelection
     */
    public function testCreateNewSelection(): void
    {
        $this->assert('Create a new selection')
            ->given($new_selection = $this->getModel()->createNewSelection('Ma nouvelle sélection', 24))
            ->object($new_selection)
            ->isInstanceOf(CustomerSelection::class)
            ->array($selection = $new_selection->extract())
            ->string['name']->isEqualTo('Ma nouvelle sélection')
            ->integer['customer_id']->isEqualTo(24)
            ->string['slug']->isEqualTo('ma-nouvelle-selection')
            ->dateTime['created_at']->hasDate(date('Y'), date('m'), date('d'));
    }
}

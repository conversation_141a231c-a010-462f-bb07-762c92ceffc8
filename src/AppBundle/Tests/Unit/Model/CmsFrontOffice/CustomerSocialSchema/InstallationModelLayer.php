<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSocialSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\Installation;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\InstallationArticleModel as AppInstallationArticleModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\InstallationModelLayer as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class InstallationModelLayer
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSocialSchema
 * <AUTHOR> <<EMAIL>>
 */
class InstallationModelLayer extends PommTest
{
    /**
     * getTestedInstance
     */
    protected function getTestedInstance(): TestedClass
    {
        return $this->getPommSession()->getModelLayer(TestedClass::class);
    }

    /**
     * testCreate
     */
    public function testCreate(): void
    {
        $data = [
            'installation_type' => 'HOME_CINEMA',
            'comment' => 'Mon super commentaire !',
            'description' => 'Mon installation à 10 000€ qui va me rapporter 10 balles.',
            'budget' => 10000,
            'surface_area_m2' => 50,
            'medias' => [],
            'articles' => json_encode([
                [
                    'article_id' => 123456,
                    'name' => null,
                    'quantity' => 12,
                ],
                [
                    'article_id' => null,
                    'name' => 'Cafetière What Else',
                    'quantity' => 1,
                ],
            ]),
        ];

        $this->assert('Create an installation with articles')
            ->given($res = $this->getTestedInstance()->create(24, $data))
            ->object($res)
            ->isInstanceOf(Installation::class)
            ->integer($installation_id = $res->get('installation_id'))
            ->string($res->get('installation_type'))
            ->isEqualTo($data['installation_type'])
            ->string($res->get('comment'))
            ->isEqualTo($data['comment'])
            ->string($res->get('description'))
            ->isEqualTo($data['description'])
            ->integer($res->get('budget'))
            ->isEqualTo($data['budget'])
            ->integer($res->get('surface_area_m2'))
            ->isEqualTo($data['surface_area_m2'])
            ->array($res->get('medias'))
            ->isEqualTo($data['medias'])

            ->assert('Check the articles of the created installation.')
            ->given($articles = $this->getInstallationArticles($installation_id))
            ->integer($articles->count())
            ->isEqualTo(is_countable(json_decode($data['articles'], true, 512, JSON_THROW_ON_ERROR)) ? count(json_decode($data['articles'], true, 512, JSON_THROW_ON_ERROR)) : 0);
    }

    /**
     * getInstallationArticles
     */
    private function getInstallationArticles(int $installation_id): CollectionIterator
    {
        return $this->getPommSession()
            ->getModel(AppInstallationArticleModel::class)
            ->findWhere('installation_id = $*', [$installation_id]);
    }
}

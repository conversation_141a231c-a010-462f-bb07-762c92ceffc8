<?php
/*
 * This file is part of CMS BackOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerSocialSchema;

use PommProject\Foundation\Pager;
use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\SiteReview;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSocialSchema\SiteReviewModel as TestedModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class CustomerOrderModel
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\CustomerOrderSchema
 * @copyright 2017 Son-Video Distribution
 * @engine inline
 */
class SiteReviewModel extends PommTest
{
    /**
     * getModel
     */
    protected function getModel(): TestedModel
    {
        return $this->getPommSession()->getModel(TestedModel::class);
    }

    /**
     * testGetAverageScore
     */
    public function testGetAverageScore(): void
    {
        $this->assert('Average score of reviews calculation is good')
            ->given($value = $this->getModel()->getAverageScore())
            ->float($value)
            ->isNearlyEqualTo(6.5);
    }

    /**
     * testRecommendationPercentage
     */
    public function testRecommendationPercentage(): void
    {
        $this->assert('Percent of recommendations calculation is good')
            ->given($value = $this->getModel()->getRecommendationPercentage())
            ->float($value)
            ->isNearlyEqualTo(50);
    }

    /**
     * testGetLatestReviews
     */
    public function testGetLatestReviews(): void
    {
        $this->assert('Return a pager of reviews ordered by creation date desc')
            ->given($reviews = $this->getModel()->getLatestReviews())
            ->object($reviews)
            ->isInstanceOf(Pager::class)
            ->integer($reviews->getCount())
            ->isEqualTo(2)
            ->given($current = $reviews->getIterator()->get(0))
            ->object($current)
            ->isInstanceOf(SiteReview::class)
            ->array($values = $current->extract())
            ->dateTime($values['created_at'])
            ->hasDate(2017, 8, 3)
            ->given($current = $reviews->getIterator()->get(1))
            ->array($values = $current->extract())
            ->dateTime($values['created_at'])
            ->hasDate(2017, 1, 1);
        $this->assert('A review contains name of its creator')
            ->given(
                $review = $reviews
                    ->getIterator()
                    ->get(0)
                    ->extract(),
            )
            ->string($review['customer_firstname'])
            ->isEqualTo('Jack')
            ->string($review['customer_lastname'])
            ->isEqualTo('Bauer');
        $this->assert('A review contains linked products review with expected data')
            ->array($review['product_reviews'])
            ->hasSize(2)
            ->string($review['product_reviews'][1]['article_name'])
            ->isEqualTo('Elipson Prestige 4i')
            ->string($review['product_reviews'][1]['article_url'])
            ->isEqualTo('/article/elipson-prestige-4i-calvados-fr')
            ->integer($review['product_reviews'][1]['score'])
            ->isEqualTo(5);
    }

    /**
     * testGetSimpleLatestReviews
     */
    public function testGetSimpleLatestReviews(): void
    {
        $this->assert('Return a collection of reviews ordered by creation date desc')
            ->given($reviews = $this->getModel()->getSimpleLatestReviews(2))
            ->object($reviews)
            ->isInstanceOf(CollectionIterator::class)
            ->integer($reviews->count())
            ->isEqualTo(2)
            ->given($data = $reviews->extract())
            ->array($values = $data[0])
            ->dateTime($values['created_at'])
            ->hasDate(2017, 8, 3)
            ->array($values = $data[1])
            ->dateTime($values['created_at'])
            ->hasDate(2017, 1, 1);
        $this->assert('A review contains name of its creator')
            ->given($review = $data[0])
            ->string($review['customer_firstname'])
            ->isEqualTo('Jack')
            ->string($review['customer_lastname'])
            ->isEqualTo('Bauer');
    }
}

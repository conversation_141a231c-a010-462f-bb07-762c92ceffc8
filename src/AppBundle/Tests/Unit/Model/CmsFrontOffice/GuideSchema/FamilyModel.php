<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\GuideSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\Family;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\FamilyModel as TestedModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class FamilyModel
 *
 * Test class for FamilyModel
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\GuideSchema
 * @copyright   2017 Son Video Distribution
 */
class FamilyModel extends PommTest
{
    /**
     * getModel
     */
    protected function getModel(): TestedModel
    {
        return $this->getPommSession()->getModel(TestedModel::class);
    }

    /**
     * testFindAllOrderedWithGuides
     */
    public function testFindAllOrderedWithGuides(): void
    {
        $this->assert('Retrieve a collection')
            ->given($families = $this->getModel()->findAllOrderedWithGuides('en'))
            ->object($families)
            ->isInstanceOf(CollectionIterator::class)
            ->assert('Collection contains only families having one or more guides, ordered by display_order')
            ->given($extracted = $families->extract())
            ->array($extracted)
            ->hasSize(4)
            ->string($extracted[0]['name'])
            ->isEqualTo('Vinyle')
            ->string($extracted[1]['name'])
            ->isEqualTo('High-fidelity')
            ->string($extracted[2]['name'])
            ->isEqualTo('Home-cinéma')
            ->string($extracted[3]['name'])
            ->isEqualTo('Décoration')
            ->assert('Guides for a family contains only the 5 more recent active guides, ordered desc')
            ->given($home_cinema_guides = $extracted[2]['guides'])
            ->array($home_cinema_guides)
            ->hasSize(5)
            // note: guide 01 has the more recent created_at, even if inserted before the others in database
            // and guide 06 is inactive
            ->string($home_cinema_guides[0]['name'])
            ->isEqualTo('Guide 01')
            ->string($home_cinema_guides[1]['name'])
            ->isEqualTo('Guide 08')
            ->string($home_cinema_guides[2]['name'])
            ->isEqualTo('Guide 07')
            ->string($home_cinema_guides[3]['name'])
            ->isEqualTo('Guide 05')
            ->string($home_cinema_guides[4]['name'])
            ->isEqualTo('Guide 04');
    }

    /**
     * testFindBySlugWithGuides
     */
    public function testFindBySlugWithGuides(): void
    {
        $this->assert('Return null when nothing found')
            ->given($family = $this->getModel()->findBySlugWithGuides('unknown', 'en'))
            ->variable($family)
            ->isNull()

            ->assert('Return a Family')
            ->given($family = $this->getModel()->findBySlugWithGuides('home-cinema', 'en'))
            ->object($family)
            ->isInstanceOf(Family::class)

            ->assert('Has i18n data')
            ->given($family_data = $family->extract())
            ->string($family_data['name'])
            ->isEqualTo('Home-cinéma')
            ->string($family_data['title'])
            ->isEqualTo('Tous les guides home cinéma')
            ->array($family_data['keywords'])
            ->isEmpty()
            ->string($family_data['description'])
            ->isEqualTo('Description des guides home cinéma')

            ->assert('Linked active guides are retrieved in order created_at desc')
            ->given($guides = $family_data['guides'])
            ->array($guides)
            ->hasSize(7)
            ->string($guides[0]['name'])
            ->isEqualTo('Guide 01')
            ->string($guides[1]['name'])
            ->isEqualTo('Guide 08')
            ->string($guides[2]['name'])
            ->isEqualTo('Guide 07')
            ->string($guides[3]['name'])
            ->isEqualTo('Guide 05')
            ->string($guides[4]['name'])
            ->isEqualTo('Guide 04')
            ->string($guides[5]['name'])
            ->isEqualTo('Guide 03')
            ->string($guides[6]['name'])
            ->isEqualTo('English guide 02')

            ->assert('A guide contains expected data')
            ->given($guide = $guides[0])
            ->array($guide)
            ->integer($guide['guide_id'])
            ->isEqualTo(1)
            ->string($guide['slug'])
            ->isEqualTo('guide-01')
            ->string($guide['thumbnail_image'])
            ->isEqualTo(
                '/images/illustration/guides/remplacer-la-membrane-haut-parleur/SVDGUI_201701-RemembranerHP_300x180.jpg',
            )
            ->string($guide['name'])
            ->isEqualTo('Guide 01')
            ->string($guide['resume'])
            ->isEqualTo('Le guide 01')
            ->boolean($guide['is_choice_guide'])
            ->isEqualTo(false)
            ->variable($guide['selection_id'])
            ->isNull()
            ->given($guide = $guides[6])
            ->string($guide['slug'])
            ->isEqualTo('guide-02')
            ->boolean($guide['is_choice_guide'])
            ->isEqualTo(true);
    }

    /**
     * testFindAllActiveWithI18n
     */
    public function testFindAllActiveWithI18n(): void
    {
        $this->assert('Retrieve a collection')
            ->given($families = $this->getModel()->findAllActiveWithI18n('en'))
            ->object($families)
            ->isInstanceOf(CollectionIterator::class)
            ->assert('Collection contains only families having one or more guides, ordered by display_order')
            ->given($extracted = $families->extract())
            ->array($extracted)
            ->hasSize(4)
            ->string($extracted[0]['name'])
            ->isEqualTo('Vinyle')
            ->string($extracted[1]['name'])
            ->isEqualTo('High-fidelity')
            ->string($extracted[2]['name'])
            ->isEqualTo('Home-cinéma')
            ->string($extracted[3]['name'])
            ->isEqualTo('Décoration');
    }

    /**
     * testFindBySlugWithLimitedGuides
     */
    public function testFindBySlugWithLimitedGuides(): void
    {
        $this->assert('Test get all guides')
            ->given($family = $this->getModel()->findBySlugWithLimitedGuides('home-cinema', 1, 'fr'))
            ->object($family)
            ->isInstanceOf(Family::class)
            ->integer($family->get('family_id'))
            ->isEqualTo(1)
            ->string($family->get('name'))
            ->isEqualTo('Home-cinéma')
            ->given($guides = $family->get('guides'))
            ->array($guides)
            ->hasSize(6)
            ->array(array_column($guides, 'guide_id'))
            ->notContains(1)
            ->assert('Guides linked to a stand by guide_stand must have a canonical_stand_slug')
            ->array($guides[5])
            ->hasKey('canonical_stand_slug')
            ->string($guides[5]['canonical_stand_slug'])
            ->isEqualTo('haute-fidelite/amplis-hi-fi-stereo')

            ->assert('Test get 3 guides')
            ->given($family = $this->getModel()->findBySlugWithLimitedGuides('home-cinema', 1, 'fr', 3))
            ->object($family)
            ->isInstanceOf(Family::class)
            ->given($guides = $family->get('guides'))
            ->array($guides)
            ->hasSize(3)
            ->string($guides[0]['slug'])
            ->isEqualTo('guide-08')
            ->string($guides[0]['name'])
            ->isEqualTo('Guide 08')
            ->string($guides[1]['slug'])
            ->isEqualTo('guide-07')
            ->string($guides[1]['name'])
            ->isEqualTo('Guide 07')
            ->string($guides[2]['slug'])
            ->isEqualTo('guide-05')
            ->string($guides[2]['name'])
            ->isEqualTo('Guide 05')

            ->assert('Test get guides from en culture')
            ->given($family = $this->getModel()->findBySlugWithLimitedGuides('haute-fidelite', 1, 'en'))
            ->object($family)
            ->isInstanceOf(Family::class)
            ->integer($family->get('family_id'))
            ->isEqualTo(2)
            ->string($family->get('name'))
            ->isEqualTo('High-fidelity')
            ->given($guides = $family->get('guides'))
            ->array($guides)
            ->hasSize(2)
            ->string($guides[0]['slug'])
            ->isEqualTo('guide-03')
            ->string($guides[0]['name'])
            ->isEqualTo('Guide 03')
            ->string($guides[1]['slug'])
            ->isEqualTo('guide-02')
            ->string($guides[1]['name'])
            ->isEqualTo('English guide 02')

            ->assert('Test get guides from a family without the right culture entry')
            ->given($family = $this->getModel()->findBySlugWithLimitedGuides('decoration', 12, 'en'))
            ->object($family)
            ->isInstanceOf(Family::class)
            ->integer($family->get('family_id'))
            ->isEqualTo(5)
            ->string($family->get('name'))
            ->isEqualTo('Décoration');
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\GuideSchema;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\StandAssociatedGuideModel as TestedClass;

/**
 * Class StandAssociatedGuideModel
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\GuideSchema
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class StandAssociatedGuideModel extends PommTest
{
    /**
     * getTestedInstance
     */
    private function getTestedInstance(): TestedClass
    {
        return $this->getPommSession()->getModel(TestedClass::class);
    }

    /**
     * testGetGuidesAssociations
     */
    public function testGetGuidesAssociations(): void
    {
        $this->assert('Fetch guide for a stand having associations.')
            ->given($stands = $this->getTestedInstance()->getGuidesAssociations(13, 'fr'))
            ->object($stands)
            ->isInstanceOf(CollectionIterator::class)
            ->integer($stands->count())
            ->isGreaterThan(0)
            ->array(array_column($stands->extract(), 'guide_id'))
            ->notContains(6) // inactive guide

            ->assert('Fetch same guide in english returns the same result count.')
            ->given($stands_en = $this->getTestedInstance()->getGuidesAssociations(13, 'en'))
            ->object($stands_en)
            ->isInstanceOf(CollectionIterator::class)
            ->integer($stands_en->count())
            ->isEqualTo($stands->count())

            ->assert('Fetch guide for a stand which doesn\'t have associations.')
            ->given($stands = $this->getTestedInstance()->getGuidesAssociations(5, 'fr'))
            ->object($stands)
            ->isInstanceOf(CollectionIterator::class)
            ->integer($stands->count())
            ->isEqualTo(0)

            ->assert('Fetch guide for an unknown stand.')
            ->given($stands = $this->getTestedInstance()->getGuidesAssociations(541_614_316, 'fr'))
            ->object($stands)
            ->isInstanceOf(CollectionIterator::class)
            ->integer($stands->count())
            ->isEqualTo(0);
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\HomeSchema;

use PommProject\Foundation\ConvertedResultIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ArticleMediaModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ProductModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\HomeSchema\StandBestSellerModel as TestedClass;

/**
 * Class StandHomeSeller
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\HomeSchema
 * <AUTHOR> <<EMAIL>>
 */
class StandBestSellerModel extends PommTest
{
    /**
     * getTestedInstance
     */
    private function getTestedInstance(): TestedClass
    {
        return $this->getPommSession()->getModel(TestedClass::class);
    }

    /**
     * testFetchHomeBestSellerArticles
     */
    public function testFetchHomeBestSellerArticles(): void
    {
        $this->assert('Find best seller for home page.')
            ->given(
                $this->getPommSession(PommTest::ADMIN_SESSION)
                    ->getModel(ArticleMediaModel::class)
                    ->updateByPk(
                        ['media_id' => '7151cbf9-fd07-4c61-9664-1a18281bd9c0'],
                        [
                            'media_variation' => json_decode(
                                '{
    "image": {
      "referential": {
        "95": "/images/dynamic/Baladeurs/articles/Fiio/FIIOX5IIGD/Fiio-X5-II-Gold_P_95.jpg",
        "140": "/images/dynamic/Baladeurs/articles/Fiio/FIIOX5IIGD/Fiio-X5-II-Gold_P_140.jpg",
        "260": "/images/dynamic/Baladeurs/articles/Fiio/FIIOX5IIGD/Fiio-X5-II-Gold_P_260.jpg",
        "600": "/images/dynamic/Baladeurs/articles/Fiio/FIIOX5IIGD/Fiio-X5-II-Gold_P_600.jpg",
        "300_square": "/images/dynamic/Baladeurs/articles/Fiio/FIIOX5IIGD/Fiio-X5-II-Gold_P_300_square.jpg"
      },
      "largest": "600"
    }
  }',
                                false,
                                512,
                                JSON_THROW_ON_ERROR,
                            ),
                        ],
                    ),
            )

            ->when($articles = $this->getTestedInstance()->fetchHomeBestSellerArticles('fr'))

            ->object($articles)
            ->isInstanceOf(ConvertedResultIterator::class)
            ->array($array = $articles->extract())
            ->array(array_column($array, 'article_id'))
            ->containsValues([123456])
            ->array(
                $article = array_reduce($array, fn($carry, $item) => $item['article_id'] === 123456 ? $item : $carry),
            )
            ->hasKeys(['highlights'])
            ->hasKeys(['note'])
            ->integer($article['article_id'])
            ->isEqualTo(123456)
            ->string($article['article_url'])
            ->isEqualTo('/article/elipson-prestige-4i-calvados-fr')
            ->string($article['brand_name'])
            ->isEqualTo('Elipson')
            ->array($article['editorial_content'])
            ->string($article['cc_short_description'])
            ->array($article['price'])
            ->float($article['price']['selling_price'])
            ->isEqualTo(599)
            ->string($article['image_url'])
            ->isEqualTo('/images/dynamic/Baladeurs/articles/Fiio/FIIOX5IIGD/Fiio-X5-II-Gold_P_600.jpg')
            ->array($article['media_meta'])
            ->string($article['stand_slug'])
            ->isEqualTo('haute-fidelite/amplis-home-cinema')
            ->string($article['stand_name'])
            ->isEqualTo('Amplis home-cinéma')
            ->array($highlight = $article['highlights'])
            ->hasSize(1)
            ->string($highlight[0]['tag_path'])
            ->isEqualTo('article.highlight.exclusive');

        $this->assert('Find best sellers after invoiced quantity changes.')
            ->given(
                $this->getPommSession(PommTest::ADMIN_SESSION)
                    ->getModel(ProductModel::class)
                    ->updateByPk(['sku' => 'ELIPSPRESTIGE4I'], ['informational_stock' => 0]),
            )

            ->when($articles = $this->getTestedInstance()->fetchHomeBestSellerArticles('fr'))

            ->array($array = $articles->extract())
            ->array(array_column($array, 'article_id'))
            ->containsValues([104130]);

        $this->assert('Find best sellers after availability changes.')
            ->given(
                $this->getPommSession(PommTest::ADMIN_SESSION)
                    ->getModel(ProductModel::class)
                    ->updateByPk(['sku' => 'BENQW2000'], ['estimated_delivery_time' => 'AVAILABILITY_3_5_DAYS']),
            )

            ->when($articles = $this->getTestedInstance()->fetchHomeBestSellerArticles('fr'))

            ->array($array = $articles->extract())
            ->array(array_column($array, 'article_id'))
            ->isEmpty();
    }
}

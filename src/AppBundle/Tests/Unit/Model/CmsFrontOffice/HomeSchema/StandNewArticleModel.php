<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\HomeSchema;

use PommProject\Foundation\ConvertedResultIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\HomeSchema\StandNewArticleModel as TestedClass;

class StandNewArticleModel extends PommTest
{
    /**
     * getTestedInstance
     */
    private function getTestedInstance(): TestedClass
    {
        return $this->getPommSession()->getModel(TestedClass::class);
    }

    /**
     * testFetchHomeNewArticles
     */
    public function testFetchHomeNewArticles(): void
    {
        $this->assert('Find new articles for home page.')
            ->given($articles = $this->getTestedInstance()->fetchHomeNewArticles('fr'))
            ->object($articles)
            ->isInstanceOf(ConvertedResultIterator::class)
            ->array($array = $articles->extract())
            ->array(array_column($array, 'article_id'))
            ->containsValues([888888])
            ->array($article = $array[0])
            ->hasKeys(['highlights'])
            ->integer($article['article_id'])
            ->isEqualTo(888888)
            ->string($article['article_url'])
            ->isEqualTo('/article/enceintes-enceintes-enceintes-centrales/cabasse/mc170-socoa-noir-2020')
            ->string($article['brand_name'])
            ->isEqualTo('Audioquest')
            ->array($article['price'])
            ->integer($article['price']['selling_price'])
            ->isEqualTo(279)
            ->string($article['image_url'])
            ->isEqualTo('/images/article/cabasse/CABMC170SOCONB/mc170-socoa-noir-2020_5fa2aa7029793_1200.jpg')
            ->string($article['stand_slug'])
            ->isEqualTo('haute-fidelite/amplis-home-cinema')
            ->string($article['stand_name'])
            ->isEqualTo('Amplis home-cinéma')
            ->array($highlight = $article['highlights'])
            ->hasSize(1)
            ->string($highlight[0]['tag_path'])
            ->isEqualTo('article.highlight.new');
    }
}

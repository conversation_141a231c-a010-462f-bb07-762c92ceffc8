<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2015 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Model\CmsFrontOffice\TaxonomySchema;

use PommProject\Foundation\ConvertedResultIterator;
use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\Category;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\CategoryModel as TestedModel;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * CategoryModel
 *
 * Test class for CategoryModel
 *
 * @package     CMS Front-Office
 * @copyright   2016 Son Video Distribution
 * <AUTHOR> <<EMAIL>>
 */
class CategoryModel extends PommTest
{
    /**
     * getModel
     */
    protected function getModel(): TestedModel
    {
        return $this->getPommSession()->getModel(TestedModel::class);
    }

    /**
     * testFindByBrandIdAndCulture
     */
    public function testFindByBrandIdAndCulture(): void
    {
        $this->assert('Get categories with brand id and fr culture')
            ->given($collection = $this->getModel()->findByBrandIdAndCulture(343, 'fr'))
            ->object($collection)
            ->isInstanceOf(ConvertedResultIterator::class)
            ->given($categories = $collection->extract())
            ->array($categories)
            ->hasSize(2)
            ->given($category = $categories[0])
            ->array($category)
            ->hasSize(6)
            ->integer['category_id']->isEqualTo(1)
            ->string['name']->isEqualTo('Enceinte')
            ->string['slug']->isEqualTo('enceinte')
            ->integer['nb_articles']->isEqualTo(2)
            ->float['min_price']->isEqualTo(599)
            ->hasKey('articles')
            ->given($articles = $category['articles'])
            ->array($articles)
            ->hasSize(2)
            ->given($article = $articles[0])
            ->array($article)
            ->string['sku']->isEqualTo('ELIPSPRESTIGE4I')
            ->string['name']->isEqualTo('Prestige 4i Calvados')
            ->integer['article_id']->isEqualTo(123456);
        $this->given($price = $article['price'])
            ->array($price)
            ->float['selling_price']->isEqualTo(599)
            ->float['selling_price_vat_excluded']->isEqualTo(499.17)
            ->integer['started_at_price']->isEqualTo(1850)
            ->string['started_at_date']->isEqualTo('2011-01-01');
        $this->given($category = $categories[1])
            ->array($category)
            ->hasSize(6)
            ->integer['category_id']->isEqualTo(2)
            ->hasKey('articles')
            ->string['name']->isEqualTo('Enceinte encastrable')
            ->string['slug']->isEqualTo('enceinte-encastrable')
            ->integer['nb_articles']->isEqualTo(1)
            ->float['min_price']->isEqualTo(200);
        $this->given($articles = $category['articles'])
            ->array($articles)
            ->hasSize(1)
            ->given($article = $articles[0])
            ->array($article)
            ->string['sku']->isEqualTo('BOI0W7PX1769-N65KTO-A')
            ->string['name']->isEqualTo('super ampli')
            ->integer['article_id']->isEqualTo(123457);
        $this->assert('Get empty categories list with fr culture')
            ->given($collection_empty = $this->getModel()->findByBrandIdAndCulture(222, 'fr'))
            ->object($collection_empty)
            ->isInstanceOf(ConvertedResultIterator::class)
            ->given($empty_categories = $collection_empty->extract())
            ->array($empty_categories)
            ->isEmpty();

        $this->assert('Get brand categories with en product and without en categories')
            ->given($collection_en = $this->getModel()->findByBrandIdAndCulture(341, 'en'))
            ->object($collection_en)
            ->isInstanceOf(ConvertedResultIterator::class);
        $this->given($en_categories = $collection_en->extract())
            ->array($en_categories)
            ->hasSize(1);

        $this->assert('Get brand categories with en product and en categories')
            ->given($collection_en_en = $this->getModel()->findByBrandIdAndCulture(23, 'en'))
            ->object($collection_en_en)
            ->isInstanceOf(ConvertedResultIterator::class)
            ->given($en_en_categories = $collection_en_en->extract())
            ->array($en_en_categories)
            ->hasSize(1)
            ->given($en_en_category = $en_en_categories[0])
            ->array($en_en_category)
            ->hasSize(6)
            ->integer['category_id']->isEqualTo(5)
            ->hasKey('articles')
            ->string['name']->isEqualTo('Speaker')
            ->string['slug']->isEqualTo('speaker')
            ->integer['nb_articles']->isEqualTo(2)
            ->float['min_price']->isEqualTo(100);
        $this->given($article = $en_en_categories[0]['articles'])
            ->array($article)
            ->hasSize(2)
            ->given($collection_fr_BE = $this->getModel()->findByBrandIdAndCulture(23, 'fr'))
            ->object($collection_fr_BE)
            ->isInstanceOf(ConvertedResultIterator::class)
            ->given($fr_BE_categories = $collection_fr_BE->extract())
            ->array($fr_BE_categories)
            ->hasSize(3)
            ->given($category = $fr_BE_categories[0])
            ->array($category)
            ->hasSize(6)
            ->integer['category_id']->isEqualTo(3)
            ->hasKey('articles')
            ->string['name']->isEqualTo('Enceinte encastrable mini')
            ->string['slug']->isEqualTo('enceinte-encastrable-mini')
            ->integer['nb_articles']->isEqualTo(2)
            ->float['min_price']->isEqualTo(10);
        $this->given($articles = $category['articles'])
            ->array($articles)
            ->hasSize(2)
            ->given($article = $articles[array_search('L13C9S', array_column($articles, 'sku'))])
            ->array($article)
            ->string['sku']->isEqualTo('L13C9S')
            ->string['name']->isEqualTo('super enceinte')
            ->integer['article_id']->isEqualTo(123458);
        $this->given($price = $article['price'])
            ->array($price)
            ->float['selling_price']->isEqualTo(100)
            ->integer['selling_price_vat_excluded']->isEqualTo(80);
    }

    /**
     * testHighlightsInFindByBrandIdAndCulture
     *
     * Test the result contains articles with a key 'article_tag' that contains highlights.
     */
    public function testHighlightsInFindByBrandIdAndCulture(): void
    {
        $this->assert('There are highlights in article list.')
            ->given(
                $collection = $this->getModel()
                    ->findByBrandIdAndCulture(475, 'fr')
                    ->extract(),
            )
            ->and($category = $collection[0])
            ->and($articles = $category['articles'])
            ->array($articles)
            ->array($article_tags = array_column($articles, 'highlights'))
            ->string(json_encode($article_tags, JSON_THROW_ON_ERROR))
            ->contains('article.highlight.new')
            ->contains('article.highlight.on_offer')
            ->contains('article.highlight.pre_order')
            ->contains('article.highlight.exclusive');
    }

    /**
     * testFindSimpleByBrandIdAndCulture
     */
    public function testFindSimpleByBrandIdAndCulture(): void
    {
        $this->assert('Find categories with simple projection by brand and culture.')
            ->given($collection = $this->getModel()->findSimpleByBrandIdAndCulture(343, 'fr'))
            ->object($collection)
            ->isInstanceOf(CollectionIterator::class)
            ->integer($collection->count())
            ->isGreaterThan(0)
            ->object($category = $collection->current())
            ->isInstanceOf(Category::class)
            ->array($category->extract())
            ->keys->isEqualTo(['category_id', 'name', 'slug']);

        $this->assert('Find categories in english returns the same number of results.')
            ->given($collection_en = $this->getModel()->findSimpleByBrandIdAndCulture(343, 'en'))
            ->object($collection_en)
            ->isInstanceOf(CollectionIterator::class)
            ->integer($collection_en->count())
            ->isEqualTo($collection->count())
            ->object($category = $collection_en->current())
            ->isInstanceOf(Category::class)
            ->array($category->extract())
            ->keys->isEqualTo(['category_id', 'name', 'slug']);
    }

    /**
     * testGetAllSlug
     */
    public function testGetAllSlug(): void
    {
        $this->assert('Check method returns slugs.')
            ->given($res = $this->getModel()->getAllSlug())
            ->array($res)
            ->contains('enceinte')
            ->contains('enceinte-encastrable');
    }
}

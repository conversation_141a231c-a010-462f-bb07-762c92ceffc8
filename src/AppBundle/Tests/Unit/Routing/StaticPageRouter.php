<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Routing;

use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;
use Symfony\Component\Routing\Exception\RouteNotFoundException;
use Symfony\Component\Routing\RouteCollection;

/**
 * Class StaticPageRouter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Routing
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class StaticPageRouter extends PommTest
{
    /**
     * testMatchOk
     */
    public function testMatchOk(): void
    {
        $this->assert('Check if a route is found for an existing static page uri')
            ->given($static_page_router = $this->getContainer()->get('app.static_page_router'))
            ->given($uri = '/avis-facebook')
            ->given($res = $static_page_router->match($uri))
            ->array($res)
            ->hasKey('_controller')
            ->string($res['_controller'])
            ->contains('indexAction');
    }

    /**
     * testMatchFails
     */
    public function testMatchFails(): void
    {
        $this->assert('Check if an exception is thrown when an uri does not exist.')
            ->given($static_page_router = $this->getContainer()->get('app.static_page_router'))
            ->given($uri = '/avis-copains-d-avant')
            ->exception(function () use ($static_page_router, $uri): void {
                $static_page_router->match($uri);
            })
            ->isInstanceOf(ResourceNotFoundException::class);
    }

    /**
     * testGenerate
     */
    public function testGenerate(): void
    {
        $this->assert('Check if a generate always returns an exception.')
            ->given($static_page_router = $this->getContainer()->get('app.static_page_router'))
            ->exception(function () use ($static_page_router): void {
                $static_page_router->generate('whatever');
            })
            ->isInstanceOf(RouteNotFoundException::class);
    }

    /**
     * testGetRouteCollection
     */
    public function testGetRouteCollection(): void
    {
        $this->assert('Check if methods return good number of route.')
            ->given($static_page_router = $this->getContainer()->get('app.static_page_router'))
            ->given($collection = $static_page_router->getRouteCollection())
            ->object($collection)
            ->isInstanceOf(RouteCollection::class)
            ->integer(is_countable($collection) ? count($collection) : 0)
            ->isEqualTo(5);
    }
}

<?php
/*
 * This file is part of bo-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\SearchEngine\ContextFilter;

use atoum\atoum\test;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\ContextFilter\RangeContextFilter as TestedClass;

/**
 * Class RangeContextFilter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\SearchEngine\ContextFilter
 * <AUTHOR> <<EMAIL>>
 */
class RangeContextFilter extends test
{
    /**
     * testGetMinValue
     */
    public function testGetMinValue(): void
    {
        $this->assert('Test get min value.')
            ->given($filter = new TestedClass())
            ->and($filter->setMinValue(18.2))
            ->and($filter->setMaxValue(33.8))
            ->float($filter->getMinValue())
            ->isEqualTo(18.2);
    }

    /**
     * testGetMaxValue
     */
    public function testGetMaxValue(): void
    {
        $this->assert('Test get max value.')
            ->given($filter = new TestedClass())
            ->and($filter->setMinValue(18.2))
            ->and($filter->setMaxValue(33.8))
            ->float($filter->getMaxValue())
            ->isEqualTo(33.8);
    }

    /**
     * testJsonSerialize
     */
    public function testJsonSerialize(): void
    {
        $this->assert('Test jsonSerialize.')
            ->given($filter = new TestedClass())
            ->and($filter->setMinValue(18.2))
            ->and($filter->setMaxValue(33.8))
            ->array($array = $filter->jsonSerialize('price'))
            ->hasKeys(['price_min', 'price_max'])
            ->float($array['price_min'])
            ->isEqualTo(18.2)
            ->float($array['price_max'])
            ->isEqualTo(33.8);
    }
}

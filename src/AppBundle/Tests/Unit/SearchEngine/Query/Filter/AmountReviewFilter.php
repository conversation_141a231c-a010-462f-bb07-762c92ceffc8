<?php
/*
 * This file is part of bo-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\SearchEngine\Query\Filter;

use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\ContextFilter\RangeContextFilter;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\Filter\AmountReviewFilter as TestedInstance;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class AmountReviewFilter
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\SearchEngine\Query\Filter
 * <AUTHOR> <PERSON> <<EMAIL>>
 */
class AmountReviewFilter extends PommTest
{
    /**
     * getTestedInstance
     */
    protected function getTestedInstance(): TestedInstance
    {
        return new TestedInstance($this->getPommSession());
    }

    /**
     * getFilter
     */
    protected static function getFilter(float $min, float $max): RangeContextFilter
    {
        return (new RangeContextFilter())->setMinValue($min)->setMaxValue($max);
    }

    /**
     * testMakeQuery
     */
    public function testMakeQuery(): void
    {
        $this->assert(sprintf('Test %s query returns a string sql.', get_class($this)))
            ->given($where = new Where('true'))
            ->and($sql = $this->getTestedInstance()->makeQuery($where, static::getFilter(6, 99)))
            ->string($sql)

            ->assert('Test the query.')
            ->given(
                $iterator = $this->getPommSession()
                    ->getQueryManager()
                    ->query($sql, $where->getValues()),
            )
            ->array(array_column($iterator->extract(), 'article_id'))
            ->containsValues([123458, 123465, 123460, 123462])
            ->notContainsValues([123456]);
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\SearchEngine\Query;

use PommProject\Foundation\ResultIterator;
use PommProject\Foundation\Where;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Product;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\BoContext as AppBoContext;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Context as AppContext;
use SonVideo\Cms\FrontOffice\AppBundle\SearchEngine\Query\SearchQueryProvider as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class SearchQueryProvider
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\SearchEngine\Query
 * <AUTHOR> <<EMAIL>>
 */
class SearchQueryProvider extends PommTest
{
    /**
     * getTestedInstance
     */
    private function getTestedInstance(bool $search_only_basketable = true): TestedClass
    {
        return new TestedClass($this->getPommSession(), $search_only_basketable);
    }

    /**
     * getSearchResult
     */
    private function getSearchResult(
        string $culture,
        array $context,
        bool $search_only_basketable = true
    ): ResultIterator {
        $where = Where::create();
        $sql = $this->getTestedInstance($search_only_basketable)->getSearchQuery(
            new AppContext($context),
            $where,
            $culture,
        );

        return $this->getPommSession()
            ->getQueryManager()
            ->query($sql, $where->getValues());
    }

    /**
     * getBoSearchResult
     */
    private function getBoSearchResult(
        string $culture,
        array $context,
        bool $search_only_basketable = true
    ): ResultIterator {
        $where = Where::create();
        $sql = $this->getTestedInstance($search_only_basketable)->getSearchQuery(
            new AppBoContext($context),
            $where,
            $culture,
        );

        return $this->getPommSession()
            ->getQueryManager()
            ->query($sql, $where->getValues());
    }

    /**
     * testGetSearchQuery
     */
    public function testGetSearchQuery(): void
    {
        $this->assert('Fetching all articles from one brand.')
            ->given($collection = $this->getSearchResult('fr', ['brands' => ['cambridge']]))
            ->integer($collection->count())
            ->isEqualTo(7)
            ->array($articles_id = array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([123462, 123458])
            ->notContains(123461) // Article with null price
            ->assert('Ranking for articles in a specific brand must always be equal to 1')
            ->array(array_column($collection->extract(), 'ranking'))
            ->strictlyContainsValues([1])
            ->assert('Fetching article with category')
            ->given($collection = $this->getSearchResult('fr', ['category' => 'enceinte']))
            ->integer($collection->count())
            ->isEqualTo(14)
            ->array($articles_id = array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([94315, 94318, 96234, 98537, 101262, 123456, 123457, 123459, 123460, 123458])
            ->assert('Ranking for articles in a specific category must always be equal to 1')
            ->array(array_column($collection->extract(), 'ranking'))
            ->strictlyContainsValues([1])
            ->assert('Fetch articles with common contents')
            ->given($collection = $this->getSearchResult('fr', ['common_contents' => [123, 321, 37525, 36463]]))
            ->integer($collection->count())
            ->isEqualTo(9)
            ->array(array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([123458, 123456, 96234, 94318, 123457, 123459, 123462, 94315, 123460])
            ->assert('Fetching all articles does not return destock.')
            ->given($collection = $this->getSearchResult('fr', ['category' => 'enceinte-encastrable-mini']))
            ->integer($collection->count())
            ->isEqualTo(1)
            ->array($articles_id = array_column($collection->extract(), 'article_id'))
            ->notContains(123465);
    }

    /**
     * testGetSearchQueryWithUnbasketableArticles
     */
    public function testGetSearchQueryWithUnavailableArticles(): void
    {
        $this->assert('Fetching basketable and unbasketable articles with category')
            ->given($collection = $this->getSearchResult('fr', ['category' => 'enceinte-colonne'], false))
            ->integer($collection->count())
            ->isEqualTo(8)
            ->array($articles_id = array_column($collection->extract(), 'article_id'))
            ->contains(101263);
    }

    /**
     * testGetSearchQuery
     */
    public function testGetBoSearchQuery(): void
    {
        $this->assert('Fetching all articles from one brand.')
            ->given($collection = $this->getBoSearchResult('fr', ['brands' => ['cambridge']]))
            ->integer($collection->count())
            ->isEqualTo(7)
            ->array($articles_id = array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([123462, 123458])
            ->notContains(123461) // Article with null price
            ->assert('Fetching article with avaibility')
            ->given($collection = $this->getBoSearchResult('fr', ['is' => ['available']]))
            ->given($expected = $this->getAvailableArticleWithPrice())
            ->integer($collection->count())
            ->isEqualTo($expected->count())
            ->array($articles_id = array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues(array_column($expected->extract(), 'article_id'))
            ->assert('Fetching article with categories')
            ->given($collection1 = $this->getBoSearchResult('fr', ['categories' => ['enceinte-encastrable']]))
            ->integer($collection1->count())
            ->isGreaterThan(0)
            ->given($collection2 = $this->getBoSearchResult('fr', ['categories' => ['enceinte-colonne']]))
            ->integer($collection2->count())
            ->isGreaterThan(0)
            ->given(
                $collection3 = $this->getBoSearchResult('fr', [
                    'categories' => ['enceinte-encastrable', 'enceinte-colonne'],
                ]),
            )
            ->integer($collection3->count())
            ->isEqualTo($collection1->count() + $collection2->count())
            ->assert('Fetching article with skus')
            ->given($collection = $this->getBoSearchResult('fr', ['skus' => ['L13C9T', 'L13C9W']]))
            ->integer($collection->count())
            ->isEqualTo(1)
            ->array($articles_id = array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([123460])
            ->assert('Fetching article with price')
            ->given($collection = $this->getBoSearchResult('fr', ['price_min' => 3990]))
            ->integer($collection->count())
            ->isEqualTo(1)
            ->array($articles_id = array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([101262])
            ->given($collection = $this->getBoSearchResult('fr', ['price_min' => 3989, 'price_max' => 3991]))
            ->integer($collection->count())
            ->isEqualTo(1)
            ->array($articles_id = array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([101262])
            ->given($collection = $this->getBoSearchResult('fr', ['price_max' => 1]))
            ->integer($collection->count())
            ->isEqualTo(0)
            ->assert('Fetch articles with common content ids')
            ->given($collection = $this->getBoSearchResult('fr', ['common_contents' => [123, 321, 37525]]))
            ->integer($collection->count())
            ->isEqualTo(7)
            ->array(array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([123458, 123456, 96234, 123457, 123459, 123462, 123460])

            ->assert('Fetching article with review score and amount.')
            ->given($context = ['average_score_min' => 4.6, 'amount_reviews_min' => 5])
            ->and($collection = $this->getBoSearchResult('fr', $context))
            ->integer($collection->count())
            ->isEqualTo(3)
            ->array(array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([123460, 123458, 123462])
            ->given($context = ['average_score_min' => 1, 'amount_reviews_min' => 500])
            ->and($collection = $this->getBoSearchResult('fr', $context))
            ->integer($collection->count())
            ->isEqualTo(0)

            ->assert('Fetching article with review score.')
            ->given($context = ['average_score_min' => 4.8])
            ->and($collection = $this->getBoSearchResult('fr', $context))
            ->integer($collection->count())
            ->isEqualTo(1)
            ->array(array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([96234])
            ->given($context = ['average_score_min' => 5])
            ->and($collection = $this->getBoSearchResult('fr', $context))
            ->integer($collection->count())
            ->isEqualTo(0);
    }

    /**
     * testGetBoSearchQueryWithUnbasketableArticles
     *
     */
    public function testGetBoSearchQueryWithUnbasketableArticles(): void
    {
        $this->assert('Fetching article with skus with unbasketables')
            ->given($context = ['skus' => ['TRIELALN02BC', 'TRIELALN02BA']])
            ->and($collection = $this->getBoSearchResult('fr', $context, false))
            ->integer($collection->count())
            ->isEqualTo(2)
            ->array(array_column($collection->extract(), 'article_id'))
            ->strictlyContainsValues([101262, 101263]);
    }

    /**
     * getAvailableArticleWithPrice
     */
    private function getAvailableArticleWithPrice(): ResultIterator
    {
        $sql = <<<SQL
        SELECT
         a.article_id
        FROM article.article a
          JOIN article.product p USING (sku)
        WHERE
          p.estimated_delivery_time = $* AND a.unbasketable_reason IS NULL AND price IS NOT NULL AND price ? 'selling_price'
        SQL;

        return $this->getPommSession()
            ->getQueryManager()
            ->query($sql, [Product::AVAILABILITY_1_2_DAYS]);
    }
}

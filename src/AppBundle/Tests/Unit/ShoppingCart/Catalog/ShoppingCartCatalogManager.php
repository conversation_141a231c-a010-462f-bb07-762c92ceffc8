<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\ShoppingCart\Catalog;

use Psr\Log\NullLogger;
use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Catalog\CatalogInBasket as AppCatalogInBasket;
use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Catalog\ShoppingCartCatalogManager as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\ShoppingCartManager;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\ProductModel;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ExternalCatalogSchema\Accessory;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

/**
 * Class ShoppingCartCatalogManager
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\ShoppingCart\Catalog
 * <AUTHOR> Marniesse <<EMAIL>>
 * @engine isolate
 */
class ShoppingCartCatalogManager extends PommTest
{
    /**
     * getTestedInstance
     */
    protected function getTestedInstance(array $config = []): TestedClass
    {
        if ($config === []) {
            $config = [
                'catalog_id' => 111286,
                'catalog_year' => 2018,
                'catalog_thumbnail' => 'toto-a-l-eglise.jpg',
                'begin_date' => '1990-11-10 00:00:00',
                'end_date' => '2199-12-31 23:59:59',
            ];
        }

        $holder = $this->getContainer()->get('app.manager.customer.shopping_cart_catalog_holder');
        $holder->clear();

        return new TestedClass(
            $this->getContainer()->get('app.manager.customer_preference'),
            $holder,
            $this->getPommSession(),
            new NullLogger(),
            $config,
        );
    }

    /**
     * getBadPeriodConfig
     * @return array{catalog_id: int, catalog_year: int, catalog_thumbnail: string, begin_date: string, end_date: string}
     */
    protected static function getBadPeriodConfig(): array
    {
        return [
            'catalog_id' => 111286,
            'catalog_year' => 2018,
            'catalog_thumbnail' => 'toto-a-l-eglise.jpg',
            'begin_date' => '1990-11-10 00:00:00',
            'end_date' => '2015-12-31 23:59:59',
        ];
    }

    /**
     * getBadAccessoryConfig
     * @return array{catalog_id: int, catalog_year: int, catalog_thumbnail: string, begin_date: string, end_date: string}
     */
    protected function getBadAccessoryConfig(): array
    {
        return [
            'catalog_id' => 111285,
            'catalog_year' => 2018,
            'catalog_thumbnail' => 'toto-a-l-eglise.jpg',
            'begin_date' => '1990-11-10 00:00:00',
            'end_date' => '2199-12-31 23:59:59',
        ];
    }

    /**
     * testGetCatalogInBasketInfo
     */
    public function testGetCatalogInBasketInfo(): void
    {
        $this->assert('Get catalog in basket info.')
            ->given($catalog_in_basket = $this->getTestedInstance()->getCatalogInBasketInfo())
            ->object($catalog_in_basket)
            ->isInstanceOf(AppCatalogInBasket::class)
            ->variable($catalog_in_basket->catalog_id)->isNull;
    }

    /**
     * testGetAvailableCatalog
     */
    public function testGetAvailableCatalog(): void
    {
        $this->assert('Returns the good catalog.')
            ->given($catalog = $this->getTestedInstance()->getAvailableCatalog())
            ->object($catalog)
            ->isInstanceOf(Accessory::class)
            ->string($catalog->get('thumbnail'))
            ->isEqualTo('toto-a-l-eglise.jpg')

            ->assert('Returns null if period is not satisfied.')
            ->given($catalog = $this->getTestedInstance(static::getBadPeriodConfig())->getAvailableCatalog())
            ->variable($catalog)
            ->isNull->assert('Returns null if stock is equal to threshold.')
            ->given($this->setCatalogStock(TestedClass::CATALOG_STOCK_THRESHOLD))
            ->and($catalog = $this->getTestedInstance(static::getBadPeriodConfig())->getAvailableCatalog())
            ->variable($catalog)
            ->isNull->assert('Returns null if stock is lower than threshold.')
            ->given($this->setCatalogStock(TestedClass::CATALOG_STOCK_THRESHOLD - 1))
            ->and($catalog = $this->getTestedInstance(static::getBadPeriodConfig())->getAvailableCatalog())
            ->variable($catalog)
            ->isNull->assert('Returns null if catalog is not in DB.')
            ->given($catalog = $this->getTestedInstance(static::getBadAccessoryConfig())->getAvailableCatalog())
            ->variable($catalog)->isNull;

        $this->setCatalogStock(TestedClass::CATALOG_STOCK_THRESHOLD + 10);
    }

    /**
     * testAddByCustomerRequest
     */
    public function testAddByCustomerRequest(): void
    {
        $this->assert('Add catalog in basket.')
            ->given($tested_instance = $this->getTestedInstance())
            ->given($catalog_in_basket = $tested_instance->addByCustomerRequest()->getCatalogInBasketInfo())
            ->object($catalog_in_basket)
            ->isInstanceOf(AppCatalogInBasket::class)
            ->integer($catalog_in_basket->catalog_id)
            ->isEqualTo(111286)

            ->assert('Add catalog in basket in bad period.')
            ->given($tested_instance = $this->getTestedInstance(static::getBadPeriodConfig()))
            ->and($catalog_in_basket = $tested_instance->addByCustomerRequest()->getCatalogInBasketInfo())
            ->object($catalog_in_basket)
            ->isInstanceOf(AppCatalogInBasket::class)
            ->variable($catalog_in_basket->catalog_id)
            ->isNull->assert('Add catalog in basket with bad accessory config.')
            ->given($tested_instance = $this->getTestedInstance(static::getBadPeriodConfig()))
            ->and($catalog_in_basket = $tested_instance->addByCustomerRequest()->getCatalogInBasketInfo())
            ->object($catalog_in_basket)
            ->isInstanceOf(AppCatalogInBasket::class)
            ->variable($catalog_in_basket->catalog_id)->isNull;
    }

    /**
     * testRemoveByCustomerRequest
     */
    public function testRemoveByCustomerRequest(): void
    {
        $preference_manager = $this->getContainer()->get('app.manager.customer_preference');

        $this->assert('Remove catalog from basket.')
            ->given($tested_instance = $this->getTestedInstance())
            ->and($catalog_in_basket = $tested_instance->addByCustomerRequest()->getCatalogInBasketInfo())
            ->object($catalog_in_basket)
            ->isInstanceOf(AppCatalogInBasket::class)
            ->integer($catalog_in_basket->catalog_id)
            ->isEqualTo(111286)
            ->given($catalog_in_basket = $tested_instance->removeByCustomerRequest()->getCatalogInBasketInfo())
            ->object($catalog_in_basket)
            ->isInstanceOf(AppCatalogInBasket::class)
            ->variable($catalog_in_basket->catalog_id)
            ->isNull->boolean($catalog_in_basket->hasCatalog())
            ->isFalse->boolean($preference_manager->catalogInBasketIsSet(2018))->isFalse;

        $this->mockUserLogin(24);
        $this->assert('Remove catalog from basket in logged mode.')
            ->given($catalog_in_basket = $tested_instance->removeByCustomerRequest()->getCatalogInBasketInfo())
            ->object($catalog_in_basket)
            ->isInstanceOf(AppCatalogInBasket::class)
            ->variable($catalog_in_basket->catalog_id)
            ->isNull->boolean($catalog_in_basket->hasCatalog())
            ->isFalse->boolean($preference_manager->catalogInBasketIsSet(2018))->isTrue;
        $preference_manager->setCatalogInBasket(2018, false);
    }

    /**
     * testRefreshAutomatically
     */
    public function testRefreshAutomatically(): void
    {
        // Force the Kernel to boot to define some globals required by mocks
        $this->getContainer();

        $this->__get('mockGenerator')->orphanize('__construct');
        $mock1 = $this->newMockInstance(ShoppingCartManager::class);
        $this->calling($mock1)->getTotalAmount = 250;
        $mock2 = $this->newMockInstance(ShoppingCartManager::class);
        $this->calling($mock2)->getTotalAmount = 350;

        $this->assert('Refresh with shopping cart that does not satisfy the catalog adding rules.')
            ->given($tested_instance = $this->getTestedInstance())
            ->and($catalog_in_basket = $tested_instance->refreshAutomatically($mock1)->getCatalogInBasketInfo())
            ->object($catalog_in_basket)
            ->isInstanceOf(AppCatalogInBasket::class)
            ->variable($catalog_in_basket->catalog_id)
            ->isNull->boolean($catalog_in_basket->hasCatalog())
            ->isFalse->assert('Refresh with shopping cart that satisfy the catalog adding rules.')
            ->given($catalog_in_basket = $tested_instance->refreshAutomatically($mock2)->getCatalogInBasketInfo())
            ->object($catalog_in_basket)
            ->isInstanceOf(AppCatalogInBasket::class)
            ->integer($catalog_in_basket->catalog_id)
            ->boolean($catalog_in_basket->hasCatalog())
            ->isTrue->assert(
                'Refresh with shopping cart that does not satisfy rules but where catalog was added previously.',
            )
            ->given($catalog_in_basket = $tested_instance->refreshAutomatically($mock1)->getCatalogInBasketInfo())
            ->object($catalog_in_basket)
            ->isInstanceOf(AppCatalogInBasket::class)
            ->integer($catalog_in_basket->catalog_id)
            ->boolean($catalog_in_basket->hasCatalog())->isTrue;
    }

    /**
     * testSaveCatalogInBasketPreferenceInNonLoggedMode
     */
    public function testSaveCatalogInBasketPreferenceInNonLoggedMode(): void
    {
        $preference_manager = $this->getContainer()->get('app.manager.customer_preference');

        $this->assert('Save preference when catalog is not removed from basket in non logged mode.')
            ->given($tested_instance = $this->getTestedInstance())
            ->and($tested_instance->saveCatalogInBasketPreference())
            ->boolean($preference_manager->catalogInBasketIsSet(2018))
            ->isFalse->assert('Save preference when catalog removed from basket by customer in non logged mode.')
            ->given($tested_instance = $this->getTestedInstance())
            ->and($tested_instance->removeByCustomerRequest())
            ->and($tested_instance->saveCatalogInBasketPreference())
            ->boolean($preference_manager->catalogInBasketIsSet(2018))->isFalse;
    }

    /**
     * testSaveCatalogInBasketPreferenceInLoggedMode
     */
    public function testSaveCatalogInBasketPreferenceInLoggedMode(): void
    {
        $this->mockUserLogin(24);
        $preference_manager = $this->getContainer()->get('app.manager.customer_preference');

        $this->assert('Save preference when catalog is not removed from basket in logged mode.')
            ->given($tested_instance = $this->getTestedInstance())
            ->and($tested_instance->saveCatalogInBasketPreference())
            ->boolean($preference_manager->catalogInBasketIsSet(2018))
            ->isFalse->assert('Save preference when catalog removed from basket by customer in logged mode.')
            ->given($tested_instance = $this->getTestedInstance())
            ->and($tested_instance->removeByCustomerRequest())
            ->and($tested_instance->saveCatalogInBasketPreference())
            ->boolean($preference_manager->catalogInBasketIsSet(2018))->isTrue;
    }

    /**
     * testHandleAfterOrder
     */
    public function testHandleAfterOrder(): void
    {
        $this->mockUserLogin(24);
        $preference_manager = $this->getContainer()->get('app.manager.customer_preference');

        $this->assert('Clean catalog in basket and save preference when order is passed.')
            ->given($preference_manager->setCatalogInBasket(2018, false))
            ->and($tested_instance = $this->getTestedInstance())
            ->and($tested_instance->addByCustomerRequest())
            ->and($tested_instance->handleAfterOrder())
            ->boolean($preference_manager->catalogInBasketIsSet(2018))
            ->isTrue->object($catalog_in_basket = $tested_instance->getCatalogInBasketInfo())
            ->isInstanceOf(AppCatalogInBasket::class)
            ->variable($catalog_in_basket->catalog_id)->isNull;
    }

    /**
     * setCatalogStock
     */
    protected function setCatalogStock(int $stock): self
    {
        $this->getPommSession(PommTest::ADMIN_SESSION)
            ->getModel(ProductModel::class)
            ->updateByPK(['sku' => 'SVCATAPREM2017'], ['informational_stock' => $stock]);

        return $this;
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\ShoppingCart\Magazine;

use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Magazine\MagazineInBasket as AppMagazineInBasket;
use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\Magazine\ShoppingCartMagazineHolder as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;

/**
 * Class ShoppingCartCatalogHolder
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\ShoppingCart\Catalog
 * <AUTHOR> <<EMAIL>>
 * @engine inline
 */
class ShoppingCartMagazineHolder extends Test
{
    /**
     * getTestedInstance
     */
    public function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get('app.manager.customer.shopping_cart_magazine_holder');
    }

    /**
     * testGetMagazineInBasketInfo
     */
    public function testGetMagazineInBasketInfo(): void
    {
        $this->assert('Test result of get magazine in basket info.')
            ->given($magazine_in_basket = $this->getTestedInstance()->getMagazineInBasketInfo())
            ->object($magazine_in_basket)
            ->isInstanceOf(AppMagazineInBasket::class);
    }

    /**
     * testAddMagazineInBasket
     */
    public function testAddMagazineInBasket(): void
    {
        $this->assert('Test add magazine in basket.')
            ->given(
                $magazine_in_basket = $this->getTestedInstance()
                    ->addMagazineInBasket(12)
                    ->getMagazineInBasketInfo(),
            )
            ->object($magazine_in_basket)
            ->isInstanceOf(AppMagazineInBasket::class)
            ->integer($magazine_in_basket->magazine_id)
            ->isEqualTo(12);
    }

    /**
     * testClear
     */
    public function testClear(): void
    {
        $this->assert('Test add magazine in basket.')
            ->given(
                $magazine_in_basket = $this->getTestedInstance()
                    ->addMagazineInBasket(12)
                    ->clear()
                    ->getMagazineInBasketInfo(),
            )
            ->object($magazine_in_basket)
            ->isInstanceOf(AppMagazineInBasket::class)
            ->variable($magazine_in_basket->magazine_id)->isNull;
    }
}

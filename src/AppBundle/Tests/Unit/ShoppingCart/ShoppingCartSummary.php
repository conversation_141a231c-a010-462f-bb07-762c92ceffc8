<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\ShoppingCart;

use SonVideo\Cms\FrontOffice\AppBundle\ShoppingCart\ShoppingCartSummary as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccessorySession;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\ArticleSessionSelection;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;

/**
 * Class ShoppingCartSummary
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\ShoppingCart
 * <AUTHOR> <PERSON> <<EMAIL>>
 */
class ShoppingCartSummary extends Test
{
    /**
     * testConstruct
     */
    public function testConstruct(): void
    {
        $this->assert('Good construct of ShoppingCartSummary.')
            ->given(
                $instance = new TestedClass(
                    new ArticleSessionSelection([
                        'nb_articles' => 12,
                        'article_ids' => [48, 10],
                        'article_ids_without_destock' => [48, 10],
                        'accessory_ids' => [5, 9],
                        'amount_selling_price' => 99.5,
                        'amount_selling_price_for_promo_code' => 99.5,
                        'weight_g' => 140,
                        'amount_ecotaxe' => 2.5,
                        'customer_id' => 24,
                        'details' => [[1]],
                    ]),
                ),
            )
            ->object($instance)
            ->isInstanceOf(TestedClass::class)
            ->integer($instance->getNbArticles())
            ->isEqualTo(12)
            ->integer($instance->getWeightG())
            ->isEqualTo(140)
            ->float($instance->getAmountSellingPrice())
            ->isEqualTo(99.5)
            ->float($instance->getAmountSellingPriceForPromoCode())
            ->isEqualTo(99.5)
            ->float($instance->getAmountEcotaxe())
            ->isEqualTo(2.5)
            ->array($instance->getArticleIds())
            ->isEqualTo([48, 10])
            ->array($instance->getArticleIdsWithoutDestock())
            ->isEqualTo([48, 10])
            ->array($instance->getAccessoryIds())
            ->isEqualTo([5, 9])
            ->array($instance->getDetails())
            ->isEqualTo([[1]]);
    }

    /**
     * testMerge
     */
    public function testMerge(): void
    {
        $this->assert('Check ShoppingCartSummary merge action.')
            ->given(
                $instance = new TestedClass(
                    new ArticleSessionSelection([
                        'nb_articles' => 12,
                        'article_ids' => [48, 10],
                        'article_ids_without_destock' => [48, 10],
                        'accessory_ids' => [5, 24],
                        'amount_selling_price' => 99.5,
                        'amount_selling_price_for_promo_code' => 98.5,
                        'weight_g' => 140,
                        'amount_ecotaxe' => 2.5,
                        'customer_id' => 24,
                        'details' => [1],
                    ]),
                ),
            )
            ->and(
                $instance2 = new TestedClass(
                    new AccessorySession([
                        'nb_articles' => 8,
                        'article_ids' => [10, 11],
                        'article_ids_without_destock' => [10],
                        'accessory_ids' => [5, 9],
                        'amount_selling_price' => 10,
                        'amount_selling_price_for_promo_code' => 9,
                        'weight_g' => 1,
                        'amount_ecotaxe' => 0.5,
                        'customer_id' => 24,
                        'details' => [2],
                    ]),
                ),
            )
            ->and($instance->merge($instance2))
            ->object($instance)
            ->isInstanceOf(TestedClass::class)
            ->integer($instance->getNbArticles())
            ->isEqualTo(20)
            ->integer($instance->getWeightG())
            ->isEqualTo(141)
            ->float($instance->getAmountSellingPrice())
            ->isEqualTo(109.5)
            ->float($instance->getAmountSellingPriceForPromoCode())
            ->isEqualTo(107.5)
            ->float($instance->getAmountEcotaxe())
            ->isEqualTo(3)
            ->array($instance->getArticleIds())
            ->isEqualTo([48, 10, 11])
            ->array($instance->getArticleIdsWithoutDestock())
            ->isEqualTo([48, 10])
            ->array($instance->getAccessoryIds())
            ->isEqualTo([5, 24, 9])
            ->array($instance->getDetails())
            ->isEqualTo([1, 2]);
    }

    /**
     * testCreateEmptySummary
     */
    public function testCreateEmptySummary(): void
    {
        $this->assert('Check create empty ShoppingCartSummary.')
            ->given($instance = TestedClass::createEmptySummary())
            ->object($instance)
            ->isInstanceOf(TestedClass::class)
            ->integer($instance->getNbArticles())
            ->isEqualTo(0)
            ->integer($instance->getWeightG())
            ->isEqualTo(0)
            ->float($instance->getAmountSellingPrice())
            ->isEqualTo(0)
            ->float($instance->getAmountSellingPriceForPromoCode())
            ->isEqualTo(0)
            ->float($instance->getAmountEcotaxe())
            ->isEqualTo(0)
            ->array($instance->getArticleIds())
            ->isEqualTo([])
            ->array($instance->getArticleIdsWithoutDestock())
            ->isEqualTo([])
            ->array($instance->getAccessoryIds())
            ->isEqualTo([])
            ->array($instance->getDetails())
            ->isEqualTo([]);
    }

    /**
     * testIsEmpty
     */
    public function testIsEmpty(): void
    {
        $this->assert('Check isEmpty returns true for empty cart.')
            ->given($instance = TestedClass::createEmptySummary())
            ->object($instance)
            ->isInstanceOf(TestedClass::class)
            ->boolean($instance->isEmpty())
            ->isTrue->assert('CCheck isEmpty returns false for non empty cart.')
            ->given(
                $instance = new TestedClass(
                    new ArticleSessionSelection([
                        'nb_articles' => 12,
                        'article_ids' => [48, 10],
                        'article_ids_without_destock' => [48, 10],
                        'accessory_ids' => [5, 24],
                        'amount_selling_price' => 99.5,
                        'amount_selling_price_for_promo_code' => 99.5,
                        'weight_g' => 140,
                        'amount_ecotaxe' => 2.5,
                        'customer_id' => 24,
                    ]),
                ),
            )
            ->object($instance)
            ->isInstanceOf(TestedClass::class)
            ->boolean($instance->isEmpty())->isFalse;
    }

    /**
     * testExtract
     */
    public function testExtract(): void
    {
        $this->assert('Extract returns an array with info.')
            ->given(
                $instance = new TestedClass(
                    new ArticleSessionSelection([
                        'nb_articles' => 12,
                        'article_ids' => [48, 10],
                        'article_ids_without_destock' => [48],
                        'accessory_ids' => [5, 9],
                        'amount_selling_price' => 99.5,
                        'amount_selling_price_for_promo_code' => 98.5,
                        'weight_g' => 140,
                        'amount_ecotaxe' => 2.5,
                        'customer_id' => 24,
                        'details' => [[1]],
                    ]),
                ),
            )
            ->object($instance)
            ->isInstanceOf(TestedClass::class)
            ->array($array = $instance->extract())
            ->integer($array['nb_articles'])
            ->isEqualTo(12)
            ->integer($array['weight_g'])
            ->isEqualTo(140)
            ->float($array['amount_selling_price'])
            ->isEqualTo(99.5)
            ->float($array['amount_selling_price_for_promo_code'])
            ->isEqualTo(98.5)
            ->float($array['amount_ecotaxe'])
            ->isEqualTo(2.5)
            ->array($array['article_ids'])
            ->isEqualTo([48, 10])
            ->array($array['article_ids_without_destock'])
            ->isEqualTo([48])
            ->array($array['accessory_ids'])
            ->isEqualTo([5, 9])
            ->array($array['details'])
            ->isEqualTo([[1]]);
    }
}

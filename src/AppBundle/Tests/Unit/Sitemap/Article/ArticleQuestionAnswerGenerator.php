<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Sitemap\Article;

use SonVideo\Cms\FrontOffice\AppBundle\Sitemap\Article\ArticleQuestionAnswerGenerator as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;

class ArticleQuestionAnswerGenerator extends PommTest
{
    /**
     * getInstance
     */
    private function getInstance(): TestedClass
    {
        return new TestedClass($this->getContainer()->get('pomm'), $this->getContainer()->get('router'));
    }

    /**
     * testGenerate
     */
    public function testGenerate(): void
    {
        $this->assert('Check url generation for article is working.')
            ->given($res = $this->getInstance()->generate())
            ->array($res)
            ->contains('/article/elipson-prestige-4i-calvados-fr/questions-reponses')
            ->notContains('/article/enceintes-compactes/kef/q100-blanc/questions-reponses');
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Tracker\Rules;

use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;
use SonVideo\Cms\FrontOffice\AppBundle\Tracker\Rules\IadvizeRule as TestedClass;
use SonVideo\Cms\FrontOffice\AppBundle\Tracker\TrackerResponse;

/**
 * Class IadvizeRule
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Tracker\Rules
 * @copyright 2017 Son-Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 */
class IadvizeRule extends Test
{
    /**
     * getTestedInstance
     *
     *
     */
    public function getTestedInstance(string $location): TestedClass
    {
        return (new TestedClass($location))->setProviderContainer(
            $this->getContainer()->get('app.manager.tracker_provider_container'),
        );
    }

    /**
     * testIsEligible
     *
     *
     * @dataProvider getDataProvider
     */
    public function testIsEligible(string $location): void
    {
        $this->assert(sprintf('Check rule is eligible for location \'%s\'.', $location))
            ->given($res = $this->getTestedInstance($location)->isEligible())
            ->boolean($res)
            ->isTrue();
    }

    /**
     * testIsNotEligible
     */
    public function testIsNotEligible(): void
    {
        $this->assert('Check rule is not eligible.')
            ->given($res = $this->getTestedInstance('about_son_video')->isEligible())
            ->boolean($res)
            ->isFalse();
    }

    /**
     * testRender
     *
     *
     * @dataProvider getDataProvider
     */
    public function testRender(string $location, array $extra_data, array $data_expected): void
    {
        $this->assert(sprintf('Check render for location \'%s\'.', $location))
            ->given($res = $this->getTestedInstance($location)->render($extra_data))
            ->object($res)
            ->isInstanceOf(TrackerResponse::class)
            ->array($res->getData())
            ->isEqualTo($data_expected);
    }

    /**
     * getDataProvider
     */
    protected function getDataProvider(): array
    {
        return [
            static::getArticleData(),
            static::getGuideListData(),
            static::getStandData(),
            static::getShopData(),
            static::getCategoryData(),
        ];
    }

    /**
     * getArticleData
     */
    private function getArticleData(): array
    {
        return [
            'article',
            [
                'categories' => [
                    [
                        'category_id' => 1,
                        'name' => 'Enceinte',
                        'slug' => 'enceinte',
                        'aliases' => ['iadvize' => 'Son hi-fi'],
                        'supported_culture_id' => 'fr',
                    ],
                ],
            ],
            [
                'page_type' => 'produit',
                'product_category' => 'Son hi-fi',
            ],
        ];
    }

    /**
     * getGuideListData
     */
    private function getGuideListData(): array
    {
        return [
            'guide_list',
            ['guide_id' => 1],
            [
                'page_type' => 'listing',
                'product_category' => 'Home-cinéma',
                'product_subcategory1' => 'Haute-fidélité',
                'product_subcategory2' => 'Décoration',
            ],
        ];
    }

    /**
     * getStandData
     */
    private function getStandData(): array
    {
        return [
            'stand',
            [
                'name' => 'Amplis home-cinéma',
                'ancestors' => [
                    [
                        'name' => 'Haute fidélité',
                        'aliases' => ['iadvize' => 'Son hi-fi'],
                    ],
                ],
            ],
            [
                'page_type' => 'listing',
                'product_category' => 'Son hi-fi',
                'product_subcategory1' => 'Amplis home-cinéma',
            ],
        ];
    }

    /**
     * getShopData
     */
    private function getShopData(): array
    {
        return [
            'shop',
            [
                'search_data' => json_encode([
                    'articles' => [
                        [
                            'main_category_aliases' => ['iadvize' => 'Son hi-fi'],
                            'category_name' => 'Enceinte colonne',
                        ],
                        [
                            'main_category_aliases' => ['iadvize' => 'Son hi-fi'],
                            'category_name' => 'Enceinte encastrable',
                        ],
                        [
                            'main_category_aliases' => ['iadvize' => 'Son hi-fi'],
                            'category_name' => 'Enceinte colonne',
                        ],
                        [
                            'main_category_aliases' => ['iadvize' => 'Son hi-fi'],
                            'category_name' => 'Enceinte colonne',
                        ],
                    ],
                ]),
            ],
            [
                'page_type' => 'listing',
                'product_category' => 'Son hi-fi',
                'product_subcategory1' => 'Enceinte colonne',
            ],
        ];
    }

    /**
     * getCategoryData
     */
    private function getCategoryData(): array
    {
        return [
            'category',
            [
                'category' => ['name' => 'Enceinte colonne'],
                'category_hierarchy' => [
                    [
                        'name' => 'Enceinte',
                        'aliases' => ['iadvize' => 'Son hi-fi'],
                    ],
                    [
                        'name' => 'Enceinte colonne',
                        'aliases' => null,
                    ],
                ],
            ],
            [
                'page_type' => 'listing',
                'product_category' => 'Son hi-fi',
                'product_subcategory1' => 'Enceinte colonne',
            ],
        ];
    }
}

require('babel-register')
const config = require('../../../../../build/config')

// http://nightwatchjs.org/getingstarted#settings-file
module.exports = {
    src_folders: ['src/AppBundle/Tests/js/e2e/specs'],
    output_folder: 'src/AppBundle/Tests/js/e2e/reports',
    custom_assertions_path: ['src/AppBundle/Tests/js/e2e/custom-assertions'],

    selenium: {
        start_process: true,
        server_path: require('selenium-server').path,
        host: '127.0.0.1',
        port: 4444,
        cli_args: {
            'webdriver.chrome.driver': require('chromedriver').path,
        },
    },

    test_settings: {
        default: {
            selenium_port: 4444,
            selenium_host: 'localhost',
            silent: true,
            globals: {
                devServerURL: 'http://localhost:' + (process.env.PORT || config.dev.port),
            },
        },

        chrome: {
            desiredCapabilities: {
                browserName: 'chrome',
                javascriptEnabled: true,
                acceptSslCerts: true,
            },
        },

        firefox: {
            desiredCapabilities: {
                browserName: 'firefox',
                javascriptEnabled: true,
                acceptSslCerts: true,
            },
        },
    },
}

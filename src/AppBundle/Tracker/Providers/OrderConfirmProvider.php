<?php
/*
 * This file is part of CMS Back-Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tracker\Providers;

use PommProject\Foundation\Session\Session;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrder;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrderModel;

/**
 * Class OrderConfirmProvider
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tracker\Providers
 * <AUTHOR> <<EMAIL>>
 */
class OrderConfirmProvider
{
    private Session $pomm_session;

    /**
     * @var array
     */
    private $products = [];

    private ?CustomerOrder $customer_order = null;

    /**
     * OrderConfirmProvider constructor.
     */
    public function __construct(Session $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    /**
     * getCustomerOrder
     *
     *
     */
    public function getCustomerOrder(array $values): CustomerOrder
    {
        if (!$this->customer_order instanceof CustomerOrder) {
            $this->customer_order = new CustomerOrder($values);
        }

        return $this->customer_order;
    }

    /**
     * getCompleteProducts
     *
     *
     */
    public function getCompleteProducts(int $order_id): array
    {
        if ($this->products === []) {
            $this->products = $this->pomm_session
                ->getModel(CustomerOrderModel::class)
                ->getCompleteProducts($order_id)
                ->extract();
        }

        return $this->products;
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Tracker\Rules;

use PommProject\ModelManager\Exception\ModelException;
use PommProject\Foundation\Inflector;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerOrderSchema\CustomerOrder;
use SonVideo\Cms\FrontOffice\AppBundle\Tracker\Providers\OrderConfirmProvider;
use SonVideo\Cms\FrontOffice\AppBundle\Tracker\TrackerResponse;

/**
 * Class AffilaeRule
 *
 * @package   SonVideo\Cms\FrontOffice\AppBundle\Tracker\Rules
 * @copyright 2018 Son-Video Distribution
 * <AUTHOR> <<EMAIL>>
 * @see       TrackerRuleInterface
 */
class AffilaeRule extends TrackerBaseRule implements TrackerRuleInterface
{
    /**
     * array
     *
     * Tracking of funnel steps is done in checkout_process_v2 app
     */
    public const LOCATIONS = ['order_confirm'];

    private array $extra_data = [];

    /**
     * isEligible
     */
    public function isEligible(): bool
    {
        return in_array($this->getLocation(), static::LOCATIONS);
    }

    public function render(array $extra_data): TrackerResponse
    {
        $this->extra_data = $extra_data;
        $data_method = Inflector::studlyCaps(sprintf('get_%s_data', $this->getLocation()));

        return new TrackerResponse(
            'AppBundle:Tracking:affilae.html.twig',
            array_merge(
                ['page_type' => $this->getLocation()],
                method_exists($this, $data_method) && $this->isEligible() ? $this->{$data_method}() : [],
            ),
        );
    }

    /**
     * @throws ModelException
     * @return array{customer_order: CustomerOrder}
     */
    protected function getOrderConfirmData(): array
    {
        /** @var OrderConfirmProvider $order_confirm_provider */
        $order_confirm_provider = $this->provider_container->loadProviderByClass(OrderConfirmProvider::class);
        $customer_order = $order_confirm_provider->getCustomerOrder($this->extra_data);
        $customer_order['complete_products'] = $this->computeProducts(
            $customer_order,
            $order_confirm_provider->getCompleteProducts($this->extra_data['customer_order_id']),
        );
        $customer_order['total_amount_taxes_excluded_without_shipping_cost'] = round(
            array_sum(array_column($customer_order['complete_products'], 'total_raw_amount')),
            2,
        );
        $customer_order['commission_group'] = $this->getCommissionGroup($customer_order['complete_products']);

        return ['customer_order' => $customer_order];
    }

    /**
     * @throws ModelException
     */
    protected function computeProducts(CustomerOrder $customer_order, array $products): array
    {
        foreach ($products as $key => $product) {
            $total_ecotaxe = $product['quantity'] * $product['ecotaxe'];
            $total_sorecop = $product['quantity'] * $product['sorecop'];
            $products[$key]['total_amount_taxes_excluded'] = round(
                ($product['total_amount'] +
                    $product['amount_extension_warranty'] +
                    $product['amount_theft_break_extension_warranty'] -
                    ($total_ecotaxe < $product['total_amount'] ? $total_ecotaxe : 0) -
                    ($total_sorecop < $product['total_amount'] ? $total_sorecop : 0)) /
                    (1 + APP_DEFAULT_VAT_RATE),
                2,
            );
        }

        $discount_vat_excluded = $customer_order->getDiscountAmountVatExcluded();
        $total_amount_taxes_excluded = array_sum(array_column($products, 'total_amount_taxes_excluded'));

        // Remove a discount part's of product total, discount is negative
        foreach ($products as $key => $product) {
            $products[$key]['total_raw_amount'] =
                $product['total_amount_taxes_excluded'] +
                round(
                    $discount_vat_excluded * ($product['total_amount_taxes_excluded'] / $total_amount_taxes_excluded),
                    2,
                );
        }

        return $products;
    }

    /**
     * getCommissionGroup
     *
     * Format commission group for Affilae tracking
     * ex : BRONZE:500|SILVER:100
     */
    protected function getCommissionGroup(array $products): string
    {
        $commission_group = [];
        foreach ($products as $product) {
            $label = $this->getCommissionLabel($product['margin_rate'] * 100);

            if ($label !== '') {
                $commission_group[$label] = ($commission_group[$label] ?? 0) + $product['total_raw_amount'];
            }
        }

        return implode(
            '|',
            array_map(
                fn($value, $key): string => sprintf('%s:%s', $key, round($value, 2)),
                $commission_group,
                array_keys($commission_group),
            ),
        );
    }

    /**
     * getCommissionLabel
     *
     *
     */
    protected function getCommissionLabel(float $margin_rate): string
    {
        $label = 'GOLD';

        if ($margin_rate != 0 && $margin_rate <= 15) {
            $label = 'BRONZE';
        } elseif ($margin_rate != 0 && $margin_rate <= 30) {
            $label = 'SILVER';
        }

        return $label;
    }
}

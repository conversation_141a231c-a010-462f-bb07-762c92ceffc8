<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Tracker;

/**
 * Class TrackerProviderContainer
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tracker
 * <AUTHOR> <<EMAIL>>
 */
class TrackerProviderContainer
{
    private array $providers = [];

    /**
     * addTrackerProvider
     *
     * @param mixed $provider
     */
    public function addTrackerProvider($provider): self
    {
        if (is_object($provider)) {
            $this->providers[get_class($provider)] = $provider;
        }

        return $this;
    }

    /**
     * loadProviderByClass
     *
     * @return mixed|null
     */
    public function loadProviderByClass(string $class_name)
    {
        return $this->providers[$class_name] ?? null;
    }
}

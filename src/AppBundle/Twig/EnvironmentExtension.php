<?php

namespace SonVideo\Cms\FrontOffice\AppBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class EnvironmentExtension extends AbstractExtension
{
    public function getFunctions(): array
    {
        return [
            new TwigFunction('isProd', fn(): bool => $this->isProd()),
            new TwigFunction('isStaging', fn(): bool => $this->isStaging()),
            new TwigFunction('isDev', fn(): bool => $this->isDev()),
            new TwigFunction('isValidation', fn(): bool => $this->isValidation()),
            new TwigFunction('getEnvironment', fn(): string => $this->getEnvironment()),
        ];
    }

    private function isProd(): bool
    {
        return $_SERVER['HTTP_HOST'] === 'www.son-video.com';
    }

    private function isStaging(): bool
    {
        return strpos($_SERVER['HTTP_HOST'], 'staging') !== false;
    }

    private function isValidation(): bool
    {
        return strpos($_SERVER['HTTP_HOST'], 'validation.aws.son-video.work') !== false;
    }

    private function isDev(): bool
    {
        return !$this->isProd() && !$this->isStaging() && !$this->isValidation();
    }

    private function getEnvironment(): string
    {
        if ($this->isProd()) {
            return 'prod';
        }
        if ($this->isStaging()) {
            return 'staging';
        }
        if ($this->isValidation()) {
            return 'validation';
        }
        return 'dev';
    }
}

<?php
/*
 * This file is part of CMS Back-Office package.
 *
 * (coffee) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Twig;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\Account;

/**
 * Class HashedEmailExtension
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Twig
 * <AUTHOR> <<EMAIL>>
 */
class HashedEmailExtension extends \Twig_Extension
{
    /**
     * getFilters
     */
    public function getFilters(): array
    {
        return [new \Twig_SimpleFilter('hashed_email', fn($user): string => $this->hashedEmail($user))];
    }

    /**
     * hashedEmail
     *
     * @param mixed $user
     */
    public function hashedEmail($user): string
    {
        if ($user instanceof Account) {
            return md5(mb_convert_encoding(trim(strtolower($user->get('email'))), 'UTF-8', 'ISO-8859-1'));
        }

        return '';
    }

    /**
     * getName
     */
    public function getName(): string
    {
        return 'hashed_email_extension';
    }
}

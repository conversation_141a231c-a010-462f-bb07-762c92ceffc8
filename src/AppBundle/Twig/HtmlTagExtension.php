<?php
/*
 * This file is part of FO CMS package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class HtmlTagExtension extends AbstractExtension
{
    private string $kernel_root_dir;

    public function __construct(string $kernel_root_dir)
    {
        $this->kernel_root_dir = $kernel_root_dir;
    }

    /**
     * @return TwigFilter[]
     */
    public function getFilters(): array
    {
        return [
            new TwigFilter(
                'svd_icon',
                fn($file_name, $classes = '', $data_context = ''): string => $this->extractSvdIconMarkupFromPath(
                    $file_name,
                    $classes,
                    $data_context,
                ),
            ),
            new TwigFilter('defer', fn($html_tag) => $this->defer($html_tag)),
        ];
    }

    /**
     * @usage {{ 'regular/arrow-down' | svd_icon('h-10 w-10 text-blue-500') | raw  }}
     */
    public function extractSvdIconMarkupFromPath(string $file_name, $classes = '', $data_context = ''): string
    {
        $path = dirname($this->kernel_root_dir) . '/src/AppBundle/Resources/icons/svd/' . $file_name . '.svg';
        $content = file_get_contents($path);

        if (!(bool) $content) {
            throw new \InvalidArgumentException(sprintf('SVG image source file not found at path : %s', $path));
        }

        $additional_attrs = '';
        preg_match('/icon-(\d{2})/', $classes, $matches);
        if ($matches !== []) {
            $additional_attrs = sprintf('width="%spx" height="%spx" ', $matches[1], $matches[1]);
        }

        return str_replace(
            '<svg ',
            sprintf(
                '<svg fill="currentColor" class="%s" data-context="%s" %s',
                $classes,
                $data_context,
                $additional_attrs,
            ),
            $content,
        );
    }

    public function defer($html_tag)
    {
        return preg_replace('/(<script.*?)(>)/', '$1 defer>', $html_tag);
    }
}

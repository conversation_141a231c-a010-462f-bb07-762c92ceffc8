<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Twig;

/**
 * Class WarrantyExtension
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Twig
 * <AUTHOR> <<EMAIL>>
 */
class WarrantyExtension extends \Twig_Extension
{
    /**
     * getFilters
     */
    public function getFilters(): array
    {
        return [
            new \Twig_SimpleFilter('getWarrantyType', fn($tag_path): string => $this->getWarrantyTypeFilter($tag_path)),
            new \Twig_SimpleFilter(
                'getWarrantyDuration',
                fn($tag_path): string => $this->getWarrantyDurationFilter($tag_path),
            ),
        ];
    }

    /**
     * getWarrantyTypeFilter
     *
     * @param array|string $tag_path
     */
    public function getWarrantyTypeFilter($tag_path): string
    {
        if (!is_array($tag_path)) {
            $tag_path = explode('.', $tag_path);
        }

        return $tag_path[1] ?? '';
    }

    /**
     * getWarrantyDurationFilter
     *
     * @param array|string $tag_path
     */
    public function getWarrantyDurationFilter($tag_path): string
    {
        if (!is_array($tag_path)) {
            $tag_path = explode('.', $tag_path);
        }

        return $tag_path[2] ?? '';
    }
}

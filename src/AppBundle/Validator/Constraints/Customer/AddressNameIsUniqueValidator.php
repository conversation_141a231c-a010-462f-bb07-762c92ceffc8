<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer;

use PommProject\Foundation\Pomm;
use PommProject\Foundation\Session\Session;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountInformationModel;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

/**
 * Class AddressNameIsUniqueValidator
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer
 * <AUTHOR> Boulard <<EMAIL>>
 */
class AddressNameIsUniqueValidator extends ConstraintValidator
{
    private Session $pomm_session;

    /**
     * NewAddressNameIsUniqueValidator constructor.
     */
    public function __construct(Pomm $pomm)
    {
        $this->pomm_session = $pomm->getDefaultSession();
    }

    /**
     * @param string     $value
     *
     * @throws UnexpectedTypeException
     */
    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof AddressNameIsUnique) {
            throw new UnexpectedTypeException($constraint, AddressNameIsUnique::class);
        }

        $values = $this->context->getRoot()->address;
        $bypass = $values['ignore_name_unicity'] ?? false;

        // When needing only valid addresses we don't want to check the name unicity
        if ($bypass) {
            return;
        }

        $addresses = $this->pomm_session->getModel(AccountInformationModel::class)->findByPK([
            'customer_id' => $values['customer_id'],
        ]);

        if (is_null($addresses)) {
            return;
        }

        $addresses = $addresses->extract()['addresses'] ?? [];

        if (isset($values['index']) && array_key_exists($values['index'], $addresses)) {
            unset($addresses[$values['index']]);
        }

        $names = array_map(fn($address): string => strtolower($address['name']), $addresses);

        if (in_array(strtolower($value), $names)) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('%name%', $value)
                ->addViolation();
        }
    }
}

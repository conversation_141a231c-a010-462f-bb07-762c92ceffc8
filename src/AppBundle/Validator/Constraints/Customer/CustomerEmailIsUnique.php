<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer;

use Symfony\Component\Validator\Constraint;

/**
 * Class CustomerEmailIsUnique
 *
 * Define constraint options for validator CustomerEmailIsUniqueValidator
 *
 * @package CMS Front-Office
 * <AUTHOR> Audic <<EMAIL>>
 * @see     Constraint
 */
class CustomerEmailIsUnique extends Constraint
{
    public $message = 'A customer account with this email already exists.';
    public $new_account_message = 'customer.subscribe.email_exists';
    public $is_new_account = false;
}

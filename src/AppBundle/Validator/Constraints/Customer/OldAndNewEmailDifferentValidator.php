<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer;

use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\ConstraintValidator;

/**
 * Class OldAndNewEmailDifferentValidator
 *
 * Validate that old and new email are different on a update email form.
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer
 * <AUTHOR> Audic <<EMAIL>>
 * @see     ConstraintValidator
 */
class OldAndNewEmailDifferentValidator extends ConstraintValidator
{
    protected TokenStorage $token_storage;

    /**
     * OldAndNewEmailDifferentValidator constructor.
     */
    public function __construct(TokenStorage $token_storage)
    {
        $this->token_storage = $token_storage;
    }

    /**
     * validate
     *
     * @param mixed      $value
     */
    public function validate($value, Constraint $constraint): void
    {
        if ($value === $this->token_storage->getToken()->getUsername()) {
            $this->context->addViolation($constraint->message);
        } else {
            // Check that new email is unique
            $this->context
                ->getValidator()
                ->inContext($this->context)
                ->atPath('new_email')
                ->validate($value, [
                    new Email([
                        'strict' => false,
                    ]),
                    new CustomerEmailIsUnique(),
                ]);
        }
    }
}

<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer;

use PommProject\ModelManager\Session;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountInformationModel;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

/**
 * Class PseudonymIsUniqueValidator
 *
 * Check that a pseudonym is not already use by another account.
 *
 * @package CMS Front-Office
 * @see     ConstraintValidator
 */
class PseudonymIsUniqueValidator extends ConstraintValidator
{
    protected Session $pomm_session;

    /**
     * __construct
     *
     * Override default constructor to set the database connection
     */
    public function __construct(Session $pomm_session)
    {
        $this->pomm_session = $pomm_session;
    }

    /**
     * validate
     *
     * Validate that the pseudonym does not already exists.
     *
     * @param mixed      $value
     */
    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof PseudonymIsUnique) {
            throw new UnexpectedTypeException($constraint, PseudonymIsUnique::class);
        }

        if ($value === null) {
            return;
        }

        $pseudonym = $this->pomm_session
            ->getModel(AccountInformationModel::class)
            ->existWhere('pseudonym = $*', [$value]);

        if ($pseudonym) {
            $this->context->addViolation($constraint->message);
        }
    }
}

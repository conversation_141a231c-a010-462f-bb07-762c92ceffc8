<?php
/*
 * This file is part of cms-front package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer;

use PommProject\Foundation\Pomm;
use PommProject\Foundation\Session\Session;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\CustomerSchema\AccountModel;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

/**
 * Class TitleValidator
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer
 * <AUTHOR> <<EMAIL>>
 */
class TitleValidator extends ConstraintValidator
{
    private Session $pomm_session;

    /**
     * TitleValidator constructor.
     */
    public function __construct(Pomm $pomm)
    {
        $this->pomm_session = $pomm->getDefaultSession();
    }

    /**
     * @param mixed      $value
     */
    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof Title) {
            throw new UnexpectedTypeException($constraint, __NAMESPACE__ . '\Title');
        }

        $titles = $this->pomm_session->getModel(AccountModel::class)->getAvailableTitles();

        if (!in_array($value, $titles)) {
            $this->context->buildViolation($constraint->message)->addViolation();
        }
    }
}

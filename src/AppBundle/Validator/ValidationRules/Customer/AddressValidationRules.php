<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Validator\ValidationRules\Customer;

use libphonenumber\PhoneNumberType;
use SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\Customer as CustomerAssert;
use SonVideo\Cms\FrontOffice\AppBundle\Validator\Constraints\PhoneNumberCompliance;
use Symfony\Component\Validator\Mapping\ClassMetadata;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class AddressValidationRules
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Validator\ValidationRules\Customer
 */
class AddressValidationRules
{
    /**
     * @var mixed[]
     */
    public $address;

    /**
     * RoleValidationRules constructor.
     */
    public function __construct(array $values)
    {
        $values['phone'] = static::formatPhoneNumber($values['phone'] ?? null);
        $values['cellphone'] = static::formatPhoneNumber($values['cellphone'] ?? null);

        $this->address = $values;
    }

    /**
     * Strip unwanted chars from a phone number field
     *
     * @param string $string
     */
    public static function formatPhoneNumber($string): string
    {
        return str_replace([' ', '.', '-'], '', $string);
    }

    /**
     * loadValidatorMetadata
     */
    public static function loadValidatorMetadata(ClassMetadata $metadata): void
    {
        $metadata->addPropertyConstraint(
            'address',
            new Assert\Collection([
                'fields' => [
                    'customer_id' => new Assert\Required([
                        new Assert\NotBlank(['message' => 'customer.information.address.customer_id.invalid']),
                        new Assert\NotNull(['message' => 'customer.information.address.customer_id.invalid']),
                    ]),
                    'firstname' => new Assert\Required([
                        new Assert\NotBlank(['message' => 'customer.information.address.firstname.invalid']),
                        new Assert\NotNull(['message' => 'customer.information.address.firstname.invalid']),
                        new Assert\Length(['max' => 64]),
                    ]),
                    'lastname' => new Assert\Required([
                        new Assert\NotBlank(['message' => 'customer.information.address.lastname.invalid']),
                        new Assert\NotNull(['message' => 'customer.information.address.lastname.invalid']),
                        new Assert\Length(['max' => 64]),
                    ]),
                    'address' => new Assert\Required([
                        new Assert\NotBlank(['message' => 'customer.information.address.address.invalid']),
                        new Assert\NotNull(['message' => 'customer.information.address.address.invalid']),
                        new Assert\Length(['max' => 32 * 4]),
                    ]),
                    'postal_code' => new Assert\Required([
                        new Assert\NotBlank(['message' => 'customer.information.address.postal_code.invalid']),
                        new Assert\NotNull(['message' => 'customer.information.address.postal_code.invalid']),
                        new Assert\Length(['max' => 12]),
                    ]),
                    'city' => new Assert\Required([
                        new Assert\NotBlank(['message' => 'customer.information.address.city.invalid']),
                        new Assert\NotNull(['message' => 'customer.information.address.city.invalid']),
                        new Assert\Length(['max' => 64]),
                    ]),
                    'company_name' => new Assert\Optional([new Assert\Length(['max' => 64])]),
                    'created_at' => new Assert\Optional(),
                    'name' => new Assert\Required([
                        new Assert\NotBlank(['message' => 'customer.information.address.name.invalid']),
                        new Assert\NotNull(['message' => 'customer.information.address.name.invalid']),
                        new CustomerAssert\AddressNameIsUnique(),
                    ]),
                    'title' => new Assert\Required([
                        new Assert\NotBlank(['message' => 'customer.information.address.title.invalid']),
                        new Assert\NotNull(['message' => 'customer.information.address.title.invalid']),
                        new CustomerAssert\Title(),
                    ]),
                    'country_code' => new Assert\Required([new CustomerAssert\CountryCode()]),
                    'cellphone' => new Assert\Required([
                        new Assert\NotBlank(['message' => 'customer.information.address.cellphone.invalid']),
                        new Assert\NotNull(['message' => 'customer.information.address.cellphone.invalid']),
                        new PhoneNumberCompliance([
                            'type' => PhoneNumberType::MOBILE,
                            'message' => 'Le numéro n’est pas un numéro de mobile.',
                        ]),
                    ]),
                    'phone' => [
                        new Assert\Regex([
                            'pattern' => '/^\+?\d+$/',
                            'message' => 'customer.information.address.phone.invalid',
                        ]),
                        new Assert\Length(['max' => 24]),
                    ],
                    'index' => new Assert\Optional(),
                    'ignore_name_unicity' => new Assert\Optional(),
                    'is_valid' => new Assert\Optional(),
                    'errors' => new Assert\Optional(),
                ],
            ]),
        );
    }
}

<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Validator\ValidationRules;

use EWZ\Bundle\RecaptchaBundle\Validator\Constraints\IsTrue;
use Symfony\Component\Validator\Mapping\ClassMetadata;

/**
 * Class ReCaptchaRules
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Validator\ValidationRules
 */
class ReCaptchaRules
{
    /**
     * @var string
     */
    protected $token;

    public function __construct()
    {
        // a token value is in fact not needed, EWZValidator will only check against the request
    }

    /**
     * loadValidatorMetadata
     */
    public static function loadValidatorMetadata(ClassMetadata $metadata): void
    {
        $metadata->addPropertyConstraint('token', new IsTrue());
    }
}

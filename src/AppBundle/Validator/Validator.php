<?php
/*
 * This file is part of CMS FrontOffice package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\AppBundle\Validator;

use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Class Validator
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Validator
 */
class Validator
{
    protected ValidatorInterface $validator;
    protected $against_rules;
    protected $errors = [];

    /**
     * Validator constructor.
     */
    public function __construct(ValidatorInterface $validator)
    {
        $this->validator = $validator;
    }

    /**
     * validate
     *
     * Takes a FQCN as first parameter and values array to validate rules against
     *
     *
     * @return $this
     */
    public function validate(string $validation_class_name, array $values): self
    {
        $this->against_rules = new $validation_class_name($values);

        return $this;
    }

    /**
     * passes
     *
     * Apply validation rules and return a boolean whether the validation succeeded or not
     */
    public function passes(): bool
    {
        $validation_errors = $this->validator->validate($this->against_rules);

        $this->errors = [];
        foreach ($validation_errors as $error) {
            $this->errors[$error->getPropertyPath()] = $error->getMessage();
        }

        // reset validation rules afterward to be reusable with the same instance
        $this->against_rules = [];

        return ($this->errors === []) > 0;
    }

    /**
     * getErrors
     *
     * Returns the formatted errors from the error container
     */
    public function getErrors(): array
    {
        return $this->errors;
    }
}

<?php

namespace SonVideo\Cms\FrontOffice\Application\Client;

use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

final class PaymentV2Client implements PaymentV2ClientInterface
{
    private LoggerInterface $logger;

    private Client $client;
    private string $payment_v2_internal_api_secret;

    public function __construct(
        string $payment_v2_url,
        string $payment_v2_internal_api_secret,
        LoggerInterface $logger,
        ?Client $http_client = null
    ) {
        $this->logger = $logger;
        $this->client =
            $http_client ??
            new Client([
                'base_uri' => $payment_v2_url,
                'timeout' => 10,
            ]);

        $this->payment_v2_internal_api_secret = $payment_v2_internal_api_secret;
    }

    public function post(string $relative_endpoint, array $data = []): ?array
    {
        return $this->call($relative_endpoint, Request::METHOD_POST, [
            'json' => $data,
        ]);
    }

    public function get(string $relative_endpoint): ?array
    {
        return $this->call($relative_endpoint, Request::METHOD_GET);
    }

    private function call(string $relative_endpoint, string $method, array $options = []): ?array
    {
        try {
            $this->logger->debug('[PAYMENT V2] Request', [
                'method' => $method,
                'relative_endpoint' => $relative_endpoint,
                'options' => $options,
            ]);

            $response = $this->client->request(
                $method,
                $relative_endpoint,
                array_merge(
                    [
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'Authorization' => sprintf('Basic %s', $this->payment_v2_internal_api_secret),
                        ],
                    ],
                    $options,
                ),
            );

            if (false === $this->isSuccessful($response->getStatusCode())) {
                throw new BadRequestHttpException('Invalid response');
            }

            if (in_array($response->getStatusCode(), [Response::HTTP_NO_CONTENT, Response::HTTP_ACCEPTED], true)) {
                return null;
            }

            $this->logger->debug('[PAYMENT V2] Response', [
                'status_code' => $response->getStatusCode(),
                'content' => $response->getBody()->__toString(),
            ]);

            return json_decode($response->getBody()->__toString(), true, 512, JSON_THROW_ON_ERROR)['data'] ?? [];
        } catch (\Exception $exception) {
            $this->logger->error('An error occurred', [
                'exception' => new \Exception('[PAYMENT V2] HTTP request failed', $exception->getCode(), $exception),
            ]);

            return null;
        }
    }

    private function isSuccessful(int $status_code): bool
    {
        return $status_code >= 200 && $status_code < 300;
    }
}

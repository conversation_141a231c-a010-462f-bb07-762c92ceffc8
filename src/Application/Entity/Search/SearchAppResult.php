<?php

namespace SonVideo\Cms\FrontOffice\Application\Entity\Search;

use SonVideo\Cms\FrontOffice\Application\Contract\EntityInterface;

/**
 * This entity contains all the expected data to display results in SearchApp.
 * SearchApp is an old thing and has grown wild.
 * I'm pretty sure that we could avoid using some of these data and have a flat DTO, without arrays.
 * Please consider cleaning it when the time will come to use another thing than SearchApp to display the results.
 */
class SearchAppResult implements EntityInterface
{
    public ?string $article_color = null;
    public int $article_id;
    public ?string $article_length = null;
    public array $article_lengths;
    public string $article_url;
    public string $brand_name;
    public string $category_name;
    public ?string $cc_short_description = null;
    public int $common_content_id;
    public string $common_content_name;
    public array $color_labels;
    public string $complementary_articles_url;
    public ?array $destock = null;
    public ?string $display_mode = null;
    public string $document_id;
    public array $editorial_content;
    public ?string $estimated_delivery_time = null;
    public string $formatted_name;
    public ?bool $has_an_ongoing_supplier_order = null;
    public array $highlights;
    public bool $is_default_article;
    public ?string $media_uri = null;
    public string $name;
    public array $packaged_articles;
    public ?array $parent_destock_price = null;
    public array $price;
    public ?float $price_discount_percent = null;
    public array $promo_codes_description;
    public float $ranking;
    public ?float $reference_price = null;
    public ?int $review_score_index = null;
    public ?array $second_life = null;
    public ?string $score = null;
    public ?string $selling_price = null;
    public string $sku;
}

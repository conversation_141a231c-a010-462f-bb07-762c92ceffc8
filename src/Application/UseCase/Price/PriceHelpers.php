<?php

namespace SonVideo\Cms\FrontOffice\Application\UseCase\Price;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\CommonContent;

abstract class PriceHelpers
{
    public static function mapReferencePriceAndDiscountOnArticle(array $article): array
    {
        PriceHelpers::addReferencePriceAndDiscountPercent($article);
        PriceHelpers::removeIfNotRelevant($article);

        return $article;
    }

    private static function addReferencePriceAndDiscountPercent(array &$article): void
    {
        $selling_price = $article['selling_price'] ?? ($article['price']['selling_price'] ?? null);
        $reference_price = $article['reference_price'] ?? ($article['price']['reference_price'] ?? null);

        $article['reference_price'] =
            $selling_price === null || $reference_price === null ? null : (float) $reference_price;
        $article['price_discount_percent'] =
            $article['reference_price'] !== null && $article['reference_price'] !== 0.0
                ? (1 - $selling_price / $article['reference_price']) * 100
                : null;
    }

    private static function removeIfNotRelevant(array &$article): void
    {
        if (
            isset($article['display_mode']) &&
            $article['display_mode'] === CommonContent::DISPLAY_MODE_CONFIDENTIAL_PRICE
        ) {
            $article['reference_price'] = null;
            $article['price_discount_percent'] = null;
        }

        if (
            $article['price_discount_percent'] !== null &&
            $article['price_discount_percent'] < PriceConstants::MINIMUM_RELEVANT_DISCOUNT_PERCENT
        ) {
            $article['reference_price'] = null;
            $article['price_discount_percent'] = null;
        }
    }
}

<?php

namespace SonVideo\Cms\FrontOffice\Application\UseCase\Search\GetAutocompleteResults;

use SonVideo\Cms\FrontOffice\Application\Contract\ResponseInterface;
use SonVideo\Cms\FrontOffice\Application\UseCase\Search\GetAutocompleteResults\Entity\AutocompleteBrand;
use SonVideo\Cms\FrontOffice\Application\UseCase\Search\GetAutocompleteResults\Entity\AutocompleteProduct;
use SonVideo\Cms\FrontOffice\Application\UseCase\Search\GetAutocompleteResults\Entity\AutocompleteStand;

class GetAutocompleteResultsResponse implements ResponseInterface
{
    /** @var AutocompleteProduct[] */
    public array $products;
    /** @var AutocompleteBrand[] */
    public array $brands;
    /** @var AutocompleteStand[] */
    public array $stands;

    public function __construct(array $products = [], array $brands = [], array $stands = [])
    {
        $this->products = $products;
        $this->brands = $brands;
        $this->stands = $stands;
    }
}

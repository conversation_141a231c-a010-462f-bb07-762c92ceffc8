<?php

namespace SonVideo\Cms\FrontOffice\Application\UseCase\Search\TrackClick;

use Psr\Log\LoggerInterface;
use SonVideo\Cms\FrontOffice\Application\Contract\UseCaseInterface;
use SonVideo\Cms\FrontOffice\Application\Gateway\Search\ProductDocumentCommandInterface;

class TrackClick implements UseCaseInterface
{
    private ProductDocumentCommandInterface $product_document_command;
    private LoggerInterface $logger;

    public function __construct(ProductDocumentCommandInterface $product_document_command, LoggerInterface $logger)
    {
        $this->product_document_command = $product_document_command;
        $this->logger = $logger;
    }

    public function execute(TrackClickRequest $request): TrackClickResponse
    {
        try {
            $success = $this->product_document_command->trackDocumentResultClick(
                $request->document_id,
                trim($request->search_terms),
                $request->tags,
            );
        } catch (\Exception $exception) {
            $this->logger->error('TrackAutocompleteClick: Failed to use client', ['exception' => $exception]);

            return new TrackClickResponse(false);
        }

        return new TrackClickResponse($success);
    }
}

<?php

namespace SonVideo\Cms\FrontOffice\Application\UseCase\Stand\GetStand;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\Guide;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18n;
use SonVideo\Cms\FrontOffice\Application\Contract\ResponseInterface;
use SonVideo\Cms\FrontOffice\Application\Entity\Search\SearchAppResult;

class GetStandResponse implements ResponseInterface
{
    public ?StandI18n $stand = null;
    public CollectionIterator $stand_ancestors;
    public string $structured_data;
    public ?Guide $guide = null;
    /** @var SearchAppResult[] $search_app_results */
    public array $search_app_results = [];
    public int $search_app_total = 0;
}

<?php
/*
 * This file is part of CMS Front Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\Controller\Widget\Stand;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18n;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18nModel;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class ChildrenController
 *
 * <AUTHOR> <<EMAIL>>
 *
 */
class ChildrenController extends BaseController
{
    /**
     * showAction
     *
     *
     */
    public function showAction(StandI18n $stand, string $_locale): Response
    {
        $stand_children = $this->getPommDefaultSession()
            ->getModel(StandI18nModel::class)
            ->findAllByCultureAndParentTagpath(implode('.', $stand['tag_path']), $_locale);
        if (!$stand_children->isEmpty()) {
            $this->addTags(array_column($stand_children->extract(), 'stand_id'), 'stand');
        }

        return $this->render(
            'CmsBundle::Widget/stand/children.html.twig',
            ['stand' => $stand, 'stand_children' => $stand_children, '_locale' => $_locale],
        );
    }

    /**
     * showWithLogoAction
     *
     *
     */
    public function showWithLogoAction(StandI18n $stand, string $_locale): Response
    {
        $stand_children = $this->getPommDefaultSession()
            ->getModel(StandI18nModel::class)
            ->findAllByCultureAndParentTagpath(implode('.', $stand['tag_path']), $_locale);
        if (!$stand_children->isEmpty()) {
            $this->addTags(array_column($stand_children->extract(), 'stand_id'), 'stand');
        }

        return $this->render('CmsBundle::Widget/stand/children_with_logo.html.twig', ['stand_children' => $stand_children]);
    }
}

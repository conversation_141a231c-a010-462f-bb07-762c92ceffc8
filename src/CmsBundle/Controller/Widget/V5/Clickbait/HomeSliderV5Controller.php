<?php
/*
 * This file is part of FO-CMS package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\Controller\Widget\V5\Clickbait;

use SonVideo\Cms\FrontOffice\AppBundle\Cache\CacheTrait;
use SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema\Clickbait;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;

class HomeSliderV5Controller extends Controller
{
    use CacheTrait;

    /**
     * Retrieve list of home_slider_v5 clickbaits for a given location
     *
     * @param int|null $limit
     *
     */
    public function showAction(string $location, string $_locale, int $limit = null): Response
    {
        $elements = $this->get('cms.manager.widget.clickbait')->resolve(
            Clickbait::HOME_SLIDER_V5,
            $_locale,
            $location,
            $limit,
        );

        if (!$elements->isEmpty()) {
            $this->addTags(array_column($elements->extract(), 'clickbait_id'), 'clickbait');
        }

        return $this->render('CmsBundle::Widget/v5/clickbait/home_slider_v5.html.twig', [
            'elements' => $elements,
            'location' => $location,
        ]);
    }
}

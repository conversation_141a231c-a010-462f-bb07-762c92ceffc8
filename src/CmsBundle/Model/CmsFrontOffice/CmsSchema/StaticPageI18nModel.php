<?php

namespace SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema;

use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;
use SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema\AutoStructure\StaticPageI18n as StaticPageI18nStructure;

/**
 * StaticPageI18nModel
 *
 * Model class for table static_page_i18n.
 *
 * @see Model
 */
class StaticPageI18nModel extends Model
{
    use WriteQueries;

    /**
     * __construct()
     *
     * Model constructor
     *
     * @access public
     */
    public function __construct()
    {
        $this->structure = new StaticPageI18nStructure();
        $this->flexible_entity_class = StaticPageI18n::class;
    }
}

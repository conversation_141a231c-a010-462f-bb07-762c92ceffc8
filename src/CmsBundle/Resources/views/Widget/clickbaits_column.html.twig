{% if banners is not empty %}
    {% for banner in banners %}
        {% if banner.getTitle(app.request.getLocale()) | slugify is same as 'rappel-immediat' %}
        <div class="SVDv3_colonne_element_appelBoutique"
             data-immediate-recall="clickbait column > rappel immédiat"
             data-immediate-recall-action="reception"
             data-context="call-shop-sidepanel-open-email"
             id="banner-{{ loop.index }}"
             data-title="{{ banner.getTitle(app.request.getLocale()) }}"
             data-id="{{ banner.clickbait_id }}"
             data-location="{{ locations[0] }}"
             data-position="{{ loop.index }}"
             data-url="{{ banner.url }}"
        >
        {% else %}
        <div class="SVDv3_colonne_element_appelBoutique" id="banner-{{ loop.index }}"
             data-title="{{ banner.getTitle(app.request.getLocale()) }}"
             data-id="{{ banner.clickbait_id }}"
             data-location="{{ locations[0] }}"
             data-position="{{ loop.index }}"
             data-url="{{ banner.url }}"
        >
        {% endif %}
            <div class="SVDv3_appelBoutique">
                <a href="{{ banner.url }}" id="SVDv3_appelBoutique_{{ banner.clickbait_id }}">
                    <span class="uiV3_appelBoutique_fond lozad" data-background-image="{{ asset(banner.getMediaUri(app.request.getLocale()), "clickbait_images") }}">
                        <span class="SVDv3_appelBoutique_libelle">
                            <strong>{{ banner.getTitle(app.request.getLocale()) }}</strong>
                            <span class="SVDv3_appelBoutique_accroche" id="SVDv3_appelBoutique_accroche_{{ banner.clickbait_id }}">{{ banner.getHook(app.request.getLocale()) | raw }}</span>
                        </span>
                        <span class="SVDv3_appelBoutique_bouton">
                            <span>{{ banner.getOverTitle(app.request.getLocale()) }}</span>
                        </span>
                    </span>
                </a>
            </div>
        </div>
    {% endfor %}
{% endif %}

{%  if guides|length > 0 %}
    {% if display is defined and display == 'left_column' %}
        <div class="title-gradient-roundcorner title-gradient-roundcorner-grey">{% trans %}advice_sheets{% endtrans %}</div>
        <div class="SVDv3_colonne_element">
            <ul class="SVDv3_colonne_element_liste">
                {% for guide in guides %}
                    <li>
                        <a href="{{ path('show_guide', {'slug': guide.slug}) }}">{{ guide.name }}</a>
                    </li>
                {% endfor %}
            </ul>
            <ul class="SVDv3_colonne_element_liste">
                <li><a href="{{ path('show_guides_families') }}">{% trans from 'guide' %}all_the_guides{% endtrans %}</a></li>
            </ul>
        </div>
    {% else %}
        <div class="static-page">
            <div class="section section-light-grey">
                <h2 class="section-title text-center">{% trans %}advice_sheets{% endtrans %}</h2>
                <div class="section-body">
                    <div class="container">
                        <div class="row row-small-gutter guide-more">
                            {% for guide in guides %}
                                <div class="col-12 col-md-6 col-lg-4 grid-item">
                                    <div class="card card-sm is-row">
                                        {% set url = path('show_guide', {'slug': guide.slug}) %}
                                        <a href="{{ url }}">
                                            <div class="card-wrapper">
                                                <div class="card-media-wrapper">
                                                    {% if guide.thumbnail_image is defined and guide.thumbnail_image is not empty %}
                                                        <div class="card-media">
                                                            <img src="{{ empty_data_image(150, 90) }}"
                                                                 data-src="{{ asset(guide.thumbnail_image, 'static_images') }}"
                                                                 alt="{{ guide.name|e }}"
                                                                 class="lozad img-responsive">
                                                        </div>
                                                    {% endif %}
                                                </div>
                                                <div class="card-body">
                                                    <p class="card-title">{{ guide.name }}</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
{% endif %}


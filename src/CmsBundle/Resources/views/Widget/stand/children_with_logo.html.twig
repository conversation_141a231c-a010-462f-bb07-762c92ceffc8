<div class="SVDv3_article_element clearfix">
    {% set j = 0 %}
    {% set nb_column = 4 %}
    {% for stand in stand_children %}
        {% if stand.logo_uri is defined and stand.logo_uri is not empty %}
            {% set uri = asset(stand.logo_uri, 'static_images') %}
        {% else %}
            {% set uri = asset('/images/ui/uiV3/graphics/imageAVenir.gif', 'static_images') %}
        {% endif %}
        <div class="SVDv3_sommaireCategorie_cellule">
            <div class="SVDv3_sommaireCategorie_cellule_image">
                <p>
                    <a href="{{ path('show_stand', {'slug': stand.slug}) }}" title="{% trans with {'%category_name%': stand.name | capitalize } %}see_category{% endtrans %}">
                        <img class="lozad" src="{{ empty_data_image(180, 180) }}" data-src="{{ uri }}" border="0" alt="{{ stand.name }}">
                    </a>
                </p>
            </div>
            <h2 class="SVDv3_sommaireCategorie_cellule_texte">
                <a href="{{ path('show_stand', {'slug': stand.slug}) }}" title="{% trans with {'%category_name%': stand.name | capitalize } %}see_category{% endtrans %}">{{ stand.name }}</a>
            </h2>
        </div>
    {% endfor %}

{% if j > 0 and j < nb_column %}
    </div>
{% endif %}

</div>

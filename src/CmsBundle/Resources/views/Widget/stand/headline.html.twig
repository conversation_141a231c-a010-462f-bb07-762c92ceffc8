{% import 'AppBundle:Common:article_macro.html.twig' as article_macro %}
<div class="gondoles-products SVDv3_appelsB SVDv3_appelsB_3x1">
    <h2 class="gondoles-title">{% trans with {'%stand.name%': stand.name } from 'stand' %}shelf_products_selection_title{% endtrans %}</h2>
    <div class="container grid_container_12">
        <div class="row">
            {% set headline_amount = 1 %}
            {% for headline in stand_headline %}
                <div class="col-4">
                    {{ article_macro.article_column(headline, false, true) }}
                </div>
                {% set headline_amount = headline_amount + 1 %}
            {% endfor %}
        </div>
    </div>
</div>
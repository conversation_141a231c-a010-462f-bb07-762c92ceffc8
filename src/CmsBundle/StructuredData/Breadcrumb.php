<?php
/*
 * This file is part of CMS Front-Office package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\StructuredData;

/**
 * Class Breadcrumb
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\StructuredData
 * <AUTHOR> <<EMAIL>>
 */
class Breadcrumb implements StructuredDataInterface
{
    protected array $item_list;

    /**
     * Breadcrumb constructor.
     */
    public function __construct(array $item_list)
    {
        $this->item_list = $item_list;
    }

    /**
     * getItem
     */
    public function getData(): array
    {
        return [
            '@context' => 'http://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $this->item_list,
        ];
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\StructuredData;

/**
 * Class Review
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\StructuredData
 * <AUTHOR> <<EMAIL>>
 */
class Review implements StructuredDataInterface
{
    public const REVIEW_BODY_MAX_LENGTH = 200;

    private string $review_body;

    private string $culture;

    private \DateTime $date_published;

    private string $author_name;

    /**
     * @var int|string
     */
    private $rating_value;

    private string $publisher;

    private string $name;

    /**
     * Review constructor.
     *
     * @param string|int $rating_value
     */
    public function __construct(
        string $review_body,
        string $culture,
        $rating_value,
        \DateTime $date_published,
        string $author_name,
        string $publisher,
        ?string $name = null
    ) {
        $this->review_body = $review_body;
        $this->culture = $culture;
        $this->rating_value = $rating_value;
        $this->date_published = $date_published;
        $this->author_name = $author_name;
        $this->publisher = $publisher;
        $this->name = $name ?? '';
    }

    /**
     * getItem
     */
    public function getData(): array
    {
        $review_body = $this->review_body;
        if (preg_match(sprintf('/^.{1,%d}\b/s', static::REVIEW_BODY_MAX_LENGTH), $this->review_body, $match)) {
            $review_body = $match[0];
        }

        $name = trim($this->name) === '' || trim($this->name) === '0' ? [] : ['name' => $this->name];

        return array_merge($name, [
            '@type' => 'Review',
            'author' => (new Author($this->author_name))->getData(),
            'reviewBody' => $review_body,
            'inLanguage' => $this->culture,
            'datePublished' => $this->date_published->format('Y-m-d'),
            'reviewRating' => (new ReviewRating($this->rating_value))->getData(),
            'publisher' => [
                '@type' => 'Organization',
                'name' => $this->publisher,
            ],
        ]);
    }
}

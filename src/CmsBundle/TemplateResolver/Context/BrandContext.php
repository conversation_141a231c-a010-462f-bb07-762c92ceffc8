<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Brand;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\ContextInterface;

/**
 * Class BrandContext
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Context
 * <AUTHOR> <<EMAIL>>
 */
class BrandContext implements ContextInterface
{
    public const CONTEXT_TYPE = 'brand';

    protected Brand $brand;

    /**
     * BrandContext constructor.
     */
    public function __construct(Brand $brand)
    {
        $this->brand = $brand;
    }

    /**
     * {@inheritdoc}
     */
    public function getType(): string
    {
        return self::CONTEXT_TYPE;
    }

    /**
     * {@inheritdoc}
     * @return array{brand: mixed}
     */
    public function getContext(): array
    {
        return [
            'brand' => $this->brand,
        ];
    }
}

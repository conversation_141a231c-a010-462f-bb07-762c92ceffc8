<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\GuideSchema\Guide;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\ContextInterface;

/**
 * Class GuideContext
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context
 * <AUTHOR> <<EMAIL>>
 */
class GuideContext implements ContextInterface
{
    public const CONTEXT_TYPE = 'guide';

    protected Guide $guide;

    /**
     * GuideContext constructor.
     */
    public function __construct(Guide $guide)
    {
        $this->guide = $guide;
    }

    /**
     * {@inheritdoc}
     */
    public function getType(): string
    {
        return self::CONTEXT_TYPE;
    }

    /**
     * {@inheritdoc}
     * @return array{guide: Guide}
     */
    public function getContext(): array
    {
        return [
            'guide' => $this->guide,
        ];
    }
}

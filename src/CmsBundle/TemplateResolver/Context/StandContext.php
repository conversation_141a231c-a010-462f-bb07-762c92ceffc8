<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18n;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\ContextInterface;

/**
 * Class StandContext
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context
 * <AUTHOR> <<EMAIL>>
 */
class StandContext implements ContextInterface
{
    public const CONTEXT_TYPE = 'stand';

    protected StandI18n $stand;

    /**
     * StandContext constructor.
     */
    public function __construct(StandI18n $stand)
    {
        $this->stand = $stand;
    }

    /**
     * {@inheritdoc}
     */
    public function getType(): string
    {
        return self::CONTEXT_TYPE;
    }

    /**
     * {@inheritdoc}
     * @return array{stand: StandI18n}
     */
    public function getContext(): array
    {
        return [
            'stand' => $this->stand,
        ];
    }
}

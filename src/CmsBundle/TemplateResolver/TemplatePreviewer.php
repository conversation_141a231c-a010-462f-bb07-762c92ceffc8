<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver;

use PommProject\Foundation\Pomm;
use SonVideo\Cms\FrontOffice\CmsBundle\Exception\PreviewNotAuthorizedException;
use SonVideo\Cms\FrontOffice\CmsBundle\Exception\TypeNotValidForPreviewException;
use SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema\TemplateResolverModel;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema\TemplateResolver as TemplateResolverEntity;

/**
 * Class TemplatePreviewer
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle
 * <AUTHOR> <<EMAIL>>
 */
class TemplatePreviewer
{
    public const TEMPLATE_PREVIEW_TOKEN = 'TOKEN-SV-%s-FOR-PREVIEW';

    private Pomm $pomm;

    private RequestStack $request_stack;

    /**
     * TemplateResolver constructor.
     */
    public function __construct(Pomm $pomm, RequestStack $request_stack)
    {
        $this->pomm = $pomm;
        $this->request_stack = $request_stack;
    }

    /**
     * preview
     *
     * @return string|null
     * @throws TypeNotValidForPreviewException
     */
    public function preview(ContextInterface $context)
    {
        $template_resolver_id = $this->checkRequestIsPreviewMode();
        if (is_null($template_resolver_id)) {
            return null;
        }

        $template_resolver = $this->getTemplateResolverModel()->findByPK([
            'template_resolver_id' => $template_resolver_id,
        ]);

        if ($template_resolver instanceof TemplateResolverEntity) {
            if ($template_resolver->get('context_type') !== $context->getType()) {
                throw new TypeNotValidForPreviewException('The alternative template type is not valid for this page.');
            }

            return $template_resolver->get('template');
        }

        return null;
    }

    /**
     * getTemplateResolverModel
     */
    private function getTemplateResolverModel(): TemplateResolverModel
    {
        return $this->pomm->getDefaultSession()->getModel(TemplateResolverModel::class);
    }

    /**
     * checkRequestIsPreviewMode
     *
     * Check if the preview mode is asked in request.
     * Returns the template_resolver_id if yes, null otherwise.
     *
     * @return string|null
     * @throws PreviewNotAuthorizedException
     */
    private function checkRequestIsPreviewMode()
    {
        $request = $this->request_stack->getCurrentRequest();
        if (
            !$request instanceof Request ||
            is_null($request->get('template_resolver_id')) ||
            is_null($request->get('token'))
        ) {
            return null;
        }

        $this->checkPreviewAuthorization($request);

        return $request->get('template_resolver_id');
    }

    /**
     * checkPreviewAuthorization
     *
     * Check if the token is valid in request.
     *
     * @throws PreviewNotAuthorizedException
     */
    private function checkPreviewAuthorization(Request $request): self
    {
        if (!$this->tokenIsValid($request->get('token', ''))) {
            throw new PreviewNotAuthorizedException(
                'The token is not valid. You are not authorized to use the preview mode.',
            );
        }

        return $this;
    }

    /**
     * tokenIsValid
     *
     * Check if the token is valid or not.
     */
    private function tokenIsValid(string $token): bool
    {
        return password_verify(sprintf(self::TEMPLATE_PREVIEW_TOKEN, date('Y-m-d')), $token);
    }
}

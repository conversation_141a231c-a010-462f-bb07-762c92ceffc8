Feature: check column banners
  In order to check column banners on Son-Vidéo website
  As a guest visitor
  I need to be able to show column banners in several pages

Scenario: check column banners on article sheet
  Given   I am on "/article/elipson-prestige-4i-calvados-fr"
  Then    I should see 6 column banners
  # Default clickbaits (immediate recall and catalog)
  And     I should see a column banner with an image with "/images/appel/appelV3/boutique/SVDRAPPEL_201207-294x96.png" source
  And     I should see a column banner with a link to "javascript:void(0);"
  And     I should see a column banner with an image with "/images/illustrations/column_banner/d2f4dde0-65e0-4b9a-9d90-657e1f3cbcef_fr.png" source
  And     I should see a column banner with a link to "/catalogue"
  # Others clickbaits
  And     I should see a column banner with a link to "/guide/passionElipson"
  And     I should see a column banner with an image with "/images/appel/appelV3/boutique/SVDELIPSON_201210-294x96.png" source
  And     I should see a column banner with a link to "/selection/enceintes-elipson-prestige-i"
  And     I should see a column banner with an image with "/images/appel/appelV3/boutique/GammePrestigei_294x96.png" source
  And     I should see a column banner with a link to "/avis-clients"
  And     I should see a column banner with an image with "/images/appel/appelV3/boutique/SVDAVIS_201207-294x96.png" source

Scenario: check column banners on stand
  Given   I am on "/rayon/haute-fidelite"
  Then    I should see 3 column banners
  # Default clickbaits (immediate recall and catalog)
  And     I should see a column banner with an image with "/images/appel/appelV3/boutique/SVDRAPPEL_201207-294x96.png" source
  And     I should see a column banner with a link to "javascript:void(0);"
  And     I should see a column banner with an image with "/images/illustrations/column_banner/d2f4dde0-65e0-4b9a-9d90-657e1f3cbcef_fr.png" source
  And     I should see a column banner with a link to "/catalogue"
  # Clickbait for promo offer
  And     I should see a column banner with a link to "/rayon/haute-fidelite"

Scenario: check column banners on shop
  Given   I am on "/selection/coupe-d-europe-de-football"
  Then    I should see 3 column banner
  # Default clickbaits (immediate recall and catalog)
  And     I should see a column banner with an image with "/images/appel/appelV3/boutique/SVDRAPPEL_201207-294x96.png" source
  And     I should see a column banner with a link to "javascript:void(0);"
  And     I should see a column banner with an image with "/images/illustrations/column_banner/d2f4dde0-65e0-4b9a-9d90-657e1f3cbcef_fr.png" source
  And     I should see a column banner with a link to "/catalogue"
  # Others clickbaits
  And     I should see a column banner with a link to "/selections"
  And     I should see a column banner with an image with "/images/appel/appelV3/boutique/SVDBOU_201207-294x96.png" source

Scenario: check column banners on homepage
  Given   I am on "/"
  Then    I should see 3 column banner
  # Default clickbaits (immediate recall and catalog)
  And     I should see a column banner with an image with "/images/appel/appelV3/boutique/SVDRAPPEL_201207-294x96.png" source
  And     I should see a column banner with a link to "javascript:void(0);"
  And     I should see a column banner with an image with "/images/illustrations/column_banner/d2f4dde0-65e0-4b9a-9d90-657e1f3cbcef_fr.png" source
  And     I should see a column banner with a link to "/catalogue"
  # Specific clickbait
  And     I should see a column banner with a link to "/hifi-vintage.html"
  And     I should see a column banner with an image with "/images/appel/appelV3/boutique/SVDVINT_201207-294x96.png" source

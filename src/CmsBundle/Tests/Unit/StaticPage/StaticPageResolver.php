<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\StaticPage;

use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Cms\FrontOffice\CmsBundle\Model\CmsFrontOffice\CmsSchema\StaticPage;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\PommTest;
use SonVideo\Cms\FrontOffice\CmsBundle\StaticPage\StaticPageResolver as TestedClass;

/**
 * Class StaticPageResolver
 *
 * @package SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Manager\StaticPage
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class StaticPageResolver extends PommTest
{
    /**
     * getResolver
     */
    private function getResolver(): TestedClass
    {
        return $this->getContainer()->get('cms.static_page.resolver');
    }

    /**
     * testResolveOk
     */
    public function testResolveOk(): void
    {
        $this->assert('Check resolve returns a good entity')
            ->given($res = $this->getResolver()->resolve('/avis-facebook', 'fr'))
            ->object($res)
            ->isInstanceOf(StaticPage::class)
            ->string($res->get('uri'))
            ->isEqualTo('/avis-facebook')
            ->string($res->get('supported_culture_id'))
            ->isEqualTo('fr')
            ->integer($res->get('static_page_id'))
            ->isEqualTo(101)
            ->string($res->get('title'))
            ->string($res->get('editorial_content'))
            ->array($res->get('keywords'))
            ->string($res->get('description'))
            ->boolean($res->get('finded_for_culture'))
            ->isTrue();
    }

    /**
     * testResolveFails
     */
    public function testResolveFails(): void
    {
        $this->assert('Check resolve returns null if uri does not exist')
            ->given($res = $this->getResolver()->resolve('/avis-copains-d-avant', 'fr'))
            ->variable($res)
            ->isNull();
    }

    /**
     * testResolveWithUnknownCulture
     */
    public function testResolveWithUnknownCulture(): void
    {
        $this->assert('Check resolve for an existing uri but with unknown culture')
            ->given($res = $this->getResolver()->resolve('/avis-facebook', 'ch'))
            ->object($res)
            ->isInstanceOf(StaticPage::class)
            ->array($res->extract())
            ->string['uri']->isEqualTo('/avis-facebook')
            ->boolean['finded_for_culture']->isFalse()
            ->string['supported_culture_id']->isEqualTo('fr');
    }

    /**
     * testFindAllActive
     */
    public function testFindAllActive(): void
    {
        $this->assert('Check resolve returns a good entity')
            ->given($res = $this->getResolver()->findAllActive())
            ->object($res)
            ->isInstanceOf(CollectionIterator::class)
            ->integer(is_countable($res) ? count($res) : 0)
            ->isEqualTo(5);
    }
}

<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\StructuredData;

use atoum\atoum\test;
use SonVideo\Cms\FrontOffice\CmsBundle\StructuredData\AggregateRating as TestedClass;

/**
 * Class AggregateRating
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\StructuredData
 * <AUTHOR> <<EMAIL>>
 */
class AggregateRating extends test
{
    /**
     * testGetData
     */
    public function testGetData(): void
    {
        $this->assert('Get AggregateRating structred data.')
            ->given($object = new TestedClass(3.56, 457))
            ->and($aggregate_rating = $object->getData())
            ->array($aggregate_rating)
            ->string($aggregate_rating['@type'])
            ->isEqualTo('AggregateRating')
            ->string($aggregate_rating['ratingValue'])
            ->isEqualTo('3.56')
            ->integer($aggregate_rating['ratingCount'])
            ->isEqualTo(457)
            ->string($aggregate_rating['bestRating'])
            ->isEqualTo('5')
            ->string($aggregate_rating['worstRating'])
            ->isEqualTo('1')

            ->assert('Get AggregateRating changing best and worst rating.')
            ->given($object = new TestedClass('B+', 201))
            ->and($object->setBestRating('A'))
            ->and($object->setWorstRating('E'))
            ->and($aggregate_rating = $object->getData())
            ->array($aggregate_rating)
            ->string($aggregate_rating['@type'])
            ->isEqualTo('AggregateRating')
            ->string($aggregate_rating['ratingValue'])
            ->isEqualTo('B+')
            ->integer($aggregate_rating['ratingCount'])
            ->isEqualTo(201)
            ->string($aggregate_rating['bestRating'])
            ->isEqualTo('A')
            ->string($aggregate_rating['worstRating'])
            ->isEqualTo('E');
    }
}

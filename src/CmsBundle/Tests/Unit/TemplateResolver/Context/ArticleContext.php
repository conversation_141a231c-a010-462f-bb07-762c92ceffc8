<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\TemplateResolver\Context;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\ArticleSchema\Article;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context\ArticleContext as TestedClass;

/**
 * Class ArticleContext
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\TemplateResolver\Context
 * <AUTHOR> <<EMAIL>>
 */
class ArticleContext extends Test
{
    /**
     * getTestedInstance
     */
    public static function getTestedInstance(int $article_id): TestedClass
    {
        return new TestedClass(new Article(['article_id' => $article_id]));
    }

    /**
     * testGetType
     */
    public function testGetType(): void
    {
        $this->assert('Check the type.')
            ->given($type = self::getTestedInstance(1)->getType())
            ->string($type)
            ->isEqualTo('article');
    }

    /**
     * testGetContext
     */
    public function testGetContext(): void
    {
        $this->assert('Check the array context.')
            ->given($context = self::getTestedInstance(1)->getContext())
            ->array($context)
            ->hasKeys(['article'])
            ->object['article']->isInstanceOf(Article::class);
    }
}

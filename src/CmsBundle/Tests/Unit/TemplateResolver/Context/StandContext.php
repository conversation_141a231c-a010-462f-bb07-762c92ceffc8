<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\TemplateResolver\Context;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18n;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context\StandContext as TestedClass;

/**
 * Class StandContext
 *
 * @package SonVideo\Cms\FrontOffice\CmsBundle\Tests\Unit\TemplateResolver\Context
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class StandContext extends Test
{
    /**
     * getTestedInstance
     */
    public static function getTestedInstance(int $stand_id): TestedClass
    {
        return new TestedClass(new StandI18n(['stand_id' => $stand_id]));
    }

    /**
     * testGetType
     */
    public function testGetType(): void
    {
        $this->assert('Check the type.')
            ->given($type = self::getTestedInstance(1)->getType())
            ->string($type)
            ->isEqualTo('stand');
    }

    /**
     * testGetContext
     */
    public function testGetContext(): void
    {
        $this->assert('Check the array context.')
            ->given($context = self::getTestedInstance(1)->getContext())
            ->array($context)
            ->hasKeys(['stand'])
            ->object['stand']->isInstanceOf(StandI18n::class);
    }
}

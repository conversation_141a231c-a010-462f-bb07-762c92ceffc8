<?php
namespace SonVideo\Cms\FrontOffice\Infrastructure\Controller\Search;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\Search\SearchControllerTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Formatter\Http\JSendResponse;
use SonVideo\Cms\FrontOffice\Application\UseCase\Search\GetArticles\GetArticles;
use SonVideo\Cms\FrontOffice\Application\UseCase\Search\GetArticles\GetArticlesRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class SearchArticlesController extends BaseController
{
    use SearchControllerTrait;

    public const DEFAULT_PAGE_SIZE = 60;

    /**
     * @Route("/articles/_search", name="search_articles", methods={"POST"})
     */
    public function _invoke(Request $sf_request, GetArticles $get_articles): Response
    {
        $this->mustBeXmlHttpRequest($sf_request);

        $parameters = json_decode($sf_request->getContent(), true, 512, JSON_THROW_ON_ERROR);

        $request = new GetArticlesRequest();
        $request->article_ids = $parameters['article_ids'];
        $request->page = $parameters['page'] ?? 1;
        $request->page_size = $parameters['item_per_page'] ?? self::DEFAULT_PAGE_SIZE;
        $request->sort = $parameters['sort_by'] ?? [];

        $use_case_response = $get_articles->execute($request);

        return JSendResponse::success([
            'pager' => [
                'item_per_page' => $use_case_response->page_size,
                'page' => $use_case_response->page,
                'last_page' => $use_case_response->last_page,
                'has_previous_page' => $use_case_response->page > 1,
                'has_next_page' => $use_case_response->page < $use_case_response->last_page,
                'count' => $use_case_response->total_count,
            ],
            'articles' => $use_case_response->articles,
        ]);
    }
}

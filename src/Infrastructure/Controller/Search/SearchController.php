<?php
namespace SonVideo\Cms\FrontOffice\Infrastructure\Controller\Search;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\Application\UseCase\Search\GetSearchResults\GetSearchResults;
use SonVideo\Cms\FrontOffice\Application\UseCase\Search\GetSearchResults\GetSearchResultsRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class SearchController extends BaseController
{
    /**
     * @Route("/recherche", name="search", methods={"GET"})
     * @Route("/recherche-resultats.html", name="old_search", methods={"GET"})
     */
    public function __invoke(Request $sf_request, GetSearchResults $get_search_results, string $_route): Response
    {
        $terms_parameter = $_route === 'old_search' ? 'mot_cle' : 'termes';

        $request = new GetSearchResultsRequest();
        $request->terms = $sf_request->query->get($terms_parameter, '');
        $request->page_size = 1_000;

        $use_case_response = $get_search_results->execute($request);
        $search_terms = trim($request->terms);

        return $this->render('AppBundle:Search:search.html.twig', [
            'search_data' => json_encode(
                [
                    'on_search_page' => true,
                    'show_all_data' => true,
                    'articles' => $use_case_response->results,
                    'search_terms' => $search_terms,
                ],
                JSON_THROW_ON_ERROR,
            ),
            'search_terms' => $search_terms,
            'total_items' => $use_case_response->total,
        ]);
    }
}

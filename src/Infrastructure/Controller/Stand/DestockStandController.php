<?php
namespace SonVideo\Cms\FrontOffice\Infrastructure\Controller\Stand;

use SonVideo\Cms\FrontOffice\AppBundle\Controller\BaseController;
use SonVideo\Cms\FrontOffice\AppBundle\Controller\Stand\StandControllerTrait;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\Stand;
use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\TaxonomySchema\StandI18n;
use SonVideo\Cms\FrontOffice\Application\UseCase\Stand\GetStand\GetStand;
use SonVideo\Cms\FrontOffice\Application\UseCase\Stand\GetStand\GetStandRequest;
use SonVideo\Cms\FrontOffice\CmsBundle\Controller\TemplateResolverControllerTrait;
use SonVideo\Cms\FrontOffice\CmsBundle\TemplateResolver\Context\StandContext;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;

class DestockStandController extends BaseController
{
    use TemplateResolverControllerTrait;
    use StandControllerTrait;

    private const SUB_SELECTIONS = [
        'accessoires' => [
            'article_selection' => '87b9c00c-9efb-4ba5-839c-8c39a1dda3b0',
            'title' => 'Seconde vie accessoires',
            'hero_banner' => '/images/illustration/pages/seconde-vie/Categorie-Accessoires-HeroHeader.jpg',
            'hero_banner_alt' => 'Offres de seconde vie Accessoires',
        ],
        'haute-fidelite' => [
            'article_selection' => '1e44ba11-b2a1-4691-bc43-4162dd88e06c',
            'title' => 'Seconde vie haute-fidélité',
            'hero_banner' => '/images/illustration/pages/seconde-vie/Categorie-Hifi-HeroHeader.jpg',
            'hero_banner_alt' => 'Offres de seconde vie haute-fidélité',
        ],
        'home-cinema' => [
            'article_selection' => '2dd6043a-0881-450f-b98e-88a59f7eb4ef',
            'title' => 'Seconde vie home-cinéma',
            'hero_banner' => '/images/illustration/pages/seconde-vie/Categorie-HC-HeroHeader.jpg',
            'hero_banner_alt' => 'Offres de seconde vie home-cinéma',
        ],
        'tv-et-videoprojection' => [
            'article_selection' => 'c248915f-db77-4e56-9475-d733d6879658',
            'title' => 'Seconde vie TV / vidéoprojection',
            'hero_banner' => '/images/illustration/pages/seconde-vie/Categorie-TVProj-HeroHeader.jpg',
            'hero_banner_alt' => 'Offres de seconde vie TV / vidéoprojection',
        ],
        'vinyle' => [
            'article_selection' => '963812e8-24ea-4899-9896-7f18bbb90349',
            'title' => 'Seconde vie vinyle',
            'hero_banner' => '/images/illustration/pages/seconde-vie/Categorie-Vinyle-HeroHeader.jpg',
            'hero_banner_alt' => 'Offres de seconde vie Vinyle',
        ],
        'maison-connectee' => [
            'article_selection' => '5ba03a5a-cfe1-4289-a59d-1047925a56bb',
            'title' => 'Seconde vie maison connectée',
            'hero_banner' => '/images/illustration/pages/seconde-vie/Categorie-maisonConnectee-HeroHeader.jpg',
            'hero_banner_alt' => 'Offres de seconde vie Maison Connectée',
        ],
        'gaming' => [
            'article_selection' => '82b61400-a042-4d28-a12e-844efa6dfdbb',
            'title' => 'Seconde vie gaming',
            'hero_banner' => '/images/illustration/pages/seconde-vie/Categorie-gaming-HeroHeader.jpg',
            'hero_banner_alt' => 'Offres de seconde vie Gaming',
        ],
        'casques' => [
            'article_selection' => 'b7fa299c-73e8-4cf2-92d8-608d7bf1c4ed',
            'title' => 'Seconde vie casques',
            'hero_banner' => '/images/illustration/pages/seconde-vie/Categorie-casques-HeroHeader.jpg',
            'hero_banner_alt' => 'Offres de seconde vie Casques',
        ],
    ];

    /**
     * @Route("/reconditionnes", name="show_old_destock_stand", methods={"GET"})
     * @Route("/reconditionnes/{slug}", requirements={"slug": "^[\w-]+$"}, name="show_old_destock_stand_slug", methods={"GET"})
     * @Route("/seconde-vie", name="show_destock_stand", methods={"GET"})
     * @Route("/seconde-vie/{slug}", requirements={"slug": "^[\w-]+$"}, name="show_destock_stand_slug", methods={"GET"})
     */
    public function _invoke(Request $sf_request, GetStand $get_stand, ?string $slug = null): Response
    {
        if ($slug !== null && !array_key_exists($slug, self::SUB_SELECTIONS)) {
            throw new NotFoundHttpException('Sélection de Seconde Vie inconnue');
        }

        $request = new GetStandRequest();
        $request->slug = Stand::DESTOCK_SLUG;
        $request->culture = $sf_request->getLocale();
        $request->destock_sub_selection = self::SUB_SELECTIONS[$slug]['article_selection'] ?? null;

        $response = $get_stand->execute($request);
        if (!$response->stand instanceof StandI18n) {
            throw $this->createNotFoundException(
                sprintf("Stand identified by '%s' (locale '%s') does not exist.", $request->slug, $request->culture),
            );
        }

        $view_parameters = [
            'stand' => $response->stand,
            'is_sub_selection' => $slug !== null,
            'structured_data' => $response->structured_data,
            'guide' => $response->guide,
            'canonical_url' => $this->generateUrl('show_destock_stand'),
            'title' => self::SUB_SELECTIONS[$slug]['title'] ?? 'Seconde vie',
            'hero_banner' =>
                self::SUB_SELECTIONS[$slug]['hero_banner'] ??
                '/images/illustration/pages/seconde-vie/Categorie-Accueil-HeroHeader.jpg',
            'hero_banner_alt' => self::SUB_SELECTIONS[$slug]['hero_banner_alt'] ?? 'Toutes les offres de seconde vie',
            'search_data' => json_encode(
                [
                    'default_selected_sorting_option' => $response->stand->get('sort_by'),
                    'show_all_data' => true,
                    'articles' => $response->search_app_results,
                    'display' => $response->stand->get('display_type'),
                ],
                JSON_THROW_ON_ERROR,
            ),

            'total_items' => $response->search_app_total,
            'articles' => $response->search_app_results,
        ];

        // TODO: si supprimé/remplacé, StandControllerTrait n’est plus utile
        $this->addStandTags($view_parameters);

        // Template Resolver
        if (
            ($resolver_response = $this->resolveTemplate(
                new StandContext($response->stand),
                $view_parameters,
            )) instanceof Response
        ) {
            return $resolver_response;
        }

        return $this->render('AppBundle:Stand:show_destock.html.twig', $view_parameters);
    }
}

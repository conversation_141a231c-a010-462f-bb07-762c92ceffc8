### FULL CLASSES DUMP FOR TAILWIND CSS ###
# This is needed since the classes are dynamically added to the utility classes
# and to do so tailwind needs to see the full string in the html
# This is configured tin tailwind.config.js

### @see src/AppBundle/Twig/HighlightV5Extension.php::classes() ###
bg-svd-blue
bg-svd-error
bg-svd-highlight-black-friday
bg-svd-highlight-destock
bg-svd-highlight-exclusive
bg-svd-highlight-french-days
bg-svd-highlight-halloween
bg-svd-highlight-pre-order
bg-svd-highlight-promotion
bg-svd-highlight-sales
bg-svd-highlight-vente-prive
bg-svd-highlight-gift-idea
bg-svd-highlight-reference-price

<?php

namespace SonVideo\Cms\FrontOffice\Tests\Unit\Application\Manager\Payment\RedirectBackReturnUrl\Handler;

use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;
use SonVideo\Cms\FrontOffice\Application\Manager\Payment\Exception\UnexpectedResultException;
use SonVideo\Cms\FrontOffice\Application\Manager\Payment\RedirectBackReturnUrl\Handler\PrestoRedirectBackAfterPaymentHandler as TestedClass;
use Symfony\Component\HttpFoundation\Request;

class PrestoRedirectBackAfterPaymentHandler extends Test
{
    private function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get(TestedClass::class);
    }

    public function test_is_not_successful(): void
    {
        $tested_instance = $this->getTestedInstance();

        $this->exception(function () use ($tested_instance): void {
            $request = new Request([], [], [], [], [], [], [], '');
            $tested_instance->isSuccessful($request);
        })->isInstanceOf(UnexpectedResultException::class);

        $request = new Request(['status' => 'SOMETHING ELSE'], [], [], [], [], [], [], '');
        $this->boolean($tested_instance->isSuccessful($request))->isFalse();
    }

    public function test_is_successful(): void
    {
        $tested_instance = $this->getTestedInstance();

        $request = new Request(['status' => 'DONE'], [], [], [], [], [], [], '');

        $this->boolean($tested_instance->isSuccessful($request))->isTrue();

        $request = new Request([], ['status' => 'LATER'], [], [], [], [], [], '');

        $this->boolean($tested_instance->isSuccessful($request))->isTrue();
    }
}

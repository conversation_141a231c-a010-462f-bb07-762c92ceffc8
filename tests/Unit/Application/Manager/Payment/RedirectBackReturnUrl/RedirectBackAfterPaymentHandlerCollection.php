<?php

namespace SonVideo\Cms\FrontOffice\Tests\Unit\Application\Manager\Payment\RedirectBackReturnUrl;

use SonVideo\Cms\FrontOffice\AppBundle\Model\CmsFrontOffice\PaymentSchema\PaymentMethod;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;
use SonVideo\Cms\FrontOffice\Application\Manager\Payment\RedirectBackReturnUrl\Handler\PrestoRedirectBackAfterPaymentHandler;
use SonVideo\Cms\FrontOffice\Application\Manager\Payment\RedirectBackReturnUrl\RedirectBackAfterPaymentHandlerCollection as TestedClass;

class RedirectBackAfterPaymentHandlerCollection extends Test
{
    private function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get(TestedClass::class);
    }

    public function test_no_handler_found(): void
    {
        $tested_instance = $this->getTestedInstance();

        $this->exception(function () use ($tested_instance): void {
            $tested_instance->getHandler('toto');
        })->isInstanceOf(\RuntimeException::class);
    }

    public function test_handler_found(): void
    {
        $handler = $this->getTestedInstance()->getHandler(PaymentMethod::PRESTO_CODE);

        $this->object($handler)->isInstanceOf(PrestoRedirectBackAfterPaymentHandler::class);
    }
}

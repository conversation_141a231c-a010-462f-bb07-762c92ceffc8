<?php

namespace SonVideo\Cms\FrontOffice\Tests\Unit\Application\Manager\Payment\Report\Worldline;

use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;
use SonVideo\Cms\FrontOffice\Application\Client\PaymentV2ClientInterface;
use SonVideo\Cms\FrontOffice\Application\Manager\Payment\Exception\UnauthorizedIpException;
use SonVideo\Cms\FrontOffice\Application\Manager\Payment\Report\Worldline\WorldlineDailyFinancialReportHandler as TestedClass;
use SonVideo\Cms\FrontOffice\Tests\Utils\PaymentV2ClientTestTrait;
use Symfony\Component\HttpFoundation\Request;

class WorldlineDailyFinancialReportHandler extends Test
{
    private const REGULAR_URL = 'worldline';
    private const DEBUG_URL = 'worldline?debug';

    private function getTestedInstance(PaymentV2ClientInterface $client, $env = 'test'): TestedClass
    {
        return new TestedClass($client, $this->getContainer()->get('logger'), $env);
    }

    private static function withAwsTrustedProxies(Request $request): Request
    {
        // Copied from index.php which is only used in prod
        $request::setTrustedProxies(
            ['10.0.0.0/8'],
            Request::HEADER_X_FORWARDED_HOST + Request::HEADER_X_FORWARDED_AWS_ELB,
        );

        return $request;
    }

    public function test_passthroughs(): void
    {
        // FORBID
        $this->assert('Should not allow the request with a non allowed IP');

        $tested_instance = $this->getTestedInstance(
            new class implements PaymentV2ClientInterface {
                use PaymentV2ClientTestTrait;
            },
        );

        $this->exception(function () use ($tested_instance): void {
            $tested_instance->passthroughs(
                self::withAwsTrustedProxies(
                    new Request(
                        [],
                        [],
                        [],
                        [],
                        [],
                        ['REMOTE_ADDR' => '********', 'REQUEST_URI' => self::REGULAR_URL],
                        json_encode(['toto' => 'a la piscine']),
                    ),
                ),
            );
        })
            ->isInstanceOf(UnauthorizedIpException::class)
            ->hasMessage('[Worldline] Invalid IP: ********');

        // BYPASS FAILED
        $this->assert('Should not allow to bypass the IP filtering when on prod env');

        $tested_instance = $this->getTestedInstance(
            new class implements PaymentV2ClientInterface {
                use PaymentV2ClientTestTrait;
            },
            'prod',
        );

        $this->exception(function () use ($tested_instance): void {
            $tested_instance->passthroughs(
                self::withAwsTrustedProxies(
                    new Request(
                        [],
                        [],
                        [],
                        [],
                        [],
                        ['REMOTE_ADDR' => '********', 'REQUEST_URI' => self::DEBUG_URL],
                        json_encode(['toto' => 'a la piscine']),
                    ),
                ),
            );
        })
            ->isInstanceOf(UnauthorizedIpException::class)
            ->hasMessage('[Worldline] Invalid IP: ********');

        // BYPASS
        // test without assertion, the method call returns type is void
        // should not raise an exception either
        $this->assert('Should allow to bypass the IP filtering when not on prod env');

        $tested_instance = $this->getTestedInstance(
            new class implements PaymentV2ClientInterface {
                use PaymentV2ClientTestTrait;
            },
        );

        $tested_instance->passthroughs(
            self::withAwsTrustedProxies(
                new Request(
                    [],
                    [],
                    [],
                    [],
                    [],
                    ['REMOTE_ADDR' => '********', 'REQUEST_URI' => self::DEBUG_URL],
                    json_encode(['toto' => 'a la piscine']),
                ),
            ),
        );

        // PASS
        $this->assert('Should pass-through when within specified IP range');

        $tested_instance = $this->getTestedInstance(
            new class implements PaymentV2ClientInterface {
                use PaymentV2ClientTestTrait;
            },
        );

        $tested_instance->passthroughs(
            self::withAwsTrustedProxies(
                new Request(
                    [],
                    [],
                    [],
                    [],
                    [],
                    [
                        'REMOTE_ADDR' => '********',
                        'HTTP_X_FORWARDED_FOR' => '***************',
                        'REQUEST_URI' => self::REGULAR_URL,
                    ],
                    json_encode(['toto' => 'a la piscine']),
                ),
            ),
        );

        // Oops !
        $this->assert('Should fail when there has been an issue while contacting Payment-API');

        $tested_instance = $this->getTestedInstance(
            new class implements PaymentV2ClientInterface {
                use PaymentV2ClientTestTrait;

                public function post(string $relative_endpoint, array $data = []): ?array
                {
                    throw new \Exception('Something went wrong');
                }
            },
        );

        $this->exception(function () use ($tested_instance): void {
            $tested_instance->passthroughs(
                self::withAwsTrustedProxies(
                    new Request(
                        [],
                        [],
                        [],
                        [],
                        [],
                        [
                            'REMOTE_ADDR' => '********',
                            'HTTP_X_FORWARDED_FOR' => '***************',
                            'REQUEST_URI' => self::REGULAR_URL,
                        ],
                        json_encode(['toto' => 'a la piscine']),
                    ),
                ),
            );
        })
            ->isInstanceOf(\Exception::class)
            ->hasMessage('Something went wrong');
    }
}

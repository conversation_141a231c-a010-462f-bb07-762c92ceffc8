<?php

namespace SonVideo\Cms\FrontOffice\Tests\Unit\Infrastructure\Adapter\AppSearch;

use Elastic\EnterpriseSearch\AppSearch\Endpoints;
use Elastic\EnterpriseSearch\AppSearch\Request\SearchExplain;
use Elastic\EnterpriseSearch\AppSearch\Schema\SearchRequestParams;
use Elastic\EnterpriseSearch\Response\Response;
use SonVideo\Cms\FrontOffice\AppBundle\Tests\Unit\Test;
use SonVideo\Cms\FrontOffice\Infrastructure\Adapter\AppSearch\AppSearchClient as TestedClass;

class AppSearchClient extends Test
{
    private $mocked_app_search;

    private function getTestedInstance()
    {
        $tested_class = new class (
            ['token' => 'dontcare', 'endpoint' => 'dontcare'],
            'my-engine',
            $this->mocked_app_search,
        ) extends TestedClass {
            public function __construct(array $app_search_config, string $engine_name, $app_search)
            {
                parent::__construct($app_search_config, $engine_name);

                $this->app_search = $app_search;
            }
        };

        return $tested_class;
    }

    public function test_limited_terms_length(): void
    {
        $this->__get('mockGenerator')->orphanize('__construct');
        $this->mocked_app_search = $this->newMockInstance(Endpoints::class);
        $tested_instance = $this->getTestedInstance();

        $this->__get('mockGenerator')->orphanize('__construct');
        $search_explain_response = $this->newMockInstance(Response::class);
        $this->__get('mockGenerator')->orphanize('__construct');
        $search_es_response = $this->newMockInstance(Response::class);
        $this->mocked_app_search->getMockController()->searchExplain = fn(): Response => $search_explain_response;
        $this->mocked_app_search->getMockController()->searchEsSearch = fn(): Response => $search_es_response;

        $search_explain_response->getMockController()->asArray = fn(): array => [
            'query_body' => [],
        ];
        $search_es_response->getMockController()->asArray = fn(): array => [
            'hits' => [
                'total' => ['value' => 0],
                'hits' => [],
            ],
        ];

        $this->assert('skip terms beyond 10th');
        $more_than_10_terms = '01 02 03 04 05 06 07 08 09 10 11 12';
        $this->given($tested_instance->searchWithAggregations($more_than_10_terms));

        $only_10_terms = '01 02 03 04 05 06 07 08 09 10';
        $limited_search_explain = new SearchExplain('my-engine', new SearchRequestParams($only_10_terms));
        $this->mock($this->mocked_app_search)
            ->call('searchExplain')
            ->withArguments($limited_search_explain)
            ->once();

        // It's a nightmare to test that analytics terms are limited too.
        // It feels like directly writing the code, I give up.
    }
}

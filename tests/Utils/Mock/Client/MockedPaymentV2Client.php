<?php

namespace SonVideo\Cms\FrontOffice\Tests\Utils\Mock\Client;

use Psr\Log\LoggerInterface;
use SonVideo\Cms\FrontOffice\Application\Client\PaymentV2ClientInterface;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

class MockedPaymentV2Client implements PaymentV2ClientInterface
{
    /** @var string */
    private $kernel_root_dir;

    /** @var LoggerInterface */
    private $logger;

    public function __construct(string $kernel_root_dir, LoggerInterface $logger)
    {
        $this->kernel_root_dir = $kernel_root_dir;
        $this->logger = $logger;
    }

    public function post(string $relative_endpoint, array $data = []): ?array
    {
        return $this->getSavedResult($relative_endpoint);
    }

    public function get(string $relative_endpoint): ?array
    {
        return $this->getSavedResult($relative_endpoint);
    }

    public function getSavedResult(string $relative_endpoint)
    {
        $cache = $this->getCache();
        $mock_result = $cache->getItem(self::getItemName($relative_endpoint));
        if ($mock_result->isHit()) {
            $data = json_decode($mock_result->get(), true, 512, JSON_THROW_ON_ERROR);

            $this->logger->debug('[Payment v2] Get saved result', compact('relative_endpoint', 'data'));

            return $data;
        }

        throw new \Exception('[Payment v2] Called get saved result without hitting the cache]');
    }

    public function mock($relative_endpoint, $expected_result)
    {
        $this->logger->debug('[Payment v2] Save mock', compact('relative_endpoint', 'expected_result'));

        $cache = $this->getCache();
        // cleanup old result if any
        $cache->deleteItem(self::getItemName($relative_endpoint));

        // set new expected result
        $mock_result = $cache->getItem(self::getItemName($relative_endpoint));
        $mock_result->set($expected_result);
        $cache->save($mock_result);
    }

    private static function getItemName(string $relative_endpoint): string
    {
        return sprintf('payment_v2.%s', md5($relative_endpoint));
    }

    private function getCache(): FilesystemAdapter
    {
        return new FilesystemAdapter('behat_test', 0, dirname($this->kernel_root_dir) . '/var/cache/test');
    }

    public function clearCache(): void
    {
        $this->getCache()->clear();
    }
}

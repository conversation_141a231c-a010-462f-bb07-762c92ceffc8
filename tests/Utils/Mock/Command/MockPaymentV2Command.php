<?php

namespace SonVideo\Cms\FrontOffice\Tests\Utils\Mock\Command;

use Psr\Cache\InvalidArgumentException;
use SonVideo\Cms\FrontOffice\Tests\Utils\Mock\Client\MockedPaymentV2Client;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class MockPaymentV2Command extends Command
{
    protected static $defaultName = 'cms:mock-payment-v2-call';

    /** @var MockedPaymentV2Client */
    private $mocked_payment_v2_client;

    public function __construct(MockedPaymentV2Client $mocked_payment_v2_client, $name = null)
    {
        parent::__construct($name);

        $this->mocked_payment_v2_client = $mocked_payment_v2_client;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setDescription('Mock a Payment v2 call with given parameters.')
            ->addOption('uri', null, InputOption::VALUE_REQUIRED, 'The uri of the RPC service to call.')
            ->addOption(
                'result',
                null,
                InputOption::VALUE_REQUIRED,
                'The result to send back once the service has been called.',
            );
    }

    /**
     * {@inheritdoc}
     * @throws InvalidArgumentException
     */
    protected function execute(InputInterface $input, OutputInterface $output): ?int
    {
        $result = $input->getOption('result');

        // $result must be a json encoded value that can be decoded properly
        try {
            json_decode($result, true, 512, JSON_THROW_ON_ERROR);
        } catch (\Exception $exception) {
            throw new \InvalidArgumentException('The result is not properly JSON encoded.', 1, $exception);
        }

        $this->mocked_payment_v2_client->mock($input->getOption('uri'), $result);

        return 0;
    }
}

#contact-header
{
    background: #1b78bb;
}

#contact-header .panel,
#contact-header h1
{
    border-color: #fff;
}

@media (min-width: 1200px)
{
    #contact-header {
        background: #1b78bb url('https://image.son-video.comimages/illustration/pages/contact/Image-fond.jpg') no-repeat -20% center;
    }
}

@media (min-width: 1500px)
{
    #contact-header {
        background-position: left center;
    }
}


@media (max-device-width: 1200px)
{
    #contact-header {
        background: #1b78bb;
    }
}

@media (max-device-width: 991px)
{
    #contact-header
    {
        height: auto;
        text-align: center;
    }

    .mceContentBody ul li:before
    {
        position: inherit;
        display: inline;
        padding-right: 5px;
    }

    #contact-header .panel
    {
        border: none;
        border-top: 1px solid #4d92c3;
        margin-bottom: 0;
    }

    .SVDv3_masthead_wrapper
    {
        padding: 30px 0 0;
    }

}

#contact-header .btn-offwhite
{
    background: #fff;
    color: #1b78bb;
    font-weight: 700;
}

#contact-header .btn-default
{
    border-color: #fff;
}

a.contact-header-link
{
    text-decoration: underline;
    color: #fff;
}

$(document).ready( function() {
//accordions default behavior on click
    $('.accordion-toggle').click(function(){

        toggleTab (this);

    });

//toggle all switch
    $('.switch input').click(function() {
        if ($(this).is(':checked')) {
            $('h3.accordion-toggle').next().slideDown('fast').prev().addClass('active');
            $('.switch-box p').replaceWith('<p>Masquer toutes les r&eacute;ponses</p>');
            $('.switch input').addClass('checked');
        } else {
            $('h3.accordion-toggle.active').toggleClass('active');
            $('h3.accordion-toggle').next().slideUp('fast');
            $('.switch-box p').replaceWith('<p>Afficher toutes les r&eacute;ponses</p>');
            $('.switch input').removeClass('checked');
        }
    });

//toggle accordion anchors when arriving from external link
    var hash = document.URL.substr(document.URL.indexOf('#'));
    toggleTab (hash);

//toggle accordion anchors from internal page links
    $('.faq-anchor').click(function() {
        //location.reload();
        var href = $(this).attr("href");
        var hash = href.substr(href.indexOf("#"));
        slideTab (hash);
    });

});

//functions for anchors links behavior
function toggleTab(target){
    $(target).toggleClass( "active" );
    //Expand or collapse this panel
    $(target).next().slideToggle('fast');
}

function slideTab(target){
    $(target).addClass( "active" );
    //Expand or collapse this panel
    $(target).next().slideDown('fast');
}

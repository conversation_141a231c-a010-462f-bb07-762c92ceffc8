const Encore = require('@symfony/webpack-encore')
const path = require('path')
const fs = require('fs')
const YAML = require('yaml')
const ASSETS_ROOT = path.resolve(__dirname, 'src/AppBundle/Resources')

// resolve path (alias @)
// https://symfony.com/doc/current/frontend/encore/faq.html#how-do-i-integrate-my-encore-configuration-with-my-ide
if (!Encore.isRuntimeEnvironmentConfigured()) {
    Encore.configureRuntimeEnvironment(process.env.NODE_ENV || 'dev')
}

Encore
    // directory where compiled assets will be stored
    .setOutputPath(path.resolve(__dirname, 'web/dist/'))
    // public path used by the web server to access the output path
    .setPublicPath('/dist')

    /*
     * ENTRY CONFIG
     *
     * Add 1 entry for each "page" of your app
     * (including one that's included on every page - e.g. "app")
     *
     * Each entry will result in one JavaScript file (e.g. app.js)
     * and one CSS file (e.g. app.css) if your JavaScript imports CSS.
     */
    .addEntry('globals', `${ASSETS_ROOT}/js/globals.js`)
    .addEntry('v5/globals', `${ASSETS_ROOT}/js/v5/globals.js`)
    .addEntry('v5/main', `${ASSETS_ROOT}/js/v5/main.js`)
    .addEntry('developer_settings/app', `${ASSETS_ROOT}/js/developer_settings/app.js`)
    // --> ALL THE ENTRIES BELOW SHOULD BE SORTED ALPHABETICALLY <--
    .addEntry('about_style', `${ASSETS_ROOT}/js/about_style.js`)
    .addEntry('account_style', `${ASSETS_ROOT}/js/account_style.js`)
    .addEntry('article_style', `${ASSETS_ROOT}/js/article.js`)
    .addEntry('article_sticky_style', `${ASSETS_ROOT}/js/article_sticky_style.js`)
    .addEntry('article/add_arrows', `${ASSETS_ROOT}/js/common/add_arrows.js`)
    .addEntry('article/desktop_basket_sticky', `${ASSETS_ROOT}/js/common/desktop_basket_sticky.js`)
    .addEntry('base', `${ASSETS_ROOT}/js/base.js`)
    .addEntry('br', `${ASSETS_ROOT}/js/common/bloomreach.js`)
    .addEntry('clickbait/clickbait_column', `${ASSETS_ROOT}/js/clickbait/clickbait_column.js`)
    .addEntry('clickbait/clickbait_column_article', `${ASSETS_ROOT}/js/clickbait/clickbait_column_article.js`)
    .addEntry('contact_us_style', `${ASSETS_ROOT}/js/contact_us_style.js`)
    .addEntry('editorial_style', `${ASSETS_ROOT}/js/editorial_style.js`)
    .addEntry('enlarge_common', `${ASSETS_ROOT}/js/enlarge_common.js`)
    .addEntry('form_style', `${ASSETS_ROOT}/js/form_style.js`)
    .addEntry('fancybox_style', `${ASSETS_ROOT}/js/fancybox_style.js`)
    .addEntry('funnel_loader', `${ASSETS_ROOT}/js/funnel_loader_style.js`)
    .addEntry('leaflet_style', `${ASSETS_ROOT}/js/leaflet_style.js`)
    .addEntry('guides', `${ASSETS_ROOT}/js/guides.js`)
    .addEntry('installations_style', `${ASSETS_ROOT}/js/installations_style.js`)
    .addEntry('login_style', `${ASSETS_ROOT}/js/login_style.js`)
    .addEntry('map/utils', `${ASSETS_ROOT}/js/shared/map.js`)
    .addEntry('order/pay', `${ASSETS_ROOT}/js/order/pay/index.js`)
    .addEntry('pdf', `${ASSETS_ROOT}/js/pdf.js`)
    .addEntry('stand_domain_style', `${ASSETS_ROOT}/js/stand_domain_style.js`)
    .addEntry('stand_level_2_style', `${ASSETS_ROOT}/js/stand_level_2_style.js`)
    .addEntry('stand_v2_style', `${ASSETS_ROOT}/js/stand_v2_style.js`)
    .addEntry('store_call_style', `${ASSETS_ROOT}/js/store_call_style.js`)
    .addEntry('sennheiser_style', `${ASSETS_ROOT}/js/sennheiser_style.js`)
    .addEntry('svdmaps', `${ASSETS_ROOT}/js/svdmaps.js`)
    .addEntry('tabs_basket', `${ASSETS_ROOT}/js/common/tabs_basket.js`)
    .addEntry('testimony', `${ASSETS_ROOT}/js/testimony.js`)
    .addEntry('timeline_style_history', `${ASSETS_ROOT}/js/timeline_style_history.js`)
    .addEntry('trombinoscope_style', `${ASSETS_ROOT}/js/trombinoscope_style.js`)
    .addEntry('touch_screen', `${ASSETS_ROOT}/js/touch_screen.js`)
    .addEntry('v5/common', `${ASSETS_ROOT}/js/v5/common.js`)
    .addEntry('v5/home', `${ASSETS_ROOT}/js/home/<USER>
    // --> ALL THE TWIG COMPONENTS <--
    .addEntry('component/input', `${ASSETS_ROOT}/views/V5/Component/input/input.js`)
    .addEntry('component/button', `${ASSETS_ROOT}/views/V5/Component/button/button.js`)
    .addEntry('component/toggle', `${ASSETS_ROOT}/views/V5/Component/toggle/toggle.js`)
    .addEntry('component/link', `${ASSETS_ROOT}/views/V5/Component/link/link.js`)
    .addEntry('component/icon', `${ASSETS_ROOT}/views/V5/Component/icon/icon.js`)
    .addEntry('component/message', `${ASSETS_ROOT}/views/V5/Component/message/message.js`)
    .addEntry('component/tooltip', `${ASSETS_ROOT}/views/V5/Component/tooltip/tooltip.js`)
    .addEntry('component/badge', `${ASSETS_ROOT}/views/V5/Component/badge/badge.js`)
    .addEntry('component/google_reviews', `${ASSETS_ROOT}/views/V5/Component/google_reviews/google_reviews.js`)
    .addEntry('component/account/menu', `${ASSETS_ROOT}/views/Customer/components/account_menu.js`)
    .addEntry('component/account/title', `${ASSETS_ROOT}/views/Customer/components/account_title.js`)
    // --> ALL THE TWIG LAYOUT <--
    .addEntry('layout/footer', `${ASSETS_ROOT}/views/V5/Layout/footer/footer.js`)
    .addEntry('layout/newsletter', `${ASSETS_ROOT}/views/V5/Layout/newsletter/newsletter.js`)
    // --> ALL THE TWIG PAGE <--
    .addEntry('page/login', `${ASSETS_ROOT}/views/V5/Page/Customer/login/login.js`)
    .addEntry('page/reset_password', `${ASSETS_ROOT}/views/V5/Page/Customer/login/reset_password.js`)
    .addEntry('page/confirm_reset_password', `${ASSETS_ROOT}/views/V5/Page/Customer/login/confirm_reset_password.js`)
    .addEntry('page/change_password', `${ASSETS_ROOT}/views/V5/Page/Customer/login/change_password.js`)
    .addEntry('page/register', `${ASSETS_ROOT}/views/V5/Page/Customer/register/register.js`)
    .addEntry('page/confirmation', `${ASSETS_ROOT}/views/Order/order_confirmation/confirmation.js`)
    .addEntry('page/basket', `${ASSETS_ROOT}/views/ShoppingCart/basket.js`)
    .addEntry('page/account', `${ASSETS_ROOT}/views/Customer/account.js`)
    .addEntry('page/brand', `${ASSETS_ROOT}/views/Brand/brand.js`)
    // --> ALL THE GUIDES TWIG  <--
    .addEntry('guide/social', `${ASSETS_ROOT}/views/Common/social_macro/social_macro.js`)
    // --> ALL THE TWIG FORM <--
    .addEntry('form/account', `${ASSETS_ROOT}/views/V5/Form/Account/FormAccount.js`)

    /**
     * Vue applications
     *   --> ALL THE ENTRIES BELOW SHOULD BE SORTED ALPHABETICALLY <--
     */
    .addEntry('account/addresses', `${ASSETS_ROOT}/js/account/addresses/app.js`)
    .addEntry('account/creation', `${ASSETS_ROOT}/js/account/creation/app.js`)
    .addEntry('account/order', `${ASSETS_ROOT}/js/account/orders/app_order.js`)
    .addEntry('account/orders', `${ASSETS_ROOT}/js/account/orders/app_orders.js`)
    .addEntry('account/validation', `${ASSETS_ROOT}/js/account/validation.js`)
    .addEntry('account/communication', `${ASSETS_ROOT}/views/V5/Form/Account/communication.js`)
    .addEntry('advice/question/submit', `${ASSETS_ROOT}/js/advice/question/submit_app.js`)
    .addEntry('advice/question/list', `${ASSETS_ROOT}/js/advice/question/list_app.js`)
    .addEntry('advice/question/display_one', `${ASSETS_ROOT}/js/advice/question/display_one_app.js`)
    .addEntry('advice/question/display_one_answer', `${ASSETS_ROOT}/js/advice/question/display_one_answer_app.js`)
    .addEntry('advice/review/display_one', `${ASSETS_ROOT}/js/advice/review/display_one_app.js`)
    .addEntry('advice/review/submit', `${ASSETS_ROOT}/js/advice/review/submit_app.js`)
    .addEntry('advice/review/list', `${ASSETS_ROOT}/js/advice/review/list_app.js`)
    .addEntry('article', `${ASSETS_ROOT}/js/article/app.js`)
    .addEntry('article_lister', `${ASSETS_ROOT}/js/article_lister/app.js`)
    .addEntry('article_answers_questions', `${ASSETS_ROOT}/js/article_answers_questions/app.js`)
    .addEntry('article_reviews', `${ASSETS_ROOT}/js/article_reviews/app.js`)
    .addEntry('characteristics_sheet', `${ASSETS_ROOT}/js/characteristics_sheet/app.js`)
    .addEntry('checkout_process_v2', `${ASSETS_ROOT}/js/checkout_process_v2/app.js`)
    .addEntry('recovery', `${ASSETS_ROOT}/js/recovery/app.js`)
    .addEntry('contact', `${ASSETS_ROOT}/js/contact/app.js`)
    .addEntry('installments', `${ASSETS_ROOT}/js/installments/app.js`)
    .addEntry('reference_price', `${ASSETS_ROOT}/js/reference_price/app.js`)
    .addEntry('contact_us', `${ASSETS_ROOT}/js/contact_us/app.js`)
    .addEntry('cookies', `${ASSETS_ROOT}/js/cookies/app.js`)
    .addEntry('installation/equipment_selector', `${ASSETS_ROOT}/js/installation/equipment_selector/app.js`)
    .addEntry('installation/edit', `${ASSETS_ROOT}/js/installation/edit/app.js`)
    .addEntry('installation/list', `${ASSETS_ROOT}/js/installation/list/app.js`)
    .addEntry('newsletter/registration', `${ASSETS_ROOT}/js/newsletter/registration_app.js`)
    .addEntry('order/basket', `${ASSETS_ROOT}/js/order/basket/app.js`)
    .addEntry('search', `${ASSETS_ROOT}/js/search/app.js`)
    .addEntry('videoprojector', `${ASSETS_ROOT}/js/videoprojector/app.js`)

    /**
     * used by Webpack when trying to resolve modules.
     */
    .addAliases({
        '@': path.resolve(__dirname, `${ASSETS_ROOT}/js`),
    })
    .enableVueLoader(() => {}, { runtimeCompilerBuild: true })

    // uncomment if you're having problems with a jQuery plugin
    .autoProvidejQuery()

    // uncomment if you use Sass/SCSS files
    .enableSassLoader()
    .enablePostCssLoader()

    // will require an extra script tag for runtime.js
    // but, you probably want this, unless you're building a single-page app
    .enableSingleRuntimeChunk()
    .cleanupOutputBeforeBuild()
    .enableSourceMaps(!Encore.isProduction())
    .splitEntryChunks()

    // enables hashed filenames (e.g. app.abc123.css)
    .enableVersioning(Encore.isProduction())

    /**
     * configure the options passed to the terser-webpack-plugin.
     * A list of available options can be found at https://github.com/webpack-contrib/terser-webpack-plugin
     * Encore uses the version 1.4.5
     */
    .configureTerserPlugin((options) => {
        options.parallel = true
        options.extractComments = false
        options.terserOptions = {
            output: {
                comments: false,
            },
            toplevel: true,
        }
    })
    .configureCssLoader((options) => {
        options.url = false
    })

    .configureMiniCssExtractPlugin(
        (loaderOptions) => {},
        (pluginOptions) => {
            pluginOptions.ignoreOrder = true
        }
    )

    /**
     * Add svg loader as specific loader to allow import as raw markup in vue components
     */
    .addLoader({
        test: /\.svg$/,
        issuer: /\.(vue|js|svg)$/,
        use: [
            'vue-loader',
            {
                loader: 'svg-to-vue-component/loader',
            },
        ],
    })

    /**
     * Remove svg from image loader default rules
     * @see https://github.com/egoist/svg-to-vue-component
     * @usage import ArbitraryName from '@/../icons/path/to/filename.svg'
     */
    .configureImageRule({}, function (rule) {
        rule.test = /\.(png|jpg|jpeg|gif|ico|webp|avif)$/
    })
    .configureDevServerOptions((options) => {
        options.server = {
            type: 'https',
            options: {
                key: '../docker-stack/shared/certs/local-key.pem',
                cert: '../docker-stack/shared/certs/local-cert.pem',
            },
        }
    })

module.exports = (env) => {
    if (Encore.isProduction()) {
        if (env.hasOwnProperty('REPORT')) {
            console.info('Using report mode: files are not going to be actually built')

            const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
            Encore.addPlugin(new BundleAnalyzerPlugin())
        }

        /**
         * change how the name of each output file is generated.
         * Necessary because by default css and js are in root in built directory and not separated
         */
        Encore.configureFilenames({
            js: 'js/[name].[contenthash].js',
            css: 'css/[name].[contenthash].css',
        })

        // Load project variables
        const file = fs.readFileSync(path.resolve(__dirname, './app/config/parameters.yml'), 'utf8')
        const parameters = YAML.parse(file).parameters
        const build_mode = (env && env.hasOwnProperty('BUILD_MODE') ? env.BUILD_MODE : 'dev').toUpperCase()

        // Handle CDN URL PREFIX from build mode
        console.info('BUILD MODE:', build_mode)
        let CDN_URL_PREFIX = ''

        switch (build_mode) {
            case 'DEPLOY':
                // On production we will replace the placeholders
                // with he good value upon deployment after building the assets
                CDN_URL_PREFIX = '[CDN_SON_VIDEO]'
                console.info('[DEPLOY] Using a placeholder as CDN:', CDN_URL_PREFIX)
                break
            case 'TEST':
                // On local environment we run tests on a specific non https url (http://test.url)
                // compared to dev (https://url)
                // Should be used on CI too as the php environment will use the same parameter
                // for webpack version strategy in config.test.yaml
                CDN_URL_PREFIX = parameters['cdn.assets.test'].trim()
                console.info(
                    '[TEST] CDN URL PREFIX (key "cdn.assets.test" extracted from "app/config/parameters.yml"):',
                    CDN_URL_PREFIX
                )
                break
            default:
                CDN_URL_PREFIX = parameters['cdn.assets'].trim()
                console.info(
                    '[DEFAULT] CDN URL PREFIX (key "cdn.assets" extracted from "app/config/parameters.yml"):',
                    CDN_URL_PREFIX
                )
        }

        Encore
            // Public path should be supplied via an external file allowing the assets to be built on production, staging and validation for CI
            .setPublicPath(`${CDN_URL_PREFIX}/dist`)
            .setManifestKeyPrefix('dist/')
    }

    Encore.configureCssMinimizerPlugin((options) => {
        options.minimizerOptions = {
            autoprefixer: true,
            discardUnused: true,
            mergeIdents: true,
            reduceIdents: true,
        }
    })

    return Encore.getWebpackConfig()
}
